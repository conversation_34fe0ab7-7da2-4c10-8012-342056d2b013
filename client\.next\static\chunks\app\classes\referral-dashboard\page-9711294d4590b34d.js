(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8694],{7583:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(95155);t(12115);var a=t(6874),l=t.n(a),n=t(66766),i=t(29911);let c=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(n.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:i.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:i.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:i.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:i.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:i.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:i.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:i.kUm,label:"Tumblr"}].map(e=>{let{href:s,icon:t,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:s,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(t,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(n.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},25931:(e,s,t)=>{Promise.resolve().then(t.bind(t,54889))},54889:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(95155),a=t(12115),l=t(66695),n=t(30285),i=t(62523),c=t(26126),d=t(37475),o=t(17580),x=t(55670);let m=(0,t(19946).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var h=t(66516),u=t(24357),f=t(33786),j=t(69074),p=t(56671),g=t(55077),y=t(7632),N=t(60287),b=t(7583),v=t(70347);function w(){let[e,s]=(0,a.useState)(null),[t,w]=(0,a.useState)(!0),[k,E]=(0,a.useState)({startDate:"",endDate:""}),O=[{accessorKey:"referredUserName",header:"Name",cell:e=>{let{row:s}=e;return(0,r.jsx)("span",{className:"font-medium",children:s.original.referredUserName})}},{accessorKey:"referredUserEmail",header:"Email",cell:e=>{let{row:s}=e;return(0,r.jsx)("span",{className:"text-gray-700",children:s.original.referredUserEmail})}},{accessorKey:"referredUserType",header:"Type",cell:e=>{let{row:s}=e;return(0,r.jsx)(c.E,{variant:"STUDENT"===s.original.referredUserType?"default":"secondary",children:s.original.referredUserType})}},{accessorKey:"earnings",header:()=>(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Earnings Status"]}),cell:e=>{let{row:s}=e,{earnings:t}=s.original;return t&&t.length>0?(0,r.jsx)("div",{className:"space-y-1",children:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("span",{className:"font-medium",children:["₹",e.amount]}),(0,r.jsxs)("span",{className:"text-gray-500 ml-1",children:["(","REGISTRATION"===e.earningType?"Registration":"U-whiz",")"]})]}),(0,r.jsx)(c.E,{variant:"PAID"===e.paymentStatus?"default":"secondary",className:"PAID"===e.paymentStatus?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800",children:"PAID"===e.paymentStatus?"Paid":"Pending"})]},e.id))}):(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:"No earnings yet"})}},{accessorKey:"createdAt",header:"Date",cell:e=>{let{row:s}=e;return(0,r.jsx)("span",{className:"text-gray-700",children:(0,y.GP)(new Date(s.original.createdAt),"MMM dd, yyyy HH:mm")})}}],D=async()=>{try{w(!0);let e=await g.S.get("/referral/dashboard");e.data.success&&s(e.data.data)}catch(e){console.error("Error fetching referral data:",e),p.oR.error("Failed to load referral data")}finally{w(!1)}},R=async()=>{try{let e=await g.S.post("/referral/generate-link");e.data.success&&(s(s=>s?{...s,referralCode:e.data.data.referralCode,links:e.data.data.links}:null),p.oR.success("Referral links generated successfully!"))}catch(e){console.error("Error generating referral link:",e),p.oR.error("Failed to generate referral links")}},P=(e,s)=>{navigator.clipboard.writeText(e),p.oR.success("".concat(s," link copied to clipboard!"))},S=(0,a.useCallback)(async()=>{try{let e=new URLSearchParams;k.startDate&&e.append("startDate",k.startDate),k.endDate&&e.append("endDate",k.endDate);let t=await g.S.get("/referral/history?".concat(e.toString()));t.data.success&&s(e=>e?{...e,history:t.data.data.history}:null)}catch(e){console.error("Error fetching history:",e),p.oR.error("Failed to load referral history")}},[k.startDate,k.endDate]);return((0,a.useEffect)(()=>{D()},[]),(0,a.useEffect)(()=>{k.startDate&&k.endDate&&S()},[k,S]),t)?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.default,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-7xl",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Referral Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track your referrals and earn rewards"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Referrals"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:(null==e?void 0:e.totalReferrals)||0})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Students Referred"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==e?void 0:e.studentsReferred)||0})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Classes Referred"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==e?void 0:e.classesReferred)||0})})]})]}),(null==e?void 0:e.earnings)&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Earnings"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["₹",e.earnings.totalEarnings]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Registration + U-whiz"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Registration Earnings"}),(0,r.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:["₹",e.earnings.registrationEarnings]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"₹10 per student"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"U-whiz Earnings"}),(0,r.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["₹",e.earnings.uwhizEarnings]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"₹25 per application"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Payment Status"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsxs)("span",{className:"text-green-600 font-semibold",children:["Paid: ₹",e.earnings.paidEarnings]})}),(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsxs)("span",{className:"text-orange-600 font-semibold",children:["Pending: ₹",e.earnings.unpaidEarnings]})})]})})]})]}),(0,r.jsxs)(l.Zp,{className:"mb-8",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"My Referral Links"]}),(0,r.jsx)(l.BT,{children:"Share these links to refer new students and classes"})]}),(0,r.jsx)(l.Wu,{className:"space-y-4",children:(null==e?void 0:e.links)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Student Registration Link"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.p,{value:e.links.studentLink,readOnly:!0,className:"flex-1"}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P(e.links.studentLink,"Student"),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.links.studentLink,"_blank"),children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Classes Registration Link"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.p,{value:e.links.classLink,readOnly:!0,className:"flex-1"}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P(e.links.classLink,"Classes"),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.links.classLink,"_blank"),children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"No referral links generated yet"}),(0,r.jsx)(n.$,{onClick:R,className:"bg-orange-500 hover:bg-orange-600",children:"Generate Referral Links"})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5"}),"Referral History"]}),(0,r.jsx)(l.BT,{children:"Track all your successful referrals"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"flex gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Start Date"}),(0,r.jsx)(i.p,{type:"date",value:k.startDate,onChange:e=>E(s=>({...s,startDate:e.target.value}))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"End Date"}),(0,r.jsx)(i.p,{type:"date",value:k.endDate,onChange:e=>E(s=>({...s,endDate:e.target.value}))})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)(n.$,{variant:"outline",onClick:()=>E({startDate:"",endDate:""}),children:"Clear Filter"})})]}),(0,r.jsx)(N.E,{columns:O,data:(null==e?void 0:e.history)||[],fetchData:()=>Promise.resolve(),totalItems:(null==e?void 0:e.history.length)||0,isLoading:t,hidePagination:!0})]})]})]}),(0,r.jsx)(b.default,{})]})}},74436:(e,s,t)=>{"use strict";t.d(s,{k5:()=>o});var r=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=r.createContext&&r.createContext(a),n=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function c(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);s&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function d(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?c(Object(t),!0).forEach(function(s){var r,a,l;r=e,a=s,l=t[s],(a=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,s||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(a))in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>r.createElement(x,i({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,t)=>r.createElement(s.tag,d({key:t},s.attr),e(s.child)))}(e.child))}function x(e){var s=s=>{var t,{attr:a,size:l,title:c}=e,o=function(e,s){if(null==e)return{};var t,r,a=function(e,s){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(s.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,s);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)t=l[r],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,n),x=l||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,a,o,{className:t,style:d(d({color:e.color||s.color},s.style),e.style),height:x,width:x,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==l?r.createElement(l.Consumer,null,e=>s(e)):s(a)}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,7632,7670,347,7270,8441,1684,7358],()=>s(25931)),_N_E=e.O()}]);