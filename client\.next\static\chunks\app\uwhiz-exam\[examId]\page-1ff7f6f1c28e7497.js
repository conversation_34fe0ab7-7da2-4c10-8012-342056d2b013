(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8561],{14186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:u,...m}=e;return(0,s.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:r,strokeWidth:i?24*Number(n)/Number(a):n,className:l("lucide",c),...m},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,s.forwardRef)((r,n)=>{let{className:o,...d}=r;return(0,s.createElement)(c,{ref:n,iconNode:t,className:l("lucide-".concat(a(i(e))),"lucide-".concat(e),o),...d})});return r.displayName=i(e),r}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>l});var s=r(95155);r(12115);var a=r(66634),n=r(74466),i=r(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:t})),...c})}},55077:(e,t,r)=>{"use strict";r.d(t,{S:()=>i});var s=r(23464),a=r(56671);let n=r(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let i=s.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":n;let r=localStorage.getItem("studentToken");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(a.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var s=r(95155),a=r(30285),n=r(12115),i=r(55077);let l=async(e,t,r,s)=>{try{return(await i.S.get("questionForStudentRouter?studentId=".concat(e,"&medium=").concat(t,"&standard=").concat(r,"&examId=").concat(s),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var a,n;throw Error("Failed To Get Student Question Detail: ".concat((null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message)||e.message))}},o=async(e,t)=>{try{return(await i.S.get("questionForStudentRouter/state?studentId=".concat(e,"&examId=").concat(t),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var r,s;throw Error("Failed To Get Quiz State: ".concat((null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||e.message))}},c=async(e,t,r)=>{try{await i.S.post("questionForStudentRouter/state?studentId=".concat(e,"&examId=").concat(t),r,{headers:{"Server-Select":"uwhizServer"}})}catch(e){var s,a;throw Error("Failed To Update Quiz State: ".concat((null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||e.message))}},d=async(e,t)=>{try{await i.S.delete("questionForStudentRouter/state?studentId=".concat(e,"&examId=").concat(t),{headers:{"Server-Select":"uwhizServer"}})}catch(e){var r,s;throw Error("Failed To Clear Quiz State: ".concat((null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||e.message))}},u=async e=>{try{return(await i.S.post("/saveExamAnswer",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,r;return{success:!1,error:"Failed to store Answer: ".concat((null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)||e.message)}}},m=async e=>{try{return(await i.S.post("/quizTermination",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,r;return{success:!1,error:"Failed to save termination log: ".concat((null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)||e.message)}}},h=async(e,t)=>{try{return(await i.S.get("check-attempt/count?studentId=".concat(e,"&examId=").concat(t),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var r,s;return{success:!1,error:"Failed To Get Count of termination: ".concat((null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||e.message)}}};var x=r(35695),g=r(94631),p=r(14186),f=r(56671),v=r(66766),w=r(88927),b=r(84355),y=r(19946);let j=(0,y.A)("camera-off",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16",key:"qmtpty"}],["path",{d:"M9.5 4h5L17 7h3a2 2 0 0 1 2 2v7.5",key:"1ufyfc"}],["path",{d:"M14.121 15.121A3 3 0 1 1 9.88 10.88",key:"11zox6"}]]),k=(0,y.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),N=async e=>{try{let t=await i.S.post("/exam-monitoring/upload-photo",e);return{success:!0,data:t.data.data}}catch(e){var t,r;return console.error("Error uploading exam photo:",e),{success:!1,error:(null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)||e.message||"Failed to upload photo"}}},A=e=>{let{studentId:t,examId:r,isExamActive:a,onCameraError:i,onCameraStatus:l}=e,o=(0,n.useRef)(null),c=(0,n.useRef)(null),d=(0,n.useRef)(null),u=(0,n.useRef)(null),[m,h]=(0,n.useState)(!1),[x,g]=(0,n.useState)(null),p=(0,n.useCallback)(async()=>{try{g(null);let e=await navigator.mediaDevices.getUserMedia({video:{width:640,height:480,facingMode:"user"}});o.current&&(o.current.srcObject=e,d.current=e,h(!0),null==l||l(!0))}catch(t){let e="NotAllowedError"===t.name?"Camera access denied":"Camera not available";g(e),null==i||i(e),null==l||l(!1),f.oR.error(e)}},[i,l]),v=(0,n.useCallback)(async()=>{if(o.current&&c.current&&m)try{let e=o.current,s=c.current,a=s.getContext("2d");if(!a)return;s.width=e.videoWidth,s.height=e.videoHeight,a.drawImage(e,0,0,s.width,s.height);let n=s.toDataURL("image/jpeg",.7),i=await N({studentId:t,examId:r,photoData:n});i.success||console.error("Failed to upload photo:",i.error)}catch(e){console.error("Failed to capture or upload photo:",e)}},[t,r,m]),w=(0,n.useCallback)(()=>{u.current&&(clearInterval(u.current),u.current=null),d.current&&(d.current.getTracks().forEach(e=>e.stop()),d.current=null),h(!1)},[]);return(0,n.useEffect)(()=>{a&&!m?p():!a&&m&&w()},[a,m,p,w]),(0,n.useEffect)(()=>{if(m&&a){let e=setTimeout(v,1e4);return u.current=setInterval(v,12e4),()=>{clearTimeout(e),u.current&&(clearInterval(u.current),u.current=null)}}return()=>{u.current&&(clearInterval(u.current),u.current=null)}},[m,a,v]),(0,n.useEffect)(()=>()=>w(),[w]),(0,s.jsxs)("div",{className:"fixed top-4 right-4 z-50",children:[(0,s.jsxs)("div",{className:"bg-black rounded-lg p-2 shadow-lg border-2 border-orange-500",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[m?(0,s.jsx)(b.A,{className:"w-4 h-4 text-green-500"}):(0,s.jsx)(j,{className:"w-4 h-4 text-red-500"}),(0,s.jsx)("span",{className:"text-white text-xs font-medium",children:m?"Monitoring Active":"Camera Inactive"})]}),x?(0,s.jsx)("div",{className:"w-32 h-24 bg-red-900 rounded flex items-center justify-center",children:(0,s.jsx)(k,{className:"w-6 h-6 text-red-400"})}):(0,s.jsx)("video",{ref:o,className:"w-32 h-24 rounded object-cover transform scale-x-[-1]",autoPlay:!0,playsInline:!0,muted:!0}),x&&(0,s.jsx)("div",{className:"mt-2 text-xs text-red-400 max-w-32",children:x})]}),(0,s.jsx)("canvas",{ref:c,className:"hidden"})]})},S=n.memo(e=>{let{examName:t}=e;return(0,s.jsx)("header",{className:"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(v.default,{height:60,width:60,src:w.A.src,alt:"Uwhiz Logo",quality:100,className:"object-contain sm:h-20 sm:w-20"}),(0,s.jsx)("h1",{className:"text-lg sm:text-2xl font-bold tracking-tight",children:t.toUpperCase()})]})})});function E(){let e=(0,x.useRouter)(),{examId:t}=(0,x.useParams)(),r=Array.isArray(t)?t[0]:t||"",i=null;try{let e=localStorage.getItem("student_data");i=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),i=null}let[v,w]=(0,n.useState)(!i),[b,y]=(0,n.useState)(!1),[j,k]=(0,n.useState)(!1),[N,E]=(0,n.useState)(!1),[z,C]=(0,n.useState)(!1),[F,I]=(0,n.useState)(!1),[R,q]=(0,n.useState)([]),[T,O]=(0,n.useState)(0),[Q,L]=(0,n.useState)(0),[U,P]=(0,n.useState)([]),[M,D]=(0,n.useState)(!1),[_,$]=(0,n.useState)(null),[G,W]=(0,n.useState)(!1),Y=(0,n.useRef)(null),[B,H]=(0,n.useState)(""),[K,J]=(0,n.useState)(0),[X,V]=(0,n.useState)(!1),Z=async()=>{if(!i||!r)return 0;try{let e=await h(i,Number(r));return"number"==typeof e?e:0}catch(e){return console.error("Failed to fetch violation count:",e),0}};(0,n.useEffect)(()=>{(async()=>{J(await Z())})()},[i,r]),(0,n.useEffect)(()=>{K>=3&&(I(!0),H("Quiz terminated due to multiple cheating attempts."))},[K]),console.log(k),(0,n.useEffect)(()=>{(async()=>{J(await Z())})()},[i,r]),(0,n.useEffect)(()=>{K>=3&&(I(!0),H("Quiz terminated due to multiple cheating attempts."))},[K]),(0,n.useEffect)(()=>(Y.current=new Audio("/clock-ticking-sound-effect.mp3"),Y.current.loop=!0,()=>{Y.current&&(Y.current.pause(),Y.current=null)}),[]),(0,n.useEffect)(()=>{!(R.length>0)||!(Q<=5)||!(Q>0)||N||F||M||v||j||!Y.current?Y.current&&Y.current.pause():Y.current.play().catch(e=>{console.error("Failed to play tick sound:",e)})},[Q,R,N,F,M,v,j]);let ee=(0,n.useCallback)((e,t)=>{$(t),P(e=>e.find(e=>e.questionId===R[T].id)?e.map(e=>e.questionId===R[T].id?{...e,selectedAnswer:t}:e):[...e,{questionId:R[T].id,selectedAnswer:t}])},[T,R]),et=(0,n.useCallback)(async()=>{if(!b){if(i&&r){X||e.push("/uwhiz"),y(!0);try{(await u({studentId:i,examId:Number(r),questionId:R[T].id,selectedAns:_})).success&&f.oR.success("Answer saved successfully!")}catch(e){f.oR.error("Failed to save answer.",{description:e instanceof Error?e.message:"Unknown error"})}finally{y(!1)}}else f.oR.info("Question skipped.");T<R.length-1?(O(T+1),$(null)):D(!0)}},[T,R,_,i,r]);(0,n.useEffect)(()=>{if(R.length>0&&Q>0&&!N&&!F&&!v&&!j){let e=setInterval(()=>{L(t=>{let r=t-1;return r<=0?(clearInterval(e),et(),0):r})},1e3);return()=>clearInterval(e)}},[Q,R,T,N,F,v,j,et]),(0,n.useEffect)(()=>{if(R.length>0&&!N&&!F&&!v&&!j){var e;L((null===(e=R[T])||void 0===e?void 0:e.timePerQuestion)||30)}},[T,R,N,F,v,j]);let er=(0,n.useCallback)(async()=>{if(!i||!r){w(!0);return}try{var e,t;let{classroom:s,medium:a}={classroom:"STD 10",medium:"GUJARATI"},n=await o(i,r);if(n){q(n.questions),O(n.currentQuestionIndex),P(n.userAnswers),L((null===(e=n.questions[n.currentQuestionIndex])||void 0===e?void 0:e.timePerQuestion)||30),n.currentQuestionIndex>=n.questions.length?D(!0):E(!0);return}let c=await l(i,a,s,r);c&&Array.isArray(c)?(q(c),L((null===(t=c[0])||void 0===t?void 0:t.timePerQuestion)||30),E(!0)):f.oR.error("No questions found or invalid response.")}catch(t){if(t instanceof Error&&t.message.startsWith("{"))try{let e=JSON.parse(t.message);if(403===e.status||500===e.status){H(e.message||"Your quiz has been terminated due to multiple cheating attempts."),I(!0),f.oR.error(e.message||"Your quiz has been terminated due to multiple cheating attempts.");return}}catch(e){f.oR.error("Failed to fetch quiz state.")}let e="Failed to fetch data: ".concat(t instanceof Error?t.message:"Unknown error");f.oR.error(e)}},[i,r]);(0,n.useEffect)(()=>{i&&r?er():w(!0)},[i,r,er]),(0,n.useEffect)(()=>{!(R.length>0)||N||F||!i||!r||v||j||c(i,r,{questions:R,currentQuestionIndex:T,userAnswers:U}).catch(e=>{f.oR.error("Failed to sync quiz progress.",{description:e instanceof Error?e.message:"Unknown error"})})},[T,R,i,r,N,F,v,j]);let es=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;try{if(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement){if(document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen&&await document.mozCancelFullScreen(),await new Promise(e=>setTimeout(e,100)),!document.fullscreenElement&&!document.webkitFullscreenElement&&!document.mozFullScreenElement)return!0;if(t<e)return await es(e,t+1);throw Error("Max attempts reached")}return!0}catch(r){if(console.error("Failed to exit full-screen mode (attempt ".concat(t,"):"),r),t<e)return await new Promise(e=>setTimeout(e,500)),await es(e,t+1);return f.oR.error("Failed to exit full-screen mode. Please press Esc to exit manually."),!1}};(0,n.useEffect)(()=>{M&&i&&r&&es().then(e=>{e&&d(i,r).catch(e=>{console.error("Failed to clear quiz state:",e)})})},[M,i,r]),(0,n.useEffect)(()=>{F&&i&&r&&es().then(e=>{console.log("Quiz terminated, cache cleared and full-screen exited.",e)})},[F,i,r]);let ea=()=>{let e=document.documentElement;e.requestFullscreen&&e.requestFullscreen().catch(e=>console.error("Failed to enter fullscreen:",e))},en=(0,n.useCallback)(async e=>{if(N||v||j||z||G)return;let t=["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"],s=e.ctrlKey&&e.shiftKey&&("I"===e.key||"J"===e.key||"C"===e.key)||e.metaKey&&e.altKey&&"I"===e.key||"F12"===e.key,a=(e.ctrlKey||e.metaKey)&&("c"===e.key||"C"===e.key);if(["Alt","Control","Tab","Shift","Enter"].includes(e.key)||t.includes(e.key)||s||a){if(e.preventDefault(),a){f.oR.warning("Copying is disabled during the quiz.");return}if(!i){J(0);return}W(!0);try{let a=s?"DevTools shortcut":t.includes(e.key)?'Function key "'.concat(e.key,'"'):'Restricted key "'.concat(e.key,'"');await m({examId:Number(r),studentId:i,reason:a});let n=await h(i,Number(r));J(n),1===n?(C(!0),f.oR.warning("".concat(a," detected."))):2===n?(C(!0),f.oR.warning("".concat(a," detected. One more violation will terminate the quiz."))):n>=3&&(I(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{W(!1)}}},[r,i,N,v,j,z,G]),ei=(0,n.useCallback)(async()=>{if(!N&&!v&&!j&&!z&&!G&&document.hidden){W(!0);try{if(await m({examId:Number(r),studentId:i,reason:"Tab switch"}),!i){J(0);return}let e=await h(i,Number(r));J(e),1===e?(C(!0),f.oR.warning("Tab switch detected.")):2===e?(C(!0),f.oR.warning("Again tab switch detected. One more violation will terminate the quiz.")):e>=3&&(I(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{W(!1)}}},[r,i,N,v,j,z,G]),el=(0,n.useCallback)(async e=>{N||v||j||z||G||(e.preventDefault(),f.oR.warning("Right-click is disabled during the quiz."))},[r,i,N,v,j,z,G]),eo=(0,n.useCallback)(async()=>{if(!N&&!v&&!j&&!z&&!G){W(!0);try{await m({examId:Number(r),studentId:i,reason:"Window blur"});let e=await h(i,Number(r));J(e),1===e?(C(!0),f.oR.warning("Window focus lost.")):2===e?(C(!0),f.oR.warning("Window focus lost again. One more violation will terminate the quiz.")):e>=3&&(I(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{W(!1)}}},[r,i,N,v,j,z,G]),ec=(0,n.useCallback)(async()=>{if(!N&&!v&&!j&&!z&&!G&&!document.fullscreenElement){W(!0);try{if(await m({examId:Number(r),studentId:i,reason:"Full-screen exit"}),!i){J(0);return}let e=await h(i,Number(r));J(e),1===e?(C(!0),f.oR.warning("You have exited full-screen mode.")):2===e?(C(!0),f.oR.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.")):e>=3&&(I(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{W(!1)}}},[r,i,N,v,j,z,G]),ed=async()=>{if(I(!1),i&&r)try{await d(i,r)}catch(e){console.error("Failed to clear quiz state:",e)}(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)&&(await es()||f.oR.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.")),e.push("/uwhiz")};(0,n.useEffect)(()=>(N||v||j||F||(document.addEventListener("visibilitychange",ei),document.addEventListener("keydown",en),window.addEventListener("blur",eo),document.addEventListener("contextmenu",el),document.addEventListener("fullscreenchange",ec)),()=>{document.removeEventListener("visibilitychange",ei),document.removeEventListener("keydown",en),window.removeEventListener("blur",eo),document.removeEventListener("contextmenu",el),document.removeEventListener("fullscreenchange",ec)}),[ei,en,eo,el,ec,N,v,j,F]);let eu=(0,n.useCallback)(e=>{let t=Math.floor(e/60);return"".concat(t.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))},[]),em=(0,n.useMemo)(()=>R.length>0?R[0].exam_name:"Uwhiz - Super kids",[R]),eh=(0,n.useMemo)(()=>R.length>0?(T+1)/R.length*100:0,[T,R]),ex=e=>"w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white ".concat(_===e?"bg-orange-100 border-orange-500":"");if(v)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Login Required"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Please log in as a student to access the quiz."}),(0,s.jsx)(a.$,{onClick:()=>e.push("/student/login?redirect=/uwhiz-exam/".concat(t)),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Login to Continue"})]})});if(j)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Complete Your Profile"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Your profile is incomplete. Please complete your profile to proceed."}),(0,s.jsx)(a.$,{onClick:()=>{e.push("/student/profile?quiz=true&examId="+t)},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Complete Profile"})]})});if(0===R.length)return(0,s.jsxs)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:[(0,s.jsx)("p",{className:"text-base sm:text-xl font-medium mr-4",children:"Loading questions..."}),(0,s.jsx)(g.A,{className:"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"})]});if(M)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,s.jsxs)("div",{className:"text-center p-4 sm:p-6 bg-white rounded-lg shadow-xl max-w-md w-full",children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-4xl font-bold text-customOrange mb-4",children:"Quiz Completed!"}),(0,s.jsx)("p",{className:"text-base sm:text-xl mb-4",children:"Your exam submitted successfully."}),(0,s.jsx)(a.$,{className:"bg-customOrange text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg transition-all",onClick:ed,children:"Go To Home"})]})});let eg=R[T];return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-100 text-gray-900",children:[N&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Start Quiz"}),(0,s.jsx)("p",{className:"font-semibold mb-4 text-sm sm:text-base text-gray-600",children:"Note: This is a mock exam for testing purposes only."}),i&&(0,s.jsx)(A,{studentId:i,examId:Number(r),isExamActive:!F&&!M,onCameraError:e=>{console.error("Camera error:",e),V(!1)},onCameraStatus:e=>{V(e)}}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",children:[(0,s.jsx)("p",{className:"font-semibold mb-2",children:"Instructions:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside mb-4 text-gray-600",children:[(0,s.jsx)("li",{children:"Do not switch tabs during the quiz."}),(0,s.jsx)("li",{children:"Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."}),(0,s.jsx)("li",{children:"Do not open Developer Tools."}),(0,s.jsx)("li",{children:"Do not exit full-screen mode."}),(0,s.jsx)("li",{children:"Do not interact with other windows or applications."}),(0,s.jsx)("li",{children:"Do not change the screen or minimize the quiz window."}),(0,s.jsx)("li",{children:"Do not receive or make calls during the quiz."}),(0,s.jsx)("li",{children:"Do not use split screen or floating windows on your device."})]}),(0,s.jsx)("p",{className:"font-semibold mb-2",children:"સૂચનાઓ (ગુજરાતી):"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,s.jsx)("li",{children:"ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."}),(0,s.jsx)("li",{children:"પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."}),(0,s.jsx)("li",{children:"ડેવલપર ટૂલ્સ ખોલશો નહીં."}),(0,s.jsx)("li",{children:"ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."}),(0,s.jsx)("li",{children:"ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."}),(0,s.jsx)("li",{children:"અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."}),(0,s.jsx)("li",{children:"સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."}),(0,s.jsx)("li",{children:"ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."}),(0,s.jsx)("li",{children:"તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."})]})]}),(0,s.jsx)(a.$,{onClick:()=>{if(E(!1),ea(),R.length>0){var e;L((null===(e=R[T])||void 0===e?void 0:e.timePerQuestion)||30)}},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",disabled:!X,children:X?"Start Quiz":"Waiting for Camera..."})]})}),z&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-customOrange",children:"Warning"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have performed a restricted action. Repeating this will terminate the quiz."}),(0,s.jsx)(a.$,{onClick:()=>C(!1),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"OK"})]})}),F&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-red-500",children:"Quiz Terminated"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:B||"Your quiz has been terminated due to multiple cheating attempts."}),(0,s.jsx)(a.$,{onClick:ed,className:"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",children:"Go to Home"})]})}),!N&&!v&&!j&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S,{examName:em}),i&&(0,s.jsx)(A,{studentId:i,examId:Number(r),isExamActive:!F&&!M,onCameraError:e=>{console.error("Camera error:",e)}}),(0,s.jsx)("div",{className:"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",children:(0,s.jsx)("div",{className:"h-1.5 bg-customOrange rounded-r-full transition-all duration-300",style:{width:"".concat(eh,"%")}})}),(0,s.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center w-full max-w-3xl",children:[(0,s.jsxs)("div",{className:"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"}),(0,s.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-customOrange",children:eu(Q)})]}),(0,s.jsxs)("div",{className:"w-full text-center flex flex-col items-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-3 sm:mb-4",children:(0,s.jsxs)("span",{className:"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",children:["Question ",T+1," of ",R.length]})}),(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",dangerouslySetInnerHTML:{__html:eg.question}}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",children:[(0,s.jsxs)(a.$,{variant:"outline",className:ex("optionOne"),onClick:()=>ee(eg.optionOne,"optionOne"),disabled:F,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"A"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eg.optionOne})]}),(0,s.jsxs)(a.$,{variant:"outline",className:ex("optionTwo"),onClick:()=>ee(eg.optionTwo,"optionTwo"),disabled:F,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"B"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eg.optionTwo})]}),(0,s.jsxs)(a.$,{variant:"outline",className:ex("optionThree"),onClick:()=>ee(eg.optionThree,"optionThree"),disabled:F,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"C"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eg.optionThree})]}),(0,s.jsxs)(a.$,{variant:"outline",className:ex("optionFour"),onClick:()=>ee(eg.optionFour,"optionFour"),disabled:F,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"D"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eg.optionFour})]})]})]}),(0,s.jsx)(a.$,{className:"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>et(),disabled:F||b,children:b?T===R.length-1?"Finish":"Next Question":"Save Answer"})]}),(0,s.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",children:[(0,s.jsx)("span",{children:"Powered by"}),(0,s.jsx)("span",{className:"font-semibold",children:"UEST EdTech"})]})]})})]})]})}S.displayName="QuizHeader"},59434:(e,t,r)=>{"use strict";r.d(t,{MB:()=>l,ZO:()=>i,cn:()=>n,xh:()=>o});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}let i=()=>localStorage.getItem("studentToken"),l=()=>{localStorage.removeItem("studentToken")},o=()=>!!i()},84355:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},88927:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s={src:"/_next/static/media/uwhizExam.5364baa3.png",height:626,width:798,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAElBMVEURCwcCAgEcEQogIB49Pj0lJSX5PC0XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nCXKuREAMAgEsb2H/lv2GDIFQtg2YsGHBIfkwGRP2k7KAwYIAEmvy1CUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6}},94631:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},99234:(e,t,r)=>{Promise.resolve().then(r.bind(r,59e3))}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4212,8441,1684,7358],()=>t(99234)),_N_E=e.O()}]);