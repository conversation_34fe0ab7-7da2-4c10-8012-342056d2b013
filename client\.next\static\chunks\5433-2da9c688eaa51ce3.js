"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5433],{60760:(e,t,r)=>{r.d(t,{N:()=>b});var n=r(95155),o=r(12115),i=r(90869),l=r(82885),s=r(97494),c=r(80845),a=r(27351),u=r(51508);class f extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,a.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:r,anchorX:i,root:l}=e,s=(0,o.useId)(),c=(0,o.useRef)(null),a=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,o.useContext)(u.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o,right:u}=a.current;if(r||!c.current||!e||!t)return;c.current.dataset.motionPopId=s;let f=document.createElement("style");p&&(f.nonce=p);let h=null!=l?l:document.head;return h.appendChild(f),f.sheet&&f.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(o):"right: ".concat(u),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{h.removeChild(f),h.contains(f)&&h.removeChild(f)}},[r]),(0,n.jsx)(f,{isPresent:r,childRef:c,sizeRef:a,children:o.cloneElement(t,{ref:c})})}let h=e=>{let{children:t,initial:r,isPresent:i,onExitComplete:s,custom:a,presenceAffectsLayout:u,mode:f,anchorX:h,root:m}=e,v=(0,l.M)(d),y=(0,o.useId)(),b=!0,g=(0,o.useMemo)(()=>(b=!1,{id:y,initial:r,isPresent:i,custom:a,onExitComplete:e=>{for(let t of(v.set(e,!0),v.values()))if(!t)return;s&&s()},register:e=>(v.set(e,!1),()=>v.delete(e))}),[i,v,s]);return u&&b&&(g={...g}),(0,o.useMemo)(()=>{v.forEach((e,t)=>v.set(t,!1))},[i]),o.useEffect(()=>{i||v.size||!s||s()},[i]),"popLayout"===f&&(t=(0,n.jsx)(p,{isPresent:i,anchorX:h,root:m,children:t})),(0,n.jsx)(c.t.Provider,{value:g,children:t})};function d(){return new Map}var m=r(32082);let v=e=>e.key||"";function y(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:r,initial:c=!0,onExitComplete:a,presenceAffectsLayout:u=!0,mode:f="sync",propagate:p=!1,anchorX:d="left",root:b}=e,[g,O]=(0,m.xQ)(p),j=(0,o.useMemo)(()=>y(t),[t]),w=p&&!g?[]:j.map(v),P=(0,o.useRef)(!0),E=(0,o.useRef)(j),x=(0,l.M)(()=>new Map),[C,k]=(0,o.useState)(j),[N,R]=(0,o.useState)(j);(0,s.E)(()=>{P.current=!1,E.current=j;for(let e=0;e<N.length;e++){let t=v(N[e]);w.includes(t)?x.delete(t):!0!==x.get(t)&&x.set(t,!1)}},[N,w.length,w.join("-")]);let S=[];if(j!==C){let e=[...j];for(let t=0;t<N.length;t++){let r=N[t],n=v(r);w.includes(n)||(e.splice(t,0,r),S.push(r))}return"wait"===f&&S.length&&(e=S),R(y(e)),k(j),null}let{forceRender:M}=(0,o.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:N.map(e=>{let t=v(e),o=(!p||!!g)&&(j===N||w.includes(t));return(0,n.jsx)(h,{isPresent:o,initial:(!P.current||!!c)&&void 0,custom:r,presenceAffectsLayout:u,mode:f,root:b,onExitComplete:o?void 0:()=>{if(!x.has(t))return;x.set(t,!0);let e=!0;x.forEach(t=>{t||(e=!1)}),e&&(null==M||M(),R(E.current),p&&(null==O||O()),a&&a())},anchorX:d,children:e},t)})})}},74436:(e,t,r)=>{r.d(t,{k5:()=>u});var n=r(12115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),l=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>n.createElement(f,s({attr:a({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,a({key:r},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var r,{attr:o,size:i,title:c}=e,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,l),f=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,u,{className:r,style:a(a({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}}}]);