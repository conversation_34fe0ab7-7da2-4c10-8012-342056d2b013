/******/ (() => { // webpackBootstrap
/*!*********************************************************!*\
  !*** ./modules/Testimonial/resources/views/js/index.js ***!
  \*********************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "message",
  name: "message"
}, {
  data: "rating",
  name: "rating"
}, {
  data: "createdAt",
  name: "createdAt"
}, {
  data: "status",
  name: "status"
}];
var table = commonDatatable("#testimonial_table", testimonialRoute.index, columns);
$(document).on("click", "#addTestimonialEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = testimonialRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Testimonial");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteTestimonialEntry", function () {
  var did = $(this).attr("data-deletetestimonialid");
  var url = testimonialRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;