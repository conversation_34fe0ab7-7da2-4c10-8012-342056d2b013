<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::open(['route' => 'thoughts.store','id'=>'createthoughts_form']) !!}
                    @include('Thoughts::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('Thoughts\Http\Requests\CreateThoughtsRequest', '#createthoughts_form') !!}
<script>
     var createthoughtsRoute = {
        store: "{{ route('thoughts.store') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/Thoughts/create.js')) }}"></script>