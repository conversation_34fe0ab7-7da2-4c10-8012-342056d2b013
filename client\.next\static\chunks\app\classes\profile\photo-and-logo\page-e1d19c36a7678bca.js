(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7624],{9604:(e,t,o)=>{Promise.resolve().then(o.bind(o,35335)),Promise.resolve().then(o.bind(o,22346))},22346:(e,t,o)=>{"use strict";o.d(t,{Separator:()=>i});var r=o(95155);o(12115);var a=o(14050),s=o(59434);function i(e){let{className:t,orientation:o="horizontal",decorative:i=!0,...l}=e;return(0,r.jsx)(a.b,{"data-slot":"separator-root",decorative:i,orientation:o,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...l})}},30285:(e,t,o)=>{"use strict";o.d(t,{$:()=>n,r:()=>l});var r=o(95155);o(12115);var a=o(66634),s=o(74466),i=o(59434);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n(e){let{className:t,variant:o,size:s,asChild:n=!1,...d}=e,c=n?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:o,size:s,className:t})),...d})}},35335:(e,t,o)=>{"use strict";o.d(t,{default:()=>j});var r=o(95155),a=o(90221),s=o(62177),i=o(55594),l=o(66766),n=o(12115),d=o(34540),c=o(94314),u=o(35695),p=o(56671),m=o(3159),g=o(75937),h=o(30285),v=o(62523),f=o(55077),x=o(45436);let b=["image/jpeg","image/png","image/gif","image/webp"],w=i.z.object({photo:i.z.instanceof(File).refine(e=>(null==e?void 0:e.size)<2097152,{message:"Photo must be less than 2MB"}).refine(e=>!e||b.includes(e.type),{message:"Only image files (JPEG, PNG, GIF, WEBP) are allowed"}).optional(),logo:i.z.instanceof(File).refine(e=>(null==e?void 0:e.size)<1048576,{message:"Logo must be less than 1MB"}).refine(e=>!e||b.includes(e.type),{message:"Only image files (JPEG, PNG, GIF, WEBP) are allowed"}).optional()});function j(){let[e,t]=(0,n.useState)(null),[o,i]=(0,n.useState)(null),[j,y]=(0,n.useState)(null),[C,S]=(0,n.useState)(null),[P,N]=(0,n.useState)(null),[I,E]=(0,n.useState)(null),[k,F]=(0,n.useState)({x:0,y:0}),[z,R]=(0,n.useState)(1),[_,O]=(0,n.useState)(null),A=(0,s.mN)({resolver:(0,a.u)(w),mode:"onChange",defaultValues:{photo:void 0,logo:void 0}}),L=(0,d.wA)(),T=(0,u.useRouter)(),B=(e,t)=>{var o;let r=null===(o=e.target.files)||void 0===o?void 0:o[0];if(r){if(!b.includes(r.type)){p.oR.error("Only image files (JPEG, PNG, GIF, WEBP) are allowed"),e.target.value="";return}let o=new FileReader;o.onload=()=>{E(o.result),N(t)},o.readAsDataURL(r)}},D=async(e,t)=>{let o=new window.Image;o.src=e,await new Promise(e=>o.onload=e);let r=document.createElement("canvas"),a=r.getContext("2d");return r.width=t.width,r.height=t.height,a.drawImage(o,t.x,t.y,t.width,t.height,0,0,t.width,t.height),new Promise(e=>{r.toBlob(t=>{t&&e(new File([t],"cropped-image.jpg",{type:"image/jpeg"}))},"image/jpeg")})},G=async()=>{if(I&&_)try{let e=await D(I,_),o=URL.createObjectURL(e);"photo"===P?(A.setValue("photo",e,{shouldValidate:!0}),t(o)):"logo"===P&&(A.setValue("logo",e,{shouldValidate:!0}),i(o)),N(null),E(null),F({x:0,y:0}),R(1)}catch(e){console.error("Error cropping image:",e),p.oR.error("Failed to crop image")}},{user:U}=(0,d.d4)(e=>e.user),V=async e=>{try{let t=new FormData;e.photo&&t.append("profilePhoto",e.photo),e.logo&&t.append("classesLogo",e.logo),await f.S.post("/classes-profile/images",t,{headers:{"Content-Type":"multipart/form-data"}}),await L((0,x.V)(U.id)),p.oR.success("Photos uploaded successfully!"),L((0,c.ac)(c._3.PHOTO_LOGO)),T.push("/classes/profile/education")}catch(e){console.error("Error uploading files:",e),p.oR.error("Failed to upload files")}},M=(0,d.d4)(e=>e.class.classData);return(0,n.useEffect)(()=>{if(M||(null==M?void 0:M.ClassAbout)){var e,t,o,r;(null==M?void 0:null===(e=M.ClassAbout)||void 0===e?void 0:e.profilePhoto)&&y("http://localhost:4005/"+(null==M?void 0:null===(o=M.ClassAbout)||void 0===o?void 0:o.profilePhoto)),(null==M?void 0:null===(t=M.ClassAbout)||void 0===t?void 0:t.classesLogo)&&S("http://localhost:4005/"+(null==M?void 0:null===(r=M.ClassAbout)||void 0===r?void 0:r.classesLogo))}},[M]),(0,r.jsxs)("div",{children:[(0,r.jsx)(g.lV,{...A,children:(0,r.jsxs)("form",{onSubmit:A.handleSubmit(V),className:"space-y-6",children:[(0,r.jsx)(g.zB,{control:A.control,name:"photo",render:()=>(0,r.jsxs)(g.eI,{children:[(0,r.jsx)(g.lR,{children:"Profile Photo"}),(0,r.jsx)(g.MJ,{children:(0,r.jsx)(v.p,{type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>B(e,"photo")})}),e||j?(0,r.jsx)(l.default,{src:e||j,alt:"Profile Preview",width:120,height:120,className:"rounded-full mt-2 border"}):null,(0,r.jsx)(g.C5,{})]})}),(0,r.jsx)(g.zB,{control:A.control,name:"logo",render:()=>(0,r.jsxs)(g.eI,{children:[(0,r.jsx)(g.lR,{children:"Classes Logo"}),(0,r.jsx)(g.MJ,{children:(0,r.jsx)(v.p,{type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>B(e,"logo")})}),o||C?(0,r.jsx)(l.default,{src:o||C,alt:"Logo Preview",width:120,height:120,className:"rounded-md mt-2 border bg-white"}):null,(0,r.jsx)(g.C5,{})]})}),(0,r.jsx)(h.$,{type:"submit",children:"Upload"})]})}),P&&I&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg w-[90%] max-w-[500px]",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Crop Image"}),(0,r.jsx)("div",{className:"relative w-full h-[300px]",children:(0,r.jsx)(m.Ay,{image:I,crop:k,zoom:z,aspect:"photo"===P?1:4/3,onCropChange:F,onZoomChange:R,onCropComplete:(e,t)=>{O(t)}})}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("input",{type:"range",min:1,max:3,step:.1,value:z,onChange:e=>R(Number(e.target.value)),className:"w-full"})}),(0,r.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,r.jsx)(h.$,{variant:"outline",onClick:()=>N(null),children:"Cancel"}),(0,r.jsx)(h.$,{onClick:G,children:"Save Crop"})]})]})})]})}},45436:(e,t,o)=>{"use strict";o.d(t,{V:()=>a});var r=o(55077);let a=(0,o(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:o}=t;try{return(await r.S.get("/classes/details/".concat(e))).data}catch(e){var a;return o((null===(a=e.response)||void 0===a?void 0:a.data)||"Fetch failed")}})},55077:(e,t,o)=>{"use strict";o.d(t,{S:()=>i});var r=o(23464),a=o(56671);let s=o(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",s);let i=r.A.create({baseURL:s,headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":s;let o=localStorage.getItem("studentToken");return o&&(e.headers.Authorization="Bearer ".concat(o)),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(a.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,o)=>{"use strict";o.d(t,{MB:()=>l,ZO:()=>i,cn:()=>s,xh:()=>n});var r=o(52596),a=o(39688);function s(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,a.QP)((0,r.$)(t))}let i=()=>localStorage.getItem("studentToken"),l=()=>{localStorage.removeItem("studentToken")},n=()=>!!i()},62523:(e,t,o)=>{"use strict";o.d(t,{p:()=>s});var r=o(95155);o(12115);var a=o(59434);function s(e){let{className:t,type:o,...s}=e;return(0,r.jsx)("input",{type:o,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},75937:(e,t,o)=>{"use strict";o.d(t,{lV:()=>c,MJ:()=>f,Rr:()=>x,zB:()=>p,eI:()=>h,lR:()=>v,C5:()=>b});var r=o(95155),a=o(12115),s=o(66634),i=o(62177),l=o(59434),n=o(24265);function d(e){let{className:t,...o}=e;return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...o})}let c=i.Op,u=a.createContext({}),p=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},m=()=>{let e=a.useContext(u),t=a.useContext(g),{getFieldState:o}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),s=o(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...s}},g=a.createContext({});function h(e){let{className:t,...o}=e,s=a.useId();return(0,r.jsx)(g.Provider,{value:{id:s},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",t),...o})})}function v(e){let{className:t,...o}=e,{error:a,formItemId:s}=m();return(0,r.jsx)(d,{"data-slot":"form-label","data-error":!!a,className:(0,l.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...o})}function f(e){let{...t}=e,{error:o,formItemId:a,formDescriptionId:i,formMessageId:l}=m();return(0,r.jsx)(s.DX,{"data-slot":"form-control",id:a,"aria-describedby":o?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!o,...t})}function x(e){let{className:t,...o}=e,{formDescriptionId:a}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:a,className:(0,l.cn)("text-muted-foreground text-sm",t),...o})}function b(e){var t;let{className:o,...a}=e,{error:s,formMessageId:i}=m(),n=s?String(null!==(t=null==s?void 0:s.message)&&void 0!==t?t:""):a.children;return n?(0,r.jsx)("p",{"data-slot":"form-message",id:i,className:(0,l.cn)("text-destructive text-sm",o),...a,children:n}):null}},94314:(e,t,o)=>{"use strict";o.d(t,{Ay:()=>n,_3:()=>a,ac:()=>i});var r=o(51990),a=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let s=(0,r.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let o=t.payload;e.completedForms[o]||(e.completedForms[o]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:l}=s.actions,n=s.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,4212,1342,6980,8441,1684,7358],()=>t(9604)),_N_E=e.O()}]);