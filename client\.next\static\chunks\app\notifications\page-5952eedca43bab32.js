(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6173],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(95155);s(12115);var r=s(6874),i=s.n(r),l=s(66766),n=s(29911);let c=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(l.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:r}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:r,children:(0,a.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},r)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(l.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},12767:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(95155);s(12115);var r=s(66634),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:i=!1,...c}=e,o=i?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),t),...c})}},27435:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var a=s(95155),r=s(12115),i=s(66695),l=s(30285),n=s(26126),c=s(62523),o=s(59409),d=s(23861),u=s(14186),x=s(40646),h=s(62525),m=s(66932),f=s(47924),p=s(52278),g=s(42355),v=s(13052),j=s(12767),b=s(18644),y=s(56671),w=s(75350),N=s(35695),k=s(90010),A=s(34540),O=s(70347),C=s(7583);function P(){let[e,t]=(0,r.useState)([]),[s,P]=(0,r.useState)([]),[S,z]=(0,r.useState)(0),[R,E]=(0,r.useState)(!0),[_,M]=(0,r.useState)(""),[F,L]=(0,r.useState)("all"),[T,D]=(0,r.useState)("all"),[Z,$]=(0,r.useState)(""),[W,U]=(0,r.useState)("all"),[q,I]=(0,r.useState)("all"),[H,B]=(0,r.useState)(!1),[V,Y]=(0,r.useState)(!1),[Q,G]=(0,r.useState)(1),[K,X]=(0,r.useState)(0),[J,ee]=(0,r.useState)(0),et=(0,N.useRouter)(),es=(0,A.d4)(e=>e.user.isAuthenticated),[ea,er]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(es){er("class");return}if(localStorage.getItem("studentToken")){er("student");return}er(null)},[es]);let ei=(0,r.useMemo)(()=>Array.isArray(e)?e:[],[e]),el=(0,r.useMemo)(()=>Array.isArray(s)?s:[],[s]),en=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{let s,a;if(E(!0),!ea||"class"===ea&&!es){t([]),P([]);return}if("class"===ea)[s,a]=await Promise.all([(0,b.HK)(e,10),(0,b.Pz)()]);else if("student"===ea)[s,a]=await Promise.all([(0,b.kI)(e,10),(0,b.sl)()]);else{t([]),P([]);return}let r=[];r=Array.isArray(s)?s:(null==s?void 0:s.notifications)&&Array.isArray(s.notifications)?s.notifications:[],t(r),P(r),z(a||0),(null==s?void 0:s.pagination)&&(G(s.pagination.currentPage),X(s.pagination.totalPages),ee(s.pagination.totalCount))}catch(e){console.error("❌ Error fetching notifications:",e),y.oR.error("Failed to fetch notifications"),t([]),P([])}finally{E(!1)}},[ea]);(0,r.useEffect)(()=>{if(ea){en();let e=setInterval(()=>en(1),3e4);return()=>clearInterval(e)}},[ea,en]);let ec=e=>{e>=1&&e<=K&&e!==Q&&en(e)};(0,r.useEffect)(()=>{let e=[...ei];_&&(e=e.filter(e=>e.title.toLowerCase().includes(_.toLowerCase())||e.message.toLowerCase().includes(_.toLowerCase()))),"all"!==F&&(e=e.filter(e=>e.type===F)),"read"===T?e=e.filter(e=>e.isRead):"unread"===T&&(e=e.filter(e=>!e.isRead)),P(e)},[ei,_,F,T]);let eo=async e=>{try{var s,a;e.isRead||("class"===ea?await (0,b.jc)(e.id):await (0,b.Ou)(e.id),t(t=>Array.isArray(t)?t.map(t=>t.id===e.id?{...t,isRead:!0}:t):[]),z(e=>Math.max(0,e-1))),(null===(s=e.data)||void 0===s?void 0:s.actionType)==="OPEN_CHAT"&&(null===(a=e.data)||void 0===a?void 0:a.redirectUrl)&&et.push(e.data.redirectUrl)}catch(e){console.error("Error marking notification as read:",e),y.oR.error("Failed to mark notification as read")}},ed=async()=>{try{"class"===ea?await (0,b.fm)():await (0,b.a1)(),t(e=>Array.isArray(e)?e.map(e=>({...e,isRead:!0})):[]),z(0),Y(!1),y.oR.success("All notifications marked as read")}catch(e){console.error("Error marking all as read:",e),y.oR.error("Failed to mark all notifications as read")}},eu=async()=>{try{"class"===ea?await (0,b.pO)():await (0,b.au)(),t([]),P([]),z(0),B(!1),y.oR.success("All notifications deleted successfully")}catch(e){console.error("Error deleting all notifications:",e),y.oR.error("Failed to delete all notifications")}},ex=[{title:"Total Notifications",value:J||0,icon:d.A,color:"text-blue-600"},{title:"Unread",value:S,icon:u.A,color:"text-red-600"},{title:"Read",value:Math.max(0,(J||0)-S),icon:x.A,color:"text-green-600"}];return R?(0,a.jsx)("div",{className:"container mx-auto p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-muted-foreground",children:"Loading notifications..."})]})})}):ea?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(O.default,{}),(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"My Notifications"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage all your notifications"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[S>0&&(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>Y(!0),className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Mark All Read"]}),ei.length>0&&0===S&&(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>B(!0),className:"flex items-center gap-2 text-red-600 hover:text-red-700",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Delete All"]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:ex.map((e,t)=>(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:e.title}),(0,a.jsx)(e.icon,{className:"h-4 w-4 ".concat(e.color)})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold ".concat(e.color),children:e.value})})]},t))}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Filters"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(c.p,{placeholder:"Search notifications...",value:Z,onChange:e=>$(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)(o.l6,{value:q,onValueChange:I,children:[(0,a.jsx)(o.bq,{className:"w-full md:w-32",children:(0,a.jsx)(o.yv,{placeholder:"Status"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"all",children:"All"}),(0,a.jsx)(o.eb,{value:"unread",children:"Unread"}),(0,a.jsx)(o.eb,{value:"read",children:"Read"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{onClick:()=>{M(Z),L(W),D(q)},className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Apply Filters"]}),(0,a.jsx)(l.$,{variant:"outline",onClick:()=>{$(""),U("all"),I("all"),M(""),L("all"),D("all")},className:"flex items-center gap-2",children:"Clear All"})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"flex items-center justify-between",children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Notifications (Page ",Q," of ",K," - ",el.length," shown)"]})})}),(0,a.jsx)(i.Wu,{children:0===el.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-muted-foreground mb-2",children:_||"all"!==T?"No notifications match your filters":"No notifications yet"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:_||"all"!==T?"Try adjusting your search or filter criteria":"New notifications will appear here when they arrive"})]}):(0,a.jsx)("div",{className:"space-y-4",children:el.map(e=>(0,a.jsx)("div",{className:"p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ".concat(e.isRead?"bg-white hover:bg-gray-50":"bg-blue-50/50 border-blue-200 hover:bg-blue-50"),onClick:()=>eo(e),children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 ".concat(e.isRead?"bg-gray-300":"bg-blue-500")}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:e.title}),!e.isRead&&(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-blue-500 flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.message}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:(0,w.m)(new Date(e.createdAt),{addSuffix:!0})})})]}),(0,a.jsx)("div",{className:"flex items-center gap-2 flex-shrink-0",children:e.isRead?(0,a.jsxs)(n.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(x.A,{className:"h-3 w-3 mr-1"}),"Read"]}):(0,a.jsxs)(n.E,{variant:"default",className:"text-xs bg-blue-600",children:[(0,a.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"New"]})})]})})]})},e.id))})}),K>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",(Q-1)*10+1," to ",Math.min(10*Q,J)," of ",J," notifications"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>ec(1),disabled:1===Q||R,className:"h-8 w-8",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>ec(Q-1),disabled:1===Q||R,className:"h-8 w-8",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"text-sm px-3 py-1 bg-gray-100 rounded",children:["Page ",Q," of ",K]}),(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>ec(Q+1),disabled:Q===K||R,className:"h-8 w-8",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>ec(K),disabled:Q===K||R,className:"h-8 w-8",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(k.Lt,{open:V,onOpenChange:Y,children:(0,a.jsxs)(k.EO,{children:[(0,a.jsxs)(k.wd,{children:[(0,a.jsx)(k.r7,{children:"Mark All Notifications as Read"}),(0,a.jsxs)(k.$v,{children:["Are you sure you want to mark all ",S," unread notifications as read?"]})]}),(0,a.jsxs)(k.ck,{children:[(0,a.jsx)(k.Zr,{children:"Cancel"}),(0,a.jsx)(k.Rx,{onClick:ed,children:"Mark All Read"})]})]})}),(0,a.jsx)(k.Lt,{open:H,onOpenChange:B,children:(0,a.jsxs)(k.EO,{children:[(0,a.jsxs)(k.wd,{children:[(0,a.jsx)(k.r7,{children:"Delete All Notifications"}),(0,a.jsx)(k.$v,{children:"Are you sure you want to delete all notifications? This action cannot be undone."})]}),(0,a.jsxs)(k.ck,{children:[(0,a.jsx)(k.Zr,{children:"Cancel"}),(0,a.jsx)(k.Rx,{onClick:eu,className:"bg-red-600 hover:bg-red-700",children:"Delete All"})]})]})})]}),(0,a.jsx)(C.default,{})]}):(0,a.jsx)("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Please log in to view notifications."})]})})}},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42355:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},52278:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>h,gC:()=>x,l6:()=>o,yv:()=>d});var a=s(95155);s(12115);var r=s(79899),i=s(66474),l=s(5196),n=s(47863),c=s(59434);function o(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:l,...n}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:s,position:i="popper",...l}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...l,children:[(0,a.jsx)(m,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(f,{})]})})}function h(e){let{className:t,children:s,...i}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function m(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},62525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l,wL:()=>d});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},66932:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var a=s(12115),r={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=a.createContext&&a.createContext(r),l=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function c(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?c(Object(s),!0).forEach(function(t){var a,r,i;a=e,r=t,i=s[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in a?Object.defineProperty(a,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[r]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):c(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>a.createElement(u,n({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,o({key:s},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var s,{attr:r,size:i,title:c}=e,d=function(e,t){if(null==e)return{};var s,a,r=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)s=i[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(r[s]=e[s])}return r}(e,l),u=i||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,r,d,{className:s,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==i?a.createElement(i.Consumer,null,e=>t(e)):t(r)}},81693:(e,t,s)=>{Promise.resolve().then(s.bind(s,27435))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,4211,347,8441,1684,7358],()=>t(81693)),_N_E=e.O()}]);