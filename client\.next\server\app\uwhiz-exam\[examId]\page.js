(()=>{var e={};e.id=8561,e.ids=[8561],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9983:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\uwhiz-exam\\\\[examId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14326:(e,t,s)=>{Promise.resolve().then(s.bind(s,24683))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24683:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(60687),a=s(29523),n=s(43210),i=s.n(n),l=s(28527);let o=async(e,t,s,r)=>{try{return(await l.S.get(`questionForStudentRouter?studentId=${e}&medium=${t}&standard=${s}&examId=${r}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){throw Error(`Failed To Get Student Question Detail: ${e.response?.data?.message||e.message}`)}},c=async(e,t)=>{try{return(await l.S.get(`questionForStudentRouter/state?studentId=${e}&examId=${t}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){throw Error(`Failed To Get Quiz State: ${e.response?.data?.message||e.message}`)}},d=async(e,t)=>{try{await l.S.delete(`questionForStudentRouter/state?studentId=${e}&examId=${t}`,{headers:{"Server-Select":"uwhizServer"}})}catch(e){throw Error(`Failed To Clear Quiz State: ${e.response?.data?.message||e.message}`)}},u=async e=>{try{return(await l.S.post("/saveExamAnswer",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed to store Answer: ${e.response?.data?.message||e.message}`}}},m=async e=>{try{return(await l.S.post("/quizTermination",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed to save termination log: ${e.response?.data?.message||e.message}`}}},x=async(e,t)=>{try{return(await l.S.get(`check-attempt/count?studentId=${e}&examId=${t}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed To Get Count of termination: ${e.response?.data?.message||e.message}`}}};var h=s(16189),p=s(43125),g=s(48730),f=s(52581),w=s(30474),b=s(71062),y=s(51361),v=s(62688);let j=(0,v.A)("camera-off",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16",key:"qmtpty"}],["path",{d:"M9.5 4h5L17 7h3a2 2 0 0 1 2 2v7.5",key:"1ufyfc"}],["path",{d:"M14.121 15.121A3 3 0 1 1 9.88 10.88",key:"11zox6"}]]),k=(0,v.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),N=async e=>{try{let t=await l.S.post("/exam-monitoring/upload-photo",e);return{success:!0,data:t.data.data}}catch(e){return console.error("Error uploading exam photo:",e),{success:!1,error:e.response?.data?.message||e.message||"Failed to upload photo"}}},A=({studentId:e,examId:t,isExamActive:s,onCameraError:a,onCameraStatus:i})=>{let l=(0,n.useRef)(null),o=(0,n.useRef)(null),c=(0,n.useRef)(null),d=(0,n.useRef)(null),[u,m]=(0,n.useState)(!1),[x,h]=(0,n.useState)(null),p=(0,n.useCallback)(async()=>{try{h(null);let e=await navigator.mediaDevices.getUserMedia({video:{width:640,height:480,facingMode:"user"}});l.current&&(l.current.srcObject=e,c.current=e,m(!0),i?.(!0))}catch(t){let e="NotAllowedError"===t.name?"Camera access denied":"Camera not available";h(e),a?.(e),i?.(!1),f.oR.error(e)}},[a,i]),g=(0,n.useCallback)(async()=>{if(l.current&&o.current&&u)try{let s=l.current,r=o.current,a=r.getContext("2d");if(!a)return;r.width=s.videoWidth,r.height=s.videoHeight,a.drawImage(s,0,0,r.width,r.height);let n=r.toDataURL("image/jpeg",.7),i=await N({studentId:e,examId:t,photoData:n});i.success||console.error("Failed to upload photo:",i.error)}catch(e){console.error("Failed to capture or upload photo:",e)}},[e,t,u]),w=(0,n.useCallback)(()=>{d.current&&(clearInterval(d.current),d.current=null),c.current&&(c.current.getTracks().forEach(e=>e.stop()),c.current=null),m(!1)},[]);return(0,n.useEffect)(()=>{s&&!u?p():!s&&u&&w()},[s,u,p,w]),(0,n.useEffect)(()=>{if(u&&s){let e=setTimeout(g,1e4);return d.current=setInterval(g,12e4),()=>{clearTimeout(e),d.current&&(clearInterval(d.current),d.current=null)}}return()=>{d.current&&(clearInterval(d.current),d.current=null)}},[u,s,g]),(0,n.useEffect)(()=>()=>w(),[w]),(0,r.jsxs)("div",{className:"fixed top-4 right-4 z-50",children:[(0,r.jsxs)("div",{className:"bg-black rounded-lg p-2 shadow-lg border-2 border-orange-500",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[u?(0,r.jsx)(y.A,{className:"w-4 h-4 text-green-500"}):(0,r.jsx)(j,{className:"w-4 h-4 text-red-500"}),(0,r.jsx)("span",{className:"text-white text-xs font-medium",children:u?"Monitoring Active":"Camera Inactive"})]}),x?(0,r.jsx)("div",{className:"w-32 h-24 bg-red-900 rounded flex items-center justify-center",children:(0,r.jsx)(k,{className:"w-6 h-6 text-red-400"})}):(0,r.jsx)("video",{ref:l,className:"w-32 h-24 rounded object-cover transform scale-x-[-1]",autoPlay:!0,playsInline:!0,muted:!0}),x&&(0,r.jsx)("div",{className:"mt-2 text-xs text-red-400 max-w-32",children:x})]}),(0,r.jsx)("canvas",{ref:o,className:"hidden"})]})},S=i().memo(({examName:e})=>(0,r.jsx)("header",{className:"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)(w.default,{height:60,width:60,src:b.A.src,alt:"Uwhiz Logo",quality:100,className:"object-contain sm:h-20 sm:w-20"}),(0,r.jsx)("h1",{className:"text-lg sm:text-2xl font-bold tracking-tight",children:e.toUpperCase()})]})}));function z(){let e=(0,h.useRouter)(),{examId:t}=(0,h.useParams)(),s=Array.isArray(t)?t[0]:t||"",i=null;try{let e=localStorage.getItem("student_data");i=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),i=null}let[l,w]=(0,n.useState)(!i),[b,y]=(0,n.useState)(!1),[v,j]=(0,n.useState)(!1),[k,N]=(0,n.useState)(!1),[z,E]=(0,n.useState)(!1),[C,F]=(0,n.useState)(!1),[q,I]=(0,n.useState)([]),[R,T]=(0,n.useState)(0),[$,P]=(0,n.useState)(0),[O,Q]=(0,n.useState)([]),[_,M]=(0,n.useState)(!1),[U,G]=(0,n.useState)(null),[D,K]=(0,n.useState)(!1);(0,n.useRef)(null);let[Y,H]=(0,n.useState)(""),[L,W]=(0,n.useState)(0),[J,B]=(0,n.useState)(!1);console.log(j);let X=(0,n.useCallback)((e,t)=>{G(t),Q(e=>e.find(e=>e.questionId===q[R].id)?e.map(e=>e.questionId===q[R].id?{...e,selectedAnswer:t}:e):[...e,{questionId:q[R].id,selectedAnswer:t}])},[R,q]),V=(0,n.useCallback)(async()=>{if(!b){if(i&&s){J||e.push("/uwhiz"),y(!0);try{(await u({studentId:i,examId:Number(s),questionId:q[R].id,selectedAns:U})).success&&f.oR.success("Answer saved successfully!")}catch(e){f.oR.error("Failed to save answer.",{description:e instanceof Error?e.message:"Unknown error"})}finally{y(!1)}}else f.oR.info("Question skipped.");R<q.length-1?(T(R+1),G(null)):M(!0)}},[R,q,U,i,s]);(0,n.useCallback)(async()=>{if(!i||!s){w(!0);return}try{let{classroom:e,medium:t}={classroom:"STD 10",medium:"GUJARATI"},r=await c(i,s);if(r){I(r.questions),T(r.currentQuestionIndex),Q(r.userAnswers),P(r.questions[r.currentQuestionIndex]?.timePerQuestion||30),r.currentQuestionIndex>=r.questions.length?M(!0):N(!0);return}let a=await o(i,t,e,s);a&&Array.isArray(a)?(I(a),P(a[0]?.timePerQuestion||30),N(!0)):f.oR.error("No questions found or invalid response.")}catch(t){if(t instanceof Error&&t.message.startsWith("{"))try{let e=JSON.parse(t.message);if(403===e.status||500===e.status){H(e.message||"Your quiz has been terminated due to multiple cheating attempts."),F(!0),f.oR.error(e.message||"Your quiz has been terminated due to multiple cheating attempts.");return}}catch{f.oR.error("Failed to fetch quiz state.")}let e=`Failed to fetch data: ${t instanceof Error?t.message:"Unknown error"}`;f.oR.error(e)}},[i,s]);let Z=async(e=3,t=1)=>{try{if(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement){if(document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen&&await document.mozCancelFullScreen(),await new Promise(e=>setTimeout(e,100)),!document.fullscreenElement&&!document.webkitFullscreenElement&&!document.mozFullScreenElement)return!0;if(t<e)return await Z(e,t+1);throw Error("Max attempts reached")}return!0}catch(s){if(console.error(`Failed to exit full-screen mode (attempt ${t}):`,s),t<e)return await new Promise(e=>setTimeout(e,500)),await Z(e,t+1);return f.oR.error("Failed to exit full-screen mode. Please press Esc to exit manually."),!1}},ee=()=>{let e=document.documentElement;e.requestFullscreen&&e.requestFullscreen().catch(e=>console.error("Failed to enter fullscreen:",e))};(0,n.useCallback)(async e=>{if(k||l||v||z||D)return;let t=["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"],r=e.ctrlKey&&e.shiftKey&&("I"===e.key||"J"===e.key||"C"===e.key)||e.metaKey&&e.altKey&&"I"===e.key||"F12"===e.key,a=(e.ctrlKey||e.metaKey)&&("c"===e.key||"C"===e.key);if(["Alt","Control","Tab","Shift","Enter"].includes(e.key)||t.includes(e.key)||r||a){if(e.preventDefault(),a){f.oR.warning("Copying is disabled during the quiz.");return}if(!i){W(0);return}K(!0);try{let a=r?"DevTools shortcut":t.includes(e.key)?`Function key "${e.key}"`:`Restricted key "${e.key}"`;await m({examId:Number(s),studentId:i,reason:a});let n=await x(i,Number(s));W(n),1===n?(E(!0),f.oR.warning(`${a} detected.`)):2===n?(E(!0),f.oR.warning(`${a} detected. One more violation will terminate the quiz.`)):n>=3&&(F(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[s,i,k,l,v,z,D]),(0,n.useCallback)(async()=>{if(!k&&!l&&!v&&!z&&!D&&document.hidden){K(!0);try{if(await m({examId:Number(s),studentId:i,reason:"Tab switch"}),!i){W(0);return}let e=await x(i,Number(s));W(e),1===e?(E(!0),f.oR.warning("Tab switch detected.")):2===e?(E(!0),f.oR.warning("Again tab switch detected. One more violation will terminate the quiz.")):e>=3&&(F(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[s,i,k,l,v,z,D]),(0,n.useCallback)(async e=>{k||l||v||z||D||(e.preventDefault(),f.oR.warning("Right-click is disabled during the quiz."))},[s,i,k,l,v,z,D]),(0,n.useCallback)(async()=>{if(!k&&!l&&!v&&!z&&!D){K(!0);try{await m({examId:Number(s),studentId:i,reason:"Window blur"});let e=await x(i,Number(s));W(e),1===e?(E(!0),f.oR.warning("Window focus lost.")):2===e?(E(!0),f.oR.warning("Window focus lost again. One more violation will terminate the quiz.")):e>=3&&(F(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[s,i,k,l,v,z,D]),(0,n.useCallback)(async()=>{if(!k&&!l&&!v&&!z&&!D&&!document.fullscreenElement){K(!0);try{if(await m({examId:Number(s),studentId:i,reason:"Full-screen exit"}),!i){W(0);return}let e=await x(i,Number(s));W(e),1===e?(E(!0),f.oR.warning("You have exited full-screen mode.")):2===e?(E(!0),f.oR.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.")):e>=3&&(F(!0),H("Quiz terminated due to multiple cheating attempts."),f.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){f.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[s,i,k,l,v,z,D]);let et=async()=>{if(F(!1),i&&s)try{await d(i,s)}catch(e){console.error("Failed to clear quiz state:",e)}(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)&&(await Z()||f.oR.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.")),e.push("/uwhiz")},es=(0,n.useCallback)(e=>{let t=Math.floor(e/60);return`${t.toString().padStart(2,"0")}:${(e%60).toString().padStart(2,"0")}`},[]),er=(0,n.useMemo)(()=>q.length>0?q[0].exam_name:"Uwhiz - Super kids",[q]),ea=(0,n.useMemo)(()=>q.length>0?(R+1)/q.length*100:0,[R,q]),en=e=>`w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white ${U===e?"bg-orange-100 border-orange-500":""}`;if(l)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Login Required"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Please log in as a student to access the quiz."}),(0,r.jsx)(a.$,{onClick:()=>e.push(`/student/login?redirect=/uwhiz-exam/${t}`),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Login to Continue"})]})});if(v)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Complete Your Profile"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Your profile is incomplete. Please complete your profile to proceed."}),(0,r.jsx)(a.$,{onClick:()=>{e.push("/student/profile?quiz=true&examId="+t)},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Complete Profile"})]})});if(0===q.length)return(0,r.jsxs)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:[(0,r.jsx)("p",{className:"text-base sm:text-xl font-medium mr-4",children:"Loading questions..."}),(0,r.jsx)(p.A,{className:"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"})]});if(_)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"text-center p-4 sm:p-6 bg-white rounded-lg shadow-xl max-w-md w-full",children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-4xl font-bold text-customOrange mb-4",children:"Quiz Completed!"}),(0,r.jsx)("p",{className:"text-base sm:text-xl mb-4",children:"Your exam submitted successfully."}),(0,r.jsx)(a.$,{className:"bg-customOrange text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg transition-all",onClick:et,children:"Go To Home"})]})});let ei=q[R];return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-100 text-gray-900",children:[k&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Start Quiz"}),(0,r.jsx)("p",{className:"font-semibold mb-4 text-sm sm:text-base text-gray-600",children:"Note: This is a mock exam for testing purposes only."}),i&&(0,r.jsx)(A,{studentId:i,examId:Number(s),isExamActive:!C&&!_,onCameraError:e=>{console.error("Camera error:",e),B(!1)},onCameraStatus:e=>{B(e)}}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",children:[(0,r.jsx)("p",{className:"font-semibold mb-2",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside mb-4 text-gray-600",children:[(0,r.jsx)("li",{children:"Do not switch tabs during the quiz."}),(0,r.jsx)("li",{children:"Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."}),(0,r.jsx)("li",{children:"Do not open Developer Tools."}),(0,r.jsx)("li",{children:"Do not exit full-screen mode."}),(0,r.jsx)("li",{children:"Do not interact with other windows or applications."}),(0,r.jsx)("li",{children:"Do not change the screen or minimize the quiz window."}),(0,r.jsx)("li",{children:"Do not receive or make calls during the quiz."}),(0,r.jsx)("li",{children:"Do not use split screen or floating windows on your device."})]}),(0,r.jsx)("p",{className:"font-semibold mb-2",children:"સૂચનાઓ (ગુજરાતી):"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."}),(0,r.jsx)("li",{children:"પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."}),(0,r.jsx)("li",{children:"ડેવલપર ટૂલ્સ ખોલશો નહીં."}),(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."}),(0,r.jsx)("li",{children:"ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."}),(0,r.jsx)("li",{children:"અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."}),(0,r.jsx)("li",{children:"સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."}),(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."}),(0,r.jsx)("li",{children:"તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."})]})]}),(0,r.jsx)(a.$,{onClick:()=>{N(!1),ee(),q.length>0&&P(q[R]?.timePerQuestion||30)},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",disabled:!J,children:J?"Start Quiz":"Waiting for Camera..."})]})}),z&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-customOrange",children:"Warning"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have performed a restricted action. Repeating this will terminate the quiz."}),(0,r.jsx)(a.$,{onClick:()=>E(!1),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"OK"})]})}),C&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-red-500",children:"Quiz Terminated"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:Y||"Your quiz has been terminated due to multiple cheating attempts."}),(0,r.jsx)(a.$,{onClick:et,className:"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",children:"Go to Home"})]})}),!k&&!l&&!v&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S,{examName:er}),i&&(0,r.jsx)(A,{studentId:i,examId:Number(s),isExamActive:!C&&!_,onCameraError:e=>{console.error("Camera error:",e)}}),(0,r.jsx)("div",{className:"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",children:(0,r.jsx)("div",{className:"h-1.5 bg-customOrange rounded-r-full transition-all duration-300",style:{width:`${ea}%`}})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full max-w-3xl",children:[(0,r.jsxs)("div",{className:"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"}),(0,r.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-customOrange",children:es($)})]}),(0,r.jsxs)("div",{className:"w-full text-center flex flex-col items-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-3 sm:mb-4",children:(0,r.jsxs)("span",{className:"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",children:["Question ",R+1," of ",q.length]})}),(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",dangerouslySetInnerHTML:{__html:ei.question}}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",children:[(0,r.jsxs)(a.$,{variant:"outline",className:en("optionOne"),onClick:()=>X(ei.optionOne,"optionOne"),disabled:C,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"A"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ei.optionOne})]}),(0,r.jsxs)(a.$,{variant:"outline",className:en("optionTwo"),onClick:()=>X(ei.optionTwo,"optionTwo"),disabled:C,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"B"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ei.optionTwo})]}),(0,r.jsxs)(a.$,{variant:"outline",className:en("optionThree"),onClick:()=>X(ei.optionThree,"optionThree"),disabled:C,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"C"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ei.optionThree})]}),(0,r.jsxs)(a.$,{variant:"outline",className:en("optionFour"),onClick:()=>X(ei.optionFour,"optionFour"),disabled:C,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"D"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ei.optionFour})]})]})]}),(0,r.jsx)(a.$,{className:"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>V(),disabled:C||b,children:b?R===q.length-1?"Finish":"Next Question":"Save Answer"})]}),(0,r.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",children:[(0,r.jsx)("span",{children:"Powered by"}),(0,r.jsx)("span",{className:"font-semibold",children:"UEST EdTech"})]})]})})]})]})}S.displayName="QuizHeader"},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43125:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51361:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60675:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["uwhiz-exam",{children:["[examId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9983)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/uwhiz-exam/[examId]/page",pathname:"/uwhiz-exam/[examId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71062:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r={src:"/_next/static/media/uwhizExam.5364baa3.png",height:626,width:798,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAElBMVEURCwcCAgEcEQogIB49Pj0lJSX5PC0XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nCXKuREAMAgEsb2H/lv2GDIFQtg2YsGHBIfkwGRP2k7KAwYIAEmvy1CUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6}},74075:e=>{"use strict";e.exports=require("zlib")},74574:(e,t,s)=>{Promise.resolve().then(s.bind(s,9983))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,2800],()=>s(60675));module.exports=r})();