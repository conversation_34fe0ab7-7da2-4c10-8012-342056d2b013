"use strict";(()=>{var e={};e.id=2755,e.ids=[2755],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},12587:(e,r,t)=>{t.r(r),t.d(r,{default:()=>m});var s=t(37413),a=t(4536),i=t.n(a),n=t(26373);let l=(0,n.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var d=t(49046);let o=(0,n.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var p=t(37075),x=t(17741),c=t(79041);async function m({params:e}){let{id:r}=await e,t=await (0,c.wE)(r);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.default,{}),(0,s.jsxs)("div",{className:"min-h-screen bg-white text-black dark:bg-black dark:text-gray-100",children:[(0,s.jsx)("section",{className:"py-16 bg-black dark:bg-black text-white border-b border-zinc-700",children:(0,s.jsxs)("div",{className:"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i(),{href:"/careers",className:"inline-flex items-center text-gray-400 hover:text-[#FD904B] font-medium mb-6 transition-colors",children:[(0,s.jsx)(l,{className:"mr-2 h-4 w-4"}),"Back to Careers"]}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.job_title}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-gray-300",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2 text-[#FD904B]"}),"Morbi"]}),(0,s.jsx)("span",{className:"bg-[#FD904B] text-white px-3 py-1 rounded-full text-sm font-medium",children:"Full-time"})]})]})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsx)("div",{className:"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-800 rounded-2xl p-8 shadow-lg transition hover:shadow-xl",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between items-start md:items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-black dark:text-white",children:"About the Role"}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-400 mt-4 md:mt-0",children:[(0,s.jsx)(o,{className:"h-4 w-4 mr-2 text-[#FD904B]"}),"Posted: ",new Date().toLocaleDateString()]})]}),(0,s.jsx)("div",{className:"prose dark:prose-invert max-w-none leading-relaxed",dangerouslySetInnerHTML:{__html:t.description}}),(0,s.jsx)("div",{className:"mt-10",children:(0,s.jsx)(i(),{href:`/careers/apply/${t.id}`,className:"inline-flex items-center px-6 py-3 bg-[#FD904B] text-white font-semibold rounded-full hover:bg-[#e67e22] transition transform hover:scale-105 shadow-lg",children:"Apply Now"})})]})})})]}),(0,s.jsx)(x.default,{})]})}},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79041:(e,r,t)=>{t.d(r,{vA:()=>n,wE:()=>l});var s=t(94612);let a="https://staff.uest.in/api",i="allowonly-uest.in-domain-super-key",n=async()=>(await s.A.get(`${a}/jobs`,{headers:{"x-career-key":i}})).data.data,l=async e=>(await s.A.get(`${a}/jobs/${e}`,{headers:{"x-career-key":i}})).data.data},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},87927:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>p,routeModule:()=>c,tree:()=>o});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o={children:["",{children:["careers",{children:["details",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,12587)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/careers/details/[id]/page",pathname:"/careers/details/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,8721,3641,2800],()=>t(87927));module.exports=s})();