(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2328],{14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:s,strokeWidth:l?24*Number(n)/Number(a):n,className:i("lucide",c),...m},[...u.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let s=(0,r.forwardRef)((s,n)=>{let{className:o,...d}=s;return(0,r.createElement)(c,{ref:n,iconNode:t,className:i("lucide-".concat(a(l(e))),"lucide-".concat(e),o),...d})});return s.displayName=l(e),s}},20185:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>l,RO:()=>c,Wz:()=>n,sA:()=>i});var r=s(94314),a=s(18159);let n=(e,t)=>{var s,a,n,l,i,o,c,d;e.contactNo&&t((0,r.ac)(r._3.PROFILE)),(null===(a=e.ClassAbout)||void 0===a?void 0:null===(s=a.tutorBio)||void 0===s?void 0:s.length)>50&&t((0,r.ac)(r._3.DESCRIPTION)),(null===(n=e.ClassAbout)||void 0===n?void 0:n.profilePhoto)&&(null===(l=e.ClassAbout)||void 0===l?void 0:l.classesLogo)&&t((0,r.ac)(r._3.PHOTO_LOGO)),(null===(i=e.education)||void 0===i?void 0:i.length)>0&&t((0,r.ac)(r._3.EDUCATION)),(null===(o=e.certificates)||void 0===o?void 0:o.length)>0&&t((0,r.ac)(r._3.CERTIFICATES)),(null===(c=e.experience)||void 0===c?void 0:c.length)>0&&t((0,r.ac)(r._3.EXPERIENCE)),(null===(d=e.tuitionClasses)||void 0===d?void 0:d.length)>0&&t((0,r.ac)(r._3.TUTIONCLASS)),e.address&&t((0,r.ac)(r._3.ADDRESS))},l=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch(t){return[e]}},i=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch(t){return e||"N/A"}},o=new TextEncoder().encode("secret123");async function c(e){try{let{payload:t}=await (0,a.V)(e,o);return t}catch(e){return console.error("Invalid token:",e),null}}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>i});var r=s(95155);s(12115);var a=s(66634),n=s(74466),l=s(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,l.cn)(i({variant:s,size:n,className:t})),...c})}},38239:(e,t,s)=>{"use strict";s.d(t,{default:()=>N});var r=s(95155),a=s(30285),n=s(12115),l=s(35695),i=s(55077);let o=async e=>{try{return(await i.S.get("/uwhizStudentData/".concat(e))).data}catch(e){var t,s;return{success:!1,error:"Failed To Get Student Detail: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}};var c=s(86214);let d=async e=>{try{return(await i.S.post("/mock-exam-terminate",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,s;return{success:!1,error:"Failed to save termination log: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}},u=async e=>{try{return(await i.S.get("mock-exam-terminate/count?studentId=".concat(e),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,s;return{success:!1,error:"Failed To Get Count of termination: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}};var m=s(94631),h=s(14186),x=s(56671),g=s(66766),p=s(88927);let f=async e=>{try{return(await i.S.get("mock-exam/".concat(e),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,s;throw Error("Failed To Get Question: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message))}},v=e=>{let t=Date.now(),s=localStorage.getItem("examAttempts"),r=s?JSON.parse(s):{};r[e]=t,localStorage.setItem("examAttempts",JSON.stringify(r))},w=async e=>{try{let t=await i.S.post("/uwhizCoinTransction/add",e);return{success:!0,data:t.data}}catch(e){var t,s;return{success:!1,error:"Failed to log transction of coins in mock exam: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}},y=async e=>{try{let t=await i.S.post("/uwhizCoinTransction/update",e);return{success:!0,data:t.data}}catch(e){var t,s;return{success:!1,error:"Failed to update coins: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}};var b=s(76079),k=s(20185);let j=()=>{let e=(0,l.useSearchParams)(),t=(0,l.useRouter)(),[s,r]=(0,n.useState)(null);return(0,n.useEffect)(()=>{(async()=>{let s=e.get("token");if(s){let e=await (0,k.RO)(s);if(null==e?void 0:e.id){let s={id:e.id};localStorage.setItem("student_data",JSON.stringify(s)),r(e.id);let a=window.location.pathname;t.replace(a)}}else{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e):null;r((null==t?void 0:t.id)||null)}})()},[e]),s},S=n.memo(()=>(0,r.jsx)("header",{className:"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)(g.default,{height:60,width:60,src:p.A.src,alt:"Uwhiz Logo",quality:100,className:"object-contain sm:h-20 sm:w-20"}),(0,r.jsx)("h1",{className:"text-lg sm:text-2xl font-bold tracking-tight",children:"U-whiz MOCK EXAM"})]})}));function N(){let e=(0,l.useRouter)(),t=j(),[i,g]=(0,n.useState)(!1),[p,k]=(0,n.useState)(!1),[N,E]=(0,n.useState)(!1),[A,C]=(0,n.useState)(!1),[F,R]=(0,n.useState)(!1),[z,O]=(0,n.useState)(!1),[T,D]=(0,n.useState)([]),[I,_]=(0,n.useState)(0),[L,P]=(0,n.useState)(0),[q,U]=(0,n.useState)([]),[Q,M]=(0,n.useState)(!1),[$,G]=(0,n.useState)(null),[B,K]=(0,n.useState)(!1),J=(0,n.useRef)(null),[X,Y]=(0,n.useState)(""),[W,H]=(0,n.useState)(0),[V,Z]=(0,n.useState)(!1),[ee,et]=(0,n.useState)(null),[es,er]=(0,n.useState)(!1),[ea,en]=(0,n.useState)(null),el=async()=>{if(!t)return 0;try{let e=await u(t);return"number"==typeof e?e:0}catch(e){return console.error("Failed to fetch violation count:",e),0}};(0,n.useEffect)(()=>{(async()=>{H(await el())})()},[t]),(0,n.useEffect)(()=>{W>=3&&(O(!0),Y("Quiz terminated due to multiple cheating attempts."),t&&v(t))},[W,t]),(0,n.useEffect)(()=>{t&&(async()=>{try{let e=await (0,c.S)(t,1,1);if(e.success&&e.data.data.mockExamResults.length>0){let t=e.data.data.mockExamResults[0],s=new Date(t.createdAt).toISOString().split("T")[0],r=new Date().toISOString().split("T")[0];s===r&&C(!0)}}catch(e){x.oR.error("Failed to verify exam eligibility.",e)}})()},[t]),(0,n.useEffect)(()=>(J.current=new Audio("/clock-ticking-sound-effect.mp3"),J.current.loop=!0,()=>{J.current&&(J.current.pause(),J.current=null)}),[]),(0,n.useEffect)(()=>{!(T.length>0)||!(L<=5)||!(L>0)||N||z||Q||i||p||A||!J.current?J.current&&J.current.pause():J.current.play().catch(e=>{console.error("Failed to play tick sound:",e)})},[L,T,N,z,Q,i,p,A]);let ei=(0,n.useCallback)(async()=>{let e=T[I];if($){let t=$===e.correctAnswer;U(s=>[...s,{questionId:e.id,selectedAnswer:$,isCorrect:t}]),Z(!0),setTimeout(()=>{Z(!1),G(null),I<T.length-1?_(e=>e+1):M(!0)},1e3)}else U(t=>[...t,{questionId:e.id,selectedAnswer:"skipped",isCorrect:!1}]),x.oR.warning("Question skipped."),I<T.length-1?_(e=>e+1):M(!0)},[$,T,I]),eo=(0,n.useMemo)(()=>q.reduce((e,t)=>e+ +!!t.isCorrect,0),[q]),ec=(0,n.useMemo)(()=>{let e=eo/T.length*100;return e>=100?5:e>=90?4:e>=80?3:e>=70?2:+(e>=60)},[eo,T.length]);(0,n.useEffect)(()=>{Q&&t&&(async()=>{try{let e=await (0,b.$)(t);if(!e.success){x.oR.error(e.error),en(e.error);return}er(!0);let s=await (0,b.G)(t);er(!1),s.success?(et(s.data),x.oR.success("Streak updated successfully! Current streak: ".concat(s.data.streak))):(en(s.error),x.oR.error(s.error));let r=s.success?s.data.streak:0,a=ec+r,n=await (0,c.q)({studentId:t,score:eo,coinEarnings:a});n.success?x.oR.success("Result saved successfully!"):x.oR.error(n.error);let l=await y({modelId:t,modelType:"STUDENT",coins:a});l.success?x.oR.success("Coins updated successfully! Added ".concat(a," coins (Score: ").concat(ec,", Streak: ").concat(r,").")):x.oR.error(l.error);let i=await w({modelId:t,modelType:"STUDENT",amount:a,type:"CREDIT",reason:"Mock Exam (Score + Streak: ".concat(r,")")});i.success?x.oR.success("Transaction logged successfully!"):x.oR.error(i.error),await v(t),await eu()}catch(e){er(!1),en("Failed to fetch streak: ".concat(e.message)),x.oR.error("Failed to save result, update coins, or update streak: ".concat(e.message))}})()},[Q,t,eo,ec]),(0,n.useEffect)(()=>{if(T.length>0&&L>0&&!N&&!z&&!i&&!p&&!A){let e=setInterval(()=>{P(t=>{let s=t-1;return s<=0?(clearInterval(e),ei(),0):s})},1e3);return()=>clearInterval(e)}},[L,T,I,N,z,i,p,A,ei]),(0,n.useEffect)(()=>{!(T.length>0)||N||z||i||p||A||P(45)},[I,T,N,z,i,p,A]),(0,n.useEffect)(()=>{Q&&s.e(5585).then(s.bind(s,5585)).then(e=>{(0,e.default)({particleCount:100,spread:80,startVelocity:30,ticks:200,origin:{x:.5,y:.2},colors:["#FF4500","#FFD700","#FF69B4","#00FF00","#1E90FF"],shapes:["square"],gravity:.3,scalar:1.2})})},[Q]);let ed=(0,n.useCallback)(async()=>{if(!t){g(!0);return}try{let e=await o(t);if(!e.success){x.oR.error(e.error),k(!0);return}let s=await f(t);s&&Array.isArray(s)?(D(s),P(45),E(!0)):x.oR.error("No questions found or invalid response.")}catch(e){x.oR.error(e)}},[t]);(0,n.useEffect)(()=>{t&&!A&&ed()},[t,ed,A]);let eu=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;try{if(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement){if(document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen&&await document.mozCancelFullScreen(),await new Promise(e=>setTimeout(e,100)),!document.fullscreenElement&&!document.webkitFullscreenElement&&!document.mozFullScreenElement)return!0;if(t<e)return await eu(e,t+1);throw Error("Max attempts reached")}return!0}catch(s){if(console.error("Failed to exit full-screen mode (attempt ".concat(t,"):"),s),t<e)return await new Promise(e=>setTimeout(e,500)),await eu(e,t+1);return x.oR.error("Failed to exit full-screen mode. Please press Esc to exit manually."),!1}};(0,n.useEffect)(()=>{Q&&t&&z&&(v(t),eu())},[Q,t]),(0,n.useEffect)(()=>{z&&t&&eu().then(e=>{console.log("Quiz terminated and full-screen exited.",e)})},[z,t]);let em=()=>{let e=document.documentElement;e.requestFullscreen&&e.requestFullscreen().catch(e=>console.error("Failed to enter fullscreen:",e))},eh=(0,n.useCallback)(async e=>{if(N||i||p||F||B||A)return;let s=["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"],r=e.ctrlKey&&e.shiftKey&&("I"===e.key||"J"===e.key||"C"===e.key)||e.metaKey&&e.altKey&&"I"===e.key||"F12"===e.key,a=(e.ctrlKey||e.metaKey)&&("c"===e.key||"C"===e.key);if(["Alt","Control","Tab","Shift","Enter"].includes(e.key)||s.includes(e.key)||r||a){if(e.preventDefault(),a){x.oR.warning("Copying is disabled during the quiz.");return}if(!t){H(0);return}K(!0);try{let a=r?"DevTools shortcut":s.includes(e.key)?'Function key "'.concat(e.key,'"'):'Restricted key "'.concat(e.key,'"');await d({studentId:t,reason:a});let n=await u(t);H(n),1===n?(R(!0),x.oR.warning("".concat(a," detected."))):2===n?(R(!0),x.oR.warning("".concat(a," detected. One more violation will terminate the quiz."))):n>=3&&(O(!0),Y("Quiz terminated due to multiple cheating attempts."),t&&v(t),x.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){x.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[t,N,i,p,F,B,A]),ex=(0,n.useCallback)(async()=>{if(!N&&!i&&!p&&!F&&!B&&!A&&document.hidden){K(!0);try{if(await d({studentId:t,reason:"Tab switch"}),!t){H(0);return}let e=await u(t);H(e),1===e?(R(!0),x.oR.warning("Tab switch detected.")):2===e?(R(!0),x.oR.warning("Again tab switch detected. One more violation will terminate the quiz.")):e>=3&&(O(!0),Y("Quiz terminated due to multiple cheating attempts."),t&&v(t),x.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){x.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[t,N,i,p,F,B,A]),eg=(0,n.useCallback)(async e=>{N||i||p||F||B||A||(e.preventDefault(),x.oR.warning("Right-click is disabled during the quiz."))},[t,N,i,p,F,B,A]),ep=(0,n.useCallback)(async()=>{if(!N&&!i&&!p&&!F&&!B&&!A){K(!0);try{await d({studentId:t,reason:"Window blur"});let e=await u(t);H(e),1===e?(R(!0),x.oR.warning("Window focus lost.")):2==e?(R(!0),x.oR.warning("Window focus lost again. One more violation will terminate the quiz.")):e>=3&&(O(!0),Y("Quiz terminated due to multiple cheating attempts."),t&&v(t),x.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){x.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[t,N,i,p,F,B,A]),ef=(0,n.useCallback)(async()=>{if(!N&&!i&&!p&&!F&&!B&&!A&&!document.fullscreenElement){K(!0);try{if(await d({studentId:t,reason:"Full-screen exit"}),!t){H(0);return}let e=await u(t);H(e),1===e?(R(!0),x.oR.warning("You have exited full-screen mode.")):2===e?(R(!0),x.oR.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.")):e>=3&&(O(!0),Y("Quiz terminated due to multiple cheating attempts."),t&&v(t),x.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){x.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{K(!1)}}},[t,N,i,p,F,B,A]),ev=async()=>{O(!1),(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)&&(await eu()||x.oR.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.")),e.push("/mock-exam-card"),setTimeout(()=>{e.push("/mock-exam-card")},1e3)};(0,n.useEffect)(()=>(N||i||p||z||A||(document.addEventListener("visibilitychange",ex),document.addEventListener("keydown",eh),window.addEventListener("blur",ep),document.addEventListener("contextmenu",eg),document.addEventListener("fullscreenchange",ef)),()=>{document.removeEventListener("visibilitychange",ex),document.removeEventListener("keydown",eh),window.removeEventListener("blur",ep),document.removeEventListener("contextmenu",eg),document.removeEventListener("fullscreenchange",ef)}),[ex,eh,ep,eg,ef,N,i,p,z,A]);let ew=(0,n.useCallback)(e=>{let t=Math.floor(e/60);return"".concat(t.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))},[]),ey=(0,n.useMemo)(()=>T.length>0?(I+1)/T.length*100:0,[I,T]),eb=e=>{let t="w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white";return $===e?"".concat(t," bg-orange-100 border-orange-500"):t},ek=e=>{V||G(e)};if(i)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Login Required"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Please log in as a student to access the quiz."}),(0,r.jsx)(a.$,{onClick:()=>e.push("/student/login?redirect=/mock-test"),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Login to Continue"})]})});if(p)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Complete Your Profile"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Your profile is incomplete. Please complete your profile to proceed."}),(0,r.jsx)(a.$,{onClick:()=>{e.push("/student/profile")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Complete Profile"})]})});if(A)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-black",children:"Exam Already Taken"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have already attempted the mock exam today. Please try again tomorrow."}),(0,r.jsx)(a.$,{onClick:()=>{C(!1),e.push("/mock-exam-card")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Go to Home"})]})});if(0===T.length)return(0,r.jsxs)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:[(0,r.jsx)("p",{className:"text-base sm:text-xl font-medium mr-4",children:"Loading questions..."}),(0,r.jsx)(m.A,{className:"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"})]});if(Q)return(0,r.jsxs)("div",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 overflow-hidden text-gray-900 dark:text-white px-4",children:[(0,r.jsx)("div",{className:"absolute inset-0 z-0 pointer-events-none",children:(0,r.jsx)("div",{className:"animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full"})}),(0,r.jsxs)("div",{className:"relative z-10 text-center p-8 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl max-w-md w-full space-y-6 transition-all border border-orange-100 dark:border-gray-700",children:[(0,r.jsx)("h1",{className:"text-3xl font-extrabold text-orange-600 dark:text-orange-400",children:"Quiz Completed \uD83C\uDF89"}),(0,r.jsxs)("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:["You ",(0,r.jsx)("span",{className:"text-orange-500 font-bold",children:"rocked it"}),"! Keep up the momentum. \uD83D\uDCAA"]}),(0,r.jsxs)("p",{className:"text-base text-gray-800 dark:text-gray-200",children:["Final Score: ",(0,r.jsx)("span",{className:"font-bold",children:eo})," / ",T.length]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,r.jsx)("div",{className:"absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping"}),(0,r.jsx)("div",{className:"absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse"}),(0,r.jsx)("div",{className:"absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce"}),(0,r.jsx)("div",{className:"relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning",children:(0,r.jsx)("span",{className:"text-4xl",children:"\uD83D\uDD25"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400",children:es?"Loading Streak...":ea?"Error Loading Streak":"\uD83D\uDD25 Streak: ".concat((null==ee?void 0:ee.streak)||0," Days")})]})}),(0,r.jsxs)("div",{className:"text-sm text-gray-800 dark:text-gray-300 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Coins Earned:"})," ",ec+((null==ee?void 0:ee.streak)||0)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Bonus:"})," +",(null==ee?void 0:ee.streak)||0," for Streak!"]})]}),(0,r.jsx)("button",{className:"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-3 rounded-xl text-base shadow-lg",onClick:ev,children:"\uD83D\uDE80 Continue Learning"})]})]});let ej=T[I];return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-100 text-gray-900",children:[N&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Start Quiz"}),(0,r.jsx)("p",{className:"font-semibold mb-4 text-sm sm:text-base text-gray-600",children:"Note: This is a mock exam for testing purposes only."}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",children:[(0,r.jsx)("p",{className:"font-semibold mb-2",children:"Instructions (English):"}),(0,r.jsxs)("ul",{className:"list-disc list-inside mb-4 text-gray-600",children:[(0,r.jsx)("li",{children:"Do not switch tabs during the quiz."}),(0,r.jsx)("li",{children:"Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."}),(0,r.jsx)("li",{children:"Do not open Developer Tools."}),(0,r.jsx)("li",{children:"Do not exit full-screen mode."}),(0,r.jsx)("li",{children:"Do not interact with other windows or applications."}),(0,r.jsx)("li",{children:"Do not change the screen or minimize the quiz window."}),(0,r.jsx)("li",{children:"Do not receive or make calls during the quiz."}),(0,r.jsx)("li",{children:"Do not use split screen or floating windows on your device."})]}),(0,r.jsx)("p",{className:"font-semibold mb-2",children:"સૂચનાઓ (ગુજરાતી):"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."}),(0,r.jsx)("li",{children:"પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."}),(0,r.jsx)("li",{children:"ડેવલપર ટૂલ્સ ખોલશો નહીં."}),(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."}),(0,r.jsx)("li",{children:"ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."}),(0,r.jsx)("li",{children:"અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."}),(0,r.jsx)("li",{children:"સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."}),(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."}),(0,r.jsx)("li",{children:"તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."})]})]}),(0,r.jsx)(a.$,{onClick:()=>{E(!1),em(),T.length>0&&P(45)},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"Start Quiz"})]})}),F&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-customOrange",children:"Warning"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have performed a restricted action. Repeating this will terminate the quiz."}),(0,r.jsx)(a.$,{onClick:()=>R(!1),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"OK"})]})}),z&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-red-500",children:"Quiz Terminated"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:X||"Your quiz has been terminated due to multiple cheating attempts."}),(0,r.jsx)(a.$,{onClick:ev,className:"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",children:"Go to Home"})]})}),!N&&!i&&!p&&!A&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S,{}),(0,r.jsx)("div",{className:"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",children:(0,r.jsx)("div",{className:"h-3.5 bg-customOrange rounded-r-full transition-all duration-300",style:{width:"".concat(ey,"%")}})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full max-w-3xl",children:[(0,r.jsxs)("div",{className:"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"}),(0,r.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-customOrange",children:ew(L)})]}),(0,r.jsxs)("div",{className:"w-full text-center flex flex-col items-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-3 sm:mb-4",children:(0,r.jsxs)("span",{className:"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",children:["Question ",I+1," of ",T.length]})}),(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",children:ej.question}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",children:[(0,r.jsxs)(a.$,{variant:"outline",className:eb("optionOne"),onClick:()=>ek("optionOne"),disabled:z||V,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"A"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ej.optionOne})]}),(0,r.jsxs)(a.$,{variant:"outline",className:eb("optionTwo"),onClick:()=>ek("optionTwo"),disabled:z||V,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"B"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ej.optionTwo})]}),(0,r.jsxs)(a.$,{variant:"outline",className:eb("optionThree"),onClick:()=>ek("optionThree"),disabled:z||V,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"C"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ej.optionThree})]}),(0,r.jsxs)(a.$,{variant:"outline",className:eb("optionFour"),onClick:()=>ek("optionFour"),disabled:z||V,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"D"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ej.optionFour})]})]})]}),(0,r.jsx)(a.$,{className:"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>ei(),disabled:z||V,children:I===T.length-1?"Finish":"Next Question"})]}),(0,r.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",children:[(0,r.jsx)("span",{children:"Powered by"}),(0,r.jsx)("span",{className:"font-semibold",children:"UEST EdTech"})]})]})})]})]})}S.displayName="QuizHeader"},54177:(e,t,s)=>{Promise.resolve().then(s.bind(s,38239))},55077:(e,t,s)=>{"use strict";s.d(t,{S:()=>l});var r=s(23464),a=s(56671);let n=s(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let l=r.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});l.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":n;let s=localStorage.getItem("studentToken");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(a.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,s)=>{"use strict";s.d(t,{MB:()=>i,ZO:()=>l,cn:()=>n,xh:()=>o});var r=s(52596),a=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}let l=()=>localStorage.getItem("studentToken"),i=()=>{localStorage.removeItem("studentToken")},o=()=>!!l()},76079:(e,t,s)=>{"use strict";s.d(t,{$:()=>a,G:()=>n});var r=s(55077);let a=async e=>{try{let t=await r.S.put("/mock-exam-streak/".concat(e),{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,s;return{success:!1,error:"Failed to save mock exam streak: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.error)||e.message)}}},n=async e=>{try{let t=await r.S.get("/mock-exam-streak/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,s;return{success:!1,error:"Failed to get mock exam streak: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.error)||e.message)}}}},86214:(e,t,s)=>{"use strict";s.d(t,{S:()=>n,q:()=>a});var r=s(55077);let a=async e=>{try{let t=await r.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,s;return{success:!1,error:"Failed to save mock exam result: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}},n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let a=await r.S.get("/mock-exam-result/".concat(e,"?page=").concat(t,"&limit=").concat(s),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data}}catch(e){var a,n;return{success:!1,error:"Failed to get mock exam result: ".concat((null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message)||e.message)}}}},88927:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r={src:"/_next/static/media/uwhizExam.5364baa3.png",height:626,width:798,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAElBMVEURCwcCAgEcEQogIB49Pj0lJSX5PC0XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nCXKuREAMAgEsb2H/lv2GDIFQtg2YsGHBIfkwGRP2k7KAwYIAEmvy1CUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6}},94314:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,_3:()=>a,ac:()=>l});var r=s(51990),a=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let n=(0,r.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let s=t.payload;e.completedForms[s]||(e.completedForms[s]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:l,setCurrentStep:i}=n.actions,o=n.reducer},94631:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,1990,4212,8159,8441,1684,7358],()=>t(54177)),_N_E=e.O()}]);