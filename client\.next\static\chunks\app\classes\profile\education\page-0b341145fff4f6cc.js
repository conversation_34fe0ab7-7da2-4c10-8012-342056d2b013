(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3882],{4652:(e,t,a)=>{"use strict";a.d(t,{EducationForm:()=>k});var r=a(95155),s=a(12115),n=a(62177),o=a(90221),i=a(55594),d=a(56671),l=a(34540),c=a(94314),u=a(75937),p=a(62523),m=a(30285),g=a(47262),x=a(55077),f=a(35695),h=a(45436),v=a(66695),b=a(62525),j=a(54165),y=a(59409);let w=i.z.object({university:i.z.string().min(2,"University is required"),degree:i.z.string().min(2,"Degree is required"),degreeType:i.z.string().min(2,"Degree Type is required"),passoutYear:i.z.string().regex(/^\d{4}$/,"Enter a valid year (e.g., 2022)"),certificate:i.z.custom(e=>e instanceof FileList&&e.length>0,{message:"Degree certificate is required"})}),N=i.z.object({noDegree:i.z.boolean().optional(),education:i.z.array(w).optional()});function k(){var e;let[t,a]=(0,s.useState)(!1),[i,w]=(0,s.useState)(!1),[k,z]=(0,s.useState)([]),C=(0,n.mN)({resolver:(0,o.u)(N),defaultValues:{noDegree:!1,education:[{university:"",degree:"",degreeType:"",passoutYear:"",certificate:void 0}]}}),{fields:S,append:D,remove:I}=(0,n.jz)({control:C.control,name:"education"}),{user:_}=(0,l.d4)(e=>e.user),E=(0,l.d4)(e=>e.class.classData),R=(0,l.wA)(),T=(0,f.useRouter)(),A=async()=>{let e=new FormData;e.append("noDegree","true");try{await x.S.post("/classes-profile/education",e,{headers:{"Content-Type":"multipart/form-data"}}),await R((0,h.V)(_.id)),d.oR.success("No degree status saved"),R((0,c.ac)(c._3.EDUCATION)),T.push("/classes/profile/experience")}catch(e){d.oR.error("Something went wrong"),console.log(e)}},F=async()=>{let e=new FormData;e.append("noDegree","false");try{await x.S.post("/classes-profile/education",e,{headers:{"Content-Type":"multipart/form-data"}}),await R((0,h.V)(_.id)),d.oR.success("You can now add your education details")}catch(e){d.oR.error("Something went wrong"),console.log(e)}},P=async e=>{if(e.noDegree){A();return}if(!e.education||0===e.education.length){d.oR.error("Please add at least one education record");return}let t=new FormData;t.append("noDegree","false"),t.append("education",JSON.stringify(e.education)),e.education.forEach(e=>{e.certificate instanceof FileList&&t.append("files",e.certificate[0])});try{await x.S.post("/classes-profile/education",t,{headers:{"Content-Type":"multipart/form-data"}}),await R((0,h.V)(_.id)),d.oR.success("Education uploaded successfully"),R((0,c.ac)(c._3.EDUCATION)),T.push("/classes/profile/experience")}catch(e){d.oR.error("Something went wrong"),console.log(e)}};s.useEffect(()=>{if((async()=>{try{let e=await x.S.get("/constant/TuitionClasses");if(e.data&&e.data.details){let t=e.data.details.find(e=>"Education"===e.name);if(t&&t.subDetails){let e=t.subDetails.find(e=>"Degree"===e.name);if(e&&e.values){let t=e.values.map(e=>e.name);z(t)}}}}catch(e){console.error("Failed to fetch degree options:",e),d.oR.error("Failed to load degree options")}})(),E&&!i){var e;(null===(e=E.education)||void 0===e?void 0:e.some(e=>!1===e.isDegree))&&(a(!0),C.setValue("noDegree",!0),C.setValue("education",[]),d.oR.info("You have selected 'I don't have a degree'. You cannot add education data unless you uncheck this option.")),w(!0)}},[E,C,i]);let L=async(e,t)=>{try{await x.S.delete("/classes-profile/education/".concat(e),{data:{classId:t}}),d.oR.success("Education deleted successfully"),await R((0,h.V)(t)),C.reset({noDegree:!1,education:[{university:"",degree:"",degreeType:"",passoutYear:"",certificate:void 0}]})}catch(e){d.oR.error("Failed to delete education"),console.log(e)}};return(0,r.jsx)(u.lV,{...C,children:(0,r.jsxs)("form",{onSubmit:C.handleSubmit(P),className:"space-y-6",children:[(0,r.jsx)(u.zB,{control:C.control,name:"noDegree",render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.MJ,{children:(0,r.jsx)(g.S,{checked:t.value,onCheckedChange:e=>{t.onChange(e),a(!!e),e?A():F()}})}),(0,r.jsx)(u.lR,{className:"font-medium",children:"I dont have a degree"})]})}}),(null==E?void 0:null===(e=E.education)||void 0===e?void 0:e.length)>0&&!t&&(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Previous Education"}),E.education.map((e,t)=>(0,r.jsxs)(v.Zp,{className:"bg-muted/30 relative",children:[(0,r.jsxs)(v.aR,{className:"flex flex-row items-start justify-between",children:[(0,r.jsxs)(v.ZB,{className:"text-base font-semibold",children:["Education #",t+1]}),(0,r.jsxs)(j.lG,{children:[(0,r.jsx)(j.zM,{asChild:!0,children:(0,r.jsx)(m.$,{variant:"ghost",size:"icon",className:"text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(j.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(j.c7,{children:[(0,r.jsx)(j.L3,{children:"Delete Education"}),(0,r.jsx)(j.rr,{children:"Are you sure you want to delete this education record? This action cannot be undone."})]}),(0,r.jsxs)(j.Es,{className:"gap-2",children:[(0,r.jsx)(m.$,{variant:"outline",onClick:()=>document.querySelector('button[data-state="open"]').click(),children:"Cancel"}),(0,r.jsx)(m.$,{variant:"destructive",onClick:()=>{L(e.id,E.id),document.querySelector('button[data-state="open"]').click()},children:"Delete"})]})]})]})]}),(0,r.jsxs)(v.Wu,{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"University:"})," ",e.university]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Degree:"})," ",e.degree]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Degree Type:"})," ",e.degreeType]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Passout Year:"})," ",e.passoutYear]}),e.certificate&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Certificate:"})," ",(0,r.jsx)("a",{href:"".concat("http://localhost:4005/","uploads/classes/").concat(E.id,"/education/").concat(e.certificate),target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 underline hover:text-blue-700",children:"View Certificate"})]})]})]},t))]}),(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Add New Education"}),!t&&S.map((e,t)=>(0,r.jsxs)("div",{className:"space-y-4 rounded-2xl border bg-muted/30 p-4 shadow-sm",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(u.zB,{control:C.control,name:"education.".concat(t,".university"),render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"University"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(p.p,{placeholder:"e.g. Delhi University",...t})}),(0,r.jsx)(u.C5,{})]})}}),(0,r.jsx)(u.zB,{control:C.control,name:"education.".concat(t,".degree"),render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Degree"}),(0,r.jsx)(u.MJ,{children:(0,r.jsxs)(y.l6,{onValueChange:t.onChange,defaultValue:t.value,...t,children:[(0,r.jsx)(y.bq,{className:"w-[300px]",children:(0,r.jsx)(y.yv,{placeholder:"Select Degree"})}),(0,r.jsx)(y.gC,{children:k.map(e=>(0,r.jsx)(y.eb,{value:e,children:e},e))})]})}),(0,r.jsx)(u.C5,{})]})}}),(0,r.jsx)(u.zB,{control:C.control,name:"education.".concat(t,".degreeType"),render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Degree Type"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(p.p,{placeholder:"e.g. Undergraduate",...t})}),(0,r.jsx)(u.C5,{})]})}}),(0,r.jsx)(u.zB,{control:C.control,name:"education.".concat(t,".passoutYear"),render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Passout Year"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(p.p,{placeholder:"e.g. 2020",...t})}),(0,r.jsx)(u.C5,{})]})}}),(0,r.jsx)(u.zB,{control:C.control,name:"education.".concat(t,".certificate"),render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Degree Certificate (PDF/Image)"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(p.p,{type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:e=>{let a=e.target.files;if(a&&a.length>0){if(!["application/pdf","image/jpeg","image/jpg","image/png"].includes(a[0].type)){d.oR.error("Only PDF and image files (.pdf, .jpg, .jpeg, .png) are allowed"),e.target.value="";return}t.onChange(a)}}})}),(0,r.jsx)(u.C5,{})]})}})]}),S.length>1&&(0,r.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>I(t),className:"mt-2",children:"Remove"})]},e.id)),!t&&(0,r.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>D({university:"",degree:"",degreeType:"",passoutYear:"",certificate:void 0}),className:"flex items-center gap-2",children:"Add New Education"}),(0,r.jsx)(m.$,{type:"submit",children:"Save Education"})]})})}},11132:(e,t,a)=>{Promise.resolve().then(a.bind(a,4652)),Promise.resolve().then(a.bind(a,22346))},22346:(e,t,a)=>{"use strict";a.d(t,{Separator:()=>o});var r=a(95155);a(12115);var s=a(14050),n=a(59434);function o(e){let{className:t,orientation:a="horizontal",decorative:o=!0,...i}=e;return(0,r.jsx)(s.b,{"data-slot":"separator-root",decorative:o,orientation:a,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...i})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d,r:()=>i});var r=a(95155);a(12115);var s=a(66634),n=a(74466),o=a(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:n,className:t})),...l})}},45436:(e,t,a)=>{"use strict";a.d(t,{V:()=>s});var r=a(55077);let s=(0,a(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:a}=t;try{return(await r.S.get("/classes/details/".concat(e))).data}catch(e){var s;return a((null===(s=e.response)||void 0===s?void 0:s.data)||"Fetch failed")}})},47262:(e,t,a)=>{"use strict";a.d(t,{S:()=>i});var r=a(95155);a(12115);var s=a(14885),n=a(5196),o=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,o.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>g,c7:()=>p,lG:()=>i,rr:()=>x,zM:()=>d});var r=a(95155);a(12115);var s=a(4033),n=a(54416),o=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[a,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}},55077:(e,t,a)=>{"use strict";a.d(t,{S:()=>o});var r=a(23464),s=a(56671);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let o=r.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});o.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":n;let a=localStorage.getItem("studentToken");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(s.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>l,yv:()=>c});var r=a(95155);a(12115);var s=a(79899),n=a(66474),o=a(5196),i=a(47863),d=a(59434);function l(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:o,...i}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[o,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:a,position:n="popper",...o}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...o,children:[(0,r.jsx)(g,{}),(0,r.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(x,{})]})})}function m(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(o.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(i.A,{className:"size-4"})})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{MB:()=>i,ZO:()=>o,cn:()=>n,xh:()=>d});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}let o=()=>localStorage.getItem("studentToken"),i=()=>{localStorage.removeItem("studentToken")},d=()=>!!o()},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>c,MJ:()=>h,Rr:()=>v,zB:()=>p,eI:()=>x,lR:()=>f,C5:()=>b});var r=a(95155),s=a(12115),n=a(66634),o=a(62177),i=a(59434),d=a(24265);function l(e){let{className:t,...a}=e;return(0,r.jsx)(d.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let c=o.Op,u=s.createContext({}),p=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(o.xI,{...t})})},m=()=>{let e=s.useContext(u),t=s.useContext(g),{getFieldState:a}=(0,o.xW)(),r=(0,o.lN)({name:e.name}),n=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},g=s.createContext({});function x(e){let{className:t,...a}=e,n=s.useId();return(0,r.jsx)(g.Provider,{value:{id:n},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...a})})}function f(e){let{className:t,...a}=e,{error:s,formItemId:n}=m();return(0,r.jsx)(l,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...a})}function h(e){let{...t}=e,{error:a,formItemId:s,formDescriptionId:o,formMessageId:i}=m();return(0,r.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(o," ").concat(i):"".concat(o),"aria-invalid":!!a,...t})}function v(e){let{className:t,...a}=e,{formDescriptionId:s}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function b(e){var t;let{className:a,...s}=e,{error:n,formMessageId:o}=m(),d=n?String(null!==(t=null==n?void 0:n.message)&&void 0!==t?t:""):s.children;return d?(0,r.jsx)("p",{"data-slot":"form-message",id:o,className:(0,i.cn)("text-destructive text-sm",a),...s,children:d}):null}},94314:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>d,_3:()=>s,ac:()=>o});var r=a(51990),s=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let n=(0,r.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let a=t.payload;e.completedForms[a]||(e.completedForms[a]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:o,setCurrentStep:i}=n.actions,d=n.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,6046,4945,4632,1342,4211,3e3,8441,1684,7358],()=>t(11132)),_N_E=e.O()}]);