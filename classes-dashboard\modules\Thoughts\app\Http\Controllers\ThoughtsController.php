<?php

namespace Thoughts\Http\Controllers;

use Thoughts\Http\Requests\CreateThoughtsRequest;
use Thoughts\Repositories\ThoughtsRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ThoughtsController extends Controller
{

    protected $thoughtsRepository;
    public function __construct(ThoughtsRepository $thoughtsRepository)
    {
        $this->thoughtsRepository = $thoughtsRepository;
    }
    
    public function index(Request $request)
    {
        $list = $this->thoughtsRepository->getAll($request);
        if (request()->ajax()) {
            return $this->thoughtsRepository->getDatatable($list);
        }
        return view('Thoughts::index', compact('list'));
    }

    public function create()
    {
        return view('Thoughts::create');
    }

    public function store(CreateThoughtsRequest $request)
    {
       $this->thoughtsRepository->createThoughts($request);
       return response()->json(['success' => 'Thoughts Created Successfully!!']);
    }

    public function edit($id)
    {
        $data = $this->thoughtsRepository->getThoughtsById($id);
        return view('Thoughts::edit', compact('data'));
    }

    public function update(CreateThoughtsRequest $request, $id)
    {
        $this->thoughtsRepository->updateThoughts($request, $id);
        return response()->json(['success' => 'Thoughts Updated successfully!!']);
    }

    public function destroy($id)
    {
        $thoughts = $this->thoughtsRepository->getThoughtsById($id);
        $thoughts->delete();
        return response()->json(['success' => 'Thoughts deleted successfully!!']);
    }
}
