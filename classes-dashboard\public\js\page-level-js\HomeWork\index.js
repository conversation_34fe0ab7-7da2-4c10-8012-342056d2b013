/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/HomeWork/resources/views/js/index.js ***!
  \******************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "homework_date",
  name: "homework_date"
}, {
  data: "classroom_id",
  name: "classroom_id"
}, {
  data: "subject_id",
  name: "subject_id"
}, {
  data: "title",
  name: "title"
}, {
  data: "description",
  name: "description"
}];
var table = commonDatatable("#homeWork_table", homeWorksRoute.index, columns);
$(document).on("click", "#addHomeWorkEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = homeWorksRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add HomeWork");
    $("#createContent").html(result);
  }, commonAjax(params);
});
$(document).on("click", ".editHomeWorkEntry", function () {
  var editdid = $(this).attr("data-editHomeWorkid");
  var url = homeWorksRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit HomeWork");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteHomeWorkEntry", function () {
  var homeWorkid = $(this).attr("data-homeWorkid");
  var url = homeWorksRoute["delete"];
  url = url.replace(":homeWorkid", homeWorkid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;