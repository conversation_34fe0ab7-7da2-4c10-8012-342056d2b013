"use strict";exports.id=9528,exports.ids=[9528],exports.modules={3416:(e,t,r)=>{r.d(t,{sG:()=>c,hO:()=>f});var n=r(43210),o=r(51215),l=r(98599),i=r(60687),a=n.forwardRef((e,t)=>{let{children:r,...o}=e,l=n.Children.toArray(r),a=l.find(d);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...o,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,l.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?a:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function f(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},11273:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(43210),o=r(60687);function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;r=[...r,l];let s=t=>{let{scope:r,children:l,...s}=t,u=r?.[e]?.[a]||i,d=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:d,children:l})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},95732:(e,t,r)=>{r.d(t,{UC:()=>tu,In:()=>ta,q7:()=>tc,VF:()=>tp,p4:()=>tf,ZL:()=>ts,bL:()=>to,wn:()=>tv,PP:()=>tm,l9:()=>tl,WT:()=>ti,LM:()=>td});var n,o=r(43210),l=r.t(o,2),i=r(51215);function a(e,[t,r]){return Math.min(r,Math.max(t,e))}function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var u=r(11273),d=r(98599),c=r(60687),f=o.forwardRef((e,t)=>{let{children:r,...n}=e,l=o.Children.toArray(r),i=l.find(v);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(p,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,c.jsx)(p,{...n,ref:t,children:r})});f.displayName="Slot";var p=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),l=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(l.ref=t?(0,d.t)(t,e):e),o.cloneElement(r,l)}return o.Children.count(r)>1?o.Children.only(null):null});p.displayName="SlotClone";var m=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function v(e){return o.isValidElement(e)&&e.type===m}var h=o.createContext(void 0),y=r(3416);function g(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var w="dismissableLayer.update",x=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:u,onDismiss:f,...p}=e,m=o.useContext(x),[v,h]=o.useState(null),b=v?.ownerDocument??globalThis?.document,[,S]=o.useState({}),R=(0,d.s)(t,e=>h(e)),j=Array.from(m.layers),[P]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),N=j.indexOf(P),A=v?j.indexOf(v):-1,T=m.layersWithOutsidePointerEventsDisabled.size>0,L=A>=N,D=function(e,t=globalThis?.document){let r=g(e),n=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){C("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=n,t.addEventListener("click",l.current,{once:!0})):n()}else t.removeEventListener("click",l.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));!L||r||(i?.(e),u?.(e),e.defaultPrevented||f?.())},b),k=function(e,t=globalThis?.document){let r=g(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&C("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...m.branches].some(e=>e.contains(t))||(a?.(e),u?.(e),e.defaultPrevented||f?.())},b);return function(e,t=globalThis?.document){let r=g(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{A===m.layers.size-1&&(l?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},b),o.useEffect(()=>{if(v)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(n=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(v)),m.layers.add(v),E(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(b.body.style.pointerEvents=n)}},[v,b,r,m]),o.useEffect(()=>()=>{v&&(m.layers.delete(v),m.layersWithOutsidePointerEventsDisabled.delete(v),E())},[v,m]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(w,e),()=>document.removeEventListener(w,e)},[]),(0,c.jsx)(y.sG.div,{...p,ref:R,style:{pointerEvents:T?L?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,k.onFocusCapture),onBlurCapture:s(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,D.onPointerDownCapture)})});function E(){let e=new CustomEvent(w);document.dispatchEvent(e)}function C(e,t,r,{discrete:n}){let o=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,y.hO)(o,l):o.dispatchEvent(l)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(x),n=o.useRef(null),l=(0,d.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(y.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch";var S=0;function R(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var j="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0},A=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:l,onUnmountAutoFocus:i,...a}=e,[s,u]=o.useState(null),f=g(l),p=g(i),m=o.useRef(null),v=(0,d.s)(t,e=>u(e)),h=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(h.paused||!s)return;let t=e.target;s.contains(t)?m.current=t:D(m.current,{select:!0})},t=function(e){if(h.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||D(m.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&D(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,h.paused]),o.useEffect(()=>{if(s){k.add(h);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(j,N);s.addEventListener(j,f),s.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(D(n,{select:t}),document.activeElement!==r)return}(T(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&D(s))}return()=>{s.removeEventListener(j,f),setTimeout(()=>{let t=new CustomEvent(P,N);s.addEventListener(P,p),s.dispatchEvent(t),t.defaultPrevented||D(e??document.body,{select:!0}),s.removeEventListener(P,p),k.remove(h)},0)}}},[s,f,p,h]);let w=o.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,l]=function(e){let t=T(e);return[L(t,e),L(t.reverse(),e)]}(t);n&&l?e.shiftKey||o!==l?e.shiftKey&&o===n&&(e.preventDefault(),r&&D(l,{select:!0})):(e.preventDefault(),r&&D(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,h.paused]);return(0,c.jsx)(y.sG.div,{tabIndex:-1,...a,ref:v,onKeyDown:w})});function T(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function L(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function D(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}A.displayName="FocusScope";var k=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=I(e,t)).unshift(t)},remove(t){e=I(e,t),e[0]?.resume()}}}();function I(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var O=globalThis?.document?o.useLayoutEffect:()=>{},M=l["useId".toString()]||(()=>void 0),F=0;function W(e){let[t,r]=o.useState(M());return O(()=>{e||r(e=>e??String(F++))},[e]),e||(t?`radix-${t}`:"")}var H=r(4503),B=r(25605),V=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...l}=e;return(0,c.jsx)(y.sG.svg,{...l,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});V.displayName="Arrow";var _="Popper",[G,K]=(0,u.A)(_),[$,z]=G(_),U=e=>{let{__scopePopper:t,children:r}=e,[n,l]=o.useState(null);return(0,c.jsx)($,{scope:t,anchor:n,onAnchorChange:l,children:r})};U.displayName=_;var q="PopperAnchor",Y=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...l}=e,i=z(q,r),a=o.useRef(null),s=(0,d.s)(t,a);return o.useEffect(()=>{i.onAnchorChange(n?.current||a.current)}),n?null:(0,c.jsx)(y.sG.div,{...l,ref:s})});Y.displayName=q;var X="PopperContent",[Z,J]=G(X),Q=o.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:l=0,align:i="center",alignOffset:a=0,arrowPadding:s=0,avoidCollisions:u=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:h="optimized",onPlaced:w,...x}=e,b=z(X,r),[E,C]=o.useState(null),S=(0,d.s)(t,e=>C(e)),[R,j]=o.useState(null),P=function(e){let[t,r]=o.useState(void 0);return O(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(R),N=P?.width??0,A=P?.height??0,T="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},L=Array.isArray(f)?f:[f],D=L.length>0,k={padding:T,boundary:L.filter(en),altBoundary:D},{refs:I,floatingStyles:M,placement:F,isPositioned:W,middlewareData:V}=(0,H.we)({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(0,B.ll)(...e,{animationFrame:"always"===h}),elements:{reference:b.anchor},middleware:[(0,H.cY)({mainAxis:l+A,alignmentAxis:a}),u&&(0,H.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?(0,H.ER)():void 0,...k}),u&&(0,H.UU)({...k}),(0,H.Ej)({...k,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:l}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${r}px`),i.setProperty("--radix-popper-available-height",`${n}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${l}px`)}}),R&&(0,H.UE)({element:R,padding:s}),eo({arrowWidth:N,arrowHeight:A}),v&&(0,H.jD)({strategy:"referenceHidden",...k})]}),[_,G]=el(F),K=g(w);O(()=>{W&&K?.()},[W,K]);let $=V.arrow?.x,U=V.arrow?.y,q=V.arrow?.centerOffset!==0,[Y,J]=o.useState();return O(()=>{E&&J(window.getComputedStyle(E).zIndex)},[E]),(0,c.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:W?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(Z,{scope:r,placedSide:_,onArrowChange:j,arrowX:$,arrowY:U,shouldHideArrow:q,children:(0,c.jsx)(y.sG.div,{"data-side":_,"data-align":G,...x,ref:S,style:{...x.style,animation:W?void 0:"none"}})})})});Q.displayName=X;var ee="PopperArrow",et={top:"bottom",right:"left",bottom:"top",left:"right"},er=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=J(ee,r),l=et[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(V,{...n,ref:t,style:{...n.style,display:"block"}})})});function en(e){return null!==e}er.displayName=ee;var eo=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,i=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[s,u]=el(r),d={start:"0%",center:"50%",end:"100%"}[u],c=(o.arrow?.x??0)+i/2,f=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===s?(p=l?d:`${c}px`,m=`${-a}px`):"top"===s?(p=l?d:`${c}px`,m=`${n.floating.height+a}px`):"right"===s?(p=`${-a}px`,m=l?d:`${f}px`):"left"===s&&(p=`${n.floating.width+a}px`,m=l?d:`${f}px`),{data:{x:p,y:m}}}});function el(e){let[t,r="center"]=e.split("-");return[t,r]}var ei=o.forwardRef((e,t)=>{let{container:r,...n}=e,[l,a]=o.useState(!1);O(()=>a(!0),[]);let s=r||l&&globalThis?.document?.body;return s?i.createPortal((0,c.jsx)(y.sG.div,{...n,ref:t}),s):null});ei.displayName="Portal";var ea=o.forwardRef((e,t)=>{let{children:r,...n}=e,l=o.Children.toArray(r),i=l.find(ed);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(es,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,c.jsx)(es,{...n,ref:t,children:r})});ea.displayName="Slot";var es=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),l=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(l.ref=t?(0,d.t)(t,e):e),o.cloneElement(r,l)}return o.Children.count(r)>1?o.Children.only(null):null});es.displayName="SlotClone";var eu=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function ed(e){return o.isValidElement(e)&&e.type===eu}function ec({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,l]=function({defaultProp:e,onChange:t}){let r=o.useState(e),[n]=r,l=o.useRef(n),i=g(t);return o.useEffect(()=>{l.current!==n&&(i(n),l.current=n)},[n,l,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,a=i?e:n,s=g(r);return[a,o.useCallback(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&s(r)}else l(t)},[i,e,l,s])]}var ef=o.forwardRef((e,t)=>(0,c.jsx)(y.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ef.displayName="VisuallyHidden";var ep=r(63376),em=r(11490),ev=[" ","Enter","ArrowUp","ArrowDown"],eh=[" ","Enter"],ey="Select",[eg,ew,ex]=function(e){let t=e+"CollectionProvider",[r,n]=(0,u.A)(t),[l,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=o.useRef(null),i=o.useRef(new Map).current;return(0,c.jsx)(l,{scope:t,itemMap:i,collectionRef:n,children:r})};a.displayName=t;let s=e+"CollectionSlot",p=o.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=i(s,r),l=(0,d.s)(t,o.collectionRef);return(0,c.jsx)(f,{ref:l,children:n})});p.displayName=s;let m=e+"CollectionItemSlot",v="data-radix-collection-item",h=o.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,a=o.useRef(null),s=(0,d.s)(t,a),u=i(m,r);return o.useEffect(()=>(u.itemMap.set(a,{ref:a,...l}),()=>void u.itemMap.delete(a))),(0,c.jsx)(f,{[v]:"",ref:s,children:n})});return h.displayName=m,[{Provider:a,Slot:p,ItemSlot:h},function(t){let r=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(ey),[eb,eE]=(0,u.A)(ey,[ex,K]),eC=K(),[eS,eR]=eb(ey),[ej,eP]=eb(ey),eN=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:y}=e,g=eC(t),[w,x]=o.useState(null),[b,E]=o.useState(null),[C,S]=o.useState(!1),R=function(e){let t=o.useContext(h);return e||t||"ltr"}(d),[j=!1,P]=ec({prop:n,defaultProp:l,onChange:i}),[N,A]=ec({prop:a,defaultProp:s,onChange:u}),T=o.useRef(null),L=!w||y||!!w.closest("form"),[D,k]=o.useState(new Set),I=Array.from(D).map(e=>e.props.value).join(";");return(0,c.jsx)(U,{...g,children:(0,c.jsxs)(eS,{required:v,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:W(),value:N,onValueChange:A,open:j,onOpenChange:P,dir:R,triggerPointerDownPosRef:T,disabled:m,children:[(0,c.jsx)(eg.Provider,{scope:t,children:(0,c.jsx)(ej,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{k(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{k(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),L?(0,c.jsxs)(tt,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>A(e.target.value),disabled:m,form:y,children:[void 0===N?(0,c.jsx)("option",{value:""}):null,Array.from(D)]},I):null]})})};eN.displayName=ey;var eA="SelectTrigger",eT=o.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...l}=e,i=eC(r),a=eR(eA,r),u=a.disabled||n,f=(0,d.s)(t,a.onTriggerChange),p=ew(r),m=o.useRef("touch"),[v,h,g]=tr(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===a.value),n=tn(t,e,r);void 0!==n&&a.onValueChange(n.value)}),w=e=>{u||(a.onOpenChange(!0),g()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)(Y,{asChild:!0,...i,children:(0,c.jsx)(y.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":te(a.value)?"":void 0,...l,ref:f,onClick:s(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&w(e)}),onPointerDown:s(l.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:s(l.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&ev.includes(e.key)&&(w(),e.preventDefault())})})})});eT.displayName=eA;var eL="SelectValue",eD=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:l,placeholder:i="",...a}=e,s=eR(eL,r),{onValueNodeHasChildrenChange:u}=s,f=void 0!==l,p=(0,d.s)(t,s.onValueNodeChange);return O(()=>{u(f)},[u,f]),(0,c.jsx)(y.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:te(s.value)?(0,c.jsx)(c.Fragment,{children:i}):l})});eD.displayName=eL;var ek=o.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,c.jsx)(y.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});ek.displayName="SelectIcon";var eI=e=>(0,c.jsx)(ei,{asChild:!0,...e});eI.displayName="SelectPortal";var eO="SelectContent",eM=o.forwardRef((e,t)=>{let r=eR(eO,e.__scopeSelect),[n,l]=o.useState();return(O(()=>{l(new DocumentFragment)},[]),r.open)?(0,c.jsx)(eH,{...e,ref:t}):n?i.createPortal((0,c.jsx)(eF,{scope:e.__scopeSelect,children:(0,c.jsx)(eg.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),n):null});eM.displayName=eO;var[eF,eW]=eb(eO),eH=o.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:f,align:p,alignOffset:m,arrowPadding:v,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:x,...E}=e,C=eR(eO,r),[j,P]=o.useState(null),[N,T]=o.useState(null),L=(0,d.s)(t,e=>P(e)),[D,k]=o.useState(null),[I,O]=o.useState(null),M=ew(r),[F,W]=o.useState(!1),H=o.useRef(!1);o.useEffect(()=>{if(j)return(0,ep.Eq)(j)},[j]),o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??R()),document.body.insertAdjacentElement("beforeend",e[1]??R()),S++,()=>{1===S&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),S--}},[]);let B=o.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&N&&(N.scrollTop=0),r===n&&N&&(N.scrollTop=N.scrollHeight),r?.focus(),document.activeElement!==o))return},[M,N]),V=o.useCallback(()=>B([D,j]),[B,D,j]);o.useEffect(()=>{F&&V()},[F,V]);let{onOpenChange:_,triggerPointerDownPosRef:G}=C;o.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(G.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(G.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||_(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,_,G]),o.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[K,$]=tr(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=tn(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),z=o.useCallback((e,t,r)=>{let n=!H.current&&!r;(void 0!==C.value&&C.value===t||n)&&(k(e),n&&(H.current=!0))},[C.value]),U=o.useCallback(()=>j?.focus(),[j]),q=o.useCallback((e,t,r)=>{let n=!H.current&&!r;(void 0!==C.value&&C.value===t||n)&&O(e)},[C.value]),Y="popper"===n?eV:eB,X=Y===eV?{side:u,sideOffset:f,align:p,alignOffset:m,arrowPadding:v,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:x}:{};return(0,c.jsx)(eF,{scope:r,content:j,viewport:N,onViewportChange:T,itemRefCallback:z,selectedItem:D,onItemLeave:U,itemTextRefCallback:q,focusSelectedItem:V,selectedItemText:I,position:n,isPositioned:F,searchRef:K,children:(0,c.jsx)(em.A,{as:ea,allowPinchZoom:!0,children:(0,c.jsx)(A,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:s(l,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(b,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,c.jsx)(Y,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...X,onPlaced:()=>W(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:s(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});eH.displayName="SelectContentImpl";var eB=o.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...l}=e,i=eR(eO,r),s=eW(eO,r),[u,f]=o.useState(null),[p,m]=o.useState(null),v=(0,d.s)(t,e=>m(e)),h=ew(r),g=o.useRef(!1),w=o.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:C}=s,S=o.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&x&&b&&E){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==i.dir){let n=o.left-t.left,l=r.left-n,i=e.left-l,s=e.width+i,d=Math.max(s,t.width),c=a(l,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let n=t.right-o.right,l=window.innerWidth-r.right-n,i=window.innerWidth-e.right-l,s=e.width+i,d=Math.max(s,t.width),c=a(l,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let l=h(),s=window.innerHeight-20,d=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),m=parseInt(c.paddingTop,10),v=parseInt(c.borderBottomWidth,10),y=f+m+d+parseInt(c.paddingBottom,10)+v,w=Math.min(5*b.offsetHeight,y),C=window.getComputedStyle(x),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),j=e.top+e.height/2-10,P=b.offsetHeight/2,N=f+m+(b.offsetTop+P);if(N<=j){let e=l.length>0&&b===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-j,P+(e?R:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+v);u.style.height=N+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;u.style.top="0px";let t=Math.max(j,f+x.offsetTop+(e?S:0)+P);u.style.height=t+(y-N)+"px",x.scrollTop=N-j+x.offsetTop}u.style.margin="10px 0",u.style.minHeight=w+"px",u.style.maxHeight=s+"px",n?.(),requestAnimationFrame(()=>g.current=!0)}},[h,i.trigger,i.valueNode,u,p,x,b,E,i.dir,n]);O(()=>S(),[S]);let[R,j]=o.useState();O(()=>{p&&j(window.getComputedStyle(p).zIndex)},[p]);let P=o.useCallback(e=>{e&&!0===w.current&&(S(),C?.(),w.current=!1)},[S,C]);return(0,c.jsx)(e_,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:P,children:(0,c.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,c.jsx)(y.sG.div,{...l,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});eB.displayName="SelectItemAlignedPosition";var eV=o.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...l}=e,i=eC(r);return(0,c.jsx)(Q,{...i,...l,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName="SelectPopperPosition";var[e_,eG]=eb(eO,{}),eK="SelectViewport",e$=o.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...l}=e,i=eW(eK,r),a=eG(eK,r),u=(0,d.s)(t,i.onViewportChange),f=o.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,c.jsx)(eg.Slot,{scope:r,children:(0,c.jsx)(y.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:s(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=a;if(n?.current&&r){let e=Math.abs(f.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let l=o+e,i=Math.min(n,l),a=l-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});e$.displayName=eK;var ez="SelectGroup",[eU,eq]=eb(ez);o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=W();return(0,c.jsx)(eU,{scope:r,id:o,children:(0,c.jsx)(y.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})}).displayName=ez;var eY="SelectLabel";o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=eq(eY,r);return(0,c.jsx)(y.sG.div,{id:o.id,...n,ref:t})}).displayName=eY;var eX="SelectItem",[eZ,eJ]=eb(eX),eQ=o.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:l=!1,textValue:i,...a}=e,u=eR(eX,r),f=eW(eX,r),p=u.value===n,[m,v]=o.useState(i??""),[h,g]=o.useState(!1),w=(0,d.s)(t,e=>f.itemRefCallback?.(e,n,l)),x=W(),b=o.useRef("touch"),E=()=>{l||(u.onValueChange(n),u.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(eZ,{scope:r,value:n,disabled:l,textId:x,isSelected:p,onItemTextChange:o.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,c.jsx)(eg.ItemSlot,{scope:r,value:n,disabled:l,textValue:m,children:(0,c.jsx)(y.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":h?"":void 0,"aria-selected":p&&h,"data-state":p?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...a,ref:w,onFocus:s(a.onFocus,()=>g(!0)),onBlur:s(a.onBlur,()=>g(!1)),onClick:s(a.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:s(a.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:s(a.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:s(a.onPointerMove,e=>{b.current=e.pointerType,l?f.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:s(a.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:s(a.onKeyDown,e=>{(f.searchRef?.current===""||" "!==e.key)&&(eh.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eQ.displayName=eX;var e0="SelectItemText",e1=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,...a}=e,s=eR(e0,r),u=eW(e0,r),f=eJ(e0,r),p=eP(e0,r),[m,v]=o.useState(null),h=(0,d.s)(t,e=>v(e),f.onItemTextChange,e=>u.itemTextRefCallback?.(e,f.value,f.disabled)),g=m?.textContent,w=o.useMemo(()=>(0,c.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return O(()=>(x(w),()=>b(w)),[x,b,w]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(y.sG.span,{id:f.textId,...a,ref:h}),f.isSelected&&s.valueNode&&!s.valueNodeHasChildren?i.createPortal(a.children,s.valueNode):null]})});e1.displayName=e0;var e2="SelectItemIndicator",e5=o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eJ(e2,r).isSelected?(0,c.jsx)(y.sG.span,{"aria-hidden":!0,...n,ref:t}):null});e5.displayName=e2;var e6="SelectScrollUpButton",e9=o.forwardRef((e,t)=>{let r=eW(e6,e.__scopeSelect),n=eG(e6,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,d.s)(t,n.onScrollButtonChange);return O(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,c.jsx)(e7,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});e9.displayName=e6;var e3="SelectScrollDownButton",e8=o.forwardRef((e,t)=>{let r=eW(e3,e.__scopeSelect),n=eG(e3,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,d.s)(t,n.onScrollButtonChange);return O(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,c.jsx)(e7,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});e8.displayName=e3;var e7=o.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...l}=e,i=eW("SelectScrollButton",r),a=o.useRef(null),u=ew(r),d=o.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return o.useEffect(()=>()=>d(),[d]),O(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,c.jsx)(y.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:s(l.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(n,50))}),onPointerMove:s(l.onPointerMove,()=>{i.onItemLeave?.(),null===a.current&&(a.current=window.setInterval(n,50))}),onPointerLeave:s(l.onPointerLeave,()=>{d()})})});o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,c.jsx)(y.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var e4="SelectArrow";function te(e){return""===e||void 0===e}o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=eC(r),l=eR(e4,r),i=eW(e4,r);return l.open&&"popper"===i.position?(0,c.jsx)(er,{...o,...n,ref:t}):null}).displayName=e4;var tt=o.forwardRef((e,t)=>{let{value:r,...n}=e,l=o.useRef(null),i=(0,d.s)(t,l),a=function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return o.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,c.jsx)(ef,{asChild:!0,children:(0,c.jsx)("select",{...n,ref:i,defaultValue:r})})});function tr(e){let t=g(e),r=o.useRef(""),n=o.useRef(0),l=o.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=o.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,l,i]}function tn(e,t,r){var n,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===l.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==r?s:void 0}tt.displayName="BubbleSelect";var to=eN,tl=eT,ti=eD,ta=ek,ts=eI,tu=eM,td=e$,tc=eQ,tf=e1,tp=e5,tm=e9,tv=e8},98599:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>l});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}}};