import { PrismaClient, UserType, NotificationType } from "@prisma/client";
import { createNotification } from "@/utils/notifications";

const prisma = new PrismaClient();

export const getPrivateMessages = async (userId1: string, userId2: string) => {
  const messages = await prisma.chatMessage.findMany({
    where: {
      OR: [
        {
          senderId: userId1,
          recipientId: userId2
        },
        {
          senderId: userId2,
          recipientId: userId1
        }
      ]
    },
    orderBy: {
      timestamp: 'asc'
    }
  });

  const messagesWithNames = await Promise.all(messages.map(async (message) => {
    let senderName = '';
    let recipientName = '';

    if (message.senderType === 'student') {
      const student = await prisma.student.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
    } else if (message.senderType === 'class') {
      const classUser = await prisma.classes.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
    }

    if (message.recipientId && message.recipientType === 'student') {
      const student = await prisma.student.findUnique({
        where: { id: message.recipientId },
        select: { firstName: true, lastName: true }
      });
      recipientName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
    } else if (message.recipientId && message.recipientType === 'class') {
      const classUser = await prisma.classes.findUnique({
        where: { id: message.recipientId },
        select: { firstName: true, lastName: true }
      });
      recipientName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
    }

    return {
      ...message,
      sender: senderName,
      recipient: recipientName
    };
  }));

  return messagesWithNames;
};

export const getMessageUsers = async (userId: string, userType: 'student' | 'class') => {
  const messages = await prisma.chatMessage.findMany({
    where: {
      OR: [
        { senderId: userId, senderType: userType },
        { recipientId: userId, recipientType: userType }
      ]
    },
    select: {
      senderId: true,
      senderType: true,
      recipientId: true,
      recipientType: true
    }
  });

  const uniqueUserIds = new Set<string>();

  messages.forEach((message: any) => {
    if (message.senderId !== userId && message.senderId) {
      uniqueUserIds.add(message.senderId);
    }
    if (message.recipientId !== userId && message.recipientId) {
      uniqueUserIds.add(message.recipientId);
    }
  });

  const usersWithIds: Array<{ username: string; userId: string }> = [];
  for (const id of uniqueUserIds) {
    const student = await prisma.student.findUnique({
      where: { id },
      select: { firstName: true, lastName: true }
    });

    if (student) {
      usersWithIds.push({
        username: `${student.firstName} ${student.lastName}`,
        userId: id
      });
    } else {
      const classUser = await prisma.classes.findUnique({
        where: { id },
        select: { firstName: true, lastName: true }
      });
      if (classUser) {
        usersWithIds.push({
          username: `${classUser.firstName} ${classUser.lastName}`,
          userId: id
        });
      }
    }
  }

  return usersWithIds;
};

export const getPendingMessages = async (userId: string, userType: 'student' | 'class') => {
  const messages = await prisma.chatMessage.findMany({
    where: {
      recipientId: userId,
      recipientType: userType
    },
    orderBy: {
      timestamp: 'asc'
    }
  });

  const messagesWithNames = await Promise.all(messages.map(async (message) => {
    let senderName = '';

    if (message.senderType === 'student') {
      const student = await prisma.student.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
    } else if (message.senderType === 'class') {
      const classUser = await prisma.classes.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
    }

    return {
      ...message,
      sender: senderName
    };
  }));

  return messagesWithNames;
};

export const createMessage = async (
  text: string,
  senderId: string,
  senderType: 'student' | 'class',
  recipientId?: string,
  recipientType?: 'student' | 'class',
) => {
  if (!text || !senderId || !senderType) {
    throw new Error('Missing required parameters: text, senderId, and senderType are required');
  }

  if (!['student', 'class'].includes(senderType)) {
    throw new Error('Invalid sender type. Must be "student" or "class".');
  }

  if (recipientType && !['student', 'class'].includes(recipientType)) {
    throw new Error('Invalid recipient type. Must be "student" or "class".');
  }

  try {
    const message = await prisma.chatMessage.create({
      data: {
        text: text.trim(),
        senderId,
        senderType,
        recipientId: recipientId,
        recipientType,
        timestamp: new Date(),
      },
    });

    // Create notification for chat message if there's a recipient
    if (recipientId && recipientType) {
      // Get sender name first
      let senderName = 'Unknown';
      if (senderType === 'student') {
        const student = await prisma.student.findUnique({
          where: { id: senderId },
          select: { firstName: true, lastName: true }
        });
        senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown Student';
      } else if (senderType === 'class') {
        const classUser = await prisma.classes.findUnique({
          where: { id: senderId },
          select: { firstName: true, lastName: true }
        });
        senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown Class';
      }

      // Count total unread messages from this sender
      const unreadMessageCount = await prisma.chatMessage.count({
        where: {
          senderId: senderId,
          recipientId: recipientId,
          isRead: false
        }
      });

      // Check if there are already unread notifications from this sender
      const existingUnreadNotification = await prisma.notification.findFirst({
        where: {
          userId: recipientId,
          userType: recipientType === 'student' ? UserType.STUDENT : UserType.CLASS,
          type: recipientType === 'student' ? NotificationType.STUDENT_CHAT_MESSAGE : NotificationType.CLASS_CHAT_MESSAGE,
          isRead: false,
          data: {
            path: ['senderId'],
            equals: senderId
          }
        }
      });

      const notificationType = recipientType === 'student' ? NotificationType.STUDENT_CHAT_MESSAGE : NotificationType.CLASS_CHAT_MESSAGE;
      const recipientUserType = recipientType === 'student' ? UserType.STUDENT : UserType.CLASS;

      if (existingUnreadNotification) {
        // Update existing notification with new message count and latest message
        const updatedNotification = await prisma.notification.update({
          where: { id: existingUnreadNotification.id },
          data: {
            message: unreadMessageCount > 1
              ? `You have ${unreadMessageCount} new messages from ${senderName}`
              : `You have a new message from ${senderName}`,
            data: {
              messageId: message.id,
              senderId: senderId,
              senderName: senderName,
              senderType: senderType,
              messageText: text.trim().substring(0, 100),
              conversationId: `${senderId}_${recipientId}`,
              unreadCount: unreadMessageCount,
              redirectUrl: recipientType === 'student'
                ? `/student/chat`
                : `/classes/chat`,
              actionType: 'OPEN_CHAT'
            },
            updatedAt: new Date()
          }
        });

      } else {
        // Create new notification
        await createNotification({
          userId: recipientId,
          userType: recipientUserType,
          type: notificationType,
          title: 'New Chat Message',
          message: unreadMessageCount > 1
            ? `You have ${unreadMessageCount} new messages from ${senderName}`
            : `You have a new message from ${senderName}`,
          data: {
            messageId: message.id,
            senderId: senderId,
            senderName: senderName,
            senderType: senderType,
            messageText: text.trim().substring(0, 100),
            conversationId: `${senderId}_${recipientId}`,
            unreadCount: unreadMessageCount,
            redirectUrl: recipientType === 'student'
              ? `/student/chat`
              : `/classes/chat`,
            actionType: 'OPEN_CHAT'
          }
        });
      }
    }

    return message;
  } catch {
    console.log("Failed to Save message in Database")
  }
};

export const getUnreadMessageUsers = async (userId: string, userType: 'student' | 'class') => {
  const unreadMessages = await prisma.chatMessage.findMany({
    where: {
      recipientId: userId,
      recipientType: userType,
      isRead: false
    },
    select: {
      senderId: true,
      senderType: true
    },
    distinct: ['senderId']
  });

  const uniqueUserIds = new Set<string>();
  unreadMessages.forEach((message: any) => {
    if (message.senderId !== userId) {
      uniqueUserIds.add(message.senderId);
    }
  });

  const userDetails = await Promise.all(
    Array.from(uniqueUserIds).map(async (senderId) => {
      const senderType = unreadMessages.find(msg => msg.senderId === senderId)?.senderType;

      if (senderType === 'student') {
        const student = await prisma.student.findUnique({
          where: { id: senderId },
          select: { firstName: true, lastName: true }
        });
        return {
          userId: senderId,
          username: student ? `${student.firstName} ${student.lastName}` : 'Unknown',
          userType: 'student'
        };
      } else if (senderType === 'class') {
        const classUser = await prisma.classes.findUnique({
          where: { id: senderId },
          select: { firstName: true, lastName: true }
        });
        return {
          userId: senderId,
          username: classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown',
          userType: 'class'
        };
      }
      return null;
    })
  );

  return userDetails.filter(user => user !== null);
};

export const markMessagesAsRead = async (senderId: string, recipientId: string) => {
  await prisma.chatMessage.updateMany({
    where: {
      senderId: senderId,
      recipientId: recipientId,
      isRead: false
    },
    data: {
      isRead: true
    }
  });
};

// New function to mark chat conversation as seen and clear notifications
export const markChatAsSeen = async (userId: string, userType: 'student' | 'class', senderId: string) => {
  try {
    // Mark chat messages as read
    await prisma.chatMessage.updateMany({
      where: {
        senderId: senderId,
        recipientId: userId,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    // Mark chat notifications as read for this specific sender
    const notificationType = userType === 'student' ? NotificationType.STUDENT_CHAT_MESSAGE : NotificationType.CLASS_CHAT_MESSAGE;
    const recipientUserType = userType === 'student' ? UserType.STUDENT : UserType.CLASS;

    await prisma.notification.updateMany({
      where: {
        userId: userId,
        userType: recipientUserType,
        type: notificationType,
        isRead: false,
        data: {
          path: ['senderId'],
          equals: senderId
        }
      },
      data: {
        isRead: true
      }
    });

    console.log(`Chat marked as seen for ${userType} ${userId} from sender ${senderId}`);
    return { success: true };
  } catch (error) {
    console.error('Error marking chat as seen:', error);
    throw error;
  }
};
