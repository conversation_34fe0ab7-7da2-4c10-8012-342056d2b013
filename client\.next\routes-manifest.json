{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/blogs/[id]", "regex": "^/blogs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/blogs/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/careers/apply/[id]", "regex": "^/careers/apply/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/careers/apply/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/careers/details/[id]", "regex": "^/careers/details/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/careers/details/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/classes-details/[id]", "regex": "^/classes\\-details/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/classes\\-details/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/mock-exam-result/[studentId]", "regex": "^/mock\\-exam\\-result/([^/]+?)(?:/)?$", "routeKeys": {"nxtPstudentId": "nxtPstudentId"}, "namedRegex": "^/mock\\-exam\\-result/(?<nxtPstudentId>[^/]+?)(?:/)?$"}, {"page": "/uwhiz-details/[examId]", "regex": "^/uwhiz\\-details/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexamId": "nxtPexamId"}, "namedRegex": "^/uwhiz\\-details/(?<nxtPexamId>[^/]+?)(?:/)?$"}, {"page": "/uwhiz-exam/[examId]", "regex": "^/uwhiz\\-exam/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexamId": "nxtPexamId"}, "namedRegex": "^/uwhiz\\-exam/(?<nxtPexamId>[^/]+?)(?:/)?$"}, {"page": "/uwhiz-info/[examid]", "regex": "^/uwhiz\\-info/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexamid": "nxtPexamid"}, "namedRegex": "^/uwhiz\\-info/(?<nxtPexamid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/Leader-Board", "regex": "^/Leader\\-Board(?:/)?$", "routeKeys": {}, "namedRegex": "^/Leader\\-Board(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/blogs", "regex": "^/blogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/blogs(?:/)?$"}, {"page": "/careers", "regex": "^/careers(?:/)?$", "routeKeys": {}, "namedRegex": "^/careers(?:/)?$"}, {"page": "/class/login", "regex": "^/class/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/class/login(?:/)?$"}, {"page": "/classes/blogs", "regex": "^/classes/blogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/blogs(?:/)?$"}, {"page": "/classes/blogs/add", "regex": "^/classes/blogs/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/blogs/add(?:/)?$"}, {"page": "/classes/chat", "regex": "^/classes/chat(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/chat(?:/)?$"}, {"page": "/classes/dashboard", "regex": "^/classes/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/dashboard(?:/)?$"}, {"page": "/classes/profile", "regex": "^/classes/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile(?:/)?$"}, {"page": "/classes/profile/address", "regex": "^/classes/profile/address(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/address(?:/)?$"}, {"page": "/classes/profile/certificates", "regex": "^/classes/profile/certificates(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/certificates(?:/)?$"}, {"page": "/classes/profile/description", "regex": "^/classes/profile/description(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/description(?:/)?$"}, {"page": "/classes/profile/education", "regex": "^/classes/profile/education(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/education(?:/)?$"}, {"page": "/classes/profile/experience", "regex": "^/classes/profile/experience(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/experience(?:/)?$"}, {"page": "/classes/profile/photo-and-logo", "regex": "^/classes/profile/photo\\-and\\-logo(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/photo\\-and\\-logo(?:/)?$"}, {"page": "/classes/profile/tution-class", "regex": "^/classes/profile/tution\\-class(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/profile/tution\\-class(?:/)?$"}, {"page": "/classes/question-bank", "regex": "^/classes/question\\-bank(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/question\\-bank(?:/)?$"}, {"page": "/classes/referral-dashboard", "regex": "^/classes/referral\\-dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/classes/referral\\-dashboard(?:/)?$"}, {"page": "/coins", "regex": "^/coins(?:/)?$", "routeKeys": {}, "namedRegex": "^/coins(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/mock-exam-card", "regex": "^/mock\\-exam\\-card(?:/)?$", "routeKeys": {}, "namedRegex": "^/mock\\-exam\\-card(?:/)?$"}, {"page": "/mock-test", "regex": "^/mock\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/mock\\-test(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/student/chat", "regex": "^/student/chat(?:/)?$", "routeKeys": {}, "namedRegex": "^/student/chat(?:/)?$"}, {"page": "/student/login", "regex": "^/student/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/student/login(?:/)?$"}, {"page": "/student/profile", "regex": "^/student/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/student/profile(?:/)?$"}, {"page": "/student/referral-dashboard", "regex": "^/student/referral\\-dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/student/referral\\-dashboard(?:/)?$"}, {"page": "/student/wishlist", "regex": "^/student/wishlist(?:/)?$", "routeKeys": {}, "namedRegex": "^/student/wishlist(?:/)?$"}, {"page": "/student-verify-otp", "regex": "^/student\\-verify\\-otp(?:/)?$", "routeKeys": {}, "namedRegex": "^/student\\-verify\\-otp(?:/)?$"}, {"page": "/support", "regex": "^/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/support(?:/)?$"}, {"page": "/terms-and-conditions", "regex": "^/terms\\-and\\-conditions(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-and\\-conditions(?:/)?$"}, {"page": "/uwhiz", "regex": "^/uwhiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/uwhiz(?:/)?$"}, {"page": "/verified-classes", "regex": "^/verified\\-classes(?:/)?$", "routeKeys": {}, "namedRegex": "^/verified\\-classes(?:/)?$"}, {"page": "/verify-email", "regex": "^/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-email(?:/)?$"}, {"page": "/verify-otp", "regex": "^/verify\\-otp(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-otp(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}