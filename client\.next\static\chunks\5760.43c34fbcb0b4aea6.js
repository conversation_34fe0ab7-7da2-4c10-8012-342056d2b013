(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5760],{1763:(t,e,i)=>{"use strict";var r;Object.defineProperty(e,"__esModule",{value:!0});let n=i(46120),s=i(49231);!function(t){t.compose=function(t={},e={},i=!1){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let r=n(e);for(let n in i||(r=Object.keys(r).reduce((t,e)=>(null!=r[e]&&(t[e]=r[e]),t),{})),t)void 0!==t[n]&&void 0===e[n]&&(r[n]=t[n]);return Object.keys(r).length>0?r:void 0},t.diff=function(t={},e={}){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let i=Object.keys(t).concat(Object.keys(e)).reduce((i,r)=>(s(t[r],e[r])||(i[r]=void 0===e[r]?null:e[r]),i),{});return Object.keys(i).length>0?i:void 0},t.invert=function(t={},e={}){t=t||{};let i=Object.keys(e).reduce((i,r)=>(e[r]!==t[r]&&void 0!==t[r]&&(i[r]=e[r]),i),{});return Object.keys(t).reduce((i,r)=>(t[r]!==e[r]&&void 0===e[r]&&(i[r]=null),i),i)},t.transform=function(t,e,i=!1){if("object"!=typeof t)return e;if("object"!=typeof e)return;if(!i)return e;let r=Object.keys(e).reduce((i,r)=>(void 0===t[r]&&(i[r]=e[r]),i),{});return Object.keys(r).length>0?r:void 0}}(r||(r={})),e.default=r},20100:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});let r=i(55110);class n{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);let e=this.ops[this.index];if(!e)return{retain:1/0};{let i=this.offset,n=r.default.length(e);if(t>=n-i?(t=n-i,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};{let r={};return e.attributes&&(r.attributes=e.attributes),"number"==typeof e.retain?r.retain=t:"object"==typeof e.retain&&null!==e.retain?r.retain=e.retain:"string"==typeof e.insert?r.insert=e.insert.substr(i,t):r.insert=e.insert,r}}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?r.default.length(this.ops[this.index])-this.offset:1/0}peekType(){let t=this.ops[this.index];if(t){if("number"==typeof t.delete)return"delete";if("number"!=typeof t.retain&&("object"!=typeof t.retain||null===t.retain))return"insert"}return"retain"}rest(){if(!this.hasNext())return[];if(0===this.offset)return this.ops.slice(this.index);{let t=this.offset,e=this.index,i=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[i].concat(r)}}}e.default=n},27501:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributeMap=e.OpIterator=e.Op=void 0;let r=i(87311),n=i(46120),s=i(49231),l=i(1763);e.AttributeMap=l.default;let o=i(55110);e.Op=o.default;let a=i(20100);e.OpIterator=a.default;let c=(t,e)=>{if("object"!=typeof t||null===t)throw Error(`cannot retain a ${typeof t}`);if("object"!=typeof e||null===e)throw Error(`cannot retain a ${typeof e}`);let i=Object.keys(t)[0];if(!i||i!==Object.keys(e)[0])throw Error(`embed types not matched: ${i} != ${Object.keys(e)[0]}`);return[i,t[i],e[i]]};class h{constructor(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){let e=this.handlers[t];if(!e)throw Error(`no handlers for embed type "${t}"`);return e}insert(t,e){let i={};return"string"==typeof t&&0===t.length?this:(i.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(i.attributes=e),this.push(i))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if("number"==typeof t&&t<=0)return this;let i={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(i.attributes=e),this.push(i)}push(t){let e=this.ops.length,i=this.ops[e-1];if(t=n(t),"object"==typeof i){if("number"==typeof t.delete&&"number"==typeof i.delete)return this.ops[e-1]={delete:i.delete+t.delete},this;if("number"==typeof i.delete&&null!=t.insert&&(e-=1,"object"!=typeof(i=this.ops[e-1])))return this.ops.unshift(t),this;if(s(t.attributes,i.attributes)){if("string"==typeof t.insert&&"string"==typeof i.insert)return this.ops[e-1]={insert:i.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof i.retain)return this.ops[e-1]={retain:i.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){let t=this.ops[this.ops.length-1];return t&&"number"==typeof t.retain&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){let e=[],i=[];return this.forEach(r=>{(t(r)?e:i).push(r)}),[e,i]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce((t,e)=>e.insert?t+o.default.length(e):e.delete?t-e.delete:t,0)}length(){return this.reduce((t,e)=>t+o.default.length(e),0)}slice(t=0,e=1/0){let i=[],r=new a.default(this.ops),n=0;for(;n<e&&r.hasNext();){let s;n<t?s=r.next(t-n):(s=r.next(e-n),i.push(s)),n+=o.default.length(s)}return new h(i)}compose(t){let e=new a.default(this.ops),i=new a.default(t.ops),r=[],n=i.peek();if(null!=n&&"number"==typeof n.retain&&null==n.attributes){let t=n.retain;for(;"insert"===e.peekType()&&e.peekLength()<=t;)t-=e.peekLength(),r.push(e.next());n.retain-t>0&&i.next(n.retain-t)}let o=new h(r);for(;e.hasNext()||i.hasNext();)if("insert"===i.peekType())o.push(i.next());else if("delete"===e.peekType())o.push(e.next());else{let t=Math.min(e.peekLength(),i.peekLength()),r=e.next(t),n=i.next(t);if(n.retain){let a={};if("number"==typeof r.retain)a.retain="number"==typeof n.retain?t:n.retain;else if("number"==typeof n.retain)null==r.retain?a.insert=r.insert:a.retain=r.retain;else{let t=null==r.retain?"insert":"retain",[e,i,s]=c(r[t],n.retain),l=h.getHandler(e);a[t]={[e]:l.compose(i,s,"retain"===t)}}let u=l.default.compose(r.attributes,n.attributes,"number"==typeof r.retain);if(u&&(a.attributes=u),o.push(a),!i.hasNext()&&s(o.ops[o.ops.length-1],a)){let t=new h(e.rest());return o.concat(t).chop()}}else"number"==typeof n.delete&&("number"==typeof r.retain||"object"==typeof r.retain&&null!==r.retain)&&o.push(n)}return o.chop()}concat(t){let e=new h(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new h;let i=[this,t].map(e=>e.map(i=>{if(null!=i.insert)return"string"==typeof i.insert?i.insert:"\0";throw Error("diff() called "+(e===t?"on":"with")+" non-document")}).join("")),n=new h,o=r(i[0],i[1],e,!0),c=new a.default(this.ops),u=new a.default(t.ops);return o.forEach(t=>{let e=t[1].length;for(;e>0;){let i=0;switch(t[0]){case r.INSERT:i=Math.min(u.peekLength(),e),n.push(u.next(i));break;case r.DELETE:i=Math.min(e,c.peekLength()),c.next(i),n.delete(i);break;case r.EQUAL:i=Math.min(c.peekLength(),u.peekLength(),e);let o=c.next(i),a=u.next(i);s(o.insert,a.insert)?n.retain(i,l.default.diff(o.attributes,a.attributes)):n.push(a).delete(i)}e-=i}}),n.chop()}eachLine(t,e="\n"){let i=new a.default(this.ops),r=new h,n=0;for(;i.hasNext();){if("insert"!==i.peekType())return;let s=i.peek(),l=o.default.length(s)-i.peekLength(),a="string"==typeof s.insert?s.insert.indexOf(e,l)-l:-1;if(a<0)r.push(i.next());else if(a>0)r.push(i.next(a));else{if(!1===t(r,i.next(1).attributes||{},n))return;n+=1,r=new h}}r.length()>0&&t(r,{},n)}invert(t){let e=new h;return this.reduce((i,r)=>{if(r.insert)e.delete(o.default.length(r));else if("number"==typeof r.retain&&null==r.attributes)return e.retain(r.retain),i+r.retain;else if(r.delete||"number"==typeof r.retain){let n=r.delete||r.retain;return t.slice(i,i+n).forEach(t=>{r.delete?e.push(t):r.retain&&r.attributes&&e.retain(o.default.length(t),l.default.invert(r.attributes,t.attributes))}),i+n}else if("object"==typeof r.retain&&null!==r.retain){let n=t.slice(i,i+1),s=new a.default(n.ops).next(),[o,u,d]=c(r.retain,s.insert),f=h.getHandler(o);return e.retain({[o]:f.invert(u,d)},l.default.invert(r.attributes,s.attributes)),i+1}return i},0),e.chop()}transform(t,e=!1){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);let i=new a.default(this.ops),r=new a.default(t.ops),n=new h;for(;i.hasNext()||r.hasNext();)if("insert"===i.peekType()&&(e||"insert"!==r.peekType()))n.retain(o.default.length(i.next()));else if("insert"===r.peekType())n.push(r.next());else{let t=Math.min(i.peekLength(),r.peekLength()),s=i.next(t),o=r.next(t);if(s.delete)continue;if(o.delete)n.push(o);else{let i=s.retain,r=o.retain,a="object"==typeof r&&null!==r?r:t;if("object"==typeof i&&null!==i&&"object"==typeof r&&null!==r){let t=Object.keys(i)[0];if(t===Object.keys(r)[0]){let n=h.getHandler(t);n&&(a={[t]:n.transform(i[t],r[t],e)})}}n.retain(a,l.default.transform(s.attributes,o.attributes,e))}}return n.chop()}transformPosition(t,e=!1){e=!!e;let i=new a.default(this.ops),r=0;for(;i.hasNext()&&r<=t;){let n=i.peekLength(),s=i.peekType();if(i.next(),"delete"===s){t-=Math.min(n,t-r);continue}"insert"===s&&(r<t||!e)&&(t+=n),r+=n}return t}}h.Op=o.default,h.OpIterator=a.default,h.AttributeMap=l.default,h.handlers={},e.default=h,t.exports=h,t.exports.default=h},35760:(t,e,i)=>{"use strict";i.r(e),i.d(e,{Quill:()=>sx,default:()=>sA});var r={};i.r(r),i.d(r,{Attributor:()=>eU,AttributorStore:()=>eX,BlockBlot:()=>e9,ClassAttributor:()=>eV,ContainerBlot:()=>it,EmbedBlot:()=>ii,InlineBlot:()=>e6,LeafBlot:()=>e1,ParentBlot:()=>e4,Registry:()=>eF,Scope:()=>eD,ScrollBlot:()=>il,StyleAttributor:()=>eZ,TextBlot:()=>ia});var n=i(12115);let s=function(t,e){return t===e||t!=t&&e!=e},l=function(t,e){for(var i=t.length;i--;)if(s(t[i][0],e))return i;return -1};var o=Array.prototype.splice;function a(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}a.prototype.clear=function(){this.__data__=[],this.size=0},a.prototype.delete=function(t){var e=this.__data__,i=l(e,t);return!(i<0)&&(i==e.length-1?e.pop():o.call(e,i,1),--this.size,!0)},a.prototype.get=function(t){var e=this.__data__,i=l(e,t);return i<0?void 0:e[i][1]},a.prototype.has=function(t){return l(this.__data__,t)>-1},a.prototype.set=function(t,e){var i=this.__data__,r=l(i,t);return r<0?(++this.size,i.push([t,e])):i[r][1]=e,this};var c="object"==typeof global&&global&&global.Object===Object&&global,h="object"==typeof self&&self&&self.Object===Object&&self,u=c||h||Function("return this")(),d=u.Symbol,f=Object.prototype,p=f.hasOwnProperty,g=f.toString,m=d?d.toStringTag:void 0;let b=function(t){var e=p.call(t,m),i=t[m];try{t[m]=void 0;var r=!0}catch(t){}var n=g.call(t);return r&&(e?t[m]=i:delete t[m]),n};var y=Object.prototype.toString,v=d?d.toStringTag:void 0;let x=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":v&&v in Object(t)?b(t):y.call(t)},N=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)},E=function(t){if(!N(t))return!1;var e=x(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};var A=u["__core-js_shared__"],w=function(){var t=/[^.]+$/.exec(A&&A.keys&&A.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),q=Function.prototype.toString;let k=function(t){if(null!=t){try{return q.call(t)}catch(t){}try{return t+""}catch(t){}}return""};var _=/^\[object .+?Constructor\]$/,L=Object.prototype,S=Function.prototype.toString,O=L.hasOwnProperty,T=RegExp("^"+S.call(O).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");let C=function(t){return!!N(t)&&(!w||!(w in t))&&(E(t)?T:_).test(k(t))},j=function(t,e){var i=null==t?void 0:t[e];return C(i)?i:void 0};var R=j(u,"Map"),I=j(Object,"create"),M=Object.prototype.hasOwnProperty,B=Object.prototype.hasOwnProperty;function D(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}D.prototype.clear=function(){this.__data__=I?I(null):{},this.size=0},D.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=+!!e,e},D.prototype.get=function(t){var e=this.__data__;if(I){var i=e[t];return"__lodash_hash_undefined__"===i?void 0:i}return M.call(e,t)?e[t]:void 0},D.prototype.has=function(t){var e=this.__data__;return I?void 0!==e[t]:B.call(e,t)},D.prototype.set=function(t,e){var i=this.__data__;return this.size+=+!this.has(t),i[t]=I&&void 0===e?"__lodash_hash_undefined__":e,this};let U=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t},P=function(t,e){var i=t.__data__;return U(e)?i["string"==typeof e?"string":"hash"]:i.map};function z(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function F(t){var e=this.__data__=new a(t);this.size=e.size}function $(t){var e=-1,i=null==t?0:t.length;for(this.__data__=new z;++e<i;)this.add(t[e])}z.prototype.clear=function(){this.size=0,this.__data__={hash:new D,map:new(R||a),string:new D}},z.prototype.delete=function(t){var e=P(this,t).delete(t);return this.size-=+!!e,e},z.prototype.get=function(t){return P(this,t).get(t)},z.prototype.has=function(t){return P(this,t).has(t)},z.prototype.set=function(t,e){var i=P(this,t),r=i.size;return i.set(t,e),this.size+=+(i.size!=r),this},F.prototype.clear=function(){this.__data__=new a,this.size=0},F.prototype.delete=function(t){var e=this.__data__,i=e.delete(t);return this.size=e.size,i},F.prototype.get=function(t){return this.__data__.get(t)},F.prototype.has=function(t){return this.__data__.has(t)},F.prototype.set=function(t,e){var i=this.__data__;if(i instanceof a){var r=i.__data__;if(!R||r.length<199)return r.push([t,e]),this.size=++i.size,this;i=this.__data__=new z(r)}return i.set(t,e),this.size=i.size,this},$.prototype.add=$.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},$.prototype.has=function(t){return this.__data__.has(t)};let H=function(t,e){for(var i=-1,r=null==t?0:t.length;++i<r;)if(e(t[i],i,t))return!0;return!1},V=function(t,e,i,r,n,s){var l=1&i,o=t.length,a=e.length;if(o!=a&&!(l&&a>o))return!1;var c=s.get(t),h=s.get(e);if(c&&h)return c==e&&h==t;var u=-1,d=!0,f=2&i?new $:void 0;for(s.set(t,e),s.set(e,t);++u<o;){var p=t[u],g=e[u];if(r)var m=l?r(g,p,u,e,t,s):r(p,g,u,t,e,s);if(void 0!==m){if(m)continue;d=!1;break}if(f){if(!H(e,function(t,e){if(!f.has(e)&&(p===t||n(p,t,i,r,s)))return f.push(e)})){d=!1;break}}else if(!(p===g||n(p,g,i,r,s))){d=!1;break}}return s.delete(t),s.delete(e),d};var K=u.Uint8Array;let W=function(t){var e=-1,i=Array(t.size);return t.forEach(function(t,r){i[++e]=[r,t]}),i},Z=function(t){var e=-1,i=Array(t.size);return t.forEach(function(t){i[++e]=t}),i};var G=d?d.prototype:void 0,X=G?G.valueOf:void 0;let Q=function(t,e,i,r,n,l,o){switch(i){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!l(new K(t),new K(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return s(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var a=W;case"[object Set]":var c=1&r;if(a||(a=Z),t.size!=e.size&&!c)break;var h=o.get(t);if(h)return h==e;r|=2,o.set(t,e);var u=V(a(t),a(e),r,n,l,o);return o.delete(t),u;case"[object Symbol]":if(X)return X.call(t)==X.call(e)}return!1},Y=function(t,e){for(var i=-1,r=e.length,n=t.length;++i<r;)t[n+i]=e[i];return t};var J=Array.isArray;let tt=function(t,e,i){var r=e(t);return J(t)?r:Y(r,i(t))},te=function(t,e){for(var i=-1,r=null==t?0:t.length,n=0,s=[];++i<r;){var l=t[i];e(l,i,t)&&(s[n++]=l)}return s},ti=function(){return[]};var tr=Object.prototype.propertyIsEnumerable,tn=Object.getOwnPropertySymbols,ts=tn?function(t){return null==t?[]:te(tn(t=Object(t)),function(e){return tr.call(t,e)})}:ti;let tl=function(t,e){for(var i=-1,r=Array(t);++i<t;)r[i]=e(i);return r},to=function(t){return null!=t&&"object"==typeof t},ta=function(t){return to(t)&&"[object Arguments]"==x(t)};var tc=Object.prototype,th=tc.hasOwnProperty,tu=tc.propertyIsEnumerable,td=ta(function(){return arguments}())?ta:function(t){return to(t)&&th.call(t,"callee")&&!tu.call(t,"callee")},tf="object"==typeof exports&&exports&&!exports.nodeType&&exports,tp=tf&&"object"==typeof module&&module&&!module.nodeType&&module,tg=tp&&tp.exports===tf?u.Buffer:void 0;let tm=(tg?tg.isBuffer:void 0)||function(){return!1};var tb=/^(?:0|[1-9]\d*)$/;let ty=function(t,e){var i=typeof t;return!!(e=null==e?0x1fffffffffffff:e)&&("number"==i||"symbol"!=i&&tb.test(t))&&t>-1&&t%1==0&&t<e},tv=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff};var tx={};tx["[object Float32Array]"]=tx["[object Float64Array]"]=tx["[object Int8Array]"]=tx["[object Int16Array]"]=tx["[object Int32Array]"]=tx["[object Uint8Array]"]=tx["[object Uint8ClampedArray]"]=tx["[object Uint16Array]"]=tx["[object Uint32Array]"]=!0,tx["[object Arguments]"]=tx["[object Array]"]=tx["[object ArrayBuffer]"]=tx["[object Boolean]"]=tx["[object DataView]"]=tx["[object Date]"]=tx["[object Error]"]=tx["[object Function]"]=tx["[object Map]"]=tx["[object Number]"]=tx["[object Object]"]=tx["[object RegExp]"]=tx["[object Set]"]=tx["[object String]"]=tx["[object WeakMap]"]=!1;let tN=function(t){return function(e){return t(e)}};var tE="object"==typeof exports&&exports&&!exports.nodeType&&exports,tA=tE&&"object"==typeof module&&module&&!module.nodeType&&module,tw=tA&&tA.exports===tE&&c.process,tq=function(){try{var t=tA&&tA.require&&tA.require("util").types;if(t)return t;return tw&&tw.binding&&tw.binding("util")}catch(t){}}(),tk=tq&&tq.isTypedArray,t_=tk?tN(tk):function(t){return to(t)&&tv(t.length)&&!!tx[x(t)]},tL=Object.prototype.hasOwnProperty;let tS=function(t,e){var i=J(t),r=!i&&td(t),n=!i&&!r&&tm(t),s=!i&&!r&&!n&&t_(t),l=i||r||n||s,o=l?tl(t.length,String):[],a=o.length;for(var c in t)(e||tL.call(t,c))&&!(l&&("length"==c||n&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||ty(c,a)))&&o.push(c);return o};var tO=Object.prototype;let tT=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||tO)},tC=function(t,e){return function(i){return t(e(i))}};var tj=tC(Object.keys,Object),tR=Object.prototype.hasOwnProperty;let tI=function(t){if(!tT(t))return tj(t);var e=[];for(var i in Object(t))tR.call(t,i)&&"constructor"!=i&&e.push(i);return e},tM=function(t){return null!=t&&tv(t.length)&&!E(t)},tB=function(t){return tM(t)?tS(t):tI(t)},tD=function(t){return tt(t,tB,ts)};var tU=Object.prototype.hasOwnProperty;let tP=function(t,e,i,r,n,s){var l=1&i,o=tD(t),a=o.length;if(a!=tD(e).length&&!l)return!1;for(var c=a;c--;){var h=o[c];if(!(l?h in e:tU.call(e,h)))return!1}var u=s.get(t),d=s.get(e);if(u&&d)return u==e&&d==t;var f=!0;s.set(t,e),s.set(e,t);for(var p=l;++c<a;){var g=t[h=o[c]],m=e[h];if(r)var b=l?r(m,g,h,e,t,s):r(g,m,h,t,e,s);if(!(void 0===b?g===m||n(g,m,i,r,s):b)){f=!1;break}p||(p="constructor"==h)}if(f&&!p){var y=t.constructor,v=e.constructor;y!=v&&"constructor"in t&&"constructor"in e&&!("function"==typeof y&&y instanceof y&&"function"==typeof v&&v instanceof v)&&(f=!1)}return s.delete(t),s.delete(e),f};var tz=j(u,"DataView"),tF=j(u,"Promise"),t$=j(u,"Set"),tH=j(u,"WeakMap"),tV="[object Map]",tK="[object Promise]",tW="[object Set]",tZ="[object WeakMap]",tG="[object DataView]",tX=k(tz),tQ=k(R),tY=k(tF),tJ=k(t$),t1=k(tH),t0=x;(tz&&t0(new tz(new ArrayBuffer(1)))!=tG||R&&t0(new R)!=tV||tF&&t0(tF.resolve())!=tK||t$&&t0(new t$)!=tW||tH&&t0(new tH)!=tZ)&&(t0=function(t){var e=x(t),i="[object Object]"==e?t.constructor:void 0,r=i?k(i):"";if(r)switch(r){case tX:return tG;case tQ:return tV;case tY:return tK;case tJ:return tW;case t1:return tZ}return e});let t2=t0;var t5="[object Arguments]",t4="[object Array]",t3="[object Object]",t6=Object.prototype.hasOwnProperty;let t8=function(t,e,i,r,n,s){var l=J(t),o=J(e),a=l?t4:t2(t),c=o?t4:t2(e);a=a==t5?t3:a,c=c==t5?t3:c;var h=a==t3,u=c==t3,d=a==c;if(d&&tm(t)){if(!tm(e))return!1;l=!0,h=!1}if(d&&!h)return s||(s=new F),l||t_(t)?V(t,e,i,r,n,s):Q(t,e,a,i,r,n,s);if(!(1&i)){var f=h&&t6.call(t,"__wrapped__"),p=u&&t6.call(e,"__wrapped__");if(f||p){var g=f?t.value():t,m=p?e.value():e;return s||(s=new F),n(g,m,i,r,s)}}return!!d&&(s||(s=new F),tP(t,e,i,r,n,s))},t9=function(t,e){return function t(e,i,r,n,s){return e===i||(null!=e&&null!=i&&(to(e)||to(i))?t8(e,i,r,n,t,s):e!=e&&i!=i)}(t,e)};var t7=function(){try{var t=j(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();let et=function(t,e,i){"__proto__"==e&&t7?t7(t,e,{configurable:!0,enumerable:!0,value:i,writable:!0}):t[e]=i},ee=function(t,e,i){(void 0===i||s(t[e],i))&&(void 0!==i||e in t)||et(t,e,i)},ei=function(t,e,i){for(var r=-1,n=Object(t),s=i(t),l=s.length;l--;){var o=s[++r];if(!1===e(n[o],o,n))break}return t};var er="object"==typeof exports&&exports&&!exports.nodeType&&exports,en=er&&"object"==typeof module&&module&&!module.nodeType&&module,es=en&&en.exports===er?u.Buffer:void 0,el=es?es.allocUnsafe:void 0;let eo=function(t,e){if(e)return t.slice();var i=t.length,r=el?el(i):new t.constructor(i);return t.copy(r),r},ea=function(t){var e=new t.constructor(t.byteLength);return new K(e).set(new K(t)),e},ec=function(t,e){var i=e?ea(t.buffer):t.buffer;return new t.constructor(i,t.byteOffset,t.length)},eh=function(t,e){var i=-1,r=t.length;for(e||(e=Array(r));++i<r;)e[i]=t[i];return e};var eu=Object.create,ed=function(){function t(){}return function(e){if(!N(e))return{};if(eu)return eu(e);t.prototype=e;var i=new t;return t.prototype=void 0,i}}(),ef=tC(Object.getPrototypeOf,Object);let ep=function(t){return"function"!=typeof t.constructor||tT(t)?{}:ed(ef(t))};var eg=Object.prototype,em=Function.prototype.toString,eb=eg.hasOwnProperty,ey=em.call(Object);let ev=function(t){if(!to(t)||"[object Object]"!=x(t))return!1;var e=ef(t);if(null===e)return!0;var i=eb.call(e,"constructor")&&e.constructor;return"function"==typeof i&&i instanceof i&&em.call(i)==ey},ex=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]};var eN=Object.prototype.hasOwnProperty;let eE=function(t,e,i){var r=t[e];eN.call(t,e)&&s(r,i)&&(void 0!==i||e in t)||et(t,e,i)},eA=function(t,e,i,r){var n=!i;i||(i={});for(var s=-1,l=e.length;++s<l;){var o=e[s],a=r?r(i[o],t[o],o,i,t):void 0;void 0===a&&(a=t[o]),n?et(i,o,a):eE(i,o,a)}return i},ew=function(t){var e=[];if(null!=t)for(var i in Object(t))e.push(i);return e};var eq=Object.prototype.hasOwnProperty;let ek=function(t){if(!N(t))return ew(t);var e=tT(t),i=[];for(var r in t)"constructor"==r&&(e||!eq.call(t,r))||i.push(r);return i},e_=function(t){return tM(t)?tS(t,!0):ek(t)},eL=function(t,e,i,r,n,s,l){var o=ex(t,i),a=ex(e,i),c=l.get(a);if(c){ee(t,i,c);return}var h=s?s(o,a,i+"",t,e,l):void 0,u=void 0===h;if(u){var d=J(a),f=!d&&tm(a),p=!d&&!f&&t_(a);(h=a,d||f||p)?J(o)?h=o:to(o)&&tM(o)?h=eh(o):f?(u=!1,h=eo(a,!0)):p?(u=!1,h=ec(a,!0)):h=[]:ev(a)||td(a)?(h=o,td(o))?h=eA(o,e_(o)):(!N(o)||E(o))&&(h=ep(a)):u=!1}u&&(l.set(a,h),n(h,a,r,s,l),l.delete(a)),ee(t,i,h)},eS=function t(e,i,r,n,s){e!==i&&ei(i,function(l,o){if(s||(s=new F),N(l))eL(e,i,o,r,t,n,s);else{var a=n?n(ex(e,o),l,o+"",e,i,s):void 0;void 0===a&&(a=l),ee(e,o,a)}},e_)},eO=function(t){return t},eT=function(t,e,i){switch(i.length){case 0:return t.call(e);case 1:return t.call(e,i[0]);case 2:return t.call(e,i[0],i[1]);case 3:return t.call(e,i[0],i[1],i[2])}return t.apply(e,i)};var eC=Math.max,ej=Date.now,eR=function(t){var e=0,i=0;return function(){var r=ej(),n=16-(r-i);if(i=r,n>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(t7?function(t,e){return t7(t,"toString",{configurable:!0,enumerable:!1,value:function(){return e},writable:!0})}:eO);let eI=function(t,e){var i;return eR((i=eC(void 0===(i=e)?t.length-1:i,0),function(){for(var e=arguments,r=-1,n=eC(e.length-i,0),s=Array(n);++r<n;)s[r]=e[i+r];r=-1;for(var l=Array(i+1);++r<i;)l[r]=e[r];return l[i]=eO(s),eT(t,this,l)}),t+"")},eM=function(t,e,i){if(!N(i))return!1;var r=typeof e;return("number"==r?!!(tM(i)&&ty(e,i.length)):"string"==r&&e in i)&&s(i[e],t)};var eB=function(t){return eI(function(e,i){var r=-1,n=i.length,s=n>1?i[n-1]:void 0,l=n>2?i[2]:void 0;for(s=t.length>3&&"function"==typeof s?(n--,s):void 0,l&&eM(i[0],i[1],l)&&(s=n<3?void 0:s,n=1),e=Object(e);++r<n;){var o=i[r];o&&t(e,o,r,s)}return e})}(function(t,e,i){eS(t,e,i)}),eD=(t=>(t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY",t))(eD||{});class eU{constructor(t,e,i={}){this.attrName=t,this.keyName=e;let r=eD.TYPE&eD.ATTRIBUTE;this.scope=null!=i.scope?i.scope&eD.LEVEL|r:eD.ATTRIBUTE,null!=i.whitelist&&(this.whitelist=i.whitelist)}static keys(t){return Array.from(t.attributes).map(t=>t.name)}add(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)}canAdd(t,e){return null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1)}remove(t){t.removeAttribute(this.keyName)}value(t){let e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class eP extends Error{constructor(t){super(t="[Parchment] "+t),this.message=t,this.name=this.constructor.name}}let ez=class t{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let i=null;try{i=t.parentNode}catch{return null}return this.find(i,e)}return null}create(e,i,r){let n=this.query(i);if(null==n)throw new eP(`Unable to create ${i} blot`);let s=i instanceof Node||i.nodeType===Node.TEXT_NODE?i:n.create(r),l=new n(e,s,r);return t.blots.set(l.domNode,l),l}find(e,i=!1){return t.find(e,i)}query(t,e=eD.ANY){let i;return"string"==typeof t?i=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?i=this.types.text:"number"==typeof t?t&eD.LEVEL&eD.BLOCK?i=this.types.block:t&eD.LEVEL&eD.INLINE&&(i=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(t=>!!(i=this.classes[t])),i=i||this.tags[t.tagName]),null==i?null:"scope"in i&&e&eD.LEVEL&i.scope&&e&eD.TYPE&i.scope?i:null}register(...t){return t.map(t=>{let e="blotName"in t,i="attrName"in t;if(!e&&!i)throw new eP("Invalid definition");if(e&&"abstract"===t.blotName)throw new eP("Cannot register abstract class");let r=e?t.blotName:i?t.attrName:void 0;return this.types[r]=t,i?"string"==typeof t.keyName&&(this.attributes[t.keyName]=t):e&&(t.className&&(this.classes[t.className]=t),t.tagName&&(Array.isArray(t.tagName)?t.tagName=t.tagName.map(t=>t.toUpperCase()):t.tagName=t.tagName.toUpperCase(),(Array.isArray(t.tagName)?t.tagName:[t.tagName]).forEach(e=>{(null==this.tags[e]||null==t.className)&&(this.tags[e]=t)}))),t})}};ez.blots=new WeakMap;let eF=ez;function e$(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter(t=>0===t.indexOf(`${e}-`))}class eH extends eU{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(t=>t.split("-").slice(0,-1).join("-"))}add(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0)}remove(t){e$(t,this.keyName).forEach(e=>{t.classList.remove(e)}),0===t.classList.length&&t.removeAttribute("class")}value(t){let e=(e$(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}let eV=eH;function eK(t){let e=t.split("-"),i=e.slice(1).map(t=>t[0].toUpperCase()+t.slice(1)).join("");return e[0]+i}class eW extends eU{static keys(t){return(t.getAttribute("style")||"").split(";").map(t=>t.split(":")[0].trim())}add(t,e){return!!this.canAdd(t,e)&&(t.style[eK(this.keyName)]=e,!0)}remove(t){t.style[eK(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){let e=t.style[eK(this.keyName)];return this.canAdd(t,e)?e:""}}let eZ=eW;class eG{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};let t=eF.find(this.domNode);if(null==t)return;let e=eU.keys(this.domNode),i=eV.keys(this.domNode),r=eZ.keys(this.domNode);e.concat(i).concat(r).forEach(e=>{let i=t.scroll.query(e,eD.ATTRIBUTE);i instanceof eU&&(this.attributes[i.attrName]=i)})}copy(t){Object.keys(this.attributes).forEach(e=>{let i=this.attributes[e].value(this.domNode);t.format(e,i)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(t=>{this.attributes[t].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}let eX=eG,eQ=class{constructor(t,e){this.scroll=t,this.domNode=e,eF.blots.set(e,this),this.prev=null,this.next=null}static create(t){let e,i;if(null==this.tagName)throw new eP("Blot definition missing tagName");return Array.isArray(this.tagName)?("string"==typeof t?parseInt(i=t.toUpperCase(),10).toString()===i&&(i=parseInt(i,10)):"number"==typeof t&&(i=t),e="number"==typeof i?document.createElement(this.tagName[i-1]):i&&this.tagName.indexOf(i)>-1?document.createElement(i):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){let t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){null!=this.parent&&this.parent.removeChild(this),eF.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,i,r){let n=this.isolate(t,e);if(null!=this.scroll.query(i,eD.BLOT)&&r)n.wrap(i,r);else if(null!=this.scroll.query(i,eD.ATTRIBUTE)){let t=this.scroll.create(this.statics.scope);n.wrap(t),t.format(i,r)}}insertAt(t,e,i){let r=null==i?this.scroll.create("text",e):this.scroll.create(e,i),n=this.split(t);this.parent.insertBefore(r,n||void 0)}isolate(t,e){let i=this.split(t);if(null==i)throw Error("Attempt to isolate at end");return i.split(e),i}length(){return 1}offset(t=this.parent){return null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){!this.statics.requiredContainer||this.parent instanceof this.statics.requiredContainer||this.wrap(this.statics.requiredContainer.blotName)}remove(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){let i="string"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(i,this.next||void 0),this.remove()),i}split(t,e){return 0===t?this:this.next}update(t,e){}wrap(t,e){let i="string"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(i,this.next||void 0),"function"!=typeof i.appendChild)throw new eP(`Cannot wrap ${t}`);return i.appendChild(this),i}};eQ.blotName="abstract";let eY=eQ,eJ=class extends eY{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let i=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(i+=1),[this.parent.domNode,i]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};eJ.scope=eD.INLINE_BLOT;let e1=eJ;class e0{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){let e=t.slice(1);this.append(...e)}}at(t){let e=this.iterator(),i=e();for(;i&&t>0;)t-=1,i=e();return i}contains(t){let e=this.iterator(),i=e();for(;i;){if(i===t)return!0;i=e()}return!1}indexOf(t){let e=this.iterator(),i=e(),r=0;for(;i;){if(i===t)return r;r+=1,i=e()}return -1}insertBefore(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,i=this.head;for(;null!=i;){if(i===t)return e;e+=i.length(),i=i.next}return -1}remove(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{let e=t;return null!=t&&(t=t.next),e}}find(t,e=!1){let i=this.iterator(),r=i();for(;r;){let n=r.length();if(t<n||e&&t===n&&(null==r.next||0!==r.next.length()))return[r,t];t-=n,r=i()}return[null,0]}forEach(t){let e=this.iterator(),i=e();for(;i;)t(i),i=e()}forEachAt(t,e,i){if(e<=0)return;let[r,n]=this.find(t),s=t-n,l=this.iterator(r),o=l();for(;o&&s<t+e;){let r=o.length();t>s?i(o,t-s,Math.min(e,s+r-t)):i(o,0,Math.min(r,t+e-s)),s+=r,o=l()}}map(t){return this.reduce((e,i)=>(e.push(t(i)),e),[])}reduce(t,e){let i=this.iterator(),r=i();for(;r;)e=t(e,r),r=i();return e}}function e2(t,e){let i=e.find(t);if(i)return i;try{return e.create(t)}catch{let i=e.create(eD.INLINE);return Array.from(t.childNodes).forEach(t=>{i.domNode.appendChild(t)}),t.parentNode&&t.parentNode.replaceChild(i.domNode,t),i.attach(),i}}let e5=class t extends eY{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(e){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=e,t.uiClass&&this.uiNode.classList.add(t.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new e0,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{let e=e2(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(t){if(t instanceof eP)return;throw t}})}deleteAt(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,(t,e,i)=>{t.deleteAt(e,i)})}descendant(e,i=0){let[r,n]=this.children.find(i);return null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e?[r,n]:r instanceof t?r.descendant(e,n):[null,-1]}descendants(e,i=0,r=Number.MAX_VALUE){let n=[],s=r;return this.children.forEachAt(i,r,(i,r,l)=>{(null==e.blotName&&e(i)||null!=e.blotName&&i instanceof e)&&n.push(i),i instanceof t&&(n=n.concat(i.descendants(e,r,s))),s-=l}),n}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let e=!1;this.children.forEach(i=>{e||this.statics.allowedChildren.some(t=>i instanceof t)||(i.statics.scope===eD.BLOCK_BLOT?(null!=i.next&&this.splitAfter(i),null!=i.prev&&this.splitAfter(i.prev),i.parent.unwrap(),e=!0):i instanceof t?i.unwrap():i.remove())})}formatAt(t,e,i,r){this.children.forEachAt(t,e,(t,e,n)=>{t.formatAt(e,n,i,r)})}insertAt(t,e,i){let[r,n]=this.children.find(t);if(r)r.insertAt(n,e,i);else{let t=null==i?this.scroll.create("text",e):this.scroll.create(e,i);this.appendChild(t)}}insertBefore(t,e){null!=t.parent&&t.parent.children.remove(t);let i=null;this.children.insertBefore(t,e||null),t.parent=this,null!=e&&(i=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==i)&&this.domNode.insertBefore(t.domNode,i),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(i=>{t.insertBefore(i,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length){if(null!=this.statics.defaultChild){let t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t)}else this.remove()}}path(e,i=!1){let[r,n]=this.children.find(e,i),s=[[this,e]];return r instanceof t?s.concat(r.path(n,i)):(null!=r&&s.push([r,n]),s)}removeChild(t){this.children.remove(t)}replaceWith(e,i){let r="string"==typeof e?this.scroll.create(e,i):e;return r instanceof t&&this.moveChildren(r),super.replaceWith(r)}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}let i=this.clone();return this.parent&&this.parent.insertBefore(i,this.next||void 0),this.children.forEachAt(t,this.length(),(t,r,n)=>{let s=t.split(r,e);null!=s&&i.appendChild(s)}),i}splitAfter(t){let e=this.clone();for(;null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){let i=[],r=[];t.forEach(t=>{t.target===this.domNode&&"childList"===t.type&&(i.push(...t.addedNodes),r.push(...t.removedNodes))}),r.forEach(t=>{if(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;let e=this.scroll.find(t);null!=e&&(null==e.domNode.parentNode||e.domNode.parentNode===this.domNode)&&e.detach()}),i.filter(t=>t.parentNode===this.domNode&&t!==this.uiNode).sort((t,e)=>t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(t=>{let e=null;null!=t.nextSibling&&(e=this.scroll.find(t.nextSibling));let i=e2(t,this.scroll);(i.next!==e||null==i.next)&&(null!=i.parent&&i.parent.removeChild(this),this.insertBefore(i,e||void 0))}),this.enforceAllowedChildren()}};e5.uiClass="";let e4=e5,e3=class t extends e4{static create(t){return super.create(t)}static formats(e,i){let r=i.query(t.blotName);if(null==r||e.tagName!==r.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new eX(this.domNode)}format(e,i){if(e!==this.statics.blotName||i){let t=this.scroll.query(e,eD.INLINE);null!=t&&(t instanceof eU?this.attributes.attribute(t,i):i&&(e!==this.statics.blotName||this.formats()[e]!==i)&&this.replaceWith(e,i))}else this.children.forEach(e=>{e instanceof t||(e=e.wrap(t.blotName,!0)),this.attributes.copy(e)}),this.unwrap()}formats(){let t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,i,r){null!=this.formats()[i]||this.scroll.query(i,eD.ATTRIBUTE)?this.isolate(t,e).format(i,r):super.formatAt(t,e,i,r)}optimize(e){super.optimize(e);let i=this.formats();if(0===Object.keys(i).length)return this.unwrap();let r=this.next;r instanceof t&&r.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(let i in t)if(t[i]!==e[i])return!1;return!0}(i,r.formats())&&(r.moveChildren(this),r.remove())}replaceWith(t,e){let i=super.replaceWith(t,e);return this.attributes.copy(i),i}update(t,e){super.update(t,e),t.some(t=>t.target===this.domNode&&"attributes"===t.type)&&this.attributes.build()}wrap(e,i){let r=super.wrap(e,i);return r instanceof t&&this.attributes.move(r),r}};e3.allowedChildren=[e3,e1],e3.blotName="inline",e3.scope=eD.INLINE_BLOT,e3.tagName="SPAN";let e6=e3,e8=class t extends e4{static create(t){return super.create(t)}static formats(e,i){let r=i.query(t.blotName);if(null==r||e.tagName!==r.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new eX(this.domNode)}format(e,i){let r=this.scroll.query(e,eD.BLOCK);null!=r&&(r instanceof eU?this.attributes.attribute(r,i):e!==this.statics.blotName||i?i&&(e!==this.statics.blotName||this.formats()[e]!==i)&&this.replaceWith(e,i):this.replaceWith(t.blotName))}formats(){let t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,i,r){null!=this.scroll.query(i,eD.BLOCK)?this.format(i,r):super.formatAt(t,e,i,r)}insertAt(t,e,i){if(null==i||null!=this.scroll.query(e,eD.INLINE))super.insertAt(t,e,i);else{let r=this.split(t);if(null!=r){let t=this.scroll.create(e,i);r.parent.insertBefore(t,r)}else throw Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){let i=super.replaceWith(t,e);return this.attributes.copy(i),i}update(t,e){super.update(t,e),t.some(t=>t.target===this.domNode&&"attributes"===t.type)&&this.attributes.build()}};e8.blotName="block",e8.scope=eD.BLOCK_BLOT,e8.tagName="P",e8.allowedChildren=[e6,e8,e1];let e9=e8,e7=class extends e4{checkMerge(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,i,r){super.formatAt(t,e,i,r),this.enforceAllowedChildren()}insertAt(t,e,i){super.insertAt(t,e,i),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};e7.blotName="container",e7.scope=eD.BLOCK_BLOT;let it=e7;class ie extends e1{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,i,r){0===t&&e===this.length()?this.format(i,r):super.formatAt(t,e,i,r)}formats(){return this.statics.formats(this.domNode,this.scroll)}}let ii=ie,ir={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},is=class extends e4{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(t=>{this.update(t)}),this.observer.observe(this.domNode,ir),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){let i=this.registry.find(t,e);return i?i.scroll===this?i:e?this.find(i.scroll.domNode.parentNode,!0):null:null}query(t,e=eD.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){null!=this.scroll&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),0===t&&e===this.length()?this.children.forEach(t=>{t.remove()}):super.deleteAt(t,e)}formatAt(t,e,i,r){this.update(),super.formatAt(t,e,i,r)}insertAt(t,e,i){this.update(),super.insertAt(t,e,i)}optimize(t=[],e={}){super.optimize(e);let i=e.mutationsMap||new WeakMap,r=Array.from(this.observer.takeRecords());for(;r.length>0;)t.push(r.pop());let n=(t,e=!0)=>{null==t||t===this||null!=t.domNode.parentNode&&(i.has(t.domNode)||i.set(t.domNode,[]),e&&n(t.parent))},s=t=>{i.has(t.domNode)&&(t instanceof e4&&t.children.forEach(s),i.delete(t.domNode),t.optimize(e))},l=t;for(let e=0;l.length>0;e+=1){if(e>=100)throw Error("[Parchment] Maximum optimize iterations reached");for(l.forEach(t=>{let e=this.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(n(this.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach(t=>{let e=this.find(t,!1);n(e,!1),e instanceof e4&&e.children.forEach(t=>{n(t,!1)})})):"attributes"===t.type&&n(e.prev)),n(e))}),this.children.forEach(s),r=(l=Array.from(this.observer.takeRecords())).slice();r.length>0;)t.push(r.pop())}}update(t,e={}){t=t||this.observer.takeRecords();let i=new WeakMap;t.map(t=>{let e=this.find(t.target,!0);return null==e?null:i.has(e.domNode)?(i.get(e.domNode).push(t),null):(i.set(e.domNode,[t]),e)}).forEach(t=>{null!=t&&t!==this&&i.has(t.domNode)&&t.update(i.get(t.domNode)||[],e)}),e.mutationsMap=i,i.has(this.domNode)&&super.update(i.get(this.domNode),e),this.optimize(t,e)}};is.blotName="scroll",is.defaultChild=e9,is.allowedChildren=[e9,it],is.scope=eD.BLOCK_BLOT,is.tagName="DIV";let il=is,io=class t extends e1{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,i){null==i?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,i)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof t&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}let i=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(i,this.next||void 0),this.text=this.statics.value(this.domNode),i}update(t,e){t.some(t=>"characterData"===t.type&&t.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};io.blotName="text",io.scope=eD.INLINE_BLOT;let ia=io;var ic=i(27501);let ih=function(t,e){for(var i=-1,r=null==t?0:t.length;++i<r&&!1!==e(t[i],i,t););return t};var iu=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)Y(e,ts(t)),t=ef(t);return e}:ti;let id=function(t){return tt(t,e_,iu)};var ip=Object.prototype.hasOwnProperty;let ig=function(t){var e=t.length,i=new t.constructor(e);return e&&"string"==typeof t[0]&&ip.call(t,"index")&&(i.index=t.index,i.input=t.input),i},im=function(t,e){var i=e?ea(t.buffer):t.buffer;return new t.constructor(i,t.byteOffset,t.byteLength)};var ib=/\w*$/;let iy=function(t){var e=new t.constructor(t.source,ib.exec(t));return e.lastIndex=t.lastIndex,e};var iv=d?d.prototype:void 0,ix=iv?iv.valueOf:void 0;let iN=function(t,e,i){var r=t.constructor;switch(e){case"[object ArrayBuffer]":return ea(t);case"[object Boolean]":case"[object Date]":return new r(+t);case"[object DataView]":return im(t,i);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return ec(t,i);case"[object Map]":case"[object Set]":return new r;case"[object Number]":case"[object String]":return new r(t);case"[object RegExp]":return iy(t);case"[object Symbol]":return ix?Object(ix.call(t)):{}}};var iE=tq&&tq.isMap,iA=iE?tN(iE):function(t){return to(t)&&"[object Map]"==t2(t)},iw=tq&&tq.isSet,iq=iw?tN(iw):function(t){return to(t)&&"[object Set]"==t2(t)},ik="[object Arguments]",i_="[object Function]",iL="[object Object]",iS={};iS[ik]=iS["[object Array]"]=iS["[object ArrayBuffer]"]=iS["[object DataView]"]=iS["[object Boolean]"]=iS["[object Date]"]=iS["[object Float32Array]"]=iS["[object Float64Array]"]=iS["[object Int8Array]"]=iS["[object Int16Array]"]=iS["[object Int32Array]"]=iS["[object Map]"]=iS["[object Number]"]=iS[iL]=iS["[object RegExp]"]=iS["[object Set]"]=iS["[object String]"]=iS["[object Symbol]"]=iS["[object Uint8Array]"]=iS["[object Uint8ClampedArray]"]=iS["[object Uint16Array]"]=iS["[object Uint32Array]"]=!0,iS["[object Error]"]=iS[i_]=iS["[object WeakMap]"]=!1;let iO=function t(e,i,r,n,s,l){var o,a=1&i,c=2&i,h=4&i;if(r&&(o=s?r(e,n,s,l):r(e)),void 0!==o)return o;if(!N(e))return e;var u=J(e);if(u){if(o=ig(e),!a)return eh(e,o)}else{var d,f,p,g,m=t2(e),b=m==i_||"[object GeneratorFunction]"==m;if(tm(e))return eo(e,a);if(m==iL||m==ik||b&&!s){if(o=c||b?{}:ep(e),!a)return c?(f=(d=o)&&eA(e,e_(e),d),eA(e,iu(e),f)):(g=(p=o)&&eA(e,tB(e),p),eA(e,ts(e),g))}else{if(!iS[m])return s?e:{};o=iN(e,m,a)}}l||(l=new F);var y=l.get(e);if(y)return y;l.set(e,o),iq(e)?e.forEach(function(n){o.add(t(n,i,r,n,e,l))}):iA(e)&&e.forEach(function(n,s){o.set(s,t(n,i,r,s,e,l))});var v=h?c?id:tD:c?e_:tB,x=u?void 0:v(e);return ih(x||e,function(n,s){x&&(n=e[s=n]),eE(o,s,t(n,i,r,s,e,l))}),o},iT=function(t){return iO(t,5)};class iC extends ii{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}iC.blotName="break",iC.tagName="BR";class ij extends ia{}let iR={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function iI(t){return t.replace(/[&<>"']/g,t=>iR[t])}class iM extends e6{static allowedChildren=[iM,iC,ii,ij];static order=["cursor","inline","link","underline","strike","italic","bold","script","code"];static compare(t,e){let i=iM.order.indexOf(t),r=iM.order.indexOf(e);return i>=0||r>=0?i-r:t===e?0:t<e?-1:1}formatAt(t,e,i,r){if(0>iM.compare(this.statics.blotName,i)&&this.scroll.query(i,eD.BLOT)){let n=this.isolate(t,e);r&&n.wrap(i,r)}else super.formatAt(t,e,i,r)}optimize(t){if(super.optimize(t),this.parent instanceof iM&&iM.compare(this.statics.blotName,this.parent.statics.blotName)>0){let t=this.parent.isolate(this.offset(),this.length());this.moveChildren(t),t.wrap(this)}}}let iB=iM;class iD extends e9{cache={};delta(){return null==this.cache.delta&&(this.cache.delta=iP(this)),this.cache.delta}deleteAt(t,e){super.deleteAt(t,e),this.cache={}}formatAt(t,e,i,r){e<=0||(this.scroll.query(i,eD.BLOCK)?t+e===this.length()&&this.format(i,r):super.formatAt(t,Math.min(e,this.length()-t-1),i,r),this.cache={})}insertAt(t,e,i){if(null!=i){super.insertAt(t,e,i),this.cache={};return}if(0===e.length)return;let r=e.split("\n"),n=r.shift();n.length>0&&(t<this.length()-1||null==this.children.tail?super.insertAt(Math.min(t,this.length()-1),n):this.children.tail.insertAt(this.children.tail.length(),n),this.cache={});let s=this;r.reduce((t,e)=>((s=s.split(t,!0)).insertAt(0,e),e.length),t+n.length)}insertBefore(t,e){let{head:i}=this.children;super.insertBefore(t,e),i instanceof iC&&i.remove(),this.cache={}}length(){return null==this.cache.length&&(this.cache.length=super.length()+1),this.cache.length}moveChildren(t,e){super.moveChildren(t,e),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){let e=this.clone();return 0===t?(this.parent.insertBefore(e,this),this):(this.parent.insertBefore(e,this.next),e)}let i=super.split(t,e);return this.cache={},i}}iD.blotName="block",iD.tagName="P",iD.defaultChild=iC,iD.allowedChildren=[iC,iB,ii,ij];class iU extends ii{attach(){super.attach(),this.attributes=new eX(this.domNode)}delta(){return new ic().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){let i=this.scroll.query(t,eD.BLOCK_ATTRIBUTE);null!=i&&this.attributes.attribute(i,e)}formatAt(t,e,i,r){this.format(i,r)}insertAt(t,e,i){if(null!=i){super.insertAt(t,e,i);return}let r=e.split("\n"),n=r.pop(),s=r.map(t=>{let e=this.scroll.create(iD.blotName);return e.insertAt(0,t),e}),l=this.split(t);s.forEach(t=>{this.parent.insertBefore(t,l)}),n&&this.parent.insertBefore(this.scroll.create("text",n),l)}}function iP(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return t.descendants(e1).reduce((t,i)=>0===i.length()?t:t.insert(i.value(),iz(i,{},e)),new ic).insert("\n",iz(t))}function iz(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return null==t?e:("formats"in t&&"function"==typeof t.formats&&(e={...e,...t.formats()},i&&delete e["code-token"]),null==t.parent||"scroll"===t.parent.statics.blotName||t.parent.statics.scope!==t.statics.scope)?e:iz(t.parent,e,i)}iU.scope=eD.BLOCK_BLOT;class iF extends ii{static blotName="cursor";static className="ql-cursor";static tagName="span";static CONTENTS="\uFEFF";static value(){}constructor(t,e,i){super(t,e),this.selection=i,this.textNode=document.createTextNode(iF.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){null!=this.parent&&this.parent.removeChild(this)}format(t,e){if(0!==this.savedLength){super.format(t,e);return}let i=this,r=0;for(;null!=i&&i.statics.scope!==eD.BLOCK_BLOT;)r+=i.offset(i.parent),i=i.parent;null!=i&&(this.savedLength=iF.CONTENTS.length,i.optimize(),i.formatAt(r,iF.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){let t;if(this.selection.composing||null==this.parent)return null;let e=this.selection.getNativeRange();for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);let i=this.prev instanceof ij?this.prev:null,r=i?i.length():0,n=this.next instanceof ij?this.next:null,s=n?n.text:"",{textNode:l}=this,o=l.data.split(iF.CONTENTS).join("");if(l.data=iF.CONTENTS,i)t=i,(o||n)&&(i.insertAt(i.length(),o+s),n&&n.remove());else if(n)t=n,n.insertAt(0,o);else{let e=document.createTextNode(o);t=this.scroll.create(e),this.parent.insertBefore(t,this)}if(this.remove(),e){let s=(t,e)=>i&&t===i.domNode?e:t===l?r+e-1:n&&t===n.domNode?r+o.length+e:null,a=s(e.start.node,e.start.offset),c=s(e.end.node,e.end.offset);if(null!==a&&null!==c)return{startNode:t.domNode,startOffset:a,endNode:t.domNode,endOffset:c}}return null}update(t,e){if(t.some(t=>"characterData"===t.type&&t.target===this.textNode)){let t=this.restore();t&&(e.range=t)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if("A"===e.domNode.tagName){this.savedLength=iF.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}}var i$=i(82661);let iH=new WeakMap,iV=["error","warn","log","info"],iK="warn";function iW(t){if(iK&&iV.indexOf(t)<=iV.indexOf(iK)){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];console[t](...i)}}function iZ(t){return iV.reduce((e,i)=>(e[i]=iW.bind(console,i,t),e),{})}iZ.level=t=>{iK=t},iW.level=iZ.level;let iG=iZ("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(t=>{document.addEventListener(t,function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];Array.from(document.querySelectorAll(".ql-container")).forEach(t=>{let i=iH.get(t);i&&i.emitter&&i.emitter.handleDOM(...e)})})});class iX extends i${static events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"};static sources={API:"api",SILENT:"silent",USER:"user"};constructor(){super(),this.domListeners={},this.on("error",iG.error)}emit(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return iG.log.call(iG,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];(this.domListeners[t.type]||[]).forEach(e=>{let{node:r,handler:n}=e;(t.target===r||r.contains(t.target))&&n(t,...i)})}listenDOM(t,e,i){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:i})}}let iQ=iZ("quill:selection");class iY{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.index=t,this.length=e}}class iJ{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new iY(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{this.mouseDown||this.composing||setTimeout(this.update.bind(this,iX.sources.USER),1)}),this.emitter.on(iX.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;let t=this.getNativeRange();null!=t&&t.start.node!==this.cursor.textNode&&this.emitter.once(iX.events.SCROLL_UPDATE,(e,i)=>{try{this.root.contains(t.start.node)&&this.root.contains(t.end.node)&&this.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset);let r=i.some(t=>"characterData"===t.type||"childList"===t.type||"attributes"===t.type&&t.target===this.root);this.update(r?iX.sources.SILENT:e)}catch(t){}})}),this.emitter.on(iX.events.SCROLL_OPTIMIZE,(t,e)=>{if(e.range){let{startNode:t,startOffset:i,endNode:r,endOffset:n}=e.range;this.setNativeRange(t,i,r,n),this.update(iX.sources.SILENT)}}),this.update(iX.sources.SILENT)}handleComposition(){this.emitter.on(iX.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(iX.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){let t=this.cursor.restore();t&&setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(iX.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();let i=this.getNativeRange();if(!(null==i||!i.native.collapsed||this.scroll.query(t,eD.BLOCK))){if(i.start.node!==this.cursor.textNode){let t=this.scroll.find(i.start.node,!1);if(null==t)return;if(t instanceof e1){let e=t.split(i.start.offset);t.parent.insertBefore(this.cursor,e)}else t.insertBefore(this.cursor,i.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e,i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();r=Math.min((t=Math.min(t,n-1))+r,n-1)-t;let[s,l]=this.scroll.leaf(t);if(null==s)return null;if(r>0&&l===s.length()){let[e]=this.scroll.leaf(t+1);if(e){let[i]=this.scroll.line(t),[r]=this.scroll.line(t+1);i===r&&(s=e,l=0)}}[e,l]=s.position(l,!0);let o=document.createRange();if(r>0)return(o.setStart(e,l),[s,l]=this.scroll.leaf(t+r),null==s)?null:([e,l]=s.position(l,!0),o.setEnd(e,l),o.getBoundingClientRect());let a="left";if(e instanceof Text){if(!e.data.length)return null;l<e.data.length?(o.setStart(e,l),o.setEnd(e,l+1)):(o.setStart(e,l-1),o.setEnd(e,l),a="right"),i=o.getBoundingClientRect()}else{if(!(s.domNode instanceof Element))return null;i=s.domNode.getBoundingClientRect(),l>0&&(a="right")}return{bottom:i.top+i.height,height:i.height,left:i[a],right:i[a],top:i.top,width:0}}getNativeRange(){let t=document.getSelection();if(null==t||t.rangeCount<=0)return null;let e=t.getRangeAt(0);if(null==e)return null;let i=this.normalizeNative(e);return iQ.info("getNativeRange",i),i}getRange(){let t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];let e=this.getNativeRange();return null==e?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||null!=document.activeElement&&i1(this.root,document.activeElement)}normalizedToRange(t){let e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);let i=e.map(t=>{let[e,i]=t,r=this.scroll.find(e,!0),n=r.offset(this.scroll);return 0===i?n:r instanceof e1?n+r.index(e,i):n+r.length()}),r=Math.min(Math.max(...i),this.scroll.length()-1),n=Math.min(r,...i);return new iY(n,r-n)}normalizeNative(t){if(!i1(this.root,t.startContainer)||!t.collapsed&&!i1(this.root,t.endContainer))return null;let e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(t=>{let{node:e,offset:i}=t;for(;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>i)e=e.childNodes[i],i=0;else if(e.childNodes.length===i)i=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length>0?e.childNodes.length:e.childNodes.length+1;else break;t.node=e,t.offset=i}),e}rangeToNative(t){let e=this.scroll.length(),i=(t,i)=>{t=Math.min(e-1,t);let[r,n]=this.scroll.leaf(t);return r?r.position(n,i):[null,-1]};return[...i(t.index,!1),...i(t.index+t.length,!0)]}setNativeRange(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(iQ.info("setNativeRange",t,e,i,r),null!=t&&(null==this.root.parentNode||null==t.parentNode||null==i.parentNode))return;let s=document.getSelection();if(null!=s){if(null!=t){this.hasFocus()||this.root.focus({preventScroll:!0});let{native:l}=this.getNativeRange()||{};if(null==l||n||t!==l.startContainer||e!==l.startOffset||i!==l.endContainer||r!==l.endOffset){t instanceof Element&&"BR"===t.tagName&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),i instanceof Element&&"BR"===i.tagName&&(r=Array.from(i.parentNode.childNodes).indexOf(i),i=i.parentNode);let n=document.createRange();n.setStart(t,e),n.setEnd(i,r),s.removeAllRanges(),s.addRange(n)}}else s.removeAllRanges(),this.root.blur()}}setRange(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:iX.sources.API;if("string"==typeof e&&(i=e,e=!1),iQ.info("setRange",t),null!=t){let i=this.rangeToNative(t);this.setNativeRange(...i,e)}else this.setNativeRange(null);this.update(i)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:iX.sources.USER,e=this.lastRange,[i,r]=this.getRange();if(this.lastRange=i,this.lastNative=r,null!=this.lastRange&&(this.savedRange=this.lastRange),!t9(e,this.lastRange)){if(!this.composing&&null!=r&&r.native.collapsed&&r.start.node!==this.cursor.textNode){let t=this.cursor.restore();t&&this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}let i=[iX.events.SELECTION_CHANGE,iT(this.lastRange),iT(e),t];this.emitter.emit(iX.events.EDITOR_CHANGE,...i),t!==iX.sources.SILENT&&this.emitter.emit(...i)}}}function i1(t,e){try{e.parentNode}catch(t){return!1}return t.contains(e)}let i0=/^[ -~]*$/;class i2{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();let i=i3(t),r=new ic;return(function(t){let e=[];return t.forEach(t=>{"string"==typeof t.insert?t.insert.split("\n").forEach((i,r)=>{r&&e.push({insert:"\n",attributes:t.attributes}),i&&e.push({insert:i,attributes:t.attributes})}):e.push(t)}),e})(i.ops.slice()).reduce((t,i)=>{let n=ic.Op.length(i),s=i.attributes||{},l=!1,o=!1;if(null!=i.insert){if(r.retain(n),"string"==typeof i.insert){let r=i.insert;o=!r.endsWith("\n")&&(e<=t||!!this.scroll.descendant(iU,t)[0]),this.scroll.insertAt(t,r);let[n,l]=this.scroll.line(t),a=eB({},iz(n));if(n instanceof iD){let[t]=n.descendant(e1,l);t&&(a=eB(a,iz(t)))}s=ic.AttributeMap.diff(a,s)||{}}else if("object"==typeof i.insert){let r=Object.keys(i.insert)[0];if(null==r)return t;let n=null!=this.scroll.query(r,eD.INLINE);if(n)(e<=t||this.scroll.descendant(iU,t)[0])&&(o=!0);else if(t>0){let[e,i]=this.scroll.descendant(e1,t-1);e instanceof ij?"\n"!==e.value()[i]&&(l=!0):e instanceof ii&&e.statics.scope===eD.INLINE_BLOT&&(l=!0)}if(this.scroll.insertAt(t,r,i.insert[r]),n){let[e]=this.scroll.descendant(e1,t);if(e){let t=eB({},iz(e));s=ic.AttributeMap.diff(t,s)||{}}}}e+=n}else if(r.push(i),null!==i.retain&&"object"==typeof i.retain){let e=Object.keys(i.retain)[0];if(null==e)return t;this.scroll.updateEmbedAt(t,e,i.retain[e])}Object.keys(s).forEach(e=>{this.scroll.formatAt(t,n,e,s[e])});let a=+!!l,c=+!!o;return e+=a+c,r.retain(a),r.delete(c),t+n+a+c},0),r.reduce((t,e)=>"number"==typeof e.delete?(this.scroll.deleteAt(t,e.delete),t):t+ic.Op.length(e),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(i)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new ic().retain(t).delete(e))}formatLine(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(i).forEach(r=>{this.scroll.lines(t,Math.max(e,1)).forEach(t=>{t.format(r,i[r])})}),this.scroll.optimize();let r=new ic().retain(t).retain(e,iT(i));return this.update(r)}formatText(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(i).forEach(r=>{this.scroll.formatAt(t,e,r,i[r])});let r=new ic().retain(t).retain(e,iT(i));return this.update(r)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new ic)}getFormat(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=[],r=[];0===e?this.scroll.path(t).forEach(t=>{let[e]=t;e instanceof iD?i.push(e):e instanceof e1&&r.push(e)}):(i=this.scroll.lines(t,e),r=this.scroll.descendants(e1,t,e));let[n,s]=[i,r].map(t=>{let e=t.shift();if(null==e)return{};let i=iz(e);for(;Object.keys(i).length>0;){let e=t.shift();if(null==e)break;i=function(t,e){return Object.keys(e).reduce((i,r)=>{if(null==t[r])return i;let n=e[r];return n===t[r]?i[r]=n:Array.isArray(n)?0>n.indexOf(t[r])?i[r]=n.concat([t[r]]):i[r]=n:i[r]=[n,t[r]],i},{})}(iz(e),i)}return i});return{...n,...s}}getHTML(t,e){let[i,r]=this.scroll.line(t);if(i){let n=i.length();return i.length()>=r+e&&(0!==r||e!==n)?i5(i,r,e,!0):i5(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(t=>"string"==typeof t.insert).map(t=>t.insert).join("")}insertContents(t,e){let i=i3(e),r=new ic().retain(t).concat(i);return this.scroll.insertContents(t,i),this.update(r)}insertEmbed(t,e,i){return this.scroll.insertAt(t,e,i),this.update(new ic().retain(t).insert({[e]:i}))}insertText(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(i).forEach(r=>{this.scroll.formatAt(t,e.length,r,i[r])}),this.update(new ic().retain(t).insert(e,iT(i)))}isBlank(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;let t=this.scroll.children.head;return t?.statics.blotName===iD.blotName&&!(t.children.length>1)&&t.children.head instanceof iC}removeFormat(t,e){let i=this.getText(t,e),[r,n]=this.scroll.line(t+e),s=0,l=new ic;null!=r&&(s=r.length()-n,l=r.delta().slice(n,n+s-1).insert("\n"));let o=this.getContents(t,e+s).diff(new ic().insert(i).concat(l)),a=new ic().retain(t).concat(o);return this.applyDelta(a)}update(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,r=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(i0)&&this.scroll.find(e[0].target)){let n=this.scroll.find(e[0].target),s=iz(n),l=n.offset(this.scroll),o=e[0].oldValue.replace(iF.CONTENTS,""),a=new ic().insert(o),c=new ic().insert(n.value()),h=i&&{oldRange:i6(i.oldRange,-l),newRange:i6(i.newRange,-l)};t=new ic().retain(l).concat(a.diff(c,h)).reduce((t,e)=>e.insert?t.insert(e.insert,s):t.push(e),new ic),this.delta=r.compose(t)}else this.delta=this.getDelta(),t&&t9(r.compose(t),this.delta)||(t=r.diff(this.delta,i));return t}}function i5(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if("html"in t&&"function"==typeof t.html)return t.html(e,i);if(t instanceof ij)return iI(t.value().slice(e,e+i)).replaceAll(" ","&nbsp;");if(t instanceof e4){if("list-container"===t.statics.blotName){let r=[];return t.children.forEachAt(e,i,(t,e,i)=>{let n="formats"in t&&"function"==typeof t.formats?t.formats():{};r.push({child:t,offset:e,length:i,indent:n.indent||0,type:n.list})}),function t(e,i,r){if(0===e.length){let[e]=i4(r.pop());return i<=0?`</li></${e}>`:`</li></${e}>${t([],i-1,r)}`}let[{child:n,offset:s,length:l,indent:o,type:a},...c]=e,[h,u]=i4(a);if(o>i)return(r.push(a),o===i+1)?`<${h}><li${u}>${i5(n,s,l)}${t(c,o,r)}`:`<${h}><li>${t(e,i+1,r)}`;let d=r[r.length-1];if(o===i&&a===d)return`</li><li${u}>${i5(n,s,l)}${t(c,o,r)}`;let[f]=i4(r.pop());return`</li></${f}>${t(e,i-1,r)}`}(r,-1,[])}let n=[];if(t.children.forEachAt(e,i,(t,e,i)=>{n.push(i5(t,e,i))}),r||"list"===t.statics.blotName)return n.join("");let{outerHTML:s,innerHTML:l}=t.domNode,[o,a]=s.split(`>${l}<`);return"<table"===o?`<table style="border: 1px solid #000;">${n.join("")}<${a}`:`${o}>${n.join("")}<${a}`}return t.domNode instanceof Element?t.domNode.outerHTML:""}function i4(t){let e="ordered"===t?"ol":"ul";switch(t){case"checked":return[e,' data-list="checked"'];case"unchecked":return[e,' data-list="unchecked"'];default:return[e,""]}}function i3(t){return t.reduce((t,e)=>{if("string"==typeof e.insert){let i=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(i,e.attributes)}return t.push(e)},new ic)}function i6(t,e){let{index:i,length:r}=t;return new iY(i+e,r)}class i8{static DEFAULTS={};constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.quill=t,this.options=e}}let i9=i8;class i7 extends ii{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(t=>{this.contentNode.appendChild(t)}),this.leftGuard=document.createTextNode("\uFEFF"),this.rightGuard=document.createTextNode("\uFEFF"),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e,i=null,r=t.data.split("\uFEFF").join("");if(t===this.leftGuard){if(this.prev instanceof ij){let t=this.prev.length();this.prev.insertAt(t,r),i={startNode:this.prev.domNode,startOffset:t+r.length}}else e=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(e),this),i={startNode:e,startOffset:r.length}}else t===this.rightGuard&&(this.next instanceof ij?(this.next.insertAt(0,r),i={startNode:this.next.domNode,startOffset:r.length}):(e=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(e),this.next),i={startNode:e,startOffset:r.length}));return t.data="\uFEFF",i}update(t,e){t.forEach(t=>{if("characterData"===t.type&&(t.target===this.leftGuard||t.target===this.rightGuard)){let i=this.restore(t.target);i&&(e.range=i)}})}}let rt=i7;class re{isComposing=!1;constructor(t,e){this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){let e=t.target instanceof Node?this.scroll.find(t.target,!0):null;!e||e instanceof rt||(this.emitter.emit(iX.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(iX.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(iX.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(iX.events.COMPOSITION_END,t),this.isComposing=!1}}class ri{static DEFAULTS={modules:{}};static themes={default:ri};modules={};constructor(t,e){this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{null==this.modules[t]&&this.addModule(t)})}addModule(t){let e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}let rr=ri,rn=t=>t.parentElement||t.getRootNode().host||null,rs=t=>{let e=t.getBoundingClientRect(),i="offsetWidth"in t&&Math.abs(e.width)/t.offsetWidth||1,r="offsetHeight"in t&&Math.abs(e.height)/t.offsetHeight||1;return{top:e.top,right:e.left+t.clientWidth*i,bottom:e.top+t.clientHeight*r,left:e.left}},rl=t=>{let e=parseInt(t,10);return Number.isNaN(e)?0:e},ro=(t,e,i,r,n,s)=>t<i&&e>r?0:t<i?-(i-t+n):e>r?e-t>r-i?t+n-i:e-r+s:0,ra=(t,e)=>{let i=t.ownerDocument,r=e,n=t;for(;n;){let t=n===i.body,e=t?{top:0,right:window.visualViewport?.width??i.documentElement.clientWidth,bottom:window.visualViewport?.height??i.documentElement.clientHeight,left:0}:rs(n),s=getComputedStyle(n),l=ro(r.left,r.right,e.left,e.right,rl(s.scrollPaddingLeft),rl(s.scrollPaddingRight)),o=ro(r.top,r.bottom,e.top,e.bottom,rl(s.scrollPaddingTop),rl(s.scrollPaddingBottom));if(l||o){if(t)i.defaultView?.scrollBy(l,o);else{let{scrollLeft:t,scrollTop:e}=n;o&&(n.scrollTop+=o),l&&(n.scrollLeft+=l);let i=n.scrollLeft-t,s=n.scrollTop-e;r={left:r.left-i,top:r.top-s,right:r.right-i,bottom:r.bottom-s}}}n=t||"fixed"===s.position?null:rn(n)}},rc=["block","break","cursor","inline","scroll","text"],rh=(t,e,i)=>{let r=new eF;return rc.forEach(t=>{let i=e.query(t);i&&r.register(i)}),t.forEach(t=>{let n=e.query(t);n||i.error(`Cannot register "${t}" specified in "formats" config. Are you sure it was registered?`);let s=0;for(;n;)if(r.register(n),n="blotName"in n?n.requiredContainer??null:null,(s+=1)>100){i.error(`Cycle detected in registering blot requiredContainer: "${t}"`);break}}),r},ru=iZ("quill"),rd=new eF;e4.uiClass="ql-ui";class rf{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:rd,theme:"default"};static events=iX.events;static sources=iX.sources;static version="2.0.3";static imports={delta:ic,parchment:r,"core/module":i9,"core/theme":rr};static debug(t){!0===t&&(t="log"),iZ.level(t)}static find(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return iH.get(t)||rd.find(t,e)}static import(t){return null==this.imports[t]&&ru.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if("string"!=typeof(arguments.length<=0?void 0:arguments[0])){let t=arguments.length<=0?void 0:arguments[0],e=!!(arguments.length<=1?void 0:arguments[1]),i="attrName"in t?t.attrName:t.blotName;"string"==typeof i?this.register(`formats/${i}`,t,e):Object.keys(t).forEach(i=>{this.register(i,t[i],e)})}else{let t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],i=!!(arguments.length<=2?void 0:arguments[2]);null==this.imports[t]||i||ru.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&"boolean"!=typeof e&&"abstract"!==e.blotName&&rd.register(e),"function"==typeof e.register&&e.register(rd)}}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.options=function(t,e){let i=rp(t);if(!i)throw Error("Invalid Quill container");let r=e.theme&&e.theme!==rf.DEFAULTS.theme?rf.import(`themes/${e.theme}`):rr;if(!r)throw Error(`Invalid theme ${e.theme}. Did you register it?`);let{modules:n,...s}=rf.DEFAULTS,{modules:l,...o}=r.DEFAULTS,a=rg(e.modules);null!=a&&a.toolbar&&a.toolbar.constructor!==Object&&(a={...a,toolbar:{container:a.toolbar}});let c=eB({},rg(n),rg(l),a),h={...s,...rm(o),...rm(e)},u=e.registry;return u?e.formats&&ru.warn('Ignoring "formats" option because "registry" is specified'):u=e.formats?rh(e.formats,h.registry,ru):h.registry,{...h,registry:u,container:i,theme:r,modules:Object.entries(c).reduce((t,e)=>{let[i,r]=e;if(!r)return t;let n=rf.import(`modules/${i}`);return null==n?(ru.error(`Cannot load ${i} module. Are you sure you registered it?`),t):{...t,[i]:eB({},n.DEFAULTS||{},r)}},{}),bounds:rp(h.bounds)}}(t,e),this.container=this.options.container,null==this.container){ru.error("Invalid Quill container",t);return}this.options.debug&&rf.debug(this.options.debug);let i=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",iH.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new iX;let r=il.blotName,n=this.options.registry.query(r);if(!n||!("blotName"in n))throw Error(`Cannot initialize Quill without "${r}" blot`);if(this.scroll=new n(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new i2(this.scroll),this.selection=new iJ(this.scroll,this.emitter),this.composition=new re(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(iX.events.EDITOR_CHANGE,t=>{t===iX.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(iX.events.SCROLL_UPDATE,(t,e)=>{let i=this.selection.lastRange,[r]=this.selection.getRange(),n=i&&r?{oldRange:i,newRange:r}:void 0;rb.call(this,()=>this.editor.update(null,e,n),t)}),this.emitter.on(iX.events.SCROLL_EMBED_UPDATE,(t,e)=>{let i=this.selection.lastRange,[r]=this.selection.getRange(),n=i&&r?{oldRange:i,newRange:r}:void 0;rb.call(this,()=>{let i=new ic().retain(t.offset(this)).retain({[t.statics.blotName]:e});return this.editor.update(i,[],n)},rf.sources.USER)}),i){let t=this.clipboard.convert({html:`${i}<p><br></p>`,text:"\n"});this.setContents(t)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof t){let e=t;(t=document.createElement("div")).classList.add(e)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,i){return[t,e,,i]=ry(t,e,i),rb.call(this,()=>this.editor.deleteText(t,e),i,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;let e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:iX.sources.API;return rb.call(this,()=>{let i=this.getSelection(!0),r=new ic;if(null==i)return r;if(this.scroll.query(t,eD.BLOCK))r=this.editor.formatLine(i.index,i.length,{[t]:e});else{if(0===i.length)return this.selection.format(t,e),r;r=this.editor.formatText(i.index,i.length,{[t]:e})}return this.setSelection(i,iX.sources.SILENT),r},i)}formatLine(t,e,i,r,n){let s;return[t,e,s,n]=ry(t,e,i,r,n),rb.call(this,()=>this.editor.formatLine(t,e,s),n,t,0)}formatText(t,e,i,r,n){let s;return[t,e,s,n]=ry(t,e,i,r,n),rb.call(this,()=>this.editor.formatText(t,e,s),n,t,0)}getBounds(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=null;if(!(i="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length)))return null;let r=this.container.getBoundingClientRect();return{bottom:i.bottom-r.top,height:i.height,left:i.left-r.left,right:i.right-r.left,top:i.top-r.top,width:i.width}}getContents(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t;return[t,e]=ry(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return"number"==typeof t&&(e=e??this.getLength()-t),[t,e]=ry(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return"number"==typeof t&&(e=e??this.getLength()-t),[t,e]=ry(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:rf.sources.API;return rb.call(this,()=>this.editor.insertEmbed(t,e,i),r,t)}insertText(t,e,i,r,n){let s;return[t,,s,n]=ry(t,0,i,r,n),rb.call(this,()=>this.editor.insertText(t,e,s),n,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,i){return[t,e,,i]=ry(t,e,i),rb.call(this,()=>this.editor.removeFormat(t,e),i,t)}scrollRectIntoView(t){ra(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){let t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iX.sources.API;return rb.call(this,()=>{t=new ic(t);let e=this.getLength(),i=this.editor.deleteText(0,e),r=this.editor.insertContents(0,t),n=this.editor.deleteText(this.getLength()-1,1);return i.compose(r).compose(n)},e)}setSelection(t,e,i){null==t?this.selection.setRange(null,e||rf.sources.API):([t,e,,i]=ry(t,e,i),this.selection.setRange(new iY(Math.max(0,t),e),i),i!==iX.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iX.sources.API,i=new ic().insert(t);return this.setContents(i,e)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:iX.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iX.sources.API;return rb.call(this,()=>(t=new ic(t),this.editor.applyDelta(t)),e,!0)}}function rp(t){return"string"==typeof t?document.querySelector(t):t}function rg(t){return Object.entries(t??{}).reduce((t,e)=>{let[i,r]=e;return{...t,[i]:!0===r?{}:r}},{})}function rm(t){return Object.fromEntries(Object.entries(t).filter(t=>void 0!==t[1]))}function rb(t,e,i,r){if(!this.isEnabled()&&e===iX.sources.USER&&!this.allowReadOnlyEdits)return new ic;let n=null==i?null:this.getSelection(),s=this.editor.delta,l=t();if(null!=n&&(!0===i&&(i=n.index),null==r?n=rv(n,l,e):0!==r&&(n=rv(n,i,r,e)),this.setSelection(n,iX.sources.SILENT)),l.length()>0){let t=[iX.events.TEXT_CHANGE,l,s,e];this.emitter.emit(iX.events.EDITOR_CHANGE,...t),e!==iX.sources.SILENT&&this.emitter.emit(...t)}return l}function ry(t,e,i,r,n){let s={};return"number"==typeof t.index&&"number"==typeof t.length?("number"!=typeof e&&(n=r,r=i,i=e),e=t.length,t=t.index):"number"!=typeof e&&(n=r,r=i,i=e,e=0),"object"==typeof i?(s=i,n=r):"string"==typeof i&&(null!=r?s[i]=r:n=i),[t,e,s,n=n||iX.sources.API]}function rv(t,e,i,r){let n,s;let l="number"==typeof i?i:0;return null==t?null:(e&&"function"==typeof e.transformPosition?[n,s]=[t.index,t.index+t.length].map(t=>e.transformPosition(t,r!==iX.sources.USER)):[n,s]=[t.index,t.index+t.length].map(t=>t<e||t===e&&r===iX.sources.USER?t:l>=0?t+l:Math.max(e,t+l)),new iY(n,s-n))}class rx extends it{}let rN=rx;function rE(t){return t instanceof iD||t instanceof iU}function rA(t){return"function"==typeof t.updateContent}class rw extends il{static blotName="scroll";static className="ql-editor";static tagName="DIV";static defaultChild=iD;static allowedChildren=[iD,iU,rN];constructor(t,e,i){let{emitter:r}=i;super(t,e),this.emitter=r,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",t=>this.handleDragStart(t))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;let t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(iX.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(iX.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(iX.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){let[i,r]=this.line(t),[n]=this.line(t+e);if(super.deleteAt(t,e),null!=n&&i!==n&&r>0){if(i instanceof iU||n instanceof iU){this.optimize();return}let t=n.children.head instanceof iC?null:n.children.head;i.moveChildren(n,t),i.remove()}this.optimize()}enable(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,i,r){super.formatAt(t,e,i,r),this.optimize()}insertAt(t,e,i){if(t>=this.length()){if(null==i||null==this.scroll.query(e,eD.BLOCK)){let t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t),null==i&&e.endsWith("\n")?t.insertAt(0,e.slice(0,-1),i):t.insertAt(0,e,i)}else{let t=this.scroll.create(e,i);this.appendChild(t)}}else super.insertAt(t,e,i);this.optimize()}insertBefore(t,e){if(t.statics.scope===eD.INLINE_BLOT){let i=this.scroll.create(this.statics.defaultChild.blotName);i.appendChild(t),super.insertBefore(i,e)}else super.insertBefore(t,e)}insertContents(t,e){let i=this.deltaToRenderBlocks(e.concat(new ic().insert("\n"))),r=i.pop();if(null==r)return;this.batchStart();let n=i.shift();if(n){let e="block"===n.type&&(0===n.delta.length()||!this.descendant(iU,t)[0]&&t<this.length()),i="block"===n.type?n.delta:new ic().insert({[n.key]:n.value});rq(this,t,i);let r=+("block"===n.type),s=t+i.length()+r;e&&this.insertAt(s-1,"\n");let l=iz(this.line(t)[0]),o=ic.AttributeMap.diff(l,n.attributes)||{};Object.keys(o).forEach(t=>{this.formatAt(s-1,1,t,o[t])}),t=s}let[s,l]=this.children.find(t);i.length&&(s&&(s=s.split(l),l=0),i.forEach(t=>{if("block"===t.type)rq(this.createBlock(t.attributes,s||void 0),0,t.delta);else{let e=this.create(t.key,t.value);this.insertBefore(e,s||void 0),Object.keys(t.attributes).forEach(i=>{e.format(i,t.attributes[i])})}})),"block"===r.type&&r.delta.length()&&rq(this,s?s.offset(s.scroll)+l:this.length(),r.delta),this.batchEnd(),this.optimize()}isEnabled(){return"true"===this.domNode.getAttribute("contenteditable")}leaf(t){let e=this.path(t).pop();if(!e)return[null,-1];let[i,r]=e;return i instanceof e1?[i,r]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(rE,t)}lines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE,i=(t,e,r)=>{let n=[],s=r;return t.children.forEachAt(e,r,(t,e,r)=>{rE(t)?n.push(t):t instanceof it&&(n=n.concat(i(t,e,s))),s-=r}),n};return i(this,t,e)}optimize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!this.batch&&(super.optimize(t,e),t.length>0&&this.emitter.emit(iX.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=iX.sources.USER;"string"==typeof t&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),(t=t.filter(t=>{let{target:e}=t,i=this.find(e,!0);return i&&!rA(i)})).length>0&&this.emitter.emit(iX.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(iX.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,i){let[r]=this.descendant(t=>t instanceof iU,t);r&&r.statics.blotName===e&&rA(r)&&r.updateContent(i)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){let e=[],i=new ic;return t.forEach(t=>{let r=t?.insert;if(r){if("string"==typeof r){let n=r.split("\n");n.slice(0,-1).forEach(r=>{i.insert(r,t.attributes),e.push({type:"block",delta:i,attributes:t.attributes??{}}),i=new ic});let s=n[n.length-1];s&&i.insert(s,t.attributes)}else{let n=Object.keys(r)[0];if(!n)return;this.query(n,eD.INLINE)?i.push(t):(i.length()&&e.push({type:"block",delta:i,attributes:{}}),i=new ic,e.push({type:"blockEmbed",key:n,value:r[n],attributes:t.attributes??{}}))}}}),i.length()&&e.push({type:"block",delta:i,attributes:{}}),e}createBlock(t,e){let i;let r={};Object.entries(t).forEach(t=>{let[e,n]=t;null!=this.query(e,eD.BLOCK&eD.BLOT)?i=e:r[e]=n});let n=this.create(i||this.statics.defaultChild.blotName,i?t[i]:void 0);this.insertBefore(n,e||void 0);let s=n.length();return Object.entries(r).forEach(t=>{let[e,i]=t;n.formatAt(0,s,e,i)}),n}}function rq(t,e,i){i.reduce((e,i)=>{let r=ic.Op.length(i),n=i.attributes||{};if(null!=i.insert){if("string"==typeof i.insert){let r=i.insert;t.insertAt(e,r);let[s]=t.descendant(e1,e),l=iz(s);n=ic.AttributeMap.diff(l,n)||{}}else if("object"==typeof i.insert){let r=Object.keys(i.insert)[0];if(null==r)return e;if(t.insertAt(e,r,i.insert[r]),null!=t.scroll.query(r,eD.INLINE)){let[i]=t.descendant(e1,e),r=iz(i);n=ic.AttributeMap.diff(r,n)||{}}}}return Object.keys(n).forEach(i=>{t.formatAt(e,r,i,n[i])}),e+r},e)}let rk={scope:eD.BLOCK,whitelist:["right","center","justify"]},r_=new eU("align","align",rk),rL=new eV("align","ql-align",rk),rS=new eZ("align","text-align",rk);class rO extends eZ{value(t){let e=super.value(t);if(!e.startsWith("rgb("))return e;let i=(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map(t=>`00${parseInt(t,10).toString(16)}`.slice(-2)).join("");return`#${i}`}}let rT=new eV("color","ql-color",{scope:eD.INLINE}),rC=new rO("color","color",{scope:eD.INLINE}),rj=new eV("background","ql-bg",{scope:eD.INLINE}),rR=new rO("background","background-color",{scope:eD.INLINE});class rI extends rN{static create(t){let e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(t=>1>=t.length()?"":t.domNode.innerText).join("\n").slice(t,t+e)}html(t,e){return`<pre>
${iI(this.code(t,e))}
</pre>`}}class rM extends iD{static TAB="  ";static register(){rf.register(rI)}}class rB extends iB{}rB.blotName="code",rB.tagName="CODE",rM.blotName="code-block",rM.className="ql-code-block",rM.tagName="DIV",rI.blotName="code-block-container",rI.className="ql-code-block-container",rI.tagName="DIV",rI.allowedChildren=[rM],rM.allowedChildren=[ij,iC,iF],rM.requiredContainer=rI;let rD={scope:eD.BLOCK,whitelist:["rtl"]},rU=new eU("direction","dir",rD),rP=new eV("direction","ql-direction",rD),rz=new eZ("direction","direction",rD),rF={scope:eD.INLINE,whitelist:["serif","monospace"]},r$=new eV("font","ql-font",rF);class rH extends eZ{value(t){return super.value(t).replace(/["']/g,"")}}let rV=new rH("font","font-family",rF),rK=new eV("size","ql-size",{scope:eD.INLINE,whitelist:["small","large","huge"]}),rW=new eZ("size","font-size",{scope:eD.INLINE,whitelist:["10px","18px","32px"]}),rZ=iZ("quill:keyboard"),rG=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class rX extends i9{static match(t,e){return!["altKey","ctrlKey","metaKey","shiftKey"].some(i=>!!e[i]!==t[i]&&null!==e[i])&&(e.key===t.key||e.key===t.which)}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(t=>{this.options.bindings[t]&&this.addBinding(this.options.bindings[t])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(t){if("string"==typeof t||"number"==typeof t)t={key:t};else{if("object"!=typeof t)return null;t=iT(t)}return t.shortKey&&(t[rG]=t.shortKey,delete t.shortKey),t}(t);if(null==r){rZ.warn("Attempted to add invalid keyboard binding",r);return}"function"==typeof e&&(e={handler:e}),"function"==typeof i&&(i={handler:i}),(Array.isArray(r.key)?r.key:[r.key]).forEach(t=>{let n={...r,key:t,...e,...i};this.bindings[n.key]=this.bindings[n.key]||[],this.bindings[n.key].push(n)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||229===t.keyCode&&("Enter"===t.key||"Backspace"===t.key))return;let e=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(e=>rX.match(t,e));if(0===e.length)return;let i=rf.find(t.target,!0);if(i&&i.scroll!==this.quill.scroll)return;let r=this.quill.getSelection();if(null==r||!this.quill.hasFocus())return;let[n,s]=this.quill.getLine(r.index),[l,o]=this.quill.getLeaf(r.index),[a,c]=0===r.length?[l,o]:this.quill.getLeaf(r.index+r.length),h=l instanceof ia?l.value().slice(0,o):"",u=a instanceof ia?a.value().slice(c):"",d={collapsed:0===r.length,empty:0===r.length&&1>=n.length(),format:this.quill.getFormat(r),line:n,offset:s,prefix:h,suffix:u,event:t};e.some(t=>{if(null!=t.collapsed&&t.collapsed!==d.collapsed||null!=t.empty&&t.empty!==d.empty||null!=t.offset&&t.offset!==d.offset)return!1;if(Array.isArray(t.format)){if(t.format.every(t=>null==d.format[t]))return!1}else if("object"==typeof t.format&&!Object.keys(t.format).every(e=>!0===t.format[e]?null!=d.format[e]:!1===t.format[e]?null==d.format[e]:t9(t.format[e],d.format[e])))return!1;return!!((null==t.prefix||t.prefix.test(d.prefix))&&(null==t.suffix||t.suffix.test(d.suffix)))&&!0!==t.handler.call(this,r,d,t)})&&t.preventDefault()})}handleBackspace(t,e){let i=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(0===t.index||1>=this.quill.getLength())return;let r={},[n]=this.quill.getLine(t.index),s=new ic().retain(t.index-i).delete(i);if(0===e.offset){let[e]=this.quill.getLine(t.index-1);if(e&&!("block"===e.statics.blotName&&1>=e.length())){let e=n.formats(),i=this.quill.getFormat(t.index-1,1);if(Object.keys(r=ic.AttributeMap.diff(e,i)||{}).length>0){let e=new ic().retain(t.index+n.length()-2).retain(1,r);s=s.compose(e)}}}this.quill.updateContents(s,rf.sources.USER),this.quill.focus()}handleDelete(t,e){let i=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-i)return;let r={},[n]=this.quill.getLine(t.index),s=new ic().retain(t.index).delete(i);if(e.offset>=n.length()-1){let[e]=this.quill.getLine(t.index+1);if(e){let i=n.formats(),l=this.quill.getFormat(t.index,1);Object.keys(r=ic.AttributeMap.diff(i,l)||{}).length>0&&(s=s.retain(e.length()-1).retain(1,r))}}this.quill.updateContents(s,rf.sources.USER),this.quill.focus()}handleDeleteRange(t){r0({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){let i=Object.keys(e.format).reduce((t,i)=>(this.quill.scroll.query(i,eD.BLOCK)&&!Array.isArray(e.format[i])&&(t[i]=e.format[i]),t),{}),r=new ic().retain(t.index).delete(t.length).insert("\n",i);this.quill.updateContents(r,rf.sources.USER),this.quill.setSelection(t.index+1,rf.sources.SILENT),this.quill.focus()}}function rQ(t){return{key:"Tab",shiftKey:!t,format:{"code-block":!0},handler(e,i){let{event:r}=i,{TAB:n}=this.quill.scroll.query("code-block");if(0===e.length&&!r.shiftKey){this.quill.insertText(e.index,n,rf.sources.USER),this.quill.setSelection(e.index+n.length,rf.sources.SILENT);return}let s=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e),{index:l,length:o}=e;s.forEach((e,i)=>{t?(e.insertAt(0,n),0===i?l+=n.length:o+=n.length):e.domNode.textContent.startsWith(n)&&(e.deleteAt(0,n.length),0===i?l-=n.length:o-=n.length)}),this.quill.update(rf.sources.USER),this.quill.setSelection(l,o,rf.sources.SILENT)}}}function rY(t,e){return{key:t,shiftKey:e,altKey:null,["ArrowLeft"===t?"prefix":"suffix"]:/^$/,handler(i){let{index:r}=i;"ArrowRight"===t&&(r+=i.length+1);let[n]=this.quill.getLeaf(r);return!(n instanceof ii)||("ArrowLeft"===t?e?this.quill.setSelection(i.index-1,i.length+1,rf.sources.USER):this.quill.setSelection(i.index-1,rf.sources.USER):e?this.quill.setSelection(i.index,i.length+1,rf.sources.USER):this.quill.setSelection(i.index+i.length+1,rf.sources.USER),!1)}}}function rJ(t){return{key:t[0],shortKey:!0,handler(e,i){this.quill.format(t,!i.format[t],rf.sources.USER)}}}function r1(t){return{key:t?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(e,i){let r=t?"prev":"next",n=i.line,s=n.parent[r];if(null!=s){if("table-row"===s.statics.blotName){let t=s.children.head,e=n;for(;null!=e.prev;)e=e.prev,t=t.next;let r=t.offset(this.quill.scroll)+Math.min(i.offset,t.length()-1);this.quill.setSelection(r,0,rf.sources.USER)}}else{let e=n.table()[r];null!=e&&(t?this.quill.setSelection(e.offset(this.quill.scroll)+e.length()-1,0,rf.sources.USER):this.quill.setSelection(e.offset(this.quill.scroll),0,rf.sources.USER))}return!1}}}function r0(t){let{quill:e,range:i}=t,r=e.getLines(i),n={};if(r.length>1){let t=r[0].formats(),e=r[r.length-1].formats();n=ic.AttributeMap.diff(e,t)||{}}e.deleteText(i,rf.sources.USER),Object.keys(n).length>0&&e.formatLine(i.index,1,n,rf.sources.USER),e.setSelection(i.index,rf.sources.SILENT)}rX.DEFAULTS={bindings:{bold:rJ("bold"),italic:rJ("italic"),underline:rJ("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(t,e){return!!e.collapsed&&0!==e.offset||(this.quill.format("indent","+1",rf.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(t,e){return!!e.collapsed&&0!==e.offset||(this.quill.format("indent","-1",rf.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(t,e){null!=e.format.indent?this.quill.format("indent","-1",rf.sources.USER):null!=e.format.list&&this.quill.format("list",!1,rf.sources.USER)}},"indent code-block":rQ(!0),"outdent code-block":rQ(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(t){this.quill.deleteText(t.index-1,1,rf.sources.USER)}},tab:{key:"Tab",handler(t,e){if(e.format.table)return!0;this.quill.history.cutoff();let i=new ic().retain(t.index).delete(t.length).insert("	");return this.quill.updateContents(i,rf.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,rf.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,rf.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(t,e){let i={list:!1};e.format.indent&&(i.indent=!1),this.quill.formatLine(t.index,t.length,i,rf.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(t){let[e,i]=this.quill.getLine(t.index),r={...e.formats(),list:"checked"},n=new ic().retain(t.index).insert("\n",r).retain(e.length()-i-1).retain(1,{list:"unchecked"});this.quill.updateContents(n,rf.sources.USER),this.quill.setSelection(t.index+1,rf.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(t,e){let[i,r]=this.quill.getLine(t.index),n=new ic().retain(t.index).insert("\n",e.format).retain(i.length()-r-1).retain(1,{header:null});this.quill.updateContents(n,rf.sources.USER),this.quill.setSelection(t.index+1,rf.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(t){let e=this.quill.getModule("table");if(e){var i,r,n,s;let[l,o,a,c]=e.getTable(t),h=(i=0,r=o,n=a,s=c,null==r.prev&&null==r.next?null==n.prev&&null==n.next?0===s?-1:1:null==n.prev?-1:1:null==r.prev?-1:null==r.next?1:null);if(null==h)return;let u=l.offset();if(h<0){let e=new ic().retain(u).insert("\n");this.quill.updateContents(e,rf.sources.USER),this.quill.setSelection(t.index+1,t.length,rf.sources.SILENT)}else if(h>0){u+=l.length();let t=new ic().retain(u).insert("\n");this.quill.updateContents(t,rf.sources.USER),this.quill.setSelection(u,rf.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(t,e){let{event:i,line:r}=e,n=r.offset(this.quill.scroll);i.shiftKey?this.quill.setSelection(n-1,rf.sources.USER):this.quill.setSelection(n+r.length(),rf.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(t,e){let i;if(null==this.quill.scroll.query("list"))return!0;let{length:r}=e.prefix,[n,s]=this.quill.getLine(t.index);if(s>r)return!0;switch(e.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(t.index," ",rf.sources.USER),this.quill.history.cutoff();let l=new ic().retain(t.index-s).delete(r+1).retain(n.length()-2-s).retain(1,{list:i});return this.quill.updateContents(l,rf.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-r,rf.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(t){let[e,i]=this.quill.getLine(t.index),r=2,n=e;for(;null!=n&&1>=n.length()&&n.formats()["code-block"];)if(n=n.prev,(r-=1)<=0){let r=new ic().retain(t.index+e.length()-i-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(r,rf.sources.USER),this.quill.setSelection(t.index-1,rf.sources.SILENT),!1}return!0}},"embed left":rY("ArrowLeft",!1),"embed left shift":rY("ArrowLeft",!0),"embed right":rY("ArrowRight",!1),"embed right shift":rY("ArrowRight",!0),"table down":r1(!1),"table up":r1(!0)}};let r2=/font-weight:\s*normal/,r5=["P","OL","UL"],r4=t=>t&&r5.includes(t.tagName),r3=t=>{Array.from(t.querySelectorAll("br")).filter(t=>r4(t.previousElementSibling)&&r4(t.nextElementSibling)).forEach(t=>{t.parentNode?.removeChild(t)})},r6=t=>{Array.from(t.querySelectorAll('b[style*="font-weight"]')).filter(t=>t.getAttribute("style")?.match(r2)).forEach(e=>{let i=t.createDocumentFragment();i.append(...e.childNodes),e.parentNode?.replaceChild(i,e)})},r8=/\bmso-list:[^;]*ignore/i,r9=/\bmso-list:[^;]*\bl(\d+)/i,r7=/\bmso-list:[^;]*\blevel(\d+)/i,nt=(t,e)=>{let i=t.getAttribute("style"),r=i?.match(r9);if(!r)return null;let n=Number(r[1]),s=i?.match(r7),l=s?Number(s[1]):1,o=RegExp(`@list l${n}:level${l}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),a=e.match(o);return{id:n,indent:l,type:a&&"bullet"===a[1]?"bullet":"ordered",element:t}},ne=t=>{let e=Array.from(t.querySelectorAll("[style*=mso-list]")),i=[],r=[];e.forEach(t=>{(t.getAttribute("style")||"").match(r8)?i.push(t):r.push(t)}),i.forEach(t=>t.parentNode?.removeChild(t));let n=t.documentElement.innerHTML,s=r.map(t=>nt(t,n)).filter(t=>t);for(;s.length;){let t=[],e=s.shift();for(;e;)t.push(e),e=s.length&&s[0]?.element===e.element.nextElementSibling&&s[0].id===e.id?s.shift():null;let i=document.createElement("ul");t.forEach(t=>{let e=document.createElement("li");e.setAttribute("data-list",t.type),t.indent>1&&e.setAttribute("class",`ql-indent-${t.indent-1}`),e.innerHTML=t.element.innerHTML,i.appendChild(e)});let r=t[0]?.element,{parentNode:n}=r??{};r&&n?.replaceChild(i,r),t.slice(1).forEach(t=>{let{element:e}=t;n?.removeChild(e)})}},ni=[function(t){"urn:schemas-microsoft-com:office:word"===t.documentElement.getAttribute("xmlns:w")&&ne(t)},function(t){t.querySelector('[id^="docs-internal-guid-"]')&&(r6(t),r3(t))}],nr=t=>{t.documentElement&&ni.forEach(e=>{e(t)})},nn=iZ("quill:clipboard"),ns=[[Node.TEXT_NODE,function(t,e,i){let r=t.data;if(t.parentElement?.tagName==="O:P")return e.insert(r.trim());if(!function t(e){return null!=e&&(nd.has(e)||("PRE"===e.tagName?nd.set(e,!0):nd.set(e,t(e.parentNode))),nd.get(e))}(t)){if(0===r.trim().length&&r.includes("\n")&&(!t.previousElementSibling||!t.nextElementSibling||nu(t.previousElementSibling,i)||nu(t.nextElementSibling,i)))return e;r=(r=r.replace(/[^\S\u00a0]/g," ")).replace(/ {2,}/g," "),(null==t.previousSibling&&null!=t.parentElement&&nu(t.parentElement,i)||t.previousSibling instanceof Element&&nu(t.previousSibling,i))&&(r=r.replace(/^ /,"")),(null==t.nextSibling&&null!=t.parentElement&&nu(t.parentElement,i)||t.nextSibling instanceof Element&&nu(t.nextSibling,i))&&(r=r.replace(/ $/,"")),r=r.replaceAll("\xa0"," ")}return e.insert(r)}],[Node.TEXT_NODE,ng],["br",function(t,e){return nh(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,ng],[Node.ELEMENT_NODE,function(t,e,i){let r=i.query(t);if(null==r)return e;if(r.prototype instanceof ii){let e={},n=r.value(t);if(null!=n)return e[r.blotName]=n,new ic().insert(e,r.formats(t,i))}else if(r.prototype instanceof e9&&!nh(e,"\n")&&e.insert("\n"),"blotName"in r&&"formats"in r&&"function"==typeof r.formats)return nc(e,r.blotName,r.formats(t,i),i);return e}],[Node.ELEMENT_NODE,function(t,e,i){let r=eU.keys(t),n=eV.keys(t),s=eZ.keys(t),l={};return r.concat(n).concat(s).forEach(e=>{let r=i.query(e,eD.ATTRIBUTE);(null==r||(l[r.attrName]=r.value(t),!l[r.attrName]))&&(null!=(r=nl[e])&&(r.attrName===e||r.keyName===e)&&(l[r.attrName]=r.value(t)||void 0),null!=(r=no[e])&&(r.attrName===e||r.keyName===e)&&(l[(r=no[e]).attrName]=r.value(t)||void 0))}),Object.entries(l).reduce((t,e)=>{let[r,n]=e;return nc(t,r,n,i)},e)}],[Node.ELEMENT_NODE,function(t,e,i){let r={},n=t.style||{};return("italic"===n.fontStyle&&(r.italic=!0),"underline"===n.textDecoration&&(r.underline=!0),"line-through"===n.textDecoration&&(r.strike=!0),(n.fontWeight?.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(r.bold=!0),e=Object.entries(r).reduce((t,e)=>{let[r,n]=e;return nc(t,r,n,i)},e),parseFloat(n.textIndent||0)>0)?new ic().insert("	").concat(e):e}],["li",function(t,e,i){let r=i.query(t);if(null==r||"list"!==r.blotName||!nh(e,"\n"))return e;let n=-1,s=t.parentNode;for(;null!=s;)["OL","UL"].includes(s.tagName)&&(n+=1),s=s.parentNode;return n<=0?e:e.reduce((t,e)=>e.insert?e.attributes&&"number"==typeof e.attributes.indent?t.push(e):t.insert(e.insert,{indent:n,...e.attributes||{}}):t,new ic)}],["ol, ul",function(t,e,i){let r="OL"===t.tagName?"ordered":"bullet",n=t.getAttribute("data-checked");return n&&(r="true"===n?"checked":"unchecked"),nc(e,"list",r,i)}],["pre",function(t,e,i){let r=i.query("code-block");return nc(e,"code-block",!r||!("formats"in r)||"function"!=typeof r.formats||r.formats(t,i),i)}],["tr",function(t,e,i){let r=t.parentElement?.tagName==="TABLE"?t.parentElement:t.parentElement?.parentElement;return null!=r?nc(e,"table",Array.from(r.querySelectorAll("tr")).indexOf(t)+1,i):e}],["b",np("bold")],["i",np("italic")],["strike",np("strike")],["style",function(){return new ic}]],nl=[r_,rU].reduce((t,e)=>(t[e.keyName]=e,t),{}),no=[rS,rR,rC,rz,rV,rW].reduce((t,e)=>(t[e.keyName]=e,t),{});class na extends i9{static DEFAULTS={matchers:[]};constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",t=>this.onCaptureCopy(t,!1)),this.quill.root.addEventListener("cut",t=>this.onCaptureCopy(t,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],ns.concat(this.options.matchers??[]).forEach(t=>{let[e,i]=t;this.addMatcher(e,i)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:i}=t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r[rM.blotName])return new ic().insert(i||"",{[rM.blotName]:r[rM.blotName]});if(!e)return new ic().insert(i||"",r);let n=this.convertHTML(e);return nh(n,"\n")&&(null==n.ops[n.ops.length-1].attributes||r.table)?n.compose(new ic().retain(n.length()-1).delete(1)):n}normalizeHTML(t){nr(t)}convertHTML(t){let e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);let i=e.body,r=new WeakMap,[n,s]=this.prepareMatching(i,r);return nf(this.quill.scroll,i,n,s,r)}dangerouslyPasteHTML(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:rf.sources.API;if("string"==typeof t){let i=this.convert({html:t,text:""});this.quill.setContents(i,e),this.quill.setSelection(0,rf.sources.SILENT)}else{let r=this.convert({html:e,text:""});this.quill.updateContents(new ic().retain(t).concat(r),i),this.quill.setSelection(t+r.length(),rf.sources.SILENT)}}onCaptureCopy(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.defaultPrevented)return;t.preventDefault();let[i]=this.quill.selection.getRange();if(null==i)return;let{html:r,text:n}=this.onCopy(i,e);t.clipboardData?.setData("text/plain",n),t.clipboardData?.setData("text/html",r),e&&r0({range:i,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(t=>"#"!==t[0]).join("\n")}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();let e=this.quill.getSelection(!0);if(null==e)return;let i=t.clipboardData?.getData("text/html"),r=t.clipboardData?.getData("text/plain");if(!i&&!r){let e=t.clipboardData?.getData("text/uri-list");e&&(r=this.normalizeURIList(e))}let n=Array.from(t.clipboardData?.files||[]);if(!i&&n.length>0){this.quill.uploader.upload(e,n);return}if(i&&n.length>0){let t=new DOMParser().parseFromString(i,"text/html");if(1===t.body.childElementCount&&t.body.firstElementChild?.tagName==="IMG"){this.quill.uploader.upload(e,n);return}}this.onPaste(e,{html:i,text:r})}onCopy(t){let e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:i,html:r}=e,n=this.quill.getFormat(t.index),s=this.convert({text:i,html:r},n);nn.log("onPaste",s,{text:i,html:r});let l=new ic().retain(t.index).delete(t.length).concat(s);this.quill.updateContents(l,rf.sources.USER),this.quill.setSelection(l.length()-t.length,rf.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){let i=[],r=[];return this.matchers.forEach(n=>{let[s,l]=n;switch(s){case Node.TEXT_NODE:r.push(l);break;case Node.ELEMENT_NODE:i.push(l);break;default:Array.from(t.querySelectorAll(s)).forEach(t=>{if(e.has(t)){let i=e.get(t);i?.push(l)}else e.set(t,[l])})}}),[i,r]}}function nc(t,e,i,r){return r.query(e)?t.reduce((t,r)=>r.insert?r.attributes&&r.attributes[e]?t.push(r):t.insert(r.insert,{...i?{[e]:i}:{},...r.attributes}):t,new ic):t}function nh(t,e){let i="";for(let r=t.ops.length-1;r>=0&&i.length<e.length;--r){let e=t.ops[r];if("string"!=typeof e.insert)break;i=e.insert+i}return i.slice(-1*e.length)===e}function nu(t,e){if(!(t instanceof Element))return!1;let i=e.query(t);return(!i||!(i.prototype instanceof ii))&&["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(t.tagName.toLowerCase())}let nd=new WeakMap;function nf(t,e,i,r,n){return e.nodeType===e.TEXT_NODE?r.reduce((i,r)=>r(e,i,t),new ic):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce((s,l)=>{let o=nf(t,l,i,r,n);return l.nodeType===e.ELEMENT_NODE&&(o=i.reduce((e,i)=>i(l,e,t),o),o=(n.get(l)||[]).reduce((e,i)=>i(l,e,t),o)),s.concat(o)},new ic):new ic}function np(t){return(e,i,r)=>nc(i,t,!0,r)}function ng(t,e,i){if(!nh(e,"\n")){if(nu(t,i)&&(t.childNodes.length>0||t instanceof HTMLParagraphElement))return e.insert("\n");if(e.length()>0&&t.nextSibling){let r=t.nextSibling;for(;null!=r;){if(nu(r,i))return e.insert("\n");let t=i.query(r);if(t&&t.prototype instanceof iU)return e.insert("\n");r=r.firstChild}}}return e}class nm extends i9{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,e){super(t,e),this.quill.on(rf.events.EDITOR_CHANGE,(t,e,i,r)=>{t===rf.events.SELECTION_CHANGE?e&&r!==rf.sources.SILENT&&(this.currentRange=e):t===rf.events.TEXT_CHANGE&&(this.ignoreChange||(this.options.userOnly&&r!==rf.sources.USER?this.transform(e):this.record(e,i)),this.currentRange=ny(this.currentRange,e))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",t=>{"historyUndo"===t.inputType?(this.undo(),t.preventDefault()):"historyRedo"===t.inputType&&(this.redo(),t.preventDefault())})}change(t,e){if(0===this.stack[t].length)return;let i=this.stack[t].pop();if(!i)return;let r=this.quill.getContents(),n=i.delta.invert(r);this.stack[e].push({delta:n,range:ny(i.range,n)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(i.delta,rf.sources.USER),this.ignoreChange=!1,this.restoreSelection(i)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,e){if(0===t.ops.length)return;this.stack.redo=[];let i=t.invert(e),r=this.currentRange,n=Date.now();if(this.lastRecorded+this.options.delay>n&&this.stack.undo.length>0){let t=this.stack.undo.pop();t&&(i=i.compose(t.delta),r=t.range)}else this.lastRecorded=n;0!==i.length()&&(this.stack.undo.push({delta:i,range:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(t){nb(this.stack.undo,t),nb(this.stack.redo,t)}undo(){this.change("undo","redo")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,rf.sources.USER);else{let e=function(t,e){let i=e.reduce((t,e)=>t+(e.delete||0),0),r=e.length()-i;return function(t,e){let i=e.ops[e.ops.length-1];return null!=i&&(null!=i.insert?"string"==typeof i.insert&&i.insert.endsWith("\n"):null!=i.attributes&&Object.keys(i.attributes).some(e=>null!=t.query(e,eD.BLOCK)))}(t,e)&&(r-=1),r}(this.quill.scroll,t.delta);this.quill.setSelection(e,rf.sources.USER)}}}function nb(t,e){let i=e;for(let e=t.length-1;e>=0;e-=1){let r=t[e];t[e]={delta:i.transform(r.delta,!0),range:r.range&&ny(r.range,i)},i=r.delta.transform(i),0===t[e].delta.length()&&t.splice(e,1)}}function ny(t,e){if(!t)return t;let i=e.transformPosition(t.index);return{index:i,length:e.transformPosition(t.index+t.length)-i}}class nv extends i9{constructor(t,e){super(t,e),t.root.addEventListener("drop",e=>{e.preventDefault();let i=null;if(document.caretRangeFromPoint)i=document.caretRangeFromPoint(e.clientX,e.clientY);else if(document.caretPositionFromPoint){let t=document.caretPositionFromPoint(e.clientX,e.clientY);(i=document.createRange()).setStart(t.offsetNode,t.offset),i.setEnd(t.offsetNode,t.offset)}let r=i&&t.selection.normalizeNative(i);if(r){let i=t.selection.normalizedToRange(r);e.dataTransfer?.files&&this.upload(i,e.dataTransfer.files)}})}upload(t,e){let i=[];Array.from(e).forEach(t=>{t&&this.options.mimetypes?.includes(t.type)&&i.push(t)}),i.length>0&&this.options.handler.call(this,t,i)}}nv.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(t,e){this.quill.scroll.query("image")&&Promise.all(e.map(t=>new Promise(e=>{let i=new FileReader;i.onload=()=>{e(i.result)},i.readAsDataURL(t)}))).then(e=>{let i=e.reduce((t,e)=>t.insert({image:e}),new ic().retain(t.index).delete(t.length));this.quill.updateContents(i,iX.sources.USER),this.quill.setSelection(t.index+e.length,iX.sources.SILENT)})}};let nx=["insertText","insertReplacementText"];class nN extends i9{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",t=>{this.handleBeforeInput(t)}),/Android/i.test(navigator.userAgent)||t.on(rf.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){r0({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(0===t.length)return!1;if(e){let i=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new ic().retain(t.index).insert(e,i),rf.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,rf.sources.SILENT),!0}handleBeforeInput(t){var e;if(this.quill.composition.isComposing||t.defaultPrevented||!nx.includes(t.inputType))return;let i=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!i||!0===i.collapsed)return;let r="string"==typeof(e=t).data?e.data:e.dataTransfer?.types.includes("text/plain")?e.dataTransfer.getData("text/plain"):null;if(null==r)return;let n=this.quill.selection.normalizeNative(i),s=n?this.quill.selection.normalizedToRange(n):null;s&&this.replaceText(s,r)&&t.preventDefault()}handleCompositionStart(){let t=this.quill.getSelection();t&&this.replaceText(t)}}let nE=/Mac/i.test(navigator.platform),nA=t=>"ArrowLeft"===t.key||"ArrowRight"===t.key||"ArrowUp"===t.key||"ArrowDown"===t.key||"Home"===t.key||!!nE&&"a"===t.key&&!0===t.ctrlKey;class nw extends i9{isListening=!1;selectionChangeDeadline=0;constructor(t,e){super(t,e),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(t,e){let{line:i,event:r}=e;if(!(i instanceof e4)||!i.uiNode)return!0;let n="rtl"===getComputedStyle(i.domNode).direction;return!!n&&"ArrowRight"!==r.key||!n&&"ArrowLeft"!==r.key||(this.quill.setSelection(t.index-1,t.length+ +!!r.shiftKey,rf.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",t=>{!t.defaultPrevented&&nA(t)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){this.selectionChangeDeadline=Date.now()+100,!this.isListening&&(this.isListening=!0,document.addEventListener("selectionchange",()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()},{once:!0}))}handleSelectionChange(){let t=document.getSelection();if(!t)return;let e=t.getRangeAt(0);if(!0!==e.collapsed||0!==e.startOffset)return;let i=this.quill.scroll.find(e.startContainer);if(!(i instanceof e4)||!i.uiNode)return;let r=document.createRange();r.setStartAfter(i.uiNode),r.setEndAfter(i.uiNode),t.removeAllRanges(),t.addRange(r)}}rf.register({"blots/block":iD,"blots/block/embed":iU,"blots/break":iC,"blots/container":rN,"blots/cursor":iF,"blots/embed":rt,"blots/inline":iB,"blots/scroll":rw,"blots/text":ij,"modules/clipboard":na,"modules/history":nm,"modules/keyboard":rX,"modules/uploader":nv,"modules/input":nN,"modules/uiNode":nw});class nq extends eV{add(t,e){let i=0;if("+1"===e||"-1"===e){let r=this.value(t)||0;i="+1"===e?r+1:r-1}else"number"==typeof e&&(i=e);return 0===i?(this.remove(t),!0):super.add(t,i.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}let nk=new nq("indent","ql-indent",{scope:eD.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class n_ extends iD{static blotName="blockquote";static tagName="blockquote"}class nL extends iD{static blotName="header";static tagName=["H1","H2","H3","H4","H5","H6"];static formats(t){return this.tagName.indexOf(t.tagName)+1}}class nS extends rN{}nS.blotName="list-container",nS.tagName="OL";class nO extends iD{static create(t){let e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){rf.register(nS)}constructor(t,e){super(t,e);let i=e.ownerDocument.createElement("span"),r=i=>{if(!t.isEnabled())return;let r=this.statics.formats(e,t);"checked"===r?(this.format("list","unchecked"),i.preventDefault()):"unchecked"===r&&(this.format("list","checked"),i.preventDefault())};i.addEventListener("mousedown",r),i.addEventListener("touchstart",r),this.attachUI(i)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}nO.blotName="list",nO.tagName="LI",nS.allowedChildren=[nO],nO.requiredContainer=nS;class nT extends iB{static blotName="bold";static tagName=["STRONG","B"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}let nC=nT;class nj extends nC{static blotName="italic";static tagName=["EM","I"]}class nR extends iB{static blotName="link";static tagName="A";static SANITIZED_URL="about:blank";static PROTOCOL_WHITELIST=["http","https","mailto","tel","sms"];static create(t){let e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return nI(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("href",this.constructor.sanitize(e)):super.format(t,e)}}function nI(t,e){let i=document.createElement("a");i.href=t;let r=i.href.slice(0,i.href.indexOf(":"));return e.indexOf(r)>-1}class nM extends iB{static blotName="script";static tagName=["SUB","SUP"];static create(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):super.create(t)}static formats(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}class nB extends nC{static blotName="strike";static tagName=["S","STRIKE"]}class nD extends iB{static blotName="underline";static tagName="U"}class nU extends rt{static blotName="formula";static className="ql-formula";static tagName="SPAN";static create(t){if(null==window.katex)throw Error("Formula module requires KaTeX.");let e=super.create(t);return"string"==typeof t&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){let{formula:t}=this.value();return`<span>${t}</span>`}}let nP=["alt","height","width"];class nz extends ii{static blotName="image";static tagName="IMG";static create(t){let e=super.create(t);return"string"==typeof t&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return nP.reduce((e,i)=>(t.hasAttribute(i)&&(e[i]=t.getAttribute(i)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return nI(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){nP.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}let nF=["height","width"];class n$ extends iU{static blotName="video";static className="ql-video";static tagName="IFRAME";static create(t){let e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return nF.reduce((e,i)=>(t.hasAttribute(i)&&(e[i]=t.getAttribute(i)),e),{})}static sanitize(t){return nR.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){nF.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){let{video:t}=this.value();return`<a href="${t}">${t}</a>`}}let nH=new eV("code-token","hljs",{scope:eD.INLINE});class nV extends iB{static formats(t,e){for(;null!=t&&t!==e.domNode;){if(t.classList&&t.classList.contains(rM.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,i){super(t,e,i),nH.add(this.domNode,i)}format(t,e){t!==nV.blotName?super.format(t,e):e?nH.add(this.domNode,e):(nH.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),nH.value(this.domNode)||this.unwrap()}}nV.blotName="code-token",nV.className="ql-token";class nK extends rM{static create(t){let e=super.create(t);return"string"==typeof t&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),nV.blotName,!1),super.replaceWith(t,e)}}class nW extends rI{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===nK.blotName&&(this.forceNext=!0,this.children.forEach(i=>{i.format(t,e)}))}formatAt(t,e,i,r){i===nK.blotName&&(this.forceNext=!0),super.formatAt(t,e,i,r)}highlight(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==this.children.head)return;let i=Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode),r=`${i.map(t=>t.textContent).join("\n")}
`,n=nK.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==r){if(r.trim().length>0||null==this.cachedText){let e=this.children.reduce((t,e)=>t.concat(iP(e,!1)),new ic),i=t(r,n);e.diff(i).reduce((t,e)=>{let{retain:i,attributes:r}=e;return i?(r&&Object.keys(r).forEach(e=>{[nK.blotName,nV.blotName].includes(e)&&this.formatAt(t,i,e,r[e])}),t+i):t},0)}this.cachedText=r,this.forceNext=!1}}html(t,e){let[i]=this.children.find(t),r=i?nK.formats(i.domNode):"plain";return`<pre data-language="${r}">
${iI(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){let t=nK.formats(this.children.head.domNode);t!==this.uiNode.value&&(this.uiNode.value=t)}}}nW.allowedChildren=[nK],nK.requiredContainer=nW,nK.allowedChildren=[nV,iF,ij,iC];let nZ=(t,e,i)=>"string"==typeof t.versionString&&parseInt(t.versionString.split(".")[0],10)>=11?t.highlight(i,{language:e}).value:t.highlight(e,i).value;class nG extends i9{static register(){rf.register(nV,!0),rf.register(nK,!0),rf.register(nW,!0)}constructor(t,e){if(super(t,e),null==this.options.hljs)throw Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((t,e)=>{let{key:i}=e;return t[i]=!0,t},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(rf.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof nW))return;let e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(t=>{let{key:i,label:r}=t,n=e.ownerDocument.createElement("option");n.textContent=r,n.setAttribute("value",i),e.appendChild(n)}),e.addEventListener("change",()=>{t.format(nK.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),null==t.uiNode&&(t.attachUI(e),t.children.head&&(e.value=nK.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(rf.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.quill.selection.composing)return;this.quill.update(rf.sources.USER);let i=this.quill.getSelection();(null==t?this.quill.scroll.descendants(nW):[t]).forEach(t=>{t.highlight(this.highlightBlot,e)}),this.quill.update(rf.sources.SILENT),null!=i&&this.quill.setSelection(i,rf.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"plain";if("plain"===(e=this.languages[e]?e:"plain"))return iI(t).split("\n").reduce((t,i,r)=>(0!==r&&t.insert("\n",{[rM.blotName]:e}),t.insert(i)),new ic);let i=this.quill.root.ownerDocument.createElement("div");return i.classList.add(rM.className),i.innerHTML=nZ(this.options.hljs,e,t),nf(this.quill.scroll,i,[(t,e)=>{let i=nH.value(t);return i?e.compose(new ic().retain(e.length(),{[nV.blotName]:i})):e}],[(t,i)=>t.data.split("\n").reduce((t,i,r)=>(0!==r&&t.insert("\n",{[rM.blotName]:e}),t.insert(i)),i)],new WeakMap)}}nG.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};class nX extends iD{static blotName="table";static tagName="TD";static create(t){let e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",n1()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===nX.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}class nQ extends rN{static blotName="table-row";static tagName="TR";checkMerge(){if(super.checkMerge()&&null!=this.next.children.head){let t=this.children.head.formats(),e=this.children.tail.formats(),i=this.next.children.head.formats(),r=this.next.children.tail.formats();return t.table===e.table&&t.table===i.table&&t.table===r.table}return!1}optimize(t){super.optimize(t),this.children.forEach(t=>{if(null==t.next)return;let e=t.formats(),i=t.next.formats();if(e.table!==i.table){let e=this.splitAfter(t);e&&e.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}class nY extends rN{static blotName="table-body";static tagName="TBODY"}class nJ extends rN{static blotName="table-container";static tagName="TABLE";balanceCells(){let t=this.descendants(nQ),e=t.reduce((t,e)=>Math.max(e.children.length,t),0);t.forEach(t=>{Array(e-t.children.length).fill(0).forEach(()=>{let e;null!=t.children.head&&(e=nX.formats(t.children.head.domNode));let i=this.scroll.create(nX.blotName,e);t.appendChild(i),i.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){let[e]=this.descendant(nY);null!=e&&null!=e.children.head&&e.children.forEach(e=>{let i=e.children.at(t);null!=i&&i.remove()})}insertColumn(t){let[e]=this.descendant(nY);null!=e&&null!=e.children.head&&e.children.forEach(e=>{let i=e.children.at(t),r=nX.formats(e.children.head.domNode),n=this.scroll.create(nX.blotName,r);e.insertBefore(n,i)})}insertRow(t){let[e]=this.descendant(nY);if(null==e||null==e.children.head)return;let i=n1(),r=this.scroll.create(nQ.blotName);e.children.head.children.forEach(()=>{let t=this.scroll.create(nX.blotName,i);r.appendChild(t)});let n=e.children.at(t);e.insertBefore(r,n)}rows(){let t=this.children.head;return null==t?[]:t.children.map(t=>t)}}function n1(){let t=Math.random().toString(36).slice(2,6);return`row-${t}`}nJ.allowedChildren=[nY],nY.requiredContainer=nJ,nY.allowedChildren=[nQ],nQ.requiredContainer=nY,nQ.allowedChildren=[nX],nX.requiredContainer=nQ;class n0 extends i9{static register(){rf.register(nX),rf.register(nQ),rf.register(nY),rf.register(nJ)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(nJ).forEach(t=>{t.balanceCells()})}deleteColumn(){let[t,,e]=this.getTable();null!=e&&(t.deleteColumn(e.cellOffset()),this.quill.update(rf.sources.USER))}deleteRow(){let[,t]=this.getTable();null!=t&&(t.remove(),this.quill.update(rf.sources.USER))}deleteTable(){let[t]=this.getTable();if(null==t)return;let e=t.offset();t.remove(),this.quill.update(rf.sources.USER),this.quill.setSelection(e,rf.sources.SILENT)}getTable(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(null==t)return[null,null,null,-1];let[e,i]=this.quill.getLine(t.index);if(null==e||e.statics.blotName!==nX.blotName)return[null,null,null,-1];let r=e.parent;return[r.parent.parent,r,e,i]}insertColumn(t){let e=this.quill.getSelection();if(!e)return;let[i,r,n]=this.getTable(e);if(null==n)return;let s=n.cellOffset();i.insertColumn(s+t),this.quill.update(rf.sources.USER);let l=r.rowOffset();0===t&&(l+=1),this.quill.setSelection(e.index+l,e.length,rf.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){let e=this.quill.getSelection();if(!e)return;let[i,r,n]=this.getTable(e);if(null==n)return;let s=r.rowOffset();i.insertRow(s+t),this.quill.update(rf.sources.USER),t>0?this.quill.setSelection(e,rf.sources.SILENT):this.quill.setSelection(e.index+r.children.length,e.length,rf.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){let i=this.quill.getSelection();if(null==i)return;let r=Array(t).fill(0).reduce(t=>{let i=Array(e).fill("\n").join("");return t.insert(i,{table:n1()})},new ic().retain(i.index));this.quill.updateContents(r,rf.sources.USER),this.quill.setSelection(i.index,rf.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(rf.events.SCROLL_OPTIMIZE,t=>{t.some(t=>!!["TD","TR","TBODY","TABLE"].includes(t.target.tagName)&&(this.quill.once(rf.events.TEXT_CHANGE,(t,e,i)=>{i===rf.sources.USER&&this.balanceTables()}),!0))})}}let n2=iZ("quill:toolbar");class n5 extends i9{constructor(t,e){if(super(t,e),Array.isArray(this.options.container)){let e=document.createElement("div");e.setAttribute("role","toolbar"),function(t,e){Array.isArray(e[0])||(e=[e]),e.forEach(e=>{let i=document.createElement("span");i.classList.add("ql-formats"),e.forEach(t=>{if("string"==typeof t)n4(i,t);else{let e=Object.keys(t)[0],r=t[e];Array.isArray(r)?function(t,e,i){let r=document.createElement("select");r.classList.add(`ql-${e}`),i.forEach(t=>{let e=document.createElement("option");!1!==t?e.setAttribute("value",String(t)):e.setAttribute("selected","selected"),r.appendChild(e)}),t.appendChild(r)}(i,e,r):n4(i,e,r)}}),t.appendChild(i)})}(e,this.options.container),t.container?.parentNode?.insertBefore(e,t.container),this.container=e}else"string"==typeof this.options.container?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){n2.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(t=>{let e=this.options.handlers?.[t];e&&this.addHandler(t,e)}),Array.from(this.container.querySelectorAll("button, select")).forEach(t=>{this.attach(t)}),this.quill.on(rf.events.EDITOR_CHANGE,()=>{let[t]=this.quill.selection.getRange();this.update(t)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(t=>0===t.indexOf("ql-"));if(!e)return;if(e=e.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[e]&&null==this.quill.scroll.query(e)){n2.warn("ignoring attaching to nonexistent format",e,t);return}let i="SELECT"===t.tagName?"change":"click";t.addEventListener(i,i=>{let r;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;let e=t.options[t.selectedIndex];r=!e.hasAttribute("selected")&&(e.value||!1)}else r=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),i.preventDefault();this.quill.focus();let[n]=this.quill.selection.getRange();if(null!=this.handlers[e])this.handlers[e].call(this,r);else if(this.quill.scroll.query(e).prototype instanceof ii){if(!(r=prompt(`Enter ${e}`)))return;this.quill.updateContents(new ic().retain(n.index).delete(n.length).insert({[e]:r}),rf.sources.USER)}else this.quill.format(e,r,rf.sources.USER);this.update(n)}),this.controls.push([e,t])}update(t){let e=null==t?{}:this.quill.getFormat(t);this.controls.forEach(i=>{let[r,n]=i;if("SELECT"===n.tagName){let i=null;if(null==t)i=null;else if(null==e[r])i=n.querySelector("option[selected]");else if(!Array.isArray(e[r])){let t=e[r];"string"==typeof t&&(t=t.replace(/"/g,'\\"')),i=n.querySelector(`option[value="${t}"]`)}null==i?(n.value="",n.selectedIndex=-1):i.selected=!0}else if(null==t)n.classList.remove("ql-active"),n.setAttribute("aria-pressed","false");else if(n.hasAttribute("value")){let t=e[r],i=t===n.getAttribute("value")||null!=t&&t.toString()===n.getAttribute("value")||null==t&&!n.getAttribute("value");n.classList.toggle("ql-active",i),n.setAttribute("aria-pressed",i.toString())}else{let t=null!=e[r];n.classList.toggle("ql-active",t),n.setAttribute("aria-pressed",t.toString())}})}}function n4(t,e,i){let r=document.createElement("button");r.setAttribute("type","button"),r.classList.add(`ql-${e}`),r.setAttribute("aria-pressed","false"),null!=i?(r.value=i,r.setAttribute("aria-label",`${e}: ${i}`)):r.setAttribute("aria-label",e),t.appendChild(r)}n5.DEFAULTS={},n5.DEFAULTS={container:null,handlers:{clean(){let t=this.quill.getSelection();null!=t&&(0===t.length?Object.keys(this.quill.getFormat()).forEach(t=>{null!=this.quill.scroll.query(t,eD.INLINE)&&this.quill.format(t,!1,rf.sources.USER)}):this.quill.removeFormat(t.index,t.length,rf.sources.USER))},direction(t){let{align:e}=this.quill.getFormat();"rtl"===t&&null==e?this.quill.format("align","right",rf.sources.USER):t||"right"!==e||this.quill.format("align",!1,rf.sources.USER),this.quill.format("direction",t,rf.sources.USER)},indent(t){let e=this.quill.getSelection(),i=this.quill.getFormat(e),r=parseInt(i.indent||0,10);if("+1"===t||"-1"===t){let e="+1"===t?1:-1;"rtl"===i.direction&&(e*=-1),this.quill.format("indent",r+e,rf.sources.USER)}},link(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,rf.sources.USER)},list(t){let e=this.quill.getSelection(),i=this.quill.getFormat(e);"check"===t?"checked"===i.list||"unchecked"===i.list?this.quill.format("list",!1,rf.sources.USER):this.quill.format("list","unchecked",rf.sources.USER):this.quill.format("list",t,rf.sources.USER)}}};let n3='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',n6={align:{"":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',center:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',right:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',justify:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>'},background:'<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',blockquote:'<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',bold:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',clean:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',code:n3,"code-block":n3,color:'<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',direction:{"":'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',rtl:'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>'},formula:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',header:{1:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',2:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',3:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',4:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',5:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',6:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>'},italic:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',image:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',indent:{"+1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',"-1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>'},link:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',list:{bullet:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',check:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',ordered:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>'},script:{sub:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',super:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>'},strike:'<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',table:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',underline:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',video:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>'},n8=0;function n9(t,e){t.setAttribute(e,`${"true"!==t.getAttribute(e)}`)}class n7{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",t=>{switch(t.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),t.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),n9(this.label,"aria-expanded"),n9(this.options,"aria-hidden")}buildItem(t){let e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");let i=t.getAttribute("value");return i&&e.setAttribute("data-value",i),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",t=>{switch(t.key){case"Enter":this.selectItem(e,!0),t.preventDefault();break;case"Escape":this.escape(),t.preventDefault()}}),e}buildLabel(){let t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>',t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){let t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${n8}`,n8+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{let i=this.buildItem(e);t.appendChild(i),!0===e.selected&&this.selectItem(i)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.container.querySelector(".ql-selected");t!==i&&(null!=i&&i.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){let e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);let e=null!=t&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}let st=n7;class se extends st{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(t=>{t.classList.add("ql-primary")})}buildItem(t){let e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);let i=this.label.querySelector(".ql-color-label"),r=t&&t.getAttribute("data-value")||"";i&&("line"===i.tagName?i.style.stroke=r:i.style.fill=r)}}class si extends st{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(t=>{t.innerHTML=e[t.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);let i=t||this.defaultItem;if(null!=i){if(this.label.innerHTML===i.innerHTML)return;this.label.innerHTML=i.innerHTML}}}let sr=t=>{let{overflowY:e}=getComputedStyle(t,null);return"visible"!==e&&"clip"!==e};class sn{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,sr(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){let e=t.left+t.width/2-this.root.offsetWidth/2,i=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${i}px`,this.root.classList.remove("ql-flip");let r=this.boundsContainer.getBoundingClientRect(),n=this.root.getBoundingClientRect(),s=0;if(n.right>r.right&&(s=r.right-n.right,this.root.style.left=`${e+s}px`),n.left<r.left&&(s=r.left-n.left,this.root.style.left=`${e+s}px`),n.bottom>r.bottom){let e=n.bottom-n.top,r=t.bottom-t.top+e;this.root.style.top=`${i-r}px`,this.root.classList.add("ql-flip")}return s}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}let ss=sn,sl=[!1,"center","right","justify"],so=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],sa=[!1,"serif","monospace"],sc=["1","2","3",!1],sh=["small",!1,"large","huge"];class su extends rr{constructor(t,e){super(t,e);let i=e=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",i);return}null==this.tooltip||this.tooltip.root.contains(e.target)||document.activeElement===this.tooltip.textbox||this.quill.hasFocus()||this.tooltip.hide(),null!=this.pickers&&this.pickers.forEach(t=>{t.container.contains(e.target)||t.close()})};t.emitter.listenDOM("click",document.body,i)}addModule(t){let e=super.addModule(t);return"toolbar"===t&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(t=>{(t.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&null!=e[i=i.slice(3)]){if("direction"===i)t.innerHTML=e[i][""]+e[i].rtl;else if("string"==typeof e[i])t.innerHTML=e[i];else{let r=t.value||"";null!=r&&e[i][r]&&(t.innerHTML=e[i][r])}}})})}buildPickers(t,e){this.pickers=Array.from(t).map(t=>{if(t.classList.contains("ql-align")&&(null==t.querySelector("option")&&sf(t,sl),"object"==typeof e.align))return new si(t,e.align);if(t.classList.contains("ql-background")||t.classList.contains("ql-color")){let i=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&sf(t,so,"background"===i?"#ffffff":"#000000"),new se(t,e[i])}return null==t.querySelector("option")&&(t.classList.contains("ql-font")?sf(t,sa):t.classList.contains("ql-header")?sf(t,sc):t.classList.contains("ql-size")&&sf(t,sh)),new st(t)}),this.quill.on(iX.events.EDITOR_CHANGE,()=>{this.pickers.forEach(t=>{t.update()})})}}su.DEFAULTS=eB({},rr.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let t=this.container.querySelector("input.ql-image[type=file]");null==t&&((t=document.createElement("input")).setAttribute("type","file"),t.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),t.classList.add("ql-image"),t.addEventListener("change",()=>{let e=this.quill.getSelection(!0);this.quill.uploader.upload(e,t.files),t.value=""}),this.container.appendChild(t)),t.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class sd extends ss{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{"Enter"===t.key?(this.save(),t.preventDefault()):"Escape"===t.key&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null==this.textbox)return;null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");let i=this.quill.getBounds(this.quill.selection.savedRange);null!=i&&this.position(i),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{let{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,iX.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,iX.sources.USER)),this.quill.root.scrollTop=e;break}case"video":var e;let i;t=(i=(e=t).match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||e.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?`${i[1]||"https"}://www.youtube.com/embed/${i[2]}?showinfo=0`:(i=e.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${i[1]||"https"}://player.vimeo.com/video/${i[2]}/`:e;case"formula":{if(!t)break;let e=this.quill.getSelection(!0);if(null!=e){let i=e.index+e.length;this.quill.insertEmbed(i,this.root.getAttribute("data-mode"),t,iX.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(i+1," ",iX.sources.USER),this.quill.setSelection(i+2,iX.sources.USER)}}}this.textbox.value="",this.hide()}}function sf(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach(e=>{let r=document.createElement("option");e===i?r.setAttribute("selected","selected"):r.setAttribute("value",String(e)),t.appendChild(r)})}let sp=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class sg extends sd{static TEMPLATE='<span class="ql-tooltip-arrow"></span><div class="ql-tooltip-editor"><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-close"></a></div>';constructor(t,e){super(t,e),this.quill.on(iX.events.EDITOR_CHANGE,(t,e,i,r)=>{if(t===iX.events.SELECTION_CHANGE){if(null!=e&&e.length>0&&r===iX.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;let t=this.quill.getLines(e.index,e.length);if(1===t.length){let t=this.quill.getBounds(e);null!=t&&this.position(t)}else{let i=t[t.length-1],r=this.quill.getIndex(i),n=Math.min(i.length()-1,e.index+e.length-r),s=this.quill.getBounds(new iY(r,n));null!=s&&this.position(s)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()}})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(iX.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;let t=this.quill.getSelection();if(null!=t){let e=this.quill.getBounds(t);null!=e&&this.position(e)}},1)})}cancel(){this.show()}position(t){let e=super.position(t),i=this.root.querySelector(".ql-tooltip-arrow");return i.style.marginLeft="",0!==e&&(i.style.marginLeft=`${-1*e-i.offsetWidth/2}px`),e}}class sm extends su{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=sp),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new sg(this.quill,this.options.bounds),null!=t.container&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),n6),this.buildPickers(t.container.querySelectorAll("select"),n6))}}sm.DEFAULTS=eB({},su.DEFAULTS,{modules:{toolbar:{handlers:{link(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1,rf.sources.USER)}}}}});let sb=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class sy extends sd{static TEMPLATE='<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-action"></a><a class="ql-remove"></a>';preview=this.root.querySelector("a.ql-preview");listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",t=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),t.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",t=>{if(null!=this.linkRange){let t=this.linkRange;this.restoreFocus(),this.quill.formatText(t,"link",!1,iX.sources.USER),delete this.linkRange}t.preventDefault(),this.hide()}),this.quill.on(iX.events.SELECTION_CHANGE,(t,e,i)=>{if(null!=t){if(0===t.length&&i===iX.sources.USER){let[e,i]=this.quill.scroll.descendant(nR,t.index);if(null!=e){this.linkRange=new iY(t.index-i,e.length());let r=nR.formats(e.domNode);this.preview.textContent=r,this.preview.setAttribute("href",r),this.show();let n=this.quill.getBounds(this.linkRange);null!=n&&this.position(n);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}class sv extends su{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=sb),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){null!=t.container&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),n6),this.buildPickers(t.container.querySelectorAll("select"),n6),this.tooltip=new sy(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,i)=>{t.handlers.link.call(t,!i.format.link)}))}}sv.DEFAULTS=eB({},su.DEFAULTS,{modules:{toolbar:{handlers:{link(t){if(t){let t=this.quill.getSelection();if(null==t||0===t.length)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&0!==e.indexOf("mailto:")&&(e=`mailto:${e}`);let{tooltip:i}=this.quill.theme;i.edit("link",e)}else this.quill.format("link",!1,rf.sources.USER)}}}}}),rf.register({"attributors/attribute/direction":rU,"attributors/class/align":rL,"attributors/class/background":rj,"attributors/class/color":rT,"attributors/class/direction":rP,"attributors/class/font":r$,"attributors/class/size":rK,"attributors/style/align":rS,"attributors/style/background":rR,"attributors/style/color":rC,"attributors/style/direction":rz,"attributors/style/font":rV,"attributors/style/size":rW},!0),rf.register({"formats/align":rL,"formats/direction":rP,"formats/indent":nk,"formats/background":rR,"formats/color":rC,"formats/font":r$,"formats/size":rK,"formats/blockquote":n_,"formats/code-block":rM,"formats/header":nL,"formats/list":nO,"formats/bold":nC,"formats/code":rB,"formats/italic":nj,"formats/link":nR,"formats/script":nM,"formats/strike":nB,"formats/underline":nD,"formats/formula":nU,"formats/image":nz,"formats/video":n$,"modules/syntax":nG,"modules/table":n0,"modules/toolbar":n5,"themes/bubble":sm,"themes/snow":sv,"ui/icons":n6,"ui/picker":st,"ui/icon-picker":si,"ui/color-picker":se,"ui/tooltip":ss},!0);let sx=rf;class sN extends n.Component{constructor(t){super(t),this.editingAreaRef=(0,n.createRef)(),this.dirtyProps=["modules","formats","bounds","theme","children"],this.cleanProps=["id","className","style","placeholder","tabIndex","onChange","onChangeSelection","onFocus","onBlur","onKeyPress","onKeyDown","onKeyUp"],this.state={generation:0},this.selection=null,this.onEditorChange=(t,e,i,r)=>{"text-change"===t?this.onEditorChangeText?.(this.editor.root.innerHTML,e,r,this.unprivilegedEditor):"selection-change"===t&&this.onEditorChangeSelection?.(e,r,this.unprivilegedEditor)};let e=this.isControlled()?t.value:t.defaultValue;this.value=e??""}validateProps(t){if(n.Children.count(t.children)>1)throw Error("The Quill editing area can only be composed of a single React element.");if(n.Children.count(t.children)){let e=n.Children.only(t.children);if(e?.type==="textarea")throw Error("Quill does not support editing on a <textarea>. Use a <div> instead.")}if(this.lastDeltaChangeSet&&t.value===this.lastDeltaChangeSet)throw Error("You are passing the `delta` object from the `onChange` event back as `value`. You most probably want `editor.getContents()` instead. See: https://github.com/zenoamaro/react-quill#using-deltas")}shouldComponentUpdate(t,e){if(this.validateProps(t),!this.editor||this.state.generation!==e.generation)return!0;if("value"in t){let e=this.getEditorContents(),i=t.value??"";this.isEqualValue(i,e)||this.setEditorContents(this.editor,i)}return t.readOnly!==this.props.readOnly&&this.setEditorReadOnly(this.editor,t.readOnly),[...this.cleanProps,...this.dirtyProps].some(e=>!t9(t[e],this.props[e]))}shouldComponentRegenerate(t){return this.dirtyProps.some(e=>!t9(t[e],this.props[e]))}componentDidMount(){this.instantiateEditor(),this.setEditorContents(this.editor,this.getEditorContents())}componentWillUnmount(){this.destroyEditor()}componentDidUpdate(t,e){if(this.editor&&this.shouldComponentRegenerate(t)){let t=this.editor.getContents(),e=this.editor.getSelection();this.regenerationSnapshot={delta:t,selection:e},this.setState({generation:this.state.generation+1}),this.destroyEditor()}if(this.state.generation!==e.generation){let{delta:t,selection:e}=this.regenerationSnapshot;delete this.regenerationSnapshot,this.instantiateEditor();let i=this.editor;i.setContents(t),sE(()=>this.setEditorSelection(i,e))}}instantiateEditor(){this.editor?this.hookEditor(this.editor):this.editor=this.createEditor(this.getEditingArea(),this.getEditorConfig())}destroyEditor(){if(!this.editor)return;this.unhookEditor(this.editor);let t=this.props.modules?.toolbar;if(!("object"==typeof t&&t&&"container"in t&&"string"==typeof t.container||"string"==typeof t)){let t=document.querySelector(".ql-toolbar");t&&t.remove()}delete this.editor}isControlled(){return"value"in this.props}getEditorConfig(){return{bounds:this.props.bounds,formats:this.props.formats,modules:this.props.modules,placeholder:this.props.placeholder,readOnly:this.props.readOnly,tabIndex:this.props.tabIndex,theme:this.props.theme}}getEditor(){if(!this.editor)throw Error("Accessing non-instantiated editor");return this.editor}createEditor(t,e){let i=new sx(t,e);return null!=e.tabIndex&&this.setEditorTabIndex(i,e.tabIndex),this.hookEditor(i),i}hookEditor(t){this.unprivilegedEditor=this.makeUnprivilegedEditor(t),t.on("editor-change",this.onEditorChange)}unhookEditor(t){t.off("editor-change",this.onEditorChange)}getEditorContents(){return this.value}getEditorSelection(){return this.selection}isDelta(t){return t&&t.ops}isEqualValue(t,e){return this.isDelta(t)&&this.isDelta(e)?t9(t.ops,e.ops):t9(t,e)}setEditorContents(t,e){this.value=e;let i=this.getEditorSelection();"string"==typeof e?t.setContents(t.clipboard.convert({html:e})):t.setContents(e),sE(()=>this.setEditorSelection(t,i))}setEditorSelection(t,e){if(this.selection=e,e){let i=t.getLength();e.index=Math.max(0,Math.min(e.index,i-1)),e.length=Math.max(0,Math.min(e.length,i-1-e.index)),t.setSelection(e)}}setEditorTabIndex(t,e){t?.scroll?.domNode&&(t.scroll.domNode.tabIndex=e)}setEditorReadOnly(t,e){e?t.disable():t.enable()}makeUnprivilegedEditor(t){return{getHTML:()=>t.root.innerHTML,getSemanticHTML:t.getSemanticHTML.bind(t),getLength:t.getLength.bind(t),getText:t.getText.bind(t),getContents:t.getContents.bind(t),getSelection:t.getSelection.bind(t),getBounds:t.getBounds.bind(t)}}getEditingArea(){let t=this.editingAreaRef.current;if(!t)throw Error("Cannot find element for editing area");if(3===t.nodeType)throw Error("Editing area cannot be a text node");return t}renderEditingArea(){let{children:t,preserveWhitespace:e}=this.props,{generation:i}=this.state,r={key:i,ref:this.editingAreaRef};return n.Children.count(t)?n.cloneElement(n.Children.only(t),r):e?n.createElement("pre",{...r}):n.createElement("div",{...r})}render(){return n.createElement("div",{id:this.props.id,style:this.props.style,key:this.state.generation,className:`quill ${this.props.className??""}`,onKeyPress:this.props.onKeyPress,onKeyDown:this.props.onKeyDown,onKeyUp:this.props.onKeyUp},this.renderEditingArea())}onEditorChangeText(t,e,i,r){if(!this.editor)return;let n=this.isDelta(this.value)?r.getContents():r.getHTML();n!==this.getEditorContents()&&(this.lastDeltaChangeSet=e,this.value=n,this.props.onChange?.(t,e,i,r))}onEditorChangeSelection(t,e,i){if(!this.editor)return;let r=this.getEditorSelection(),n=!r&&t,s=r&&!t;!t9(t,r)&&(this.selection=t,this.props.onChangeSelection?.(t,e,i),n?this.props.onFocus?.(t,e,i):s&&this.props.onBlur?.(r,e,i))}focus(){this.editor&&this.editor.focus()}blur(){this.editor&&(this.selection=null,this.editor.blur())}}function sE(t){Promise.resolve().then(t)}sN.displayName="React Quill",sN.Quill=sx,sN.defaultProps={theme:"snow",modules:{},readOnly:!1};let sA=sN},46120:(t,e,i)=>{t=i.nmd(t);var r="__lodash_hash_undefined__",n="[object Arguments]",s="[object Boolean]",l="[object Date]",o="[object Function]",a="[object GeneratorFunction]",c="[object Map]",h="[object Number]",u="[object Object]",d="[object Promise]",f="[object RegExp]",p="[object Set]",g="[object String]",m="[object Symbol]",b="[object WeakMap]",y="[object ArrayBuffer]",v="[object DataView]",x="[object Float32Array]",N="[object Float64Array]",E="[object Int8Array]",A="[object Int16Array]",w="[object Int32Array]",q="[object Uint8Array]",k="[object Uint8ClampedArray]",_="[object Uint16Array]",L="[object Uint32Array]",S=/\w*$/,O=/^\[object .+?Constructor\]$/,T=/^(?:0|[1-9]\d*)$/,C={};C[n]=C["[object Array]"]=C[y]=C[v]=C[s]=C[l]=C[x]=C[N]=C[E]=C[A]=C[w]=C[c]=C[h]=C[u]=C[f]=C[p]=C[g]=C[m]=C[q]=C[k]=C[_]=C[L]=!0,C["[object Error]"]=C[o]=C[b]=!1;var j="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,R="object"==typeof self&&self&&self.Object===Object&&self,I=j||R||Function("return this")(),M=e&&!e.nodeType&&e,B=M&&t&&!t.nodeType&&t,D=B&&B.exports===M;function U(t,e){return t.set(e[0],e[1]),t}function P(t,e){return t.add(e),t}function z(t,e,i,r){var n=-1,s=t?t.length:0;for(r&&s&&(i=t[++n]);++n<s;)i=e(i,t[n],n,t);return i}function F(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function $(t){var e=-1,i=Array(t.size);return t.forEach(function(t,r){i[++e]=[r,t]}),i}function H(t,e){return function(i){return t(e(i))}}function V(t){var e=-1,i=Array(t.size);return t.forEach(function(t){i[++e]=t}),i}var K=Array.prototype,W=Function.prototype,Z=Object.prototype,G=I["__core-js_shared__"],X=function(){var t=/[^.]+$/.exec(G&&G.keys&&G.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),Q=W.toString,Y=Z.hasOwnProperty,J=Z.toString,tt=RegExp("^"+Q.call(Y).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),te=D?I.Buffer:void 0,ti=I.Symbol,tr=I.Uint8Array,tn=H(Object.getPrototypeOf,Object),ts=Object.create,tl=Z.propertyIsEnumerable,to=K.splice,ta=Object.getOwnPropertySymbols,tc=te?te.isBuffer:void 0,th=H(Object.keys,Object),tu=tj(I,"DataView"),td=tj(I,"Map"),tf=tj(I,"Promise"),tp=tj(I,"Set"),tg=tj(I,"WeakMap"),tm=tj(Object,"create"),tb=tB(tu),ty=tB(td),tv=tB(tf),tx=tB(tp),tN=tB(tg),tE=ti?ti.prototype:void 0,tA=tE?tE.valueOf:void 0;function tw(t){var e=-1,i=t?t.length:0;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function tq(t){var e=-1,i=t?t.length:0;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function tk(t){var e=-1,i=t?t.length:0;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function t_(t){this.__data__=new tq(t)}function tL(t,e,i){var r=t[e];Y.call(t,e)&&tD(r,i)&&(void 0!==i||e in t)||(t[e]=i)}function tS(t,e){for(var i=t.length;i--;)if(tD(t[i][0],e))return i;return -1}function tO(t){var e=new t.constructor(t.byteLength);return new tr(e).set(new tr(t)),e}function tT(t,e,i,r){i||(i={});for(var n=-1,s=e.length;++n<s;){var l=e[n],o=r?r(i[l],t[l],l,i,t):void 0;tL(i,l,void 0===o?t[l]:o)}return i}function tC(t,e){var i,r,n=t.__data__;return("string"==(r=typeof(i=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==i:null===i)?n["string"==typeof e?"string":"hash"]:n.map}function tj(t,e){var i,r=null==t?void 0:t[e];return!(!t$(r)||(i=r,X&&X in i))&&(tF(r)||F(r)?tt:O).test(tB(r))?r:void 0}tw.prototype.clear=function(){this.__data__=tm?tm(null):{}},tw.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},tw.prototype.get=function(t){var e=this.__data__;if(tm){var i=e[t];return i===r?void 0:i}return Y.call(e,t)?e[t]:void 0},tw.prototype.has=function(t){var e=this.__data__;return tm?void 0!==e[t]:Y.call(e,t)},tw.prototype.set=function(t,e){return this.__data__[t]=tm&&void 0===e?r:e,this},tq.prototype.clear=function(){this.__data__=[]},tq.prototype.delete=function(t){var e=this.__data__,i=tS(e,t);return!(i<0)&&(i==e.length-1?e.pop():to.call(e,i,1),!0)},tq.prototype.get=function(t){var e=this.__data__,i=tS(e,t);return i<0?void 0:e[i][1]},tq.prototype.has=function(t){return tS(this.__data__,t)>-1},tq.prototype.set=function(t,e){var i=this.__data__,r=tS(i,t);return r<0?i.push([t,e]):i[r][1]=e,this},tk.prototype.clear=function(){this.__data__={hash:new tw,map:new(td||tq),string:new tw}},tk.prototype.delete=function(t){return tC(this,t).delete(t)},tk.prototype.get=function(t){return tC(this,t).get(t)},tk.prototype.has=function(t){return tC(this,t).has(t)},tk.prototype.set=function(t,e){return tC(this,t).set(t,e),this},t_.prototype.clear=function(){this.__data__=new tq},t_.prototype.delete=function(t){return this.__data__.delete(t)},t_.prototype.get=function(t){return this.__data__.get(t)},t_.prototype.has=function(t){return this.__data__.has(t)},t_.prototype.set=function(t,e){var i=this.__data__;if(i instanceof tq){var r=i.__data__;if(!td||r.length<199)return r.push([t,e]),this;i=this.__data__=new tk(r)}return i.set(t,e),this};var tR=ta?H(ta,Object):function(){return[]},tI=function(t){return J.call(t)};function tM(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Z)}function tB(t){if(null!=t){try{return Q.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tD(t,e){return t===e||t!=t&&e!=e}(tu&&tI(new tu(new ArrayBuffer(1)))!=v||td&&tI(new td)!=c||tf&&tI(tf.resolve())!=d||tp&&tI(new tp)!=p||tg&&tI(new tg)!=b)&&(tI=function(t){var e=J.call(t),i=e==u?t.constructor:void 0,r=i?tB(i):void 0;if(r)switch(r){case tb:return v;case ty:return c;case tv:return d;case tx:return p;case tN:return b}return e});var tU=Array.isArray;function tP(t){var e;return null!=t&&"number"==typeof(e=t.length)&&e>-1&&e%1==0&&e<=0x1fffffffffffff&&!tF(t)}var tz=tc||function(){return!1};function tF(t){var e=t$(t)?J.call(t):"";return e==o||e==a}function t$(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function tH(t){return tP(t)?function(t,e){var i,r,s,l,o,a=tU(t)||(s=r=i=t)&&"object"==typeof s&&tP(r)&&Y.call(i,"callee")&&(!tl.call(i,"callee")||J.call(i)==n)?function(t,e){for(var i=-1,r=Array(t);++i<t;)r[i]=e(i);return r}(t.length,String):[],c=a.length,h=!!c;for(var u in t){Y.call(t,u)&&!(h&&("length"==u||(l=u,(o=null==(o=c)?0x1fffffffffffff:o)&&("number"==typeof l||T.test(l))&&l>-1&&l%1==0&&l<o)))&&a.push(u)}return a}(t):function(t){if(!tM(t))return th(t);var e=[];for(var i in Object(t))Y.call(t,i)&&"constructor"!=i&&e.push(i);return e}(t)}t.exports=function(t){return function t(e,i,r,d,b,O,T){if(d&&(j=O?d(e,b,O,T):d(e)),void 0!==j)return j;if(!t$(e))return e;var j,R=tU(e);if(R){if(M=(I=e).length,B=I.constructor(M),M&&"string"==typeof I[0]&&Y.call(I,"index")&&(B.index=I.index,B.input=I.input),j=B,!i)return function(t,e){var i=-1,r=t.length;for(e||(e=Array(r));++i<r;)e[i]=t[i];return e}(e,j)}else{var I,M,B,D,H,K,W,Z,G=tI(e),X=G==o||G==a;if(tz(e))return function(t,e){if(e)return t.slice();var i=new t.constructor(t.length);return t.copy(i),i}(e,i);if(G==u||G==n||X&&!O){if(F(e))return O?e:{};if(j="function"!=typeof(D=X?{}:e).constructor||tM(D)?{}:t$(H=tn(D))?ts(H):{},!i){return K=e,W=(Z=j)&&tT(e,tH(e),Z),tT(K,tR(K),W)}}else{if(!C[G])return O?e:{};j=function(t,e,i,r){var n,o,a,u=t.constructor;switch(e){case y:return tO(t);case s:case l:return new u(+t);case v:return n=r?tO(t.buffer):t.buffer,new t.constructor(n,t.byteOffset,t.byteLength);case x:case N:case E:case A:case w:case q:case k:case _:case L:return o=r?tO(t.buffer):t.buffer,new t.constructor(o,t.byteOffset,t.length);case c:return z(r?i($(t),!0):$(t),U,new t.constructor);case h:case g:return new u(t);case f:return(a=new t.constructor(t.source,S.exec(t))).lastIndex=t.lastIndex,a;case p:return z(r?i(V(t),!0):V(t),P,new t.constructor);case m:return tA?Object(tA.call(t)):{}}}(e,G,t,i)}}T||(T=new t_);var Q=T.get(e);if(Q)return Q;if(T.set(e,j),!R)var J=r?function(t){var e;return e=tH(t),tU(t)?e:function(t,e){for(var i=-1,r=e.length,n=t.length;++i<r;)t[n+i]=e[i];return t}(e,tR(t))}(e):tH(e);return!function(t,e){for(var i=-1,r=t?t.length:0;++i<r&&!1!==e(t[i],i,t););}(J||e,function(n,s){J&&(n=e[s=n]),tL(j,s,t(n,i,r,d,s,e,T))}),j}(t,!0,!0)}},49231:(t,e,i)=>{t=i.nmd(t);var r,n,s="__lodash_hash_undefined__",l="[object Arguments]",o="[object Array]",a="[object Boolean]",c="[object Date]",h="[object Error]",u="[object Function]",d="[object Map]",f="[object Number]",p="[object Object]",g="[object Promise]",m="[object RegExp]",b="[object Set]",y="[object String]",v="[object WeakMap]",x="[object ArrayBuffer]",N="[object DataView]",E=/^\[object .+?Constructor\]$/,A=/^(?:0|[1-9]\d*)$/,w={};w["[object Float32Array]"]=w["[object Float64Array]"]=w["[object Int8Array]"]=w["[object Int16Array]"]=w["[object Int32Array]"]=w["[object Uint8Array]"]=w["[object Uint8ClampedArray]"]=w["[object Uint16Array]"]=w["[object Uint32Array]"]=!0,w[l]=w[o]=w[x]=w[a]=w[N]=w[c]=w[h]=w[u]=w[d]=w[f]=w[p]=w[m]=w[b]=w[y]=w[v]=!1;var q="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,k="object"==typeof self&&self&&self.Object===Object&&self,_=q||k||Function("return this")(),L=e&&!e.nodeType&&e,S=L&&t&&!t.nodeType&&t,O=S&&S.exports===L,T=O&&q.process,C=function(){try{return T&&T.binding&&T.binding("util")}catch(t){}}(),j=C&&C.isTypedArray;function R(t){var e=-1,i=Array(t.size);return t.forEach(function(t,r){i[++e]=[r,t]}),i}function I(t){var e=-1,i=Array(t.size);return t.forEach(function(t){i[++e]=t}),i}var M=Array.prototype,B=Function.prototype,D=Object.prototype,U=_["__core-js_shared__"],P=B.toString,z=D.hasOwnProperty,F=function(){var t=/[^.]+$/.exec(U&&U.keys&&U.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),$=D.toString,H=RegExp("^"+P.call(z).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),V=O?_.Buffer:void 0,K=_.Symbol,W=_.Uint8Array,Z=D.propertyIsEnumerable,G=M.splice,X=K?K.toStringTag:void 0,Q=Object.getOwnPropertySymbols,Y=V?V.isBuffer:void 0,J=(r=Object.keys,n=Object,function(t){return r(n(t))}),tt=tw(_,"DataView"),te=tw(_,"Map"),ti=tw(_,"Promise"),tr=tw(_,"Set"),tn=tw(_,"WeakMap"),ts=tw(Object,"create"),tl=t_(tt),to=t_(te),ta=t_(ti),tc=t_(tr),th=t_(tn),tu=K?K.prototype:void 0,td=tu?tu.valueOf:void 0;function tf(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function tp(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function tg(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}function tm(t){var e=-1,i=null==t?0:t.length;for(this.__data__=new tg;++e<i;)this.add(t[e])}function tb(t){var e=this.__data__=new tp(t);this.size=e.size}function ty(t,e){for(var i=t.length;i--;)if(tL(t[i][0],e))return i;return -1}function tv(t){var e;return null==t?void 0===t?"[object Undefined]":"[object Null]":X&&X in Object(t)?function(t){var e=z.call(t,X),i=t[X];try{t[X]=void 0;var r=!0}catch(t){}var n=$.call(t);return r&&(e?t[X]=i:delete t[X]),n}(t):(e=t,$.call(e))}function tx(t){return tI(t)&&tv(t)==l}function tN(t,e,i,r,n,s){var l=1&i,o=t.length,a=e.length;if(o!=a&&!(l&&a>o))return!1;var c=s.get(t);if(c&&s.get(e))return c==e;var h=-1,u=!0,d=2&i?new tm:void 0;for(s.set(t,e),s.set(e,t);++h<o;){var f=t[h],p=e[h];if(r)var g=l?r(p,f,h,e,t,s):r(f,p,h,t,e,s);if(void 0!==g){if(g)continue;u=!1;break}if(d){if(!function(t,e){for(var i=-1,r=null==t?0:t.length;++i<r;)if(e(t[i],i,t))return!0;return!1}(e,function(t,e){if(!d.has(e)&&(f===t||n(f,t,i,r,s)))return d.push(e)})){u=!1;break}}else if(!(f===p||n(f,p,i,r,s))){u=!1;break}}return s.delete(t),s.delete(e),u}function tE(t){var e;return e=tB(t),tO(t)?e:function(t,e){for(var i=-1,r=e.length,n=t.length;++i<r;)t[n+i]=e[i];return t}(e,tq(t))}function tA(t,e){var i,r,n=t.__data__;return("string"==(r=typeof(i=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==i:null===i)?n["string"==typeof e?"string":"hash"]:n.map}function tw(t,e){var i,r=null==t?void 0:t[e];return!(!tR(r)||(i=r,F&&F in i))&&(tC(r)?H:E).test(t_(r))?r:void 0}tf.prototype.clear=function(){this.__data__=ts?ts(null):{},this.size=0},tf.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=+!!e,e},tf.prototype.get=function(t){var e=this.__data__;if(ts){var i=e[t];return i===s?void 0:i}return z.call(e,t)?e[t]:void 0},tf.prototype.has=function(t){var e=this.__data__;return ts?void 0!==e[t]:z.call(e,t)},tf.prototype.set=function(t,e){var i=this.__data__;return this.size+=+!this.has(t),i[t]=ts&&void 0===e?s:e,this},tp.prototype.clear=function(){this.__data__=[],this.size=0},tp.prototype.delete=function(t){var e=this.__data__,i=ty(e,t);return!(i<0)&&(i==e.length-1?e.pop():G.call(e,i,1),--this.size,!0)},tp.prototype.get=function(t){var e=this.__data__,i=ty(e,t);return i<0?void 0:e[i][1]},tp.prototype.has=function(t){return ty(this.__data__,t)>-1},tp.prototype.set=function(t,e){var i=this.__data__,r=ty(i,t);return r<0?(++this.size,i.push([t,e])):i[r][1]=e,this},tg.prototype.clear=function(){this.size=0,this.__data__={hash:new tf,map:new(te||tp),string:new tf}},tg.prototype.delete=function(t){var e=tA(this,t).delete(t);return this.size-=+!!e,e},tg.prototype.get=function(t){return tA(this,t).get(t)},tg.prototype.has=function(t){return tA(this,t).has(t)},tg.prototype.set=function(t,e){var i=tA(this,t),r=i.size;return i.set(t,e),this.size+=+(i.size!=r),this},tm.prototype.add=tm.prototype.push=function(t){return this.__data__.set(t,s),this},tm.prototype.has=function(t){return this.__data__.has(t)},tb.prototype.clear=function(){this.__data__=new tp,this.size=0},tb.prototype.delete=function(t){var e=this.__data__,i=e.delete(t);return this.size=e.size,i},tb.prototype.get=function(t){return this.__data__.get(t)},tb.prototype.has=function(t){return this.__data__.has(t)},tb.prototype.set=function(t,e){var i=this.__data__;if(i instanceof tp){var r=i.__data__;if(!te||r.length<199)return r.push([t,e]),this.size=++i.size,this;i=this.__data__=new tg(r)}return i.set(t,e),this.size=i.size,this};var tq=Q?function(t){return null==t?[]:function(t,e){for(var i=-1,r=null==t?0:t.length,n=0,s=[];++i<r;){var l=t[i];e(l,i,t)&&(s[n++]=l)}return s}(Q(t=Object(t)),function(e){return Z.call(t,e)})}:function(){return[]},tk=tv;function t_(t){if(null!=t){try{return P.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tL(t,e){return t===e||t!=t&&e!=e}(tt&&tk(new tt(new ArrayBuffer(1)))!=N||te&&tk(new te)!=d||ti&&tk(ti.resolve())!=g||tr&&tk(new tr)!=b||tn&&tk(new tn)!=v)&&(tk=function(t){var e=tv(t),i=e==p?t.constructor:void 0,r=i?t_(i):"";if(r)switch(r){case tl:return N;case to:return d;case ta:return g;case tc:return b;case th:return v}return e});var tS=tx(function(){return arguments}())?tx:function(t){return tI(t)&&z.call(t,"callee")&&!Z.call(t,"callee")},tO=Array.isArray,tT=Y||function(){return!1};function tC(t){if(!tR(t))return!1;var e=tv(t);return e==u||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}function tj(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}function tR(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function tI(t){return null!=t&&"object"==typeof t}var tM=j?function(t){return j(t)}:function(t){return tI(t)&&tj(t.length)&&!!w[tv(t)]};function tB(t){return null!=t&&tj(t.length)&&!tC(t)?function(t,e){var i,r,n=tO(t),s=!n&&tS(t),l=!n&&!s&&tT(t),o=!n&&!s&&!l&&tM(t),a=n||s||l||o,c=a?function(t,e){for(var i=-1,r=Array(t);++i<t;)r[i]=e(i);return r}(t.length,String):[],h=c.length;for(var u in t){z.call(t,u)&&!(a&&("length"==u||l&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||(i=u,(r=null==(r=h)?0x1fffffffffffff:r)&&("number"==typeof i||A.test(i))&&i>-1&&i%1==0&&i<r)))&&c.push(u)}return c}(t):function(t){if(i=(e=t)&&e.constructor,e!==("function"==typeof i&&i.prototype||D))return J(t);var e,i,r=[];for(var n in Object(t))z.call(t,n)&&"constructor"!=n&&r.push(n);return r}(t)}t.exports=function(t,e){return function t(e,i,r,n,s){return e===i||(null!=e&&null!=i&&(tI(e)||tI(i))?function(t,e,i,r,n,s){var u=tO(t),g=tO(e),v=u?o:tk(t),E=g?o:tk(e);v=v==l?p:v,E=E==l?p:E;var A=v==p,w=E==p,q=v==E;if(q&&tT(t)){if(!tT(e))return!1;u=!0,A=!1}if(q&&!A)return s||(s=new tb),u||tM(t)?tN(t,e,i,r,n,s):function(t,e,i,r,n,s,l){switch(i){case N:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case x:if(t.byteLength!=e.byteLength||!s(new W(t),new W(e)))break;return!0;case a:case c:case f:return tL(+t,+e);case h:return t.name==e.name&&t.message==e.message;case m:case y:return t==e+"";case d:var o=R;case b:var u=1&r;if(o||(o=I),t.size!=e.size&&!u)break;var p=l.get(t);if(p)return p==e;r|=2,l.set(t,e);var g=tN(o(t),o(e),r,n,s,l);return l.delete(t),g;case"[object Symbol]":if(td)return td.call(t)==td.call(e)}return!1}(t,e,v,i,r,n,s);if(!(1&i)){var k=A&&z.call(t,"__wrapped__"),_=w&&z.call(e,"__wrapped__");if(k||_){var L=k?t.value():t,S=_?e.value():e;return s||(s=new tb),n(L,S,i,r,s)}}return!!q&&(s||(s=new tb),function(t,e,i,r,n,s){var l=1&i,o=tE(t),a=o.length;if(a!=tE(e).length&&!l)return!1;for(var c=a;c--;){var h=o[c];if(!(l?h in e:z.call(e,h)))return!1}var u=s.get(t);if(u&&s.get(e))return u==e;var d=!0;s.set(t,e),s.set(e,t);for(var f=l;++c<a;){var p=t[h=o[c]],g=e[h];if(r)var m=l?r(g,p,h,e,t,s):r(p,g,h,t,e,s);if(!(void 0===m?p===g||n(p,g,i,r,s):m)){d=!1;break}f||(f="constructor"==h)}if(d&&!f){var b=t.constructor,y=e.constructor;b!=y&&"constructor"in t&&"constructor"in e&&!("function"==typeof b&&b instanceof b&&"function"==typeof y&&y instanceof y)&&(d=!1)}return s.delete(t),s.delete(e),d}(t,e,i,r,n,s))}(e,i,r,n,t,s):e!=e&&i!=i)}(t,e)}},55110:(t,e)=>{"use strict";var i;Object.defineProperty(e,"__esModule",{value:!0}),(i||(i={})).length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"object"==typeof t.retain&&null!==t.retain?1:"string"==typeof t.insert?t.insert.length:1},e.default=i},82661:t=>{"use strict";var e=Object.prototype.hasOwnProperty,i="~";function r(){}function n(t,e,i){this.fn=t,this.context=e,this.once=i||!1}function s(t,e,r,s,l){if("function"!=typeof r)throw TypeError("The listener must be a function");var o=new n(r,s||t,l),a=i?i+e:e;return t._events[a]?t._events[a].fn?t._events[a]=[t._events[a],o]:t._events[a].push(o):(t._events[a]=o,t._eventsCount++),t}function l(t,e){0==--t._eventsCount?t._events=new r:delete t._events[e]}function o(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(i=!1)),o.prototype.eventNames=function(){var t,r,n=[];if(0===this._eventsCount)return n;for(r in t=this._events)e.call(t,r)&&n.push(i?r.slice(1):r);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},o.prototype.listeners=function(t){var e=i?i+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var n=0,s=r.length,l=Array(s);n<s;n++)l[n]=r[n].fn;return l},o.prototype.listenerCount=function(t){var e=i?i+t:t,r=this._events[e];return r?r.fn?1:r.length:0},o.prototype.emit=function(t,e,r,n,s,l){var o=i?i+t:t;if(!this._events[o])return!1;var a,c,h=this._events[o],u=arguments.length;if(h.fn){switch(h.once&&this.removeListener(t,h.fn,void 0,!0),u){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,e),!0;case 3:return h.fn.call(h.context,e,r),!0;case 4:return h.fn.call(h.context,e,r,n),!0;case 5:return h.fn.call(h.context,e,r,n,s),!0;case 6:return h.fn.call(h.context,e,r,n,s,l),!0}for(c=1,a=Array(u-1);c<u;c++)a[c-1]=arguments[c];h.fn.apply(h.context,a)}else{var d,f=h.length;for(c=0;c<f;c++)switch(h[c].once&&this.removeListener(t,h[c].fn,void 0,!0),u){case 1:h[c].fn.call(h[c].context);break;case 2:h[c].fn.call(h[c].context,e);break;case 3:h[c].fn.call(h[c].context,e,r);break;case 4:h[c].fn.call(h[c].context,e,r,n);break;default:if(!a)for(d=1,a=Array(u-1);d<u;d++)a[d-1]=arguments[d];h[c].fn.apply(h[c].context,a)}}return!0},o.prototype.on=function(t,e,i){return s(this,t,e,i,!1)},o.prototype.once=function(t,e,i){return s(this,t,e,i,!0)},o.prototype.removeListener=function(t,e,r,n){var s=i?i+t:t;if(!this._events[s])return this;if(!e)return l(this,s),this;var o=this._events[s];if(o.fn)o.fn!==e||n&&!o.once||r&&o.context!==r||l(this,s);else{for(var a=0,c=[],h=o.length;a<h;a++)(o[a].fn!==e||n&&!o[a].once||r&&o[a].context!==r)&&c.push(o[a]);c.length?this._events[s]=1===c.length?c[0]:c:l(this,s)}return this},o.prototype.removeAllListeners=function(t){var e;return t?(e=i?i+t:t,this._events[e]&&l(this,e)):(this._events=new r,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=i,o.EventEmitter=o,t.exports=o},87311:t=>{function e(t,d,f,p,g){if(t===d)return t?[[0,t]]:[];if(null!=f){var b=function(t,e,i){var r="number"==typeof i?{index:i,length:0}:i.oldRange,n="number"==typeof i?null:i.newRange,s=t.length,l=e.length;if(0===r.length&&(null===n||0===n.length)){var o=r.index,a=t.slice(0,o),c=t.slice(o),h=n?n.index:null;t:{var u=o+l-s;if(null!==h&&h!==u||u<0||u>l)break t;var d=e.slice(0,u),f=e.slice(u);if(f!==c)break t;var p=Math.min(o,u),g=a.slice(0,p),b=d.slice(0,p);if(g!==b)break t;var y=a.slice(p),v=d.slice(p);return m(g,y,v,c)}e:if(null===h||h===o){var d=e.slice(0,o),f=e.slice(o);if(d!==a)break e;var x=Math.min(s-o,l-o),N=c.slice(c.length-x),E=f.slice(f.length-x);if(N!==E)break e;var y=c.slice(0,c.length-x),v=f.slice(0,f.length-x);return m(a,y,v,N)}}if(r.length>0&&n&&0===n.length)i:{var g=t.slice(0,r.index),N=t.slice(r.index+r.length),p=g.length,x=N.length;if(l<p+x)break i;var b=e.slice(0,p),E=e.slice(l-x);if(g!==b||N!==E)break i;var y=t.slice(p,s-x),v=e.slice(p,l-x);return m(g,y,v,N)}return null}(t,d,f);if(b)return b}var y=r(t,d),v=t.substring(0,y);y=s(t=t.substring(y),d=d.substring(y));var x=t.substring(t.length-y),N=function(t,n){if(!t)return[[1,n]];if(!n)return[[-1,t]];var l,o=t.length>n.length?t:n,a=t.length>n.length?n:t,c=o.indexOf(a);if(-1!==c)return l=[[1,o.substring(0,c)],[0,a],[1,o.substring(c+a.length)]],t.length>n.length&&(l[0][0]=l[2][0]=-1),l;if(1===a.length)return[[-1,t],[1,n]];var h=function(t,e){var i,n,l,o,a,c=t.length>e.length?t:e,h=t.length>e.length?e:t;if(c.length<4||2*h.length<c.length)return null;function u(t,e,i){for(var n,l,o,a,c=t.substring(i,i+Math.floor(t.length/4)),h=-1,u="";-1!==(h=e.indexOf(c,h+1));){var d=r(t.substring(i),e.substring(h)),f=s(t.substring(0,i),e.substring(0,h));u.length<f+d&&(u=e.substring(h-f,h)+e.substring(h,h+d),n=t.substring(0,i-f),l=t.substring(i+d),o=e.substring(0,h-f),a=e.substring(h+d))}return 2*u.length>=t.length?[n,l,o,a,u]:null}var d=u(c,h,Math.ceil(c.length/4)),f=u(c,h,Math.ceil(c.length/2));return d||f?(i=f?d&&d[4].length>f[4].length?d:f:d,t.length>e.length?(n=i[0],l=i[1],o=i[2],a=i[3]):(o=i[0],a=i[1],n=i[2],l=i[3]),[n,l,o,a,i[4]]):null}(t,n);if(h){var u=h[0],d=h[1],f=h[2],p=h[3],g=h[4],m=e(u,f),b=e(d,p);return m.concat([[0,g]],b)}return function(t,e){for(var r=t.length,n=e.length,s=Math.ceil((r+n)/2),l=2*s,o=Array(l),a=Array(l),c=0;c<l;c++)o[c]=-1,a[c]=-1;o[s+1]=0,a[s+1]=0;for(var h=r-n,u=h%2!=0,d=0,f=0,p=0,g=0,m=0;m<s;m++){for(var b=-m+d;b<=m-f;b+=2){for(var y,v=s+b,x=(y=b===-m||b!==m&&o[v-1]<o[v+1]?o[v+1]:o[v-1]+1)-b;y<r&&x<n&&t.charAt(y)===e.charAt(x);)y++,x++;if(o[v]=y,y>r)f+=2;else if(x>n)d+=2;else if(u){var N=s+h-b;if(N>=0&&N<l&&-1!==a[N]){var E=r-a[N];if(y>=E)return i(t,e,y,x)}}}for(var A=-m+p;A<=m-g;A+=2){for(var E,N=s+A,w=(E=A===-m||A!==m&&a[N-1]<a[N+1]?a[N+1]:a[N-1]+1)-A;E<r&&w<n&&t.charAt(r-E-1)===e.charAt(n-w-1);)E++,w++;if(a[N]=E,E>r)g+=2;else if(w>n)p+=2;else if(!u){var v=s+h-A;if(v>=0&&v<l&&-1!==o[v]){var y=o[v],x=s+y-v;if(y>=(E=r-E))return i(t,e,y,x)}}}}return[[-1,t],[1,e]]}(t,n)}(t=t.substring(0,t.length-y),d=d.substring(0,d.length-y));return v&&N.unshift([0,v]),x&&N.push([0,x]),u(N,g),p&&function(t){for(var e=!1,i=[],r=0,d=null,f=0,p=0,g=0,m=0,b=0;f<t.length;)0==t[f][0]?(i[r++]=f,p=m,g=b,m=0,b=0,d=t[f][1]):(1==t[f][0]?m+=t[f][1].length:b+=t[f][1].length,d&&d.length<=Math.max(p,g)&&d.length<=Math.max(m,b)&&(t.splice(i[r-1],0,[-1,d]),t[i[r-1]+1][0]=1,r--,f=--r>0?i[r-1]:-1,p=0,g=0,m=0,b=0,d=null,e=!0)),f++;for(e&&u(t),function(t){function e(t,e){if(!t||!e)return 6;var i=t.charAt(t.length-1),r=e.charAt(0),n=i.match(l),s=r.match(l),u=n&&i.match(o),d=s&&r.match(o),f=u&&i.match(a),p=d&&r.match(a),g=f&&t.match(c),m=p&&e.match(h);if(g||m)return 5;if(f||p)return 4;if(n&&!u&&d)return 3;if(u||d)return 2;if(n||s)return 1;return 0}for(var i=1;i<t.length-1;){if(0==t[i-1][0]&&0==t[i+1][0]){var r=t[i-1][1],n=t[i][1],u=t[i+1][1],d=s(r,n);if(d){var f=n.substring(n.length-d);r=r.substring(0,r.length-d),n=f+n.substring(0,n.length-d),u=f+u}for(var p=r,g=n,m=u,b=e(r,n)+e(n,u);n.charAt(0)===u.charAt(0);){r+=n.charAt(0),n=n.substring(1)+u.charAt(0),u=u.substring(1);var y=e(r,n)+e(n,u);y>=b&&(b=y,p=r,g=n,m=u)}t[i-1][1]!=p&&(p?t[i-1][1]=p:(t.splice(i-1,1),i--),t[i][1]=g,m?t[i+1][1]=m:(t.splice(i+1,1),i--))}i++}}(t),f=1;f<t.length;){if(-1==t[f-1][0]&&1==t[f][0]){var y=t[f-1][1],v=t[f][1],x=n(y,v),N=n(v,y);x>=N?(x>=y.length/2||x>=v.length/2)&&(t.splice(f,0,[0,v.substring(0,x)]),t[f-1][1]=y.substring(0,y.length-x),t[f+1][1]=v.substring(x),f++):(N>=y.length/2||N>=v.length/2)&&(t.splice(f,0,[0,y.substring(0,N)]),t[f-1][0]=1,t[f-1][1]=v.substring(0,v.length-N),t[f+1][0]=-1,t[f+1][1]=y.substring(N),f++),f++}f++}}(N),N}function i(t,i,r,n){var s=t.substring(0,r),l=i.substring(0,n),o=t.substring(r),a=i.substring(n),c=e(s,l),h=e(o,a);return c.concat(h)}function r(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var i=0,r=Math.min(t.length,e.length),n=r,s=0;i<n;)t.substring(s,n)==e.substring(s,n)?s=i=n:r=n,n=Math.floor((r-i)/2+i);return d(t.charCodeAt(n-1))&&n--,n}function n(t,e){var i=t.length,r=e.length;if(0==i||0==r)return 0;i>r?t=t.substring(i-r):i<r&&(e=e.substring(0,i));var n=Math.min(i,r);if(t==e)return n;for(var s=0,l=1;;){var o=t.substring(n-l),a=e.indexOf(o);if(-1==a)return s;l+=a,(0==a||t.substring(n-l)==e.substring(0,l))&&(s=l,l++)}}function s(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var i=0,r=Math.min(t.length,e.length),n=r,s=0;i<n;)t.substring(t.length-n,t.length-s)==e.substring(e.length-n,e.length-s)?s=i=n:r=n,n=Math.floor((r-i)/2+i);return f(t.charCodeAt(t.length-n))&&n--,n}var l=/[^a-zA-Z0-9]/,o=/\s/,a=/[\r\n]/,c=/\n\r?\n$/,h=/^\r?\n\r?\n/;function u(t,e){t.push([0,""]);for(var i,n=0,l=0,o=0,a="",c="";n<t.length;){if(n<t.length-1&&!t[n][1]){t.splice(n,1);continue}switch(t[n][0]){case 1:o++,c+=t[n][1],n++;break;case -1:l++,a+=t[n][1],n++;break;case 0:var h=n-o-l-1;if(e){if(h>=0&&g(t[h][1])){var d=t[h][1].slice(-1);if(t[h][1]=t[h][1].slice(0,-1),a=d+a,c=d+c,!t[h][1]){t.splice(h,1),n--;var f=h-1;t[f]&&1===t[f][0]&&(o++,c=t[f][1]+c,f--),t[f]&&-1===t[f][0]&&(l++,a=t[f][1]+a,f--),h=f}}if(p(t[n][1])){var d=t[n][1].charAt(0);t[n][1]=t[n][1].slice(1),a+=d,c+=d}}if(n<t.length-1&&!t[n][1]){t.splice(n,1);break}if(a.length>0||c.length>0){a.length>0&&c.length>0&&(0!==(i=r(c,a))&&(h>=0?t[h][1]+=c.substring(0,i):(t.splice(0,0,[0,c.substring(0,i)]),n++),c=c.substring(i),a=a.substring(i)),0!==(i=s(c,a))&&(t[n][1]=c.substring(c.length-i)+t[n][1],c=c.substring(0,c.length-i),a=a.substring(0,a.length-i)));var m=o+l;0===a.length&&0===c.length?(t.splice(n-m,m),n-=m):0===a.length?(t.splice(n-m,m,[1,c]),n=n-m+1):0===c.length?(t.splice(n-m,m,[-1,a]),n=n-m+1):(t.splice(n-m,m,[-1,a],[1,c]),n=n-m+2)}0!==n&&0===t[n-1][0]?(t[n-1][1]+=t[n][1],t.splice(n,1)):n++,o=0,l=0,a="",c=""}}""===t[t.length-1][1]&&t.pop();var b=!1;for(n=1;n<t.length-1;)0===t[n-1][0]&&0===t[n+1][0]&&(t[n][1].substring(t[n][1].length-t[n-1][1].length)===t[n-1][1]?(t[n][1]=t[n-1][1]+t[n][1].substring(0,t[n][1].length-t[n-1][1].length),t[n+1][1]=t[n-1][1]+t[n+1][1],t.splice(n-1,1),b=!0):t[n][1].substring(0,t[n+1][1].length)==t[n+1][1]&&(t[n-1][1]+=t[n+1][1],t[n][1]=t[n][1].substring(t[n+1][1].length)+t[n+1][1],t.splice(n+1,1),b=!0)),n++;b&&u(t,e)}function d(t){return t>=55296&&t<=56319}function f(t){return t>=56320&&t<=57343}function p(t){return f(t.charCodeAt(0))}function g(t){return d(t.charCodeAt(t.length-1))}function m(t,e,i,r){return g(t)||p(r)?null:function(t){for(var e=[],i=0;i<t.length;i++)t[i][1].length>0&&e.push(t[i]);return e}([[0,t],[-1,e],[1,i],[0,r]])}function b(t,i,r,n){return e(t,i,r,n,!0)}b.INSERT=1,b.DELETE=-1,b.EQUAL=0,t.exports=b}}]);