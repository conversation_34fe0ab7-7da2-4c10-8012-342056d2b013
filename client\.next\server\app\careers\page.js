"use strict";(()=>{var e={};e.id=3846,e.ids=[3846],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},51845:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>x,routeModule:()=>c,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["careers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75559)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/careers/page",pathname:"/careers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},75559:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var s=r(37413),a=r(4536),i=r.n(a);let n=(0,r(26373).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var o=r(49046),l=r(79041),d=r(37075),x=r(17741);async function p(){let e=await (0,l.vA)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.default,{}),(0,s.jsxs)("div",{className:"min-h-screen bg-white text-black dark:bg-black dark:text-white",children:[(0,s.jsxs)("section",{className:"bg-black text-white py-24 text-center px-6 dark:bg-gray-950",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Join Our Mission"}),(0,s.jsx)("p",{className:"text-lg md:text-xl max-w-2xl mx-auto mb-8",children:"We are looking for passionate individuals to shape the future of technology. Explore our open roles and start your journey with us."}),(0,s.jsxs)(i(),{href:"#jobs",className:"inline-flex items-center px-6 py-3 bg-white text-black font-semibold rounded-full hover:bg-gray-200 transition dark:bg-white dark:text-black",children:["View Openings ",(0,s.jsx)(n,{className:"ml-2 h-5 w-5"})]})]}),(0,s.jsx)("section",{id:"jobs",className:"py-20 bg-gray-50 dark:bg-black",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white",children:"Open Positions"}),(0,s.jsx)("div",{className:"grid gap-10 sm:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,s.jsxs)("div",{className:"group bg-white border border-gray-200 rounded-xl shadow-md hover:shadow-xl transition-all p-6 flex flex-col justify-between   dark:bg-gray-800 dark:border-gray-700",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 group-hover:text-[#FD904B] transition-colors dark:text-white",children:e.job_title}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2 dark:text-gray-300",children:e.short_description.length>150?`${e.short_description.slice(0,150)}...`:e.short_description}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mt-4 dark:text-gray-400",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-1 text-[#FD904B]"}),"Morbi"]}),(0,s.jsx)("span",{className:"inline-block mt-3 bg-[#FD904B] text-white text-xs font-medium px-3 py-1 rounded-full",children:"Full-time"})]}),(0,s.jsxs)("div",{className:"mt-6 flex flex-col gap-2",children:[(0,s.jsxs)(i(),{href:`/careers/details/${e.id}`,className:"inline-flex items-center justify-center rounded-md border border-[#FD904B] text-[#FD904B] px-4 py-2 text-sm font-medium hover:bg-[#FD904B] hover:text-white transition",children:["View Details ",(0,s.jsx)(n,{className:"ml-2 h-4 w-4"})]}),(0,s.jsxs)(i(),{href:`/careers/apply/${e.id}`,className:"inline-flex items-center justify-center rounded-md bg-[#FD904B] text-white px-4 py-2 text-sm font-medium hover:bg-[#e67e22] transition",children:["Apply Now ",(0,s.jsx)(n,{className:"ml-2 h-4 w-4"})]})]})]},e.id))})]})})]}),(0,s.jsx)(x.default,{})]})}},79041:(e,t,r)=>{r.d(t,{vA:()=>n,wE:()=>o});var s=r(94612);let a="https://staff.uest.in/api",i="allowonly-uest.in-domain-super-key",n=async()=>(await s.A.get(`${a}/jobs`,{headers:{"x-career-key":i}})).data.data,o=async e=>(await s.A.get(`${a}/jobs/${e}`,{headers:{"x-career-key":i}})).data.data},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},94735:e=>{e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,8721,3641,2800],()=>r(51845));module.exports=s})();