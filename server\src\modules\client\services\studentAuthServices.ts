import prisma from "@/config/prismaClient";
import { Status } from "@prisma/client";

export const findStudentByEmail = (email: string) => {
  return prisma.student.findUnique({ 
    where: { email },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contact: true,
      isVerified: true,
    }
  });
};

export const findUserByContactNo = async (contactNo: string) => {
  return prisma.student.findFirst({ 
    where: { contact: contactNo },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contact: true,
      isVerified: true,
    }
  });
};

export const updateStudentContact = async (email: string, contactNo: string) => {
  return prisma.student.update({
    where: { email },
    data: { contact:contactNo, isVerified: true },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contact: true,
      isVerified: true,
    },
  });
};

export const createOtp = async (contactNo: string) => {
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const expiredAt = new Date(Date.now() + 2 * 60 * 1000);

  // Check recent OTP requests
  const recentOtp = await prisma.otpMessage.findFirst({
    where: {
      contactNo,
    },
    orderBy: { createdAt: 'desc' },
  });

  let requestCount = 1;
  if (recentOtp && recentOtp.createdAt > new Date(Date.now() - 60 * 60 * 1000)) {
    requestCount = recentOtp.requestCount + 1;
  }

  if (requestCount > 3) {
    throw new Error('Too many OTP requests, try again later');
  }

  await prisma.otpMessage.deleteMany({ where: { contactNo } });

  return prisma.otpMessage.create({
    data: {
      id: undefined,
      contactNo,
      otp,
      createdAt: new Date(),
      expiredAt,
      requestCount,
    },
  });
};

export const verifyOtp = async (contactNo: string, otp: string) => {
  const otpRecord = await prisma.otpMessage.findFirst({
    where: {
      contactNo,
      otp,
      expiredAt: { gt: new Date() },
    },
  });

  if (otpRecord) {
    await prisma.otpMessage.deleteMany({ where: { contactNo } });
  }

  return otpRecord;
};

export const createStudent = async (
  firstName: string,
  lastName: string,
  contactNo: string
) => {
  return prisma.student.create({
    data: {
      firstName,
      lastName,
      contact: contactNo,
      isVerified: true,
      createdAt: new Date(),
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contact: true,
      isVerified: true,
    },
  });
};

export const findUserByContactNoForLogin = async (contactNo: string) => {
  return prisma.student.findFirst({
    where: { contact: contactNo },
    select: {
      id: true,
      contact: true,
      firstName: true,
      lastName: true,
      isVerified: true,
    },
  });
};


export const getAllStudent = async (
  skip: number,
  limit: number,
  includeProfile: boolean = false,
  filters: {
    name?: string;
    email?: string;
    contact?: string;
    status?: Status;
  } = {}
) => {
  const students = await prisma.student.findMany({
    skip,
    take: limit,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      contact: true,
      isVerified: true,
      createdAt: true,
      updatedAt: true,
      profile: includeProfile,
    },
    where: {
      ...(filters.name && {
        OR: [
          { firstName: { contains: filters.name, mode: "insensitive" } },
          { lastName: { contains: filters.name, mode: "insensitive" } },
        ],
      }),
      ...(filters.email && {
        email: { contains: filters.email, mode: "insensitive" },
      }),
      ...(filters.contact && {
        contact: { contains: filters.contact, mode: "insensitive" },
      }),
      ...(filters.status && {
        profile: {
          status: filters.status,
        },
      }),
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  const studentIds = students.map((s) => s.id);

  const coins = await prisma.uestCoins.findMany({
    where: {
      modelType: "STUDENT",
      modelId: { in: studentIds },
    },
  });

  const coinsMap = new Map(coins.map((coin) => [coin.modelId, coin.coins]));

  const enrichedStudents = students.map((student) => ({
    ...student,
    coins: coinsMap.get(student.id) ?? 0,
  }));

  return enrichedStudents;
};

export const getAllStudentCount = () => {
  return prisma.student.count();
};

export const updateStudent = async (
  id: string,
  data: {
    firstName?: string;
    lastName?: string;
    contact?: string;
  }
) => {
  return prisma.student.update({
    where: { id },
    data,
  });
};



export const getAllStudentsCounts = async () => {
  const [totalStudents, pending, approved, rejected, totalCoinsResult] =
    await Promise.all([
      prisma.student.count(),
      prisma.student.count({ where: { profile: { status: "PENDING" } } }),
      prisma.student.count({ where: { profile: { status: "APPROVED" } } }),
      prisma.student.count({ where: { profile: { status: "REJECTED" } } }),
      prisma.uestCoins.aggregate({
        where: {
          modelType: "STUDENT",
        },
        _sum: {
          coins: true,
        },
      }),
    ]);

  return {
    total : totalStudents,
    pending,
    approved,
    rejected,
    totalCoins: totalCoinsResult._sum.coins ?? 0,
  };
};
