"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9216],{7583:(e,t,r)=>{r.d(t,{default:()=>o});var i=r(95155);r(12115);var l=r(6874),n=r.n(l),s=r(66766),a=r(29911);let o=()=>(0,i.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,i.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,i.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,i.jsx)(n(),{href:"/",className:"flex items-center gap-2",children:(0,i.jsx)(s.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,i.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:a.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:a.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:a.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:a.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:a.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:a.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:a.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:r,label:l}=e;return(0,i.jsx)("div",{className:"flex flex-col items-center",children:(0,i.jsx)(n(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:l,children:(0,i.jsx)(r,{className:"text-xl text-white hover:text-gray-400 transition"})})},l)})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,i.jsx)("li",{children:(0,i.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,i.jsx)("li",{children:(0,i.jsx)(n(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,i.jsx)("li",{children:(0,i.jsx)(n(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,i.jsx)("li",{children:(0,i.jsx)(n(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,i.jsx)("li",{children:(0,i.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,i.jsx)("li",{children:(0,i.jsx)(n(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,i.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,i.jsx)("p",{children:"Head Office"}),(0,i.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,i.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,i.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,i.jsx)(n(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,i.jsx)(s.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,i.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,i.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(n(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,i.jsx)(n(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},24265:(e,t,r)=>{r.d(t,{b:()=>c});var i=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(47650);var n=r(95155),s=Symbol("radix.slottable");function a(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var s;let e,a;let o=(s=r,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),d=function(e,t){let r={...t};for(let i in t){let l=e[i],n=t[i];/^on[A-Z]/.test(i)?l&&n?r[i]=(...e)=>{n(...e),l(...e)}:l&&(r[i]=l):"style"===i?r[i]={...l,...n}:"className"===i&&(r[i]=[l,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,i=e.map(e=>{let i=l(e,t);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let t=0;t<i.length;t++){let r=i[t];"function"==typeof r?r():l(e[t],null)}}}}(t,o):o),i.cloneElement(r,d)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:l,...s}=e,o=i.Children.toArray(l),d=o.find(a);if(d){let e=d.props.children,l=o.map(t=>t!==d?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,l):null})}return(0,n.jsx)(t,{...s,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),s=i.forwardRef((e,i)=>{let{asChild:l,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(l?r:t,{...s,ref:i})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{}),d=i.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},51154:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55365:(e,t,r)=>{r.d(t,{Fc:()=>o,TN:()=>d});var i=r(95155),l=r(12115),n=r(74466),s=r(59434);let a=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=l.forwardRef((e,t)=>{let{className:r,variant:l,...n}=e;return(0,i.jsx)("div",{ref:t,role:"alert",className:(0,s.cn)(a({variant:l}),r),...n})});o.displayName="Alert",l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,i.jsx)("h5",{ref:t,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",r),...l})}).displayName="AlertTitle";let d=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,i.jsx)("div",{ref:t,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",r),...l})});d.displayName="AlertDescription"},62523:(e,t,r)=>{r.d(t,{p:()=>n});var i=r(95155);r(12115);var l=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,i.jsx)("input",{type:r,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},75937:(e,t,r)=>{r.d(t,{lV:()=>c,MJ:()=>g,Rr:()=>v,zB:()=>f,eI:()=>h,lR:()=>p,C5:()=>b});var i=r(95155),l=r(12115),n=r(66634),s=r(62177),a=r(59434),o=r(24265);function d(e){let{className:t,...r}=e;return(0,i.jsx)(o.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}let c=s.Op,u=l.createContext({}),f=e=>{let{...t}=e;return(0,i.jsx)(u.Provider,{value:{name:t.name},children:(0,i.jsx)(s.xI,{...t})})},m=()=>{let e=l.useContext(u),t=l.useContext(x),{getFieldState:r}=(0,s.xW)(),i=(0,s.lN)({name:e.name}),n=r(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:a}=t;return{id:a,name:e.name,formItemId:"".concat(a,"-form-item"),formDescriptionId:"".concat(a,"-form-item-description"),formMessageId:"".concat(a,"-form-item-message"),...n}},x=l.createContext({});function h(e){let{className:t,...r}=e,n=l.useId();return(0,i.jsx)(x.Provider,{value:{id:n},children:(0,i.jsx)("div",{"data-slot":"form-item",className:(0,a.cn)("grid gap-2",t),...r})})}function p(e){let{className:t,...r}=e,{error:l,formItemId:n}=m();return(0,i.jsx)(d,{"data-slot":"form-label","data-error":!!l,className:(0,a.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...r})}function g(e){let{...t}=e,{error:r,formItemId:l,formDescriptionId:s,formMessageId:a}=m();return(0,i.jsx)(n.DX,{"data-slot":"form-control",id:l,"aria-describedby":r?"".concat(s," ").concat(a):"".concat(s),"aria-invalid":!!r,...t})}function v(e){let{className:t,...r}=e,{formDescriptionId:l}=m();return(0,i.jsx)("p",{"data-slot":"form-description",id:l,className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function b(e){var t;let{className:r,...l}=e,{error:n,formMessageId:s}=m(),o=n?String(null!==(t=null==n?void 0:n.message)&&void 0!==t?t:""):l.children;return o?(0,i.jsx)("p",{"data-slot":"form-message",id:s,className:(0,a.cn)("text-destructive text-sm",r),...l,children:o}):null}},85339:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);