(()=>{var e={};e.id=612,e.ids=[612],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3635:(e,t,r)=>{Promise.resolve().then(r.bind(r,96614))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26452:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\chat\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42123:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var s=r(43210);r(51215);var i=r(11329),a=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=s.forwardRef((e,s)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...o,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),n="horizontal",p=["horizontal","vertical"],u=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:i=n,...u}=e,l=(r=i,p.includes(r))?i:n;return(0,a.jsx)(o.div,{"data-orientation":l,...s?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...u,ref:t})});u.displayName="Separator";var l=u},50491:(e,t,r)=>{Promise.resolve().then(r.bind(r,26452))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58185:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>d,tree:()=>u});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),p={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(t,p);let u={children:["",{children:["classes",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26452)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/classes/chat/page",pathname:"/classes/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96614:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687),i=r(43210),a=r(54864),o=r(12814);function n(){let[e,t]=(0,i.useState)(""),[r,n]=(0,i.useState)(""),{user:p,isAuthenticated:u}=(0,a.d4)(e=>e.user);return(0,s.jsx)(o.A,{userType:"class",isAuthenticated:u,username:e,userId:r,loginPath:"/"})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,8721,9191,9663,6426,2800,7200,6463],()=>r(58185));module.exports=s})();