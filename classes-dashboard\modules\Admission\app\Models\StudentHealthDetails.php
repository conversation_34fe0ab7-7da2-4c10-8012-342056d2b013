<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentHealthDetails extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_health_details';

    protected $fillable = [
        'student_id',
        'eye_sight',
        'hear_ability',
        'allergy_1',
        'allergy_2',
        'any_health_issue',
        'doctors_name',
    ];
}
