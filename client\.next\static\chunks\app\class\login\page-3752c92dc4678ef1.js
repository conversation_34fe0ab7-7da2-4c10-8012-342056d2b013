(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2155],{18097:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var s=a(95155),r=a(70347),l=a(7583),o=a(12115),i=a(30285),n=a(55594),c=a(62177),d=a(90221),m=a(10351),f=a(62523),u=a(55365),g=a(85339),h=a(51154),x=a(75937),N=a(56671),p=a(21751),b=a(35695),j=a(60760),v=a(19320);let y=(e,t)=>n.z.object({contactNo:n.z.string().optional().refine(e=>!e||/^\d{10}$/.test(e),"Please enter a valid 10-digit mobile number"),email:n.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}).refine(a=>"mobile"===e||t?!!a.contactNo:!!a.email,{message:"Either mobile number or email is required",path:["contactNo","email"]}),C=n.z.object({firstName:n.z.string().min(2,"First name is required").regex(/^[a-zA-Z]+$/,"Invalid first name"),lastName:n.z.string().min(2,"Last name is required").regex(/^[a-zA-Z]+$/,"Invalid last name"),contactNo:n.z.string().regex(/^\d{10}$/,"Please enter a valid 10-digit mobile number"),referralCode:n.z.string().optional(),email:n.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}),w=e=>{let{message:t}=e;return t?(0,s.jsxs)(u.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)(u.TN,{className:"text-red-500",children:t})]}):null};function R(){let e=(0,b.useRouter)(),t=(0,b.useSearchParams)(),[a,n]=(0,o.useState)(!0),[u,g]=(0,o.useState)(!1),[R,S]=(0,o.useState)(""),[P,I]=(0,o.useState)(null),[z,k]=(0,o.useState)("mobile"),[F,E]=(0,o.useState)(!1),[A,T]=(0,o.useState)(null),U=(0,c.mN)({resolver:(0,d.u)(y(z,F)),defaultValues:{contactNo:"",email:""},mode:"onChange"}),O=(0,c.mN)({resolver:(0,d.u)(C),defaultValues:{firstName:"",lastName:"",contactNo:"",referralCode:t.get("ref")||localStorage.getItem("referralCode")||"",email:""},mode:"onChange"});(0,o.useEffect)(()=>{localStorage.getItem("user")&&(e.push("/"),setTimeout(()=>{N.oR.error("You are already logged in as a tutor. Please logout first to login as a class.")},500))},[e]),(0,o.useEffect)(()=>{localStorage.getItem("clientToken")&&(e.push("/"),setTimeout(()=>{N.oR.error("You are already logged in as a class.")},500))},[e]),(0,o.useEffect)(()=>{let e=t.get("ref");e&&(I(e),localStorage.setItem("referralCode",e),O.setValue("referralCode",e))},[t,O]),(0,o.useEffect)(()=>{a?(U.reset({contactNo:"mobile"===z?U.getValues().contactNo:"",email:"email"===z?U.getValues().email:""}),U.setFocus("mobile"===z?"contactNo":"email"),U.trigger()):(O.reset({firstName:"",lastName:"",contactNo:"",referralCode:P||"",email:""}),O.setFocus("firstName"),O.trigger())},[a,z,F,P,U,O]);let L=async(a,s)=>{g(!0),S("");try{if(s){let r=await (0,p.Lx)({contactNo:s,email:a});if(!1===r.success){S(r.message||"Authentication failed"),N.oR.error(r.message||"Authentication failed");return}let l=t.get("redirect")||"",o=l?"&redirect=".concat(encodeURIComponent(l)):"";e.push("/verify-otp?contactNo=".concat(s,"&flow=login&email=").concat(encodeURIComponent(a)).concat(o)),N.oR.success("OTP sent successfully. Please check your phone."),U.reset({contactNo:"",email:""}),E(!1),T(null)}else{let s=await (0,p.iM)({email:a});if(!1===s.success){S(s.message||"Email check failed"),N.oR.error(s.message||"Email check failed");return}if(T(s.data),E(s.data.isOldUser),U.setValue("contactNo",s.data.contactNo||""),U.setValue("email",a),U.trigger(),!s.data.isOldUser&&s.data.otpSent){let r=t.get("redirect")||"",l=r?"&redirect=".concat(encodeURIComponent(r)):"";e.push("/verify-otp?contactNo=".concat(s.data.contactNo,"&flow=login&email=").concat(encodeURIComponent(a)).concat(l)),N.oR.success("OTP sent successfully. Please check your phone."),U.reset({contactNo:"",email:""}),E(!1),T(null)}else s.data.isOldUser&&N.oR.info("This email is not linked to a mobile number. Please enter a mobile number to proceed.")}}catch(t){var r,l;let e=(null==t?void 0:null===(l=t.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||"Something went wrong";S(e),N.oR.error(e)}finally{g(!1)}},$=async a=>{g(!0),S("");try{if("email"===z)await L(a.email,F?a.contactNo:void 0);else if("mobile"===z){let s=await (0,p.Lx)({contactNo:a.contactNo});if(!1===s.success){S(s.message||"Authentication failed"),N.oR.error(s.message||"Authentication failed");return}let r=t.get("redirect")||"",l=r?"&redirect=".concat(encodeURIComponent(r)):"";e.push("/verify-otp?contactNo=".concat(a.contactNo,"&flow=login").concat(l)),N.oR.success("OTP sent successfully. Please check your phone."),U.reset({contactNo:"",email:""}),E(!1),T(null)}}catch(t){var s,r;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.message)||"Something went wrong";S(e),N.oR.error(e)}finally{g(!1)}},V=async a=>{g(!0),S("");try{let s={...a,...P?{referralCode:P}:{}},r=await (0,p.DY)(s);if(!1===r.success){S(r.message||"Authentication failed"),N.oR.error(r.message||"Authentication failed");return}let l=t.get("redirect")||"",o=l?"&redirect=".concat(encodeURIComponent(l)):"";e.push("/verify-otp?contactNo=".concat(a.contactNo,"&flow=register&firstName=").concat(encodeURIComponent(a.firstName),"&lastName=").concat(encodeURIComponent(a.lastName)).concat(a.referralCode?"&referralCode=".concat(encodeURIComponent(a.referralCode)):"").concat(a.email?"&email=".concat(encodeURIComponent(a.email)):"").concat(o)),N.oR.success("OTP sent successfully. Please check your phone."),O.reset({firstName:"",lastName:"",contactNo:"",referralCode:"",email:""}),localStorage.removeItem("referralCode"),I(null),E(!1),T(null)}catch(t){var s,r;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.message)||"Something went wrong";S(e),N.oR.error(e)}finally{g(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.default,{}),(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4",children:(0,s.jsx)("p",{className:"text-center text-orange-700 font-medium",children:a?"Coaching Classes Login Portal":"Coaching Classes Registration Portal"})}),(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)(i.$,{variant:a?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat(a?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{n(!0),S(""),k("mobile"),E(!1),T(null),U.reset({contactNo:"",email:""}),U.trigger()},children:"Class Login"}),(0,s.jsx)(i.$,{variant:a?"ghost":"default",className:"px-4 py-2 rounded-lg ".concat(a?"text-gray-600 hover:text-[#ff914d]":"bg-[#ff914d] text-white hover:bg-[#ff914d]/90"),onClick:()=>{n(!1),S(""),k("mobile"),E(!1),T(null),O.reset({firstName:"",lastName:"",contactNo:"",referralCode:P||"",email:""}),O.trigger()},children:"Class Sign Up"})]})}),a&&(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)(i.$,{variant:"mobile"===z?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat("mobile"===z?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{k("mobile"),S(""),E(!1),T(null),U.reset({contactNo:"",email:""}),U.trigger()},children:"Mobile"}),(0,s.jsx)(i.$,{variant:"email"===z?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat("email"===z?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{k("email"),S(""),E(!1),T(null),U.reset({contactNo:"",email:""}),U.trigger()},children:"Email"})]})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:a?"Welcome Back to Your Class Portal":"Register Your Coaching Class"}),(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 mr-3"}),(0,s.jsx)("span",{className:"text-[#ff914d] font-medium",children:"COACHING CLASS PORTAL"}),(0,s.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 ml-3"})]}),P&&!a&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-700 text-center",children:["\uD83C\uDF89 You're joining via referral code: ",(0,s.jsx)("span",{className:"font-semibold",children:P})]})})]}),(0,s.jsx)("div",{children:a?(0,s.jsx)(x.lV,{...U,children:(0,s.jsxs)("form",{onSubmit:U.handleSubmit($),className:"space-y-6",children:[R&&(0,s.jsx)(w,{message:R}),"email"===z&&(0,s.jsx)(x.zB,{control:U.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"Email"}),(0,s.jsx)(x.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.pHD,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(f.p,{type:"email",placeholder:"Enter email address",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),U.formState.touchedFields.email&&(0,s.jsx)(x.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(j.N,{children:("mobile"===z||F)&&(0,s.jsx)(v.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,s.jsx)(x.zB,{control:U.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,s.jsx)(x.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(f.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...t,disabled:!!(null==A?void 0:A.contactNo)&&!F&&"email"===z,autoFocus:F})]})}),U.formState.touchedFields.contactNo&&(0,s.jsx)(x.C5,{className:"text-red-500"})]})}})})}),(0,s.jsx)(i.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:u||!U.formState.isValid,children:u?(0,s.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):"email"!==z||F?"Send OTP to Login":"Check Email"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,s.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})}):(0,s.jsx)(x.lV,{...O,children:(0,s.jsxs)("form",{onSubmit:O.handleSubmit(V),className:"space-y-6",children:[R&&(0,s.jsx)(w,{message:R}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsx)(x.zB,{control:O.control,name:"firstName",render:e=>{let{field:t}=e;return(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"First Name"}),(0,s.jsx)(x.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(f.p,{placeholder:"First Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),O.formState.touchedFields.firstName&&(0,s.jsx)(x.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(x.zB,{control:O.control,name:"lastName",render:e=>{let{field:t}=e;return(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"Last Name"}),(0,s.jsx)(x.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(f.p,{placeholder:"Last Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),O.formState.touchedFields.lastName&&(0,s.jsx)(x.C5,{className:"text-red-500"})]})}})]}),(0,s.jsx)(x.zB,{control:O.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,s.jsx)(x.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(f.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...t})]})}),O.formState.touchedFields.contactNo&&(0,s.jsx)(x.C5,{className:"text-red-500"})]})}}),P&&(0,s.jsx)(x.zB,{control:O.control,name:"referralCode",render:e=>{let{field:t}=e;return(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"Referral Code"}),(0,s.jsx)(x.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(f.p,{placeholder:"Referral Code",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t,disabled:!0})})}),O.formState.touchedFields.referralCode&&(0,s.jsx)(x.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(i.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:u||!O.formState.isValid,children:u?(0,s.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):"Send OTP to Register"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,s.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})})})]})}),(0,s.jsx)(l.default,{})]})}function S(){return(0,s.jsx)(o.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}),children:(0,s.jsx)(R,{})})}},32358:(e,t,a)=>{Promise.resolve().then(a.bind(a,18097))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,844,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1342,5433,347,9216,8441,1684,7358],()=>t(32358)),_N_E=e.O()}]);