"use strict";exports.id=3766,exports.ids=[3766],exports.modules={63766:(e,t,r)=>{let a;r.d(t,{V:()=>X});let i=new TextEncoder,o=new TextDecoder;function s(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:o.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=o.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}class n extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class c extends n{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class d extends n{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class l extends n{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class y extends n{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class p extends n{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class f extends n{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class u extends n{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class h extends n{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}let m=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new y(`alg ${e} is not supported either by JOSE or your javascript runtime`)}},S=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}};function w(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function E(e,t){return e.name===t}function g(e){return parseInt(e.name.slice(4),10)}function b(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let A=(e,...t)=>b("Key must be ",e,...t);function v(e,t,...r){return b(`Key for the ${e} algorithm must be `,t,...r)}let k=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(A(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!E(e.algorithm,"HMAC"))throw w("HMAC");let r=parseInt(t.slice(2),10);if(g(e.algorithm.hash)!==r)throw w(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!E(e.algorithm,"RSASSA-PKCS1-v1_5"))throw w("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(g(e.algorithm.hash)!==r)throw w(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!E(e.algorithm,"RSA-PSS"))throw w("RSA-PSS");let r=parseInt(t.slice(2),10);if(g(e.algorithm.hash)!==r)throw w(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!E(e.algorithm,"Ed25519"))throw w("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!E(e.algorithm,"ECDSA"))throw w("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw w(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}(t,e,r),t},R=async(e,t,r,a)=>{let i=await k(e,t,"verify");S(e,i);let o=m(e,i.algorithm);try{return await crypto.subtle.verify(o,i,r,a)}catch{return!1}},C=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},K=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function P(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function T(e){return e?.[Symbol.toStringTag]==="KeyObject"}let W=e=>P(e)||T(e);function _(e){return K(e)&&"string"==typeof e.kty}let H=e=>e?.[Symbol.toStringTag],O=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let a;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):a=r;break;case e.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):a=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):a="wrapKey";break;case"decrypt"===r:a=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(a&&t.key_ops?.includes?.(a)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${a}" when present`)}return!0},J=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(_(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&O(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!W(t))throw TypeError(v(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${H(t)} instances for symmetric algorithms must be of type "secret"`)}},I=(e,t,r)=>{if(_(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&O(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&O(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!W(t))throw TypeError(v(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${H(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${H(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${H(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${H(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${H(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},D=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?J(e,t,r):I(e,t,r)},$=(e,t,r,a,i)=>{let o;if(void 0!==i.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let s of(o=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!o.has(s))throw new y(`Extension Header Parameter "${s}" is not recognized`);if(void 0===i[s])throw new e(`Extension Header Parameter "${s}" is missing`);if(o.get(s)&&void 0===a[s])throw new e(`Extension Header Parameter "${s}" MUST be integrity protected`)}return new Set(a.crit)},x=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)},j=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new y('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new y('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new y('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new y('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),a={...e};return delete a.alg,delete a.use,crypto.subtle.importKey("jwk",a,t,e.ext??!e.d,e.key_ops??r)},M=async(e,t,r,i=!1)=>{let o=(a||=new WeakMap).get(e);if(o?.[r])return o[r];let s=await j({...t,alg:r});return i&&Object.freeze(e),o?o[r]=s:a.set(e,{[r]:s}),s},N=(e,t)=>{let r;let i=(a||=new WeakMap).get(e);if(i?.[t])return i[t];let o="public"===e.type,s=!!o;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,s,o?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,s,[o?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let a;switch(t){case"RSA-OAEP":a="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":a="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":a="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":a="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:a},s,o?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:a},s,[o?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let a=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!a)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},s,[o?"verify":"sign"])),"ES384"===t&&"P-384"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},s,[o?"verify":"sign"])),"ES512"===t&&"P-521"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},s,[o?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:a},s,o?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return i?i[t]=r:a.set(e,{[t]:r}),r},U=async(e,t)=>{if(e instanceof Uint8Array||P(e))return e;if(T(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return N(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return M(e,r,t)}if(_(e))return e.k?s(e.k):M(e,e,t,!0);throw Error("unreachable")};async function L(e,t,r){let a,n;if(!K(e))throw new p("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new p('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new p("JWS Protected Header incorrect type");if(void 0===e.payload)throw new p("JWS Payload missing");if("string"!=typeof e.signature)throw new p("JWS Signature missing or incorrect type");if(void 0!==e.header&&!K(e.header))throw new p("JWS Unprotected Header incorrect type");let c={};if(e.protected)try{let t=s(e.protected);c=JSON.parse(o.decode(t))}catch{throw new p("JWS Protected Header is invalid")}if(!C(c,e.header))throw new p("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let d={...c,...e.header},y=$(p,new Map([["b64",!0]]),r?.crit,c,d),f=!0;if(y.has("b64")&&"boolean"!=typeof(f=c.b64))throw new p('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:u}=d;if("string"!=typeof u||!u)throw new p('JWS "alg" (Algorithm) Header Parameter missing or invalid');let m=r&&x("algorithms",r.algorithms);if(m&&!m.has(u))throw new l('"alg" (Algorithm) Header Parameter value not allowed');if(f){if("string"!=typeof e.payload)throw new p("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new p("JWS Payload must be a string or an Uint8Array instance");let S=!1;"function"==typeof t&&(t=await t(c,e),S=!0),D(u,t,"verify");let w=function(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}(i.encode(e.protected??""),i.encode("."),"string"==typeof e.payload?i.encode(e.payload):e.payload);try{a=s(e.signature)}catch{throw new p("Failed to base64url decode the signature")}let E=await U(t,u);if(!await R(u,E,a,w))throw new h;if(f)try{n=s(e.payload)}catch{throw new p("Failed to base64url decode the payload")}else n="string"==typeof e.payload?i.encode(e.payload):e.payload;let g={payload:n};return(void 0!==e.protected&&(g.protectedHeader=c),void 0!==e.header&&(g.unprotectedHeader=e.header),S)?{...g,key:E}:g}async function B(e,t,r){if(e instanceof Uint8Array&&(e=o.decode(e)),"string"!=typeof e)throw new p("Compact JWS must be a string or Uint8Array");let{0:a,1:i,2:s,length:n}=e.split(".");if(3!==n)throw new p("Invalid Compact JWS");let c=await L({payload:i,protected:a,signature:s},t,r),d={payload:c.payload,protectedHeader:c.protectedHeader};return"function"==typeof t?{...d,key:c.key}:d}let F=e=>Math.floor(e.getTime()/1e3),G=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,V=e=>{let t;let r=G.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t},q=e=>e.toLowerCase().replace(/^application\//,""),z=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));async function X(e,t,r){let a=await B(e,t,r);if(a.protectedHeader.crit?.includes("b64")&&!1===a.protectedHeader.b64)throw new f("JWTs MUST NOT use unencoded payload");let i={payload:function(e,t,r={}){let a,i;try{a=JSON.parse(o.decode(t))}catch{}if(!K(a))throw new f("JWT Claims Set must be a top-level JSON object");let{typ:s}=r;if(s&&("string"!=typeof e.typ||q(e.typ)!==q(s)))throw new c('unexpected "typ" JWT header value',a,"typ","check_failed");let{requiredClaims:n=[],issuer:l,subject:y,audience:p,maxTokenAge:u}=r,h=[...n];for(let e of(void 0!==u&&h.push("iat"),void 0!==p&&h.push("aud"),void 0!==y&&h.push("sub"),void 0!==l&&h.push("iss"),new Set(h.reverse())))if(!(e in a))throw new c(`missing required "${e}" claim`,a,e,"missing");if(l&&!(Array.isArray(l)?l:[l]).includes(a.iss))throw new c('unexpected "iss" claim value',a,"iss","check_failed");if(y&&a.sub!==y)throw new c('unexpected "sub" claim value',a,"sub","check_failed");if(p&&!z(a.aud,"string"==typeof p?[p]:p))throw new c('unexpected "aud" claim value',a,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=V(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:m}=r,S=F(m||new Date);if((void 0!==a.iat||u)&&"number"!=typeof a.iat)throw new c('"iat" claim must be a number',a,"iat","invalid");if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw new c('"nbf" claim must be a number',a,"nbf","invalid");if(a.nbf>S+i)throw new c('"nbf" claim timestamp check failed',a,"nbf","check_failed")}if(void 0!==a.exp){if("number"!=typeof a.exp)throw new c('"exp" claim must be a number',a,"exp","invalid");if(a.exp<=S-i)throw new d('"exp" claim timestamp check failed',a,"exp","check_failed")}if(u){let e=S-a.iat;if(e-i>("number"==typeof u?u:V(u)))throw new d('"iat" claim timestamp check failed (too far in the past)',a,"iat","check_failed");if(e<0-i)throw new c('"iat" claim timestamp check failed (it should be in the past)',a,"iat","check_failed")}return a}(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...i,key:a.key}:i}}};