<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentPastDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'prev_standard' => 'required|string|max:200',
            "prev_school"    => "required|string|max:200",
            "prev_passing_year"    => "required|string|max:200",
            "prev_school_left_date"    => "required|string|max:200",
            "left_reason"    => "required|string|max:200",
        ];
    }
}
