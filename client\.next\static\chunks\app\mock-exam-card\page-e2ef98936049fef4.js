(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2540],{2070:(e,t,a)=>{Promise.resolve().then(a.bind(a,89018))},7583:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var r=a(95155);a(12115);var s=a(6874),i=a.n(s),l=a(66766),n=a(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(l.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:s}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:s,children:(0,r.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},s)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(l.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>l,wL:()=>d});var r=a(95155);a(12115);var s=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},74436:(e,t,a)=>{"use strict";a.d(t,{k5:()=>d});var r=a(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(s),l=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach(function(t){var r,s,i;r=e,s=t,i=a[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in r?Object.defineProperty(r,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[s]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function d(e){return t=>r.createElement(x,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,a)=>r.createElement(t.tag,c({key:a},t.attr),e(t.child)))}(e.child))}function x(e){var t=t=>{var a,{attr:s,size:i,title:o}=e,d=function(e,t){if(null==e)return{};var a,r,s=function(e,t){if(null==e)return{};var a={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;a[r]=e[r]}return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)a=i[r],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(s[a]=e[a])}return s}(e,l),x=i||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),r.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:a,style:c(c({color:e.color||t.color},t.style),e.style),height:x,width:x,xmlns:"http://www.w3.org/2000/svg"}),o&&r.createElement("title",null,o),e.children)};return void 0!==i?r.createElement(i.Consumer,null,e=>t(e)):t(s)}},86214:(e,t,a)=>{"use strict";a.d(t,{S:()=>i,q:()=>s});var r=a(55077);let s=async e=>{try{let t=await r.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam result: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}},i=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let s=await r.S.get("/mock-exam-result/".concat(e,"?page=").concat(t,"&limit=").concat(a),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){var s,i;return{success:!1,error:"Failed to get mock exam result: ".concat((null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.message)||e.message)}}}},89018:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(95155),s=a(70347),i=a(7583),l=a(66695),n=a(30285),o=a(66766);let c={src:"/_next/static/media/mockExamImage.b1be346b.svg"};var d=a(64315),x=a(93588),h=a(35695),m=a(19320),u=a(56671),g=a(12115),p=a(86214);function f(){let e=(0,h.useRouter)(),[t,a]=(0,g.useState)(!1),[s,i]=(0,g.useState)(null),[l,o]=(0,g.useState)(null),c=(0,g.useRef)(null),d=(0,g.useRef)(null);(0,g.useEffect)(()=>{try{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e).id:null;o(t)}catch(e){console.error("Error retrieving studentId:",e),o(null)}},[]),(0,g.useEffect)(()=>((async()=>{if(!l){a(!0),i(null);return}try{let e=await (0,p.S)(l,1,1);if(e.success&&e.data.data.mockExamResults.length>0){let t=e.data.data.mockExamResults[0],r=new Date(t.createdAt).toISOString().split("T")[0],s=new Date().toISOString().split("T")[0];if(r===s){a(!0);let e=new Date,t=new Date;t.setDate(e.getDate()+1),t.setHours(0,0,0,0);let r=t.getTime()-e.getTime(),s=Math.ceil(r/1e3);i(s>0?s:null),c.current=setInterval(()=>{i(e=>null===e||e<=1?(clearInterval(c.current),null):e-1)},1e3),d.current=setTimeout(()=>{a(!1),i(null)},r)}else a(!1),i(null)}else a(!1),i(null)}catch(e){console.error("Error checking exam attempt:",e),u.oR.error("Failed to verify exam eligibility."),a(!0),i(null)}})(),()=>{c.current&&clearInterval(c.current),d.current&&clearTimeout(d.current)}),[l]);let x=()=>{if(null===s)return null;let e=Math.floor(s/3600),t=Math.floor(s%3600/60),a=s%60;return"".concat(e.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"))};return(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(n.$,{className:"w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>{if(!l){u.oR.error("Please log in to attempt the exam."),e.push("/login");return}if(t&&s){u.oR.error("You can attempt the exam again in ".concat(x(),"."));return}e.push("/mock-test")},disabled:t||!l,children:"Try Daily Quiz"}),t&&s&&(0,r.jsxs)("p",{className:"text-center mt-2 text-gray-500 text-sm",children:["Next attempt available in: ",x()]})]})}let y=()=>{let e=(0,h.useRouter)(),t=null;try{let e=localStorage.getItem("student_data");t=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),t=null}return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-800 dark:text-gray-100 transition-colors duration-500",children:[(0,r.jsx)(s.default,{}),(0,r.jsx)("section",{className:"bg-gradient-to-r from-gray-900 to-gray-700 py-12 flex justify-center",children:(0,r.jsx)(m.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,ease:"easeOut"},children:(0,r.jsx)(o.default,{height:200,width:300,src:c.src,alt:"Current Affairs Quiz Logo",priority:!0,quality:100,className:"object-contain rounded-lg shadow-lg"})})}),(0,r.jsxs)("section",{className:"text-center mt-10 px-4 pt-7",children:[(0,r.jsxs)(m.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-4xl md:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white",children:["Daily Current Affairs ",(0,r.jsx)("span",{className:"text-amber-500",children:"Quiz"})]}),(0,r.jsx)(m.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"mt-3 text-lg text-gray-600 dark:text-gray-300",children:"Stay informed, test your knowledge, and earn exclusive rewards!"})]}),(0,r.jsx)("section",{className:"flex justify-center p-4 sm:p-6 md:p-10 lg:p-16",children:(0,r.jsx)(m.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7,delay:.6},children:(0,r.jsxs)(l.Zp,{className:"max-w-4xl w-full mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-3xl shadow-xl transition-all duration-500 p-8 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between border-b border-gray-200 dark:border-gray-700/50 pb-5 mb-8",children:[(0,r.jsxs)("h2",{className:"text-2xl md:text-3xl font-extrabold text-gray-900 dark:text-white",children:["Daily Current Affairs ",(0,r.jsx)("span",{className:"text-amber-500",children:"Quiz"})]}),(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 hidden sm:inline",children:"Stay updated & earn rewards daily"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 shadow-sm transition-all duration-300",children:[(0,r.jsxs)(m.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.8},whileHover:{scale:1.02},children:[(0,r.jsx)(d.O6N,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Questions:"})," 10"]})]}),(0,r.jsxs)(m.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:1},whileHover:{scale:1.02},children:[(0,r.jsx)(x.gPQ,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Badges:"})," Streak & Rank-Based"]})]}),(0,r.jsxs)(m.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:1.1},whileHover:{scale:1.02},children:[(0,r.jsx)(d.w_X,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Duration:"})," 8 min"]})]}),(0,r.jsxs)(m.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:1.2},whileHover:{scale:1.02},children:[(0,r.jsx)(d.cEG,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Earn Up to:"})," 5 Coins"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col justify-between space-y-6",children:[(0,r.jsx)(m.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.3},children:(0,r.jsx)(f,{})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 text-center",children:"Attempt once every 24 hours to earn coins & build your streak!"}),(0,r.jsxs)(m.P.div,{className:"text-sm bg-gray-100/50 dark:bg-gray-700/50 rounded-xl px-6 py-5 space-y-4 text-gray-800 dark:text-gray-200 backdrop-blur-sm shadow-sm",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.4},children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Coin Rewards:"}),(0,r.jsxs)("ul",{className:"list-none space-y-2",children:[(0,r.jsxs)(m.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"0 coins for 50% score"]}),(0,r.jsxs)(m.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"1 coins for 60% score"]}),(0,r.jsxs)(m.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"2 coins for 70% score"]}),(0,r.jsxs)(m.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"3 coins for 80% score"]}),(0,r.jsxs)(m.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"4 coins for 90% score"]}),(0,r.jsxs)(m.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"5 coins for 100% score"]})]}),(0,r.jsxs)("p",{className:"font-semibold",children:[(0,r.jsx)("strong",{children:"Streak Bonus:"})," +1 coin per daily attempt"]})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-row flex-wrap gap-4 justify-center",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(o.default,{src:"/scholer.svg",alt:"100 Coins",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"100 Coins"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(o.default,{src:"/Mastermind.svg",alt:"1000 Coins",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"500 Coins"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(o.default,{src:"/Achiever.svg",alt:"10000 Coins",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"1000 Coins"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(o.default,{src:"/Perfect Month.svg",alt:"30 Days Streak",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"30 Days Streak"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(o.default,{src:"/Perfect Year.svg",alt:"365 Days Streak",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"365 Days Streak"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(o.default,{src:"/Streak.svg",alt:"Daily Streak",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"Daily Streak"})]})]}),(0,r.jsxs)("div",{className:"mt-10 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(m.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.4},children:(0,r.jsx)(n.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-customOrange hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-lg py-4",onClick:()=>e.push("/mock-exam-result/".concat(t)),children:"View Quiz Results & Earned Coins"})}),(0,r.jsx)(m.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.5},children:(0,r.jsx)(n.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-customOrange hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-lg py-4",onClick:()=>e.push("/Leader-Board"),children:"View Leaderboards"})})]})]})})}),(0,r.jsx)(i.default,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6446,7672,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,347,8441,1684,7358],()=>t(2070)),_N_E=e.O()}]);