(()=>{var e={};e.id=3736,e.ids=[3736],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7361:(e,s,t)=>{Promise.resolve().then(t.bind(t,39153))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39153:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var r=t(60687),a=t(43210),i=t(16189),l=t(30474),o=t(69587),n=t(90471),c=t(29523),d=t(44493),u=t(85726),h=t(52581),p=t(90269),m=t(46303),x=t(53774),f=t(4780),g=t(47033),w=t(14952);(0,t(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);let j=({className:e,...s})=>(0,r.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,f.cn)("mx-auto flex w-full justify-center",e),...s});j.displayName="Pagination";let v=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("ul",{ref:t,className:(0,f.cn)("flex flex-row items-center gap-1",e),...s}));v.displayName="PaginationContent";let N=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("li",{ref:t,className:(0,f.cn)("",e),...s}));N.displayName="PaginationItem";let y=({className:e,isActive:s,size:t="icon",...a})=>(0,r.jsx)("a",{"aria-current":s?"page":void 0,className:(0,f.cn)((0,c.r)({variant:s?"outline":"ghost",size:t}),e),...a});y.displayName="PaginationLink";let b=({className:e,...s})=>(0,r.jsxs)(y,{"aria-label":"Go to previous page",size:"default",className:(0,f.cn)("gap-1 pl-2.5",e),...s,children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Previous"})]});b.displayName="PaginationPrevious";let E=({className:e,...s})=>(0,r.jsxs)(y,{"aria-label":"Go to next page",size:"default",className:(0,f.cn)("gap-1 pr-2.5",e),...s,children:[(0,r.jsx)("span",{children:"Next"}),(0,r.jsx)(w.A,{className:"h-4 w-4"})]});E.displayName="PaginationNext";let P=()=>{let[e,s]=(0,a.useState)(null),[t,g]=(0,a.useState)(!0),[w,P]=(0,a.useState)(1),k=(0,i.useRouter)(),_=(0,a.useCallback)(async()=>{try{g(!0);let e=await (0,x.o3)(w,10);s(e.data)}catch(e){h.oR.error(e.message||"Failed to fetch wishlist")}finally{g(!1)}},[w]);(0,a.useEffect)(()=>{if(!(0,f.xh)()){k.push("/"),h.oR.error("Please login as a student to view your wishlist");return}_()},[w,k,_]);let A=async e=>{try{await (0,x.Qg)(e),h.oR.success("Removed from wishlist"),_()}catch(e){h.oR.error(e.message||"Failed to remove from wishlist")}},q=e=>{P(e)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"My Wishlist"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Classes you've saved for later"})]}),(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsxs)(c.$,{variant:"outline",size:"sm",className:"text-xs border-orange-200 text-orange-600 hover:bg-orange-50",onClick:()=>k.push("/verified-classes"),children:[(0,r.jsx)(o.YNd,{className:"mr-1 h-3 w-3"}),"Browse More Classes"]})})]}),(0,r.jsx)("div",{className:"h-px bg-gray-200 w-full mb-6"}),t?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[...Array(8)].map((e,s)=>(0,r.jsxs)(d.Zp,{className:"overflow-hidden border border-gray-200 h-full flex flex-col p-0 rounded-md",children:[(0,r.jsx)("div",{className:"relative h-40 bg-gray-100 rounded-t-md overflow-hidden",children:(0,r.jsx)(u.E,{className:"h-full w-full"})}),(0,r.jsxs)(d.Wu,{className:"p-2 py-1.5 flex-grow",children:[(0,r.jsx)(u.E,{className:"h-5 w-3/4"}),(0,r.jsx)(u.E,{className:"h-3 w-1/2 mt-1 mb-0.5"}),(0,r.jsx)(u.E,{className:"h-3 w-2/3"})]}),(0,r.jsx)(d.wL,{className:"p-2 flex justify-between gap-2 mt-0",children:(0,r.jsx)(u.E,{className:"h-7 w-full"})})]},s))}):e?.items.length===0?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.YNd,{className:"mx-auto h-16 w-16 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"No items in wishlist"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"You haven't added any classes to your wishlist yet."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(c.$,{onClick:()=>k.push("/verified-classes"),className:"bg-orange-500 hover:bg-orange-600",children:"Browse Classes"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:e?.items.map(e=>{let{savedClass:s}=e,t=`${s.firstName} ${s.lastName}`,a=s.ClassAbout?.classesLogo?`http://localhost:4005/${s.ClassAbout.classesLogo}`:"/teacher-profile.jpg";return r.jsxs(d.Zp,{className:"overflow-hidden hover:shadow-md transition-shadow border border-gray-200 h-full flex flex-col p-0 rounded-md",children:[r.jsxs("div",{className:"relative h-40 bg-gray-50 rounded-t-md overflow-hidden",children:[r.jsx(l.default,{src:a,alt:t,fill:!0,className:"object-cover"}),s.status?.status==="APPROVED"&&r.jsx("div",{className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm",children:r.jsx(n.VqV,{className:"text-green-500 h-4 w-4"})})]}),r.jsxs(d.Wu,{className:"p-2 py-1.5 flex-grow",children:[r.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[r.jsx(o.x$1,{className:"text-[#ff914d] h-4 w-4 flex-shrink-0"}),r.jsx("h3",{className:"text-xl font-bold line-clamp-1",children:t}),r.jsxs("p",{className:"text-xs text-muted-foreground line-clamp-1",children:["( ",s.className||""," )"]})]}),r.jsx("p",{className:"text-xs text-muted-foreground line-clamp-1 mt-0.5 mb-0.5",children:s.ClassAbout?.catchyHeadline||"Professional Educator"}),r.jsx("div",{className:"flex items-center gap-2"})]}),r.jsxs(d.wL,{className:"p-2 flex justify-between gap-2 mt-0",children:[r.jsx(c.$,{variant:"default",size:"sm",className:"flex-1 bg-orange-500 hover:bg-orange-600 text-xs h-7",onClick:()=>k.push(`/classes-details/${s.id}`),children:"View Details"}),r.jsx(c.$,{variant:"outline",size:"icon",className:"w-7 h-7 p-0 flex items-center justify-center",onClick:()=>A(e.id),children:r.jsx(o.Mbv,{className:"text-orange-500 h-4 w-4"})})]})]},e.id)})}),e&&e.totalPages>1&&(0,r.jsx)(j,{className:"mt-8",children:(0,r.jsxs)(v,{children:[(0,r.jsx)(N,{children:(0,r.jsx)(b,{size:"default",onClick:()=>q(Math.max(1,w-1)),className:1===w?"pointer-events-none opacity-50":"cursor-pointer"})}),Array.from({length:e.totalPages},(e,s)=>s+1).map(e=>(0,r.jsx)(N,{children:(0,r.jsx)(y,{size:"default",onClick:()=>q(e),isActive:w===e,className:"cursor-pointer",children:e})},e)),(0,r.jsx)(N,{children:(0,r.jsx)(E,{size:"default",onClick:()=>q(Math.min(e.totalPages,w+1)),className:w===e.totalPages?"pointer-events-none opacity-50":"cursor-pointer"})})]})})]})]}),(0,r.jsx)(m.default,{})]})}},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>n,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l,wL:()=>d});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function o({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...s})}},47033:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},53774:(e,s,t)=>{"use strict";t.d(s,{Kh:()=>o,Qg:()=>l,U4:()=>i,o3:()=>n});var r=t(4780);let a=process.env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1",i=async e=>{try{let s=(0,r.ZO)();if(!s)throw Error("Authentication required");let t=await fetch(`${a}/student-wishlist`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({classId:e}),credentials:"include"});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to add to wishlist")}return await t.json()}catch(e){throw console.error("Error adding to wishlist:",e),e}},l=async e=>{try{let s=(0,r.ZO)();if(!s)throw Error("Authentication required");let t=await fetch(`${a}/student-wishlist/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${s}`},credentials:"include"});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to remove from wishlist")}return await t.json()}catch(e){throw console.error("Error removing from wishlist:",e),e}},o=async e=>{try{let s=(0,r.ZO)();if(!s)return{inWishlist:!1};let t=await fetch(`${a}/student-wishlist/check/${e}`,{method:"GET",headers:{Authorization:`Bearer ${s}`},credentials:"include"});if(!t.ok)return{inWishlist:!1};return(await t.json()).data}catch(e){return console.error("Error checking wishlist status:",e),{inWishlist:!1}}},n=async(e=1,s=10)=>{try{let t=(0,r.ZO)();if(!t)throw Error("Authentication required");let i=await fetch(`${a}/student-wishlist?page=${e}&limit=${s}`,{method:"GET",headers:{Authorization:`Bearer ${t}`},credentials:"include"});if(!i.ok){let e=await i.json();throw Error(e.message||"Failed to fetch wishlist")}return await i.json()}catch(e){throw console.error("Error fetching wishlist:",e),e}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70913:(e,s,t)=>{Promise.resolve().then(t.bind(t,77504))},74075:e=>{"use strict";e.exports=require("zlib")},77504:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student\\\\wishlist\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},79985:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let c={children:["",{children:["student",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77504)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/student/wishlist/page",pathname:"/student/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var r=t(60687),a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",e),...s})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,8721,471,2800],()=>t(79985));module.exports=r})();