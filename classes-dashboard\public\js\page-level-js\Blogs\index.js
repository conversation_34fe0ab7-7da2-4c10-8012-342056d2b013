/******/ (() => { // webpackBootstrap
/*!***************************************************!*\
  !*** ./modules/Blogs/resources/views/js/index.js ***!
  \***************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "blogTitle",
  name: "blogTitle"
}, {
  data: "createdAt",
  name: "createdAt"
}, {
  data: "status",
  name: "status"
}];
var table = commonDatatable("#blogs_table", blogsRoute.index, columns);
$(document).on("click", ".deleteBlogsEntry", function () {
  var did = $(this).attr("data-deleteblogsid");
  var url = blogsRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;