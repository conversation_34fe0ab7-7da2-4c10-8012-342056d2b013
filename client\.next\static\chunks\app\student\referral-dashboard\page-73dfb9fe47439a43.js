(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5823],{18280:(e,s,a)=>{Promise.resolve().then(a.bind(a,56821))},56821:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var r=a(95155),t=a(12115),l=a(66695),n=a(30285),i=a(62523),c=a(26126),d=a(37475),x=a(17580),o=a(55670),m=a(66516),h=a(24357),u=a(33786),j=a(69074),f=a(56671),g=a(55077),p=a(7632),N=a(70347),y=a(60287);function b(){let[e,s]=(0,t.useState)(null),[a,b]=(0,t.useState)(!0),[v,w]=(0,t.useState)({startDate:"",endDate:""}),k=[{accessorKey:"referredUserName",header:"Name",cell:e=>{let{row:s}=e;return(0,r.jsx)("span",{className:"font-medium",children:s.original.referredUserName})}},{accessorKey:"referredUserEmail",header:"Email",cell:e=>{let{row:s}=e;return(0,r.jsx)("span",{className:"text-gray-700",children:s.original.referredUserEmail})}},{accessorKey:"referredUserType",header:"Type",cell:e=>{let{row:s}=e;return(0,r.jsx)(c.E,{variant:"STUDENT"===s.original.referredUserType?"default":"secondary",children:s.original.referredUserType})}},{accessorKey:"earnings",header:()=>(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Earnings Status"]}),cell:e=>{let{row:s}=e,{earnings:a}=s.original;return a&&a.length>0?(0,r.jsx)("div",{className:"space-y-1",children:a.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("span",{className:"font-medium",children:["₹",e.amount]}),(0,r.jsxs)("span",{className:"text-gray-500 ml-1",children:["(","REGISTRATION"===e.earningType?"Registration":"U-whiz",")"]})]}),(0,r.jsx)(c.E,{variant:"PAID"===e.paymentStatus?"default":"secondary",className:"PAID"===e.paymentStatus?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800",children:"PAID"===e.paymentStatus?"Paid":"Pending"})]},e.id))}):(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:"No earnings yet"})}},{accessorKey:"createdAt",header:"Date",cell:e=>{let{row:s}=e;return(0,r.jsx)("span",{className:"text-gray-700",children:(0,p.GP)(new Date(s.original.createdAt),"MMM dd, yyyy HH:mm")})}}],R=async()=>{try{b(!0);let e=await g.S.get("/referral/dashboard/student");e.data.success&&s(e.data.data)}catch(e){console.error("Error fetching referral data:",e),f.oR.error("Failed to load referral data")}finally{b(!1)}},D=async()=>{try{let e=await g.S.post("/referral/generate-link/student");e.data.success&&(s(s=>s?{...s,referralCode:e.data.data.referralCode,links:e.data.data.links}:null),f.oR.success("Referral links generated successfully!"))}catch(e){console.error("Error generating referral link:",e),f.oR.error("Failed to generate referral links")}},E=(e,s)=>{navigator.clipboard.writeText(e),f.oR.success("".concat(s," link copied to clipboard!"))},S=(0,t.useCallback)(async()=>{try{let e=new URLSearchParams;v.startDate&&e.append("startDate",v.startDate),v.endDate&&e.append("endDate",v.endDate);let a=await g.S.get("/referral/history/student?".concat(e.toString()));a.data.success&&s(e=>e?{...e,history:a.data.data.history}:null)}catch(e){console.error("Error fetching history:",e),f.oR.error("Failed to load referral history")}},[v.startDate,v.endDate]);return((0,t.useEffect)(()=>{R()},[]),(0,t.useEffect)(()=>{v.startDate&&v.endDate&&S()},[v,S]),a)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.default,{}),(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.default,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Referral Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Share Uest.in with friends, track your referrals, and earn money!"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Referrals"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:(null==e?void 0:e.totalReferrals)||0})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Students Referred"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==e?void 0:e.studentsReferred)||0})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Classes Referred"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==e?void 0:e.classesReferred)||0})})]})]}),(null==e?void 0:e.earnings)&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Earnings"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["₹",e.earnings.totalEarnings]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Registration + U-whiz"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Registration Earnings"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:["₹",e.earnings.registrationEarnings]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"₹10 per student registration"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"U-whiz Earnings"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["₹",e.earnings.uwhizEarnings]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"₹25 per exam application"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ZB,{className:"text-sm font-medium",children:"Payment Status"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsxs)("span",{className:"text-green-600 font-semibold",children:["Paid: ₹",e.earnings.paidEarnings]})}),(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsxs)("span",{className:"text-orange-600 font-semibold",children:["Pending: ₹",e.earnings.unpaidEarnings]})})]})})]})]}),(0,r.jsxs)(l.Zp,{className:"mb-8",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5"}),"My Referral Links"]}),(0,r.jsx)(l.BT,{children:"Share these links to refer new students and classes to Uest.in and earn ₹10 per registration + ₹25 per U-whiz application"})]}),(0,r.jsx)(l.Wu,{className:"space-y-4",children:(null==e?void 0:e.links)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Student Registration Link"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.p,{value:e.links.studentLink,readOnly:!0,className:"flex-1"}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>E(e.links.studentLink,"Student"),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.links.studentLink,"_blank"),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Classes Registration Link"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.p,{value:e.links.classLink,readOnly:!0,className:"flex-1"}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>E(e.links.classLink,"Classes"),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.links.classLink,"_blank"),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"No referral links generated yet"}),(0,r.jsx)(n.$,{onClick:D,className:"bg-orange-500 hover:bg-orange-600",children:"Generate Referral Links"})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5"}),"Referral History"]}),(0,r.jsx)(l.BT,{children:"Track all your successful referrals"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Start Date"}),(0,r.jsx)(i.p,{type:"date",value:v.startDate,onChange:e=>w(s=>({...s,startDate:e.target.value}))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"End Date"}),(0,r.jsx)(i.p,{type:"date",value:v.endDate,onChange:e=>w(s=>({...s,endDate:e.target.value}))})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)(n.$,{variant:"outline",onClick:()=>w({startDate:"",endDate:""}),children:"Clear Filter"})})]}),(0,r.jsx)(y.E,{columns:k,data:(null==e?void 0:e.history)||[],fetchData:()=>Promise.resolve(),totalItems:(null==e?void 0:e.history.length)||0,isLoading:a,hidePagination:!0})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,7632,7670,347,7270,8441,1684,7358],()=>s(18280)),_N_E=e.O()}]);