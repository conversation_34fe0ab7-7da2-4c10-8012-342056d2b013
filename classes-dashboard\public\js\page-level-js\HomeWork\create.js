/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/HomeWork/resources/views/js/create.js ***!
  \*******************************************************/
$("#createhomeWork_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandlercircular(form, createHomeWorkRoute.store, 'post', '#createhomeWork_form', '#saveHomeWorks', '#newHomeWorkEntry', '#homeWork_table');
    return false;
  }
});
/******/ })()
;