(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8156],{5040:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},12767:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},23562:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("calendar-days",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},42355:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52278:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var a=r(12115),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=a.createContext&&a.createContext(i),n=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var a,i,s;a=e,i=t,s=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[i]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>a.createElement(h,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function h(e){var t=t=>{var r,{attr:i,size:s,title:o}=e,d=function(e,t){if(null==e)return{};var r,a,i=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,n),h=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:h,width:h,xmlns:"http://www.w3.org/2000/svg"}),o&&a.createElement("title",null,o),e.children)};return void 0!==s?a.createElement(s.Consumer,null,e=>t(e)):t(i)}},75894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(95155),i=r(12115),s=r(36754),n=r(51154),l=r(52278),o=r(42355),c=r(13052),d=r(12767),h=r(30285),u=r(70347),m=r(7583),p=r(54568),y=r(19320);let f=()=>{let[e,t]=(0,i.useState)([]),[r,f]=(0,i.useState)(!0),[b,v]=(0,i.useState)(1),[x,g]=(0,i.useState)(1);(0,i.useEffect)(()=>{(async()=>{try{f(!0);let e=await (0,s.BU)(b,10);t(e.blogs),g(e.totalPages)}catch(e){console.error("Failed to fetch blogs:",e)}finally{f(!1)}})()},[b]);let j=e=>{v(e),window.scrollTo({top:0,behavior:"smooth"})};return(0,a.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,a.jsx)(u.default,{}),(0,a.jsxs)("main",{className:"container mx-auto py-12 px-4",children:[(0,a.jsxs)(y.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,a.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Our Blog"}),(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Latest Articles & News"}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Stay updated with our latest news, tips, and insights"})]}),r?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)(n.A,{className:"h-8 w-8 animate-spin text-primary"})}):0===e.length?(0,a.jsx)("div",{className:"text-center py-10",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No blogs available at the moment."})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10",children:e.map((e,t)=>(0,a.jsx)(y.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t,duration:.5},whileHover:{y:-5},children:(0,a.jsx)(p.A,{blog:e})},e.id))}),e.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2 p-4",children:[(0,a.jsx)(h.$,{variant:"outline",size:"icon",onClick:()=>j(1),disabled:1===b,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{variant:"outline",size:"icon",onClick:()=>j(Math.max(b-1,1)),disabled:1===b,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"text-sm px-2",children:["Page ",b," of ",x]}),(0,a.jsx)(h.$,{variant:"outline",size:"icon",onClick:()=>j(Math.min(b+1,x)),disabled:b===x,children:(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{variant:"outline",size:"icon",onClick:()=>j(x),disabled:b===x,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(m.default,{})]})}},88472:(e,t,r)=>{Promise.resolve().then(r.bind(r,75894))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,347,5881,8441,1684,7358],()=>t(88472)),_N_E=e.O()}]);