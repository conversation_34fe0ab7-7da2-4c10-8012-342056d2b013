(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{12675:(e,a,s)=>{Promise.resolve().then(s.bind(s,38728))},38728:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>en});var t=s(95155),r=s(12115),i=s(30285),l=s(53896),o=s(41066),n=s(30227),c=s(76028),d=s(80465),m=s(47835),x=s(42148),u=s(53311),h=s(14738),g=s(22226),p=s(82137),b=s(38619),f=s(79397),v=s(2708),j=s(47298),w=s(57100),N=s(38564),y=s(35169),k=s(92138),A=s(16785),C=s(14186),F=s(59964),B=s(54416),P=s(70347),D=s(7583),E=s(35695),S=s(56671),z=s(34540),V=s(92560);let L=()=>{let e=(0,E.useSearchParams)().get("authError"),a=(0,z.wA)();return(0,r.useEffect)(()=>{"1"===e&&(S.oR.error("Login Expired, Please login to continue"),a((0,V.lM)()))},[e]),null};var I=s(19320),R=s(56787),O=s(66766),_=s(55077),T=s(66695),M=s(27677),H=s(97469);s(2252),s(59408),s(56970);var W=s(40224);let q=e=>{let{thoughts:a}=e,s="http://localhost:4005/".replace(/\/+$/,""),r=e=>{e.currentTarget.style.display="none"},i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},l={hidden:{opacity:0,x:-20},visible:e=>({opacity:1,x:0,transition:{delay:.2*e,duration:.5,ease:"easeOut"}})},o=a.filter(e=>"APPROVED"===e.status);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("style",{children:"\n  .swiper-container {\n    position: relative;\n  }\n  .swiper-pagination {\n    position: absolute;\n    bottom: 20px !important;\n  }\n  .swiper-pagination-bullet {\n    background: #d1d5db;\n    opacity: 0.5;\n    width: 12px;\n    height: 12px;\n    margin: 0 6px !important;\n    border-radius: 12px;\n    transition: all 0.3s ease;\n  }\n  .swiper-pagination-bullet-active {\n    background: #FD904B;\n    opacity: 0.5;\n    width: 36px;\n    border-radius: 12px;\n    transform: none;\n  }\n"}),(0,t.jsx)(M.RC,{modules:[H.Vx,H.dK,H.Ij],spaceBetween:30,slidesPerView:1,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},pagination:{clickable:!0},autoplay:{delay:4e3,disableOnInteraction:!1},className:"w-full max-w-6xl mx-auto swiper-container",children:o.map(e=>{var a;let o=(null===(a=e.class.ClassAbout)||void 0===a?void 0:a.classesLogo)?"".concat(s).concat(e.class.ClassAbout.classesLogo.startsWith("/")?"":"/").concat(e.class.ClassAbout.classesLogo):"";return(0,t.jsx)(M.qr,{children:(0,t.jsxs)(I.P.div,{variants:i,initial:"hidden",animate:"visible",className:"relative dark:bg-siderbar rounded-2xl p-8 sm:p-8 flex flex-col md:flex-row items-center gap-6 sm:gap-8 overflow-hidden border border-gray-200 dark:border-gray-700/50 backdrop-blur-lg shadow-sm mb-12",children:[(0,t.jsx)("div",{className:"absolute top-4 left-4 opacity-20",children:(0,t.jsx)(W.A,{className:"w-12 h-12 text-[#FD904B]"})}),(0,t.jsx)("div",{className:"flex-shrink-0 relative",children:(0,t.jsx)(I.P.div,{transition:{duration:.3},className:"h-24 w-24 sm:h-28 sm:w-28 rounded-full overflow-hidden border-4 border-[#FD904B]/20 shadow-sm",children:o?(0,t.jsx)(O.default,{width:200,height:200,src:o,alt:"Class Logo",className:"h-full w-full object-cover",onError:r}):(0,t.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-100 dark:bg-gray-700",children:(0,t.jsx)("span",{className:"text-gray-400 dark:text-gray-500 text-xs",children:"No logo"})})})}),(0,t.jsxs)("div",{className:"flex-1 text-center md:text-left space-y-3 relative z-10",children:[(0,t.jsxs)(I.P.p,{custom:0,variants:l,initial:"hidden",animate:"visible",className:"text-xl sm:text-2xl font-semibold text-gray-900 dark:text-gray-50 leading-tight tracking-wide",children:['"',e.thoughts,'"']}),(0,t.jsx)(I.P.p,{custom:1,variants:l,initial:"hidden",animate:"visible",className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:e.class.className}),(0,t.jsxs)(I.P.p,{custom:2,variants:l,initial:"hidden",animate:"visible",className:"text-md font-light text-gray-600 dark:text-gray-400 italic",children:["— ",e.class.firstName," ",e.class.lastName]})]})]})},e.id)})})]})},U=async function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;try{return(await _.S.get("/classes-thought",{params:{status:e||void 0,classId:a||void 0,page:s,limit:t}})).data}catch(e){var r,i;throw Error((null===(i=e.response)||void 0===i?void 0:null===(r=i.data)||void 0===r?void 0:r.message)||"Failed to fetch thoughts: ".concat(e.message))}},G=e=>[void 0,void 0,void 0,void 0,void 0].map((a,s)=>(0,t.jsx)(N.A,{className:"w-4 h-4 ".concat(s<e?"fill-[#FD904B] text-[#FD904B]":"text-gray-300")},s)),Y=e=>{let{testimonial:a}=e,s=a.class.fullName||a.class.className,r=a.class.className,i=a.message,l=a.rating,o=a.class.classesLogo?"".concat("http://localhost:4005/").concat(a.class.classesLogo):a.class.profilePhoto?"".concat("http://localhost:4005/").concat(a.class.profilePhoto):"/teacher-profile.jpg";return(0,t.jsx)("div",{className:"inline-flex flex-shrink-0 w-[360px] mx-4",children:(0,t.jsxs)(I.P.div,{className:"dark:bg-siderbar rounded-3xl p-8 w-full relative overflow-hidden border-2 border-gray-200 dark:border-gray-700",whileHover:{scale:1.02,borderColor:"#FD904B",zIndex:1},children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,t.jsx)(I.P.div,{className:"relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border-2 border-gray-200 dark:border-gray-700",whileHover:{scale:1.1},children:(0,t.jsx)(O.default,{src:o,alt:s,fill:!0,className:"object-cover"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"font-semibold text-base dark:text-white text-gray-800 truncate",children:s}),(0,t.jsx)("p",{className:"text-orange-500 text-sm font-medium truncate",children:r}),(0,t.jsx)("div",{className:"flex items-center gap-1 mt-1",children:G(l)})]})]}),(0,t.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,t.jsxs)("p",{className:"text-gray-700 text-base leading-relaxed break-words line-clamp-3 italic dark:text-white",children:['"',i,'"']})})]})})},Z=e=>{let{direction:a=1,testimonials:s}=e,[i,l]=(0,r.useState)(0),o=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=()=>{o.current&&l(392*s.length)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[s.length]);let n=[];for(let e=0;e<6;e++)n.push(...s);return(0,t.jsx)("div",{className:"overflow-hidden",children:(0,t.jsx)(I.P.div,{ref:o,className:"flex",animate:{x:a>0?[-i,0]:[0,-i]},transition:{x:{repeat:1/0,repeatType:"loop",duration:40,ease:"linear",times:[0,1]}},style:{gap:"32px"},children:n.map((e,a)=>(0,t.jsx)(Y,{testimonial:e},"".concat(e.id,"-").concat(a)))})})},$=()=>{let[e,a]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{try{i(!0);let e=await _.S.get("/testimonials/approved");a(e.data)}catch(e){console.error("Error fetching testimonials:",e)}finally{i(!1)}})()},[]),s||0!==e.length)?(0,t.jsx)("section",{className:"py-20 dark:bg-slidebar",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"text-center mb-16 px-4",children:[(0,t.jsx)(I.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"text-4xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent dark:text-white",children:"What Our Clients Say"}),(0,t.jsx)(I.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-gray-600 text-lg",children:"Trusted by thousands of satisfied customers"})]}),s?(0,t.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FD904B]"})}):(0,t.jsxs)("div",{className:"space-y-12",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(Z,{direction:-1,testimonials:e}),(0,t.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-40  z-10"}),(0,t.jsx)("div",{className:"absolute right-0 top-0 bottom-0 w-40 z-10"})]}),e.length>5&&(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(Z,{direction:1,testimonials:e}),(0,t.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-40 z-10"}),(0,t.jsx)("div",{className:"absolute right-0 top-0 bottom-0 w-40 z-10"})]})]})]})}):null};var K=s(6874),J=s.n(K),Q=s(51154),X=s(36754),ee=s(54568);let ea=()=>{let[e,a]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{try{l(!0);let e=await (0,X.BU)(1,3);a(e.blogs)}catch(e){console.error("Failed to fetch recent blogs:",e)}finally{l(!1)}})()},[]),0!==e.length||s)?(0,t.jsxs)("section",{className:"py-20 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,t.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,t.jsxs)(I.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,t.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Latest Blogs"}),(0,t.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"Our Latest Articles"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Stay updated with our latest news, tips, and insights"})]}),s?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)(Q.A,{className:"h-8 w-8 animate-spin text-primary"})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10",children:e.map(e=>(0,t.jsx)(I.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1},whileHover:{y:-5},children:(0,t.jsx)(ee.A,{blog:e})},e.id))}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(J(),{href:"/blogs",passHref:!0,children:(0,t.jsx)(i.$,{variant:"outline",className:"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300",children:"Visit More Blogs"})})})]})]})]}):null};var es=s(54165),et=s(17580),er=s(35376),ei=s(87949);let el=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1500,[s,t]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let s=performance.now(),r=i=>{let l=Math.min((i-s)/a,1);t(Math.floor(l*e)),l<1?requestAnimationFrame(r):t(e)};requestAnimationFrame(r)},[e,a]),s};function eo(e){let{totalTutors:a,totalStudent:s}=e,r=el(a),i=el(16),l=el(s),o=[{icon:(0,t.jsx)(et.A,{className:"w-8 h-8"}),count:r,suffix:"+",label:"Verified Classes"},{icon:(0,t.jsx)(er.A,{className:"w-8 h-8"}),count:i,suffix:"+",label:"Categories"},{icon:(0,t.jsx)(ei.A,{className:"w-8 h-8"}),count:l,suffix:"+",label:"Students"}];return(0,t.jsxs)("section",{className:"py-20 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,t.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8",children:o.map((e,a)=>(0,t.jsxs)(I.P.div,{className:"relative group",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:.15*a},viewport:{once:!0},children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FD904B]/20 to-transparent rounded-2xl blur-2xl group-hover:blur-3xl transition-all duration-300 opacity-0 group-hover:opacity-100"}),(0,t.jsxs)("div",{className:"relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border shadow-sm hover:shadow-sm transition-all duration-300",children:[(0,t.jsx)("div",{className:"text-[#FD904B] mb-4 transform group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,t.jsxs)("h3",{className:"text-4xl font-bold mb-2 bg-gradient-to-br from-foreground to-foreground/80 bg-clip-text text-transparent",children:[e.count,e.suffix]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:e.label})]})]},a))})})]})}s(49042);let en=()=>{let e=(0,E.useRouter)(),[a,s]=(0,r.useState)({}),[z,V]=(0,r.useState)([]),[W,G]=(0,r.useState)([]),[Y,Z]=(0,r.useState)(!0),[K,J]=(0,r.useState)(!0),[Q,X]=(0,r.useState)(!1),[ee,et]=(0,r.useState)([]);(0,r.useEffect)(()=>{X(!0)},[]);let er=e=>({Education:(0,t.jsx)(l.A,{className:"w-10 h-10"}),Drama:(0,t.jsx)(o.A,{className:"w-10 h-10"}),Music:(0,t.jsx)(n.A,{className:"w-10 h-10"}),"Art & Craft":(0,t.jsx)(c.A,{className:"w-10 h-10"}),Sports:(0,t.jsx)(d.A,{className:"w-10 h-10"}),"Foreign Languages":(0,t.jsx)(m.A,{className:"w-10 h-10"}),Technology:(0,t.jsx)(x.A,{className:"w-10 h-10"}),Dance:(0,t.jsx)(u.A,{className:"w-10 h-10"}),"Computer Classes":(0,t.jsx)(h.A,{className:"w-10 h-10"}),"Cooking Classes":(0,t.jsx)(g.A,{className:"w-10 h-10"}),"Garba Classes":(0,t.jsx)(p.A,{className:"w-10 h-10"}),"Vaidik Maths":(0,t.jsx)(b.A,{className:"w-10 h-10"}),"Gymnastic Classes":(0,t.jsx)(f.A,{className:"w-10 h-10"}),"Yoga Classes":(0,t.jsx)(v.A,{className:"w-10 h-10"}),"Aviation Classes":(0,t.jsx)(j.A,{className:"w-10 h-10"}),"Designing Classes":(0,t.jsx)(w.A,{className:"w-10 h-10"})})[e]||(0,t.jsx)(l.A,{className:"w-10 h-10"}),ei=async()=>{try{let e=await _.S.get("/constant/TuitionClasses");if(e.data&&e.data.details){let a=e.data.details.map(e=>({name:e.name,icon:er(e.name)}));et(a)}}catch(e){console.error("Failed to fetch categories:",e),et([])}finally{}},[el,en]=(0,r.useState)(0),[ec,ed]=(0,r.useState)(0);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await _.S.get("/student/count");ed(e.data||0)}catch(e){console.error("Error fetching total students:",e),ed(0)}};(async()=>{try{let e=await _.S.get("/classes/category-counts");s(e.data)}catch(e){console.error("Error fetching category counts:",e)}})(),e()},[]);let em=a=>{e.push("/verified-classes?education=".concat(a))},ex=async()=>{Z(!0);try{let e=await _.S.get("/classes/approved-tutors",{params:{page:1,limit:4,sortByRating:!0,sortByReviewCount:!0}});if(e.data&&"object"==typeof e.data){if(void 0!==e.data.success&&void 0!==e.data.data){let a=e.data.data;en(a.totalClasses||0),V(a.data||[])}else en(e.data.totalClasses||0),V(e.data.data||[])}else en(0),V([])}catch(e){console.error("Failed to fetch tutors:",e),S.oR.error("Failed to fetch tutors"),en(0),V([])}finally{Z(!1)}},eu=async()=>{J(!0);try{var e;let a=await U("APPROVED",void 0,1,5),s=(null===(e=a.thoughts)||void 0===e?void 0:e.filter(e=>"APPROVED"===e.status))||[];G(s)}catch(e){console.error("Error fetching thoughts:",e),S.oR.error("Failed to fetch thoughts"),G([])}finally{J(!1)}};(0,r.useEffect)(()=>{ex(),eu(),ei()},[]);let eh={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100}}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(P.default,{}),(0,t.jsxs)("div",{className:"min-h-screen bg-background text-foreground overflow-hidden",children:[(0,t.jsx)(r.Suspense,{children:(0,t.jsx)(L,{})}),(0,t.jsxs)("main",{className:"relative",children:[(0,t.jsx)("section",{className:"pt-4 sm:pt-0",children:(0,t.jsxs)("div",{className:"w-[85%] max-sm:w-[95%] mx-auto m-2",children:[(0,t.jsxs)(M.RC,{modules:[H.Ij,H.dK],autoplay:{delay:4e3,disableOnInteraction:!1},spaceBetween:0,slidesPerView:1,pagination:{clickable:!0,bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",el:".custom-pagination"},className:"w-full",children:[(0,t.jsx)(M.qr,{children:(0,t.jsx)("div",{className:"w-full aspect-[2/1] max-sm:aspect-[3/2]",children:(0,t.jsx)(O.default,{src:"/slide1.png",alt:"Slide 1",fill:!0,className:"w-full h-auto object-contain",priority:!0,sizes:"85vw"})})}),(0,t.jsx)(M.qr,{children:(0,t.jsx)("div",{className:"w-full aspect-[2/1] max-sm:aspect-[3/2]",children:(0,t.jsx)(O.default,{src:"/banner_maths_marvel1.png",alt:"Slide 2",fill:!0,className:"w-full h-auto object-contain",priority:!0,sizes:"85vw"})})})]}),(0,t.jsx)("div",{className:"custom-pagination text-center mt-4"})]})}),(0,t.jsx)(eo,{totalTutors:el,totalStudent:ec}),(0,t.jsxs)("section",{className:"py-20 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,t.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,t.jsxs)(I.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,t.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Featured Classes"}),(0,t.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"Meet Our Top Tutors"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Connect with our top verified tutors and start your learning journey today."})]}),Y?(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,a)=>(0,t.jsx)("div",{className:"h-96 w-full rounded-xl bg-gray-200 dark:bg-gray-700 animate-pulse"},a))}):0===z.length?(0,t.jsx)(I.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-10",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No tutors found at the moment."})}):(0,t.jsx)(I.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:z.map((a,s)=>(0,t.jsx)(I.P.div,{variants:eh,whileHover:{y:-5},className:"h-full",children:(0,t.jsxs)(T.Zp,{className:"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300",children:[(0,t.jsxs)(T.aR,{className:"flex flex-row items-center gap-4",children:[(0,t.jsx)(I.P.div,{className:"relative w-20 h-20 rounded-full overflow-hidden ring-2 ring-[#FD904B]/20",whileHover:{scale:1.05},children:(0,t.jsx)(O.default,{src:a.ClassAbout&&a.ClassAbout.classesLogo?"".concat("http://localhost:4005/").concat(a.ClassAbout.classesLogo):"/default-profile.jpg",alt:a.firstName,fill:!0,className:"object-cover"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold hover:text-[#FD904B] transition-colors",children:[a.firstName," ",a.lastName]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:a.className})]})]}),(0,t.jsx)(T.Wu,{className:"flex-1 space-y-4",children:(0,t.jsx)("p",{className:"line-clamp-2 text-sm text-muted-foreground",children:a.ClassAbout&&a.ClassAbout.tutorBio||"No bio available."})}),(0,t.jsxs)(T.wL,{className:"flex flex-col items-start gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1 pt-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,t.jsx)("span",{className:"font-semibold text-foreground",children:a.averageRating?a.averageRating.toFixed(1):"0"}),(0,t.jsxs)("span",{children:["(",a.reviewCount||0," reviews)"]})]}),(0,t.jsx)(i.$,{className:"w-full bg-orange-500 hover:bg-orange-600",onClick:()=>e.push("/classes-details/".concat(a.id)),children:"View Profile"})]})]})},s))})]})]}),(0,t.jsx)(R.x,{transition:{duration:.4},children:(0,t.jsxs)("section",{className:"py-20 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,t.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,t.jsxs)(I.P.div,{className:"text-center mb-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,t.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Categories"}),(0,t.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"Explore Your Interests"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Discover classes across various categories with our verified tutors."})]}),(0,t.jsxs)("div",{className:"flex justify-end items-center gap-4 mb-6",children:[(0,t.jsx)("button",{className:"swiper-button-prev-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition",children:(0,t.jsx)(y.A,{size:20})}),(0,t.jsx)("button",{className:"swiper-button-next-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition",children:(0,t.jsx)(k.A,{size:20})})]}),(0,t.jsx)(M.RC,{modules:[H.Vx,H.Ij],autoplay:{delay:3e3,disableOnInteraction:!1},navigation:{nextEl:".swiper-button-next-custom",prevEl:".swiper-button-prev-custom"},spaceBetween:20,breakpoints:{320:{slidesPerView:1.2},640:{slidesPerView:2},1024:{slidesPerView:3},1280:{slidesPerView:4}},className:"!px-2 !pt-3",children:ee.map((e,s)=>(0,t.jsx)(M.qr,{children:(0,t.jsx)(I.P.div,{className:"group cursor-pointer",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:.1*s},viewport:{once:!0},whileHover:{y:-5,transition:{duration:.2}},onClick:()=>em(e.name),children:(0,t.jsxs)("div",{className:"relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B] transition-all duration-300 overflow-hidden shadow-sm group-hover:shadow-md",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FD904B]/0 to-transparent group-hover:from-[#FD904B]/10 rounded-2xl transition-all duration-300"}),!Y&&(0,t.jsx)("div",{className:"absolute top-3 right-3 z-10",children:(0,t.jsxs)("span",{className:"text-sm font-bold bg-[#FD904B] text-white px-3 py-1.5 rounded-full shadow-sm flex items-center justify-center min-w-[40px] transform transition-all duration-300 group-hover:scale-110",children:[a[e.name]||0,(0,t.jsx)("span",{className:"ml-1 text-xs hidden group-hover:inline",children:"classes"})]})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"text-[#FD904B] mb-6 p-4 bg-[#FD904B]/10 rounded-full inline-flex transform group-hover:scale-110 transition-all duration-300 group-hover:shadow-md group-hover:bg-[#FD904B]/20",children:e.icon}),(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.name})}),(0,t.jsxs)("p",{className:"text-muted-foreground group-hover:text-[#FD904B] transition-colors duration-300 flex items-center gap-1 font-medium",children:["Explore courses"," ",(0,t.jsx)("span",{className:"transform transition-transform group-hover:translate-x-1",children:"→"})]})]})]})},s)},s))})]})]})}),(W.length>0||K)&&(0,t.jsxs)("section",{className:"py-20 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,t.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,t.jsxs)(I.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,t.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Thoughts"}),(0,t.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"What Our Community Thinks"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Hear from our verified students and tutors about their experiences."})]}),K?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"})}):(0,t.jsx)(q,{thoughts:W})]})]}),(0,t.jsxs)("section",{className:"py-20 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,t.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,t.jsxs)(I.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,t.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Process"}),(0,t.jsx)("h2",{className:"text-4xl font-bold bg-clip-text",children:"How UEST Works"})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:[{icon:(0,t.jsx)(A.A,{className:"w-8 h-8"}),title:"Find Your Perfect Match",description:"Browse through our verified classes and find your ideal match"},{icon:(0,t.jsx)(C.A,{className:"w-8 h-8"}),title:"Schedule Lessons",description:"Book lessons at times that work best for your schedule"},{icon:(0,t.jsx)(F.A,{className:"w-8 h-8"}),title:"Start Learning",description:"Begin your learning journey with personalized guidance"}].map((e,a)=>(0,t.jsxs)(I.P.div,{className:"group relative",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:.2*a},viewport:{once:!0},children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FD904B]/5 to-transparent rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"}),(0,t.jsxs)("div",{className:"relative p-8 h-52 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B]/50 transition-all duration-300 shadow-sm",children:[(0,t.jsx)("div",{className:"text-[#FD904B] mb-6 transform group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-foreground",children:e.title}),(0,t.jsx)("p",{className:"text-muted-foreground",children:e.description})]})]},a))})]})]}),(0,t.jsx)($,{}),(0,t.jsx)(ea,{}),(0,t.jsx)(es.lG,{open:Q,onOpenChange:X,children:(0,t.jsxs)(es.Cf,{className:"max-w-xs p-0 overflow-hidden",children:[(0,t.jsx)("div",{className:"sr-only",children:(0,t.jsx)(es.c7,{children:(0,t.jsx)(es.L3,{children:"Uwhiz Winner"})})}),(0,t.jsx)("button",{onClick:()=>X(!1),className:"absolute top-2 right-2 z-10 bg-white rounded-full p-1 shadow-md",children:(0,t.jsx)(B.A,{className:"w-6 h-6"})}),(0,t.jsx)(O.default,{src:"/MathsMarvelWinner.png",alt:"Uwhiz Winner",width:600,height:400,className:"w-full h-full object-cover rounded-lg"})]})})]})]}),(0,t.jsx)(D.default,{})]})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>o,rr:()=>g,zM:()=>n});var t=s(95155);s(12115);var r=s(4033),i=s(54416),l=s(59434);function o(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...a})}function n(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[s,(0,t.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...s})}function g(e){let{className:a,...s}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...s})}},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>n,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l,wL:()=>d});var t=s(95155);s(12115);var r=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[1892,6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1517,347,5881,8441,1684,7358],()=>a(12675)),_N_E=e.O()}]);