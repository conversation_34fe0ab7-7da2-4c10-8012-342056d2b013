"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4945],{22475:(t,e,n)=>{n.d(e,{UE:()=>td,ll:()=>tl,rD:()=>tp,UU:()=>tc,jD:()=>ts,ER:()=>th,cY:()=>tf,BN:()=>tu,Ej:()=>ta});let i=["top","right","bottom","left"],r=Math.min,o=Math.max,l=Math.round,f=Math.floor,u=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}function g(t){return["top","bottom"].includes(d(t))?"y":"x"}function y(t){return t.replace(/start|end/g,t=>a[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function x(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function v(t){let{x:e,y:n,width:i,height:r}=t;return{width:i,height:r,top:n,left:e,right:e+i,bottom:n+r,x:e,y:n}}function b(t,e,n){let i,{reference:r,floating:o}=t,l=g(e),f=p(g(e)),u=m(f),c=d(e),a="y"===l,s=r.x+r.width/2-o.width/2,y=r.y+r.height/2-o.height/2,w=r[u]/2-o[u]/2;switch(c){case"top":i={x:s,y:r.y-o.height};break;case"bottom":i={x:s,y:r.y+r.height};break;case"right":i={x:r.x+r.width,y:y};break;case"left":i={x:r.x-o.width,y:y};break;default:i={x:r.x,y:r.y}}switch(h(e)){case"start":i[f]-=w*(n&&a?-1:1);break;case"end":i[f]+=w*(n&&a?-1:1)}return i}let R=async(t,e,n)=>{let{placement:i="bottom",strategy:r="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(e)),c=await l.getElementRects({reference:t,floating:e,strategy:r}),{x:a,y:s}=b(c,i,u),d=i,h={},p=0;for(let n=0;n<f.length;n++){let{name:o,fn:m}=f[n],{x:g,y:y,data:w,reset:x}=await m({x:a,y:s,initialPlacement:i,placement:d,strategy:r,middlewareData:h,rects:c,platform:l,elements:{reference:t,floating:e}});a=null!=g?g:a,s=null!=y?y:s,h={...h,[o]:{...h[o],...w}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(c=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:r}):x.rects),{x:a,y:s}=b(c,d,u)),n=-1)}return{x:a,y:s,placement:d,strategy:r,middlewareData:h}};async function E(t,e){var n;void 0===e&&(e={});let{x:i,y:r,platform:o,rects:l,elements:f,strategy:u}=t,{boundary:c="clippingAncestors",rootBoundary:a="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=s(e,t),m=x(p),g=f[h?"floating"===d?"reference":"floating":d],y=v(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:c,rootBoundary:a,strategy:u})),w="floating"===d?{x:i,y:r,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),R=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},E=v(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-E.top+m.top)/R.y,bottom:(E.bottom-y.bottom+m.bottom)/R.y,left:(y.left-E.left+m.left)/R.x,right:(E.right-y.right+m.right)/R.x}}function L(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function T(t){return i.some(e=>t[e]>=0)}async function A(t,e){let{placement:n,platform:i,elements:r}=t,o=await (null==i.isRTL?void 0:i.isRTL(r.floating)),l=d(n),f=h(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,a=o&&u?-1:1,p=s(e,t),{mainAxis:m,crossAxis:y,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return f&&"number"==typeof w&&(y="end"===f?-1*w:w),u?{x:y*a,y:m*c}:{x:m*c,y:y*a}}function D(){return"undefined"!=typeof window}function O(t){return P(t)?(t.nodeName||"").toLowerCase():"#document"}function C(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function k(t){var e;return null==(e=(P(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function P(t){return!!D()&&(t instanceof Node||t instanceof C(t).Node)}function S(t){return!!D()&&(t instanceof Element||t instanceof C(t).Element)}function H(t){return!!D()&&(t instanceof HTMLElement||t instanceof C(t).HTMLElement)}function F(t){return!!D()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof C(t).ShadowRoot)}function M(t){let{overflow:e,overflowX:n,overflowY:i,display:r}=U(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(r)}function j(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function B(t){let e=N(),n=S(t)?U(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function N(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function W(t){return["html","body","#document"].includes(O(t))}function U(t){return C(t).getComputedStyle(t)}function V(t){return S(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function _(t){if("html"===O(t))return t;let e=t.assignedSlot||t.parentNode||F(t)&&t.host||k(t);return F(e)?e.host:e}function z(t,e,n){var i;void 0===e&&(e=[]),void 0===n&&(n=!0);let r=function t(e){let n=_(e);return W(n)?e.ownerDocument?e.ownerDocument.body:e.body:H(n)&&M(n)?n:t(n)}(t),o=r===(null==(i=t.ownerDocument)?void 0:i.body),l=C(r);if(o){let t=Y(l);return e.concat(l,l.visualViewport||[],M(r)?r:[],t&&n?z(t):[])}return e.concat(r,z(r,[],n))}function Y(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function I(t){let e=U(t),n=parseFloat(e.width)||0,i=parseFloat(e.height)||0,r=H(t),o=r?t.offsetWidth:n,f=r?t.offsetHeight:i,u=l(n)!==o||l(i)!==f;return u&&(n=o,i=f),{width:n,height:i,$:u}}function $(t){return S(t)?t:t.contextElement}function q(t){let e=$(t);if(!H(e))return u(1);let n=e.getBoundingClientRect(),{width:i,height:r,$:o}=I(e),f=(o?l(n.width):n.width)/i,c=(o?l(n.height):n.height)/r;return f&&Number.isFinite(f)||(f=1),c&&Number.isFinite(c)||(c=1),{x:f,y:c}}let X=u(0);function G(t){let e=C(t);return N()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:X}function J(t,e,n,i){var r;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=$(t),f=u(1);e&&(i?S(i)&&(f=q(i)):f=q(t));let c=(void 0===(r=n)&&(r=!1),i&&(!r||i===C(l))&&r)?G(l):u(0),a=(o.left+c.x)/f.x,s=(o.top+c.y)/f.y,d=o.width/f.x,h=o.height/f.y;if(l){let t=C(l),e=i&&S(i)?C(i):i,n=t,r=Y(n);for(;r&&i&&e!==n;){let t=q(r),e=r.getBoundingClientRect(),i=U(r),o=e.left+(r.clientLeft+parseFloat(i.paddingLeft))*t.x,l=e.top+(r.clientTop+parseFloat(i.paddingTop))*t.y;a*=t.x,s*=t.y,d*=t.x,h*=t.y,a+=o,s+=l,r=Y(n=C(r))}}return v({width:d,height:h,x:a,y:s})}function K(t,e){let n=V(t).scrollLeft;return e?e.left+n:J(k(t)).left+n}function Q(t,e,n){void 0===n&&(n=!1);let i=t.getBoundingClientRect();return{x:i.left+e.scrollLeft-(n?0:K(t,i)),y:i.top+e.scrollTop}}function Z(t,e,n){let i;if("viewport"===e)i=function(t,e){let n=C(t),i=k(t),r=n.visualViewport,o=i.clientWidth,l=i.clientHeight,f=0,u=0;if(r){o=r.width,l=r.height;let t=N();(!t||t&&"fixed"===e)&&(f=r.offsetLeft,u=r.offsetTop)}return{width:o,height:l,x:f,y:u}}(t,n);else if("document"===e)i=function(t){let e=k(t),n=V(t),i=t.ownerDocument.body,r=o(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),l=o(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight),f=-n.scrollLeft+K(t),u=-n.scrollTop;return"rtl"===U(i).direction&&(f+=o(e.clientWidth,i.clientWidth)-r),{width:r,height:l,x:f,y:u}}(k(t));else if(S(e))i=function(t,e){let n=J(t,!0,"fixed"===e),i=n.top+t.clientTop,r=n.left+t.clientLeft,o=H(t)?q(t):u(1),l=t.clientWidth*o.x,f=t.clientHeight*o.y;return{width:l,height:f,x:r*o.x,y:i*o.y}}(e,n);else{let n=G(t);i={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return v(i)}function tt(t){return"static"===U(t).position}function te(t,e){if(!H(t)||"fixed"===U(t).position)return null;if(e)return e(t);let n=t.offsetParent;return k(t)===n&&(n=n.ownerDocument.body),n}function tn(t,e){let n=C(t);if(j(t))return n;if(!H(t)){let e=_(t);for(;e&&!W(e);){if(S(e)&&!tt(e))return e;e=_(e)}return n}let i=te(t,e);for(;i&&["table","td","th"].includes(O(i))&&tt(i);)i=te(i,e);return i&&W(i)&&tt(i)&&!B(i)?n:i||function(t){let e=_(t);for(;H(e)&&!W(e);){if(B(e))return e;if(j(e))break;e=_(e)}return null}(t)||n}let ti=async function(t){let e=this.getOffsetParent||tn,n=this.getDimensions,i=await n(t.floating);return{reference:function(t,e,n){let i=H(e),r=k(e),o="fixed"===n,l=J(t,!0,o,e),f={scrollLeft:0,scrollTop:0},c=u(0);if(i||!i&&!o){if(("body"!==O(e)||M(r))&&(f=V(e)),i){let t=J(e,!0,o,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else r&&(c.x=K(r))}let a=!r||i||o?u(0):Q(r,f);return{x:l.left+f.scrollLeft-c.x-a.x,y:l.top+f.scrollTop-c.y-a.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},tr={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:i,strategy:r}=t,o="fixed"===r,l=k(i),f=!!e&&j(e.floating);if(i===l||f&&o)return n;let c={scrollLeft:0,scrollTop:0},a=u(1),s=u(0),d=H(i);if((d||!d&&!o)&&(("body"!==O(i)||M(l))&&(c=V(i)),H(i))){let t=J(i);a=q(i),s.x=t.x+i.clientLeft,s.y=t.y+i.clientTop}let h=!l||d||o?u(0):Q(l,c,!0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+s.x+h.x,y:n.y*a.y-c.scrollTop*a.y+s.y+h.y}},getDocumentElement:k,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:i,strategy:l}=t,f=[..."clippingAncestors"===n?j(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let i=z(t,[],!1).filter(t=>S(t)&&"body"!==O(t)),r=null,o="fixed"===U(t).position,l=o?_(t):t;for(;S(l)&&!W(l);){let e=U(l),n=B(l);n||"fixed"!==e.position||(r=null),(o?!n&&!r:!n&&"static"===e.position&&!!r&&["absolute","fixed"].includes(r.position)||M(l)&&!n&&function t(e,n){let i=_(e);return!(i===n||!S(i)||W(i))&&("fixed"===U(i).position||t(i,n))}(t,l))?i=i.filter(t=>t!==l):r=e,l=_(l)}return e.set(t,i),i}(e,this._c):[].concat(n),i],u=f[0],c=f.reduce((t,n)=>{let i=Z(e,n,l);return t.top=o(i.top,t.top),t.right=r(i.right,t.right),t.bottom=r(i.bottom,t.bottom),t.left=o(i.left,t.left),t},Z(e,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:tn,getElementRects:ti,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=I(t);return{width:e,height:n}},getScale:q,isElement:S,isRTL:function(t){return"rtl"===U(t).direction}};function to(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function tl(t,e,n,i){let l;void 0===i&&(i={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=i,h=$(t),p=u||c?[...h?z(h):[],...z(e)]:[];p.forEach(t=>{u&&t.addEventListener("scroll",n,{passive:!0}),c&&t.addEventListener("resize",n)});let m=h&&s?function(t,e){let n,i=null,l=k(t);function u(){var t;clearTimeout(n),null==(t=i)||t.disconnect(),i=null}return!function c(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),u();let d=t.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=d;if(a||e(),!m||!g)return;let y=f(p),w=f(l.clientWidth-(h+m)),x={rootMargin:-y+"px "+-w+"px "+-f(l.clientHeight-(p+g))+"px "+-f(h)+"px",threshold:o(0,r(1,s))||1},v=!0;function b(e){let i=e[0].intersectionRatio;if(i!==s){if(!v)return c();i?c(!1,i):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==i||to(d,t.getBoundingClientRect())||c(),v=!1}try{i=new IntersectionObserver(b,{...x,root:l.ownerDocument})}catch(t){i=new IntersectionObserver(b,x)}i.observe(t)}(!0),u}(h,n):null,g=-1,y=null;a&&(y=new ResizeObserver(t=>{let[i]=t;i&&i.target===h&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),h&&!d&&y.observe(h),y.observe(e));let w=d?J(t):null;return d&&function e(){let i=J(t);w&&!to(w,i)&&n(),w=i,l=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach(t=>{u&&t.removeEventListener("scroll",n),c&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(l)}}let tf=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;let{x:r,y:o,placement:l,middlewareData:f}=e,u=await A(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(i=f.arrow)&&i.alignmentOffset?{}:{x:r+u.x,y:o+u.y,data:{...u,placement:l}}}}},tu=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:i,placement:l}=e,{mainAxis:f=!0,crossAxis:u=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...a}=s(t,e),h={x:n,y:i},m=await E(e,a),y=g(d(l)),w=p(y),x=h[w],v=h[y];if(f){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=x+m[t],i=x-m[e];x=o(n,r(x,i))}if(u){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=v+m[t],i=v-m[e];v=o(n,r(v,i))}let b=c.fn({...e,[w]:x,[y]:v});return{...b,data:{x:b.x-n,y:b.y-i,enabled:{[w]:f,[y]:u}}}}}},tc=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i,r,o,l;let{placement:f,middlewareData:u,rects:c,initialPlacement:a,platform:x,elements:v}=e,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:L,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:D=!0,...O}=s(t,e);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let C=d(f),k=g(a),P=d(a)===a,S=await (null==x.isRTL?void 0:x.isRTL(v.floating)),H=L||(P||!D?[w(a)]:function(t){let e=w(t);return[y(t),e,y(e)]}(a)),F="none"!==A;!L&&F&&H.push(...function(t,e,n,i){let r=h(t),o=function(t,e,n){let i=["left","right"],r=["right","left"];switch(t){case"top":case"bottom":if(n)return e?r:i;return e?i:r;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(d(t),"start"===n,i);return r&&(o=o.map(t=>t+"-"+r),e&&(o=o.concat(o.map(y)))),o}(a,D,A,S));let M=[a,...H],j=await E(e,O),B=[],N=(null==(i=u.flip)?void 0:i.overflows)||[];if(b&&B.push(j[C]),R){let t=function(t,e,n){void 0===n&&(n=!1);let i=h(t),r=p(g(t)),o=m(r),l="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(f,c,S);B.push(j[t[0]],j[t[1]])}if(N=[...N,{placement:f,overflows:B}],!B.every(t=>t<=0)){let t=((null==(r=u.flip)?void 0:r.index)||0)+1,e=M[t];if(e)return{data:{index:t,overflows:N},reset:{placement:e}};let n=null==(o=N.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(T){case"bestFit":{let t=null==(l=N.filter(t=>{if(F){let e=g(t.placement);return e===k||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=a}if(f!==n)return{reset:{placement:n}}}return{}}}},ta=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,i;let l,f;let{placement:u,rects:c,platform:a,elements:p}=e,{apply:m=()=>{},...y}=s(t,e),w=await E(e,y),x=d(u),v=h(u),b="y"===g(u),{width:R,height:L}=c.floating;"top"===x||"bottom"===x?(l=x,f=v===(await (null==a.isRTL?void 0:a.isRTL(p.floating))?"start":"end")?"left":"right"):(f=x,l="end"===v?"top":"bottom");let T=L-w.top-w.bottom,A=R-w.left-w.right,D=r(L-w[l],T),O=r(R-w[f],A),C=!e.middlewareData.shift,k=D,P=O;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(P=A),null!=(i=e.middlewareData.shift)&&i.enabled.y&&(k=T),C&&!v){let t=o(w.left,0),e=o(w.right,0),n=o(w.top,0),i=o(w.bottom,0);b?P=R-2*(0!==t||0!==e?t+e:o(w.left,w.right)):k=L-2*(0!==n||0!==i?n+i:o(w.top,w.bottom))}await m({...e,availableWidth:P,availableHeight:k});let S=await a.getDimensions(p.floating);return R!==S.width||L!==S.height?{reset:{rects:!0}}:{}}}},ts=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:i="referenceHidden",...r}=s(t,e);switch(i){case"referenceHidden":{let t=L(await E(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:T(t)}}}case"escaped":{let t=L(await E(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:T(t)}}}default:return{}}}}},td=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:i,placement:l,rects:f,platform:u,elements:c,middlewareData:a}=e,{element:d,padding:y=0}=s(t,e)||{};if(null==d)return{};let w=x(y),v={x:n,y:i},b=p(g(l)),R=m(b),E=await u.getDimensions(d),L="y"===b,T=L?"clientHeight":"clientWidth",A=f.reference[R]+f.reference[b]-v[b]-f.floating[R],D=v[b]-f.reference[b],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),C=O?O[T]:0;C&&await (null==u.isElement?void 0:u.isElement(O))||(C=c.floating[T]||f.floating[R]);let k=C/2-E[R]/2-1,P=r(w[L?"top":"left"],k),S=r(w[L?"bottom":"right"],k),H=C-E[R]-S,F=C/2-E[R]/2+(A/2-D/2),M=o(P,r(F,H)),j=!a.arrow&&null!=h(l)&&F!==M&&f.reference[R]/2-(F<P?P:S)-E[R]/2<0,B=j?F<P?F-P:F-H:0;return{[b]:v[b]+B,data:{[b]:M,centerOffset:F-M-B,...j&&{alignmentOffset:B}},reset:j}}}),th=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:i,placement:r,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:u=!0,crossAxis:c=!0}=s(t,e),a={x:n,y:i},h=g(r),m=p(h),y=a[m],w=a[h],x=s(f,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(u){let t="y"===m?"height":"width",e=o.reference[m]-o.floating[t]+v.mainAxis,n=o.reference[m]+o.reference[t]-v.mainAxis;y<e?y=e:y>n&&(y=n)}if(c){var b,R;let t="y"===m?"width":"height",e=["top","left"].includes(d(r)),n=o.reference[h]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[h])||0)+(e?0:v.crossAxis),i=o.reference[h]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[h])||0)-(e?v.crossAxis:0);w<n?w=n:w>i&&(w=i)}return{[m]:y,[h]:w}}}},tp=(t,e,n)=>{let i=new Map,r={platform:tr,...n},o={...r.platform,_c:i};return R(t,e,{...r,platform:o})}},84945:(t,e,n)=>{n.d(e,{BN:()=>p,ER:()=>m,Ej:()=>y,UE:()=>x,UU:()=>g,cY:()=>h,jD:()=>w,we:()=>s});var i=n(22475),r=n(12115),o=n(47650),l="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function f(t,e){let n,i,r;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(i=n;0!=i--;)if(!f(t[i],e[i]))return!1;return!0}if((n=(r=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(i=n;0!=i--;)if(!({}).hasOwnProperty.call(e,r[i]))return!1;for(i=n;0!=i--;){let n=r[i];if(("_owner"!==n||!t.$$typeof)&&!f(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function u(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function c(t,e){let n=u(t);return Math.round(e*n)/n}function a(t){let e=r.useRef(t);return l(()=>{e.current=t}),e}function s(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:h,floating:p}={},transform:m=!0,whileElementsMounted:g,open:y}=t,[w,x]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=r.useState(s);f(v,s)||b(s);let[R,E]=r.useState(null),[L,T]=r.useState(null),A=r.useCallback(t=>{t!==k.current&&(k.current=t,E(t))},[]),D=r.useCallback(t=>{t!==P.current&&(P.current=t,T(t))},[]),O=h||R,C=p||L,k=r.useRef(null),P=r.useRef(null),S=r.useRef(w),H=null!=g,F=a(g),M=a(d),j=a(y),B=r.useCallback(()=>{if(!k.current||!P.current)return;let t={placement:e,strategy:n,middleware:v};M.current&&(t.platform=M.current),(0,i.rD)(k.current,P.current,t).then(t=>{let e={...t,isPositioned:!1!==j.current};N.current&&!f(S.current,e)&&(S.current=e,o.flushSync(()=>{x(e)}))})},[v,e,n,M,j]);l(()=>{!1===y&&S.current.isPositioned&&(S.current.isPositioned=!1,x(t=>({...t,isPositioned:!1})))},[y]);let N=r.useRef(!1);l(()=>(N.current=!0,()=>{N.current=!1}),[]),l(()=>{if(O&&(k.current=O),C&&(P.current=C),O&&C){if(F.current)return F.current(O,C,B);B()}},[O,C,B,F,H]);let W=r.useMemo(()=>({reference:k,floating:P,setReference:A,setFloating:D}),[A,D]),U=r.useMemo(()=>({reference:O,floating:C}),[O,C]),V=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!U.floating)return t;let e=c(U.floating,w.x),i=c(U.floating,w.y);return m?{...t,transform:"translate("+e+"px, "+i+"px)",...u(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:i}},[n,m,U.floating,w.x,w.y]);return r.useMemo(()=>({...w,update:B,refs:W,elements:U,floatingStyles:V}),[w,B,W,U,V])}let d=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,i.UE)({element:n.current,padding:r}).fn(e):{}:n?(0,i.UE)({element:n,padding:r}).fn(e):{}}}),h=(t,e)=>({...(0,i.cY)(t),options:[t,e]}),p=(t,e)=>({...(0,i.BN)(t),options:[t,e]}),m=(t,e)=>({...(0,i.ER)(t),options:[t,e]}),g=(t,e)=>({...(0,i.UU)(t),options:[t,e]}),y=(t,e)=>({...(0,i.Ej)(t),options:[t,e]}),w=(t,e)=>({...(0,i.jD)(t),options:[t,e]}),x=(t,e)=>({...d(t),options:[t,e]})}}]);