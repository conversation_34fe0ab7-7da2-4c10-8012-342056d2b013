"use strict";exports.id=5236,exports.ids=[5236],exports.modules={5336:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25112:(e,t,r)=>{r.d(t,{C1:()=>P,bL:()=>C});var n=r(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function l(...e){return n.useCallback(o(...e),e)}var a=r(60687);function u(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function s(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}var d=globalThis?.document?n.useLayoutEffect:()=>{},f=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[i,o]=n.useState(),l=n.useRef({}),a=n.useRef(e),u=n.useRef("none"),[s,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=c(l.current);u.current="mounted"===s?e:"none"},[s]),d(()=>{let t=l.current,r=a.current;if(r!==e){let n=u.current,i=c(t);e?f("MOUNT"):"none"===i||t?.display==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),d(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=c(l.current).includes(r.animationName);if(r.target===i&&n&&(f("ANIMATION_END"),!a.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(u.current=c(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:n.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(t),o="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),a=l(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||i.isPresent?n.cloneElement(o,{ref:a}):null};function c(e){return e?.animationName||"none"}f.displayName="Presence",r(51215);var p=Symbol("radix.slottable");function m(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,a;let u=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(s.ref=t?o(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...o}=e,l=n.Children.toArray(i),u=l.find(m);if(u){let e=u.props.children,i=l.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...o,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...o,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),y="Checkbox",[h,b]=function(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return i.scopeName=e,[function(t,i){let o=n.createContext(i),l=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,s=r?.[e]?.[l]||o,d=n.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:d,children:i})};return u.displayName=t+"Provider",[u,function(r,a){let u=a?.[e]?.[l]||o,s=n.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(i,...t)]}(y),[g,x]=h(y),N=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:o,defaultChecked:d,required:f,disabled:c,value:p="on",onCheckedChange:m,form:y,...h}=e,[b,x]=n.useState(null),N=l(t,e=>x(e)),w=n.useRef(!1),E=!b||y||!!b.closest("form"),[C=!1,P]=function({prop:e,defaultProp:t,onChange:r=()=>{}}){let[i,o]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[i]=r,o=n.useRef(i),l=s(t);return n.useEffect(()=>{o.current!==i&&(l(i),o.current=i)},[i,o,l]),r}({defaultProp:t,onChange:r}),l=void 0!==e,a=l?e:i,u=s(r);return[a,n.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else o(t)},[l,e,o,u])]}({prop:o,defaultProp:d,onChange:m}),M=n.useRef(C);return n.useEffect(()=>{let e=b?.form;if(e){let t=()=>P(M.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,P]),(0,a.jsxs)(g,{scope:r,state:C,disabled:c,children:[(0,a.jsx)(v.button,{type:"button",role:"checkbox","aria-checked":O(C)?"mixed":C,"aria-required":f,"data-state":j(C),"data-disabled":c?"":void 0,disabled:c,value:p,...h,ref:N,onKeyDown:u(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:u(e.onClick,e=>{P(e=>!!O(e)||!e),E&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),E&&(0,a.jsx)(R,{control:b,bubbles:!w.current,name:i,value:p,checked:C,required:f,disabled:c,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!O(d)&&d})]})});N.displayName=y;var w="CheckboxIndicator",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...i}=e,o=x(w,r);return(0,a.jsx)(f,{present:n||O(o.state)||!0===o.state,children:(0,a.jsx)(v.span,{"data-state":j(o.state),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=w;var R=e=>{let{control:t,checked:r,bubbles:i=!0,defaultChecked:o,...l}=e,u=n.useRef(null),s=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),f=function(e){let[t,r]=n.useState(void 0);return d(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(t);n.useEffect(()=>{let e=u.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(s!==r&&t){let n=new Event("click",{bubbles:i});e.indeterminate=O(r),t.call(e,!O(r)&&r),e.dispatchEvent(n)}},[s,r,i]);let c=n.useRef(!O(r)&&r);return(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??c.current,...l,tabIndex:-1,ref:u,style:{...e.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function O(e){return"indeterminate"===e}function j(e){return O(e)?"indeterminate":e?"checked":"unchecked"}var C=N,P=E},25177:(e,t,r)=>{r.d(t,{C1:()=>N,bL:()=>x});var n=r(43210),i=r(11273),o=r(3416),l=r(60687),a="Progress",[u,s]=(0,i.A)(a),[d,f]=u(a),c=n.forwardRef((e,t)=>{var r,n;let{__scopeProgress:i,value:a=null,max:u,getValueLabel:s=v,...f}=e;(u||0===u)&&!b(u)&&console.error((r=`${u}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=b(u)?u:100;null===a||g(a,c)||console.error((n=`${a}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=g(a,c)?a:null,m=h(p)?s(p,c):void 0;return(0,l.jsx)(d,{scope:i,value:p,max:c,children:(0,l.jsx)(o.sG.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":h(p)?p:void 0,"aria-valuetext":m,role:"progressbar","data-state":y(p,c),"data-value":p??void 0,"data-max":c,...f,ref:t})})});c.displayName=a;var p="ProgressIndicator",m=n.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,i=f(p,r);return(0,l.jsx)(o.sG.div,{"data-state":y(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...n,ref:t})});function v(e,t){return`${Math.round(e/t*100)}%`}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function b(e){return h(e)&&!isNaN(e)&&e>0}function g(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=p;var x=c,N=m},42123:(e,t,r)=>{r.d(t,{b:()=>d});var n=r(43210);r(51215);var i=r(11329),o=r(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?r:t,{...l,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),a="horizontal",u=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:i=a,...s}=e,d=(r=i,u.includes(r))?i:a;return(0,o.jsx)(l.div,{"data-orientation":d,...n?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...s,ref:t})});s.displayName="Separator";var d=s},61170:(e,t,r)=>{r.d(t,{b:()=>d});var n=r(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var o=r(60687),l=Symbol("radix.slottable");function a(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var l;let e,a;let u=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(s.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...l}=e,u=n.Children.toArray(i),s=u.find(a);if(s){let e=s.props.children,i=u.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...l,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?r:t,{...l,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),s=n.forwardRef((e,t)=>(0,o.jsx)(u.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var d=s}};