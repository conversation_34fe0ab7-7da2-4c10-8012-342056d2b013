(()=>{var e={};e.id=8150,e.ids=[8150],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34457:(e,t,s)=>{Promise.resolve().then(s.bind(s,79616))},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42193:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),o=s(88170),l=s.n(o),i=s(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let d={children:["",{children:["blogs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71734)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,60837)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blogs/[id]/page",pathname:"/blogs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>n,Wu:()=>d,ZB:()=>i,Zp:()=>o,aR:()=>l,wL:()=>c});var r=s(60687);s(43210);var a=s(4780);function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60837:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413);function a({children:e}){return(0,r.jsx)("div",{children:e})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63833:(e,t,s)=>{Promise.resolve().then(s.bind(s,71734))},71734:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\blogs\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79616:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(43210),o=s(16189),l=s(30474),i=s(82150),n=s(79663),d=s(29523),c=s(41862),p=s(28559),u=s(58869),m=s(82080),x=s(40228),g=s(90269),h=s(46303),b=s(44493);let f=({params:e})=>{let t;let s=(0,o.useRouter)(),[f,v]=(0,a.useState)(null),[y,j]=(0,a.useState)(!0),[w,N]=(0,a.useState)("");if((0,a.useEffect)(()=>{(async()=>{let{id:t}=await e;N(t)})()},[e]),(0,a.useEffect)(()=>{let e=async()=>{try{if(!w)return;j(!0);let e=await (0,i.dZ)(w);v(e)}catch{s.push("/blogs")}finally{j(!1)}};w&&e()},[w,s]),y)return(0,r.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,r.jsx)(g.default,{}),(0,r.jsx)("div",{className:"flex justify-center items-center h-[70vh]",children:(0,r.jsx)(c.A,{className:"h-8 w-8 animate-spin text-primary"})}),(0,r.jsx)(h.default,{})]});if(!f)return(0,r.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,r.jsx)(g.default,{}),(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-[70vh]",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Blog not found"}),(0,r.jsx)(d.$,{onClick:()=>s.push("/blogs"),children:"Go back to blogs"})]}),(0,r.jsx)(h.default,{})]});let k="http://localhost:4005/".replace(/\/$/,""),_=f.blogImage?f.blogImage.startsWith("/")?f.blogImage:`/${f.blogImage}`:"",q=f.blogImage?`${k}${_}`:"";return(0,r.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,r.jsx)(g.default,{}),(0,r.jsxs)("main",{className:"container mx-auto py-12 px-4 max-w-5xl",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsx)(d.$,{variant:"outline",size:"icon",className:"mr-3 rounded-full hover:bg-[#FD904B]/10 hover:text-[#FD904B] transition-colors",onClick:()=>s.back(),children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Back to Blogs"})]}),(0,r.jsxs)("div",{className:"mb-12",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6 leading-tight",children:f.blogTitle}),f.class&&(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 text-[#FD904B]"}),(0,r.jsxs)("span",{children:[f.class.firstName," ",f.class.lastName]})]}),(0,r.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-[#FD904B]"}),(0,r.jsx)("span",{children:f.class.className})]}),(0,r.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-[#FD904B]"}),(0,r.jsx)("span",{children:(t=f.createdAt,(0,n.GP)(new Date(t),"MMMM dd, yyyy"))})]})]})]}),(0,r.jsx)("div",{className:"relative w-full h-[300px] md:h-[500px] mb-10 rounded-2xl overflow-hidden shadow-lg",children:f.blogImage&&(0,r.jsx)("div",{className:"relative w-full h-full",children:(0,r.jsx)(l.default,{src:q,alt:f.blogTitle,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 80vw",priority:!0})})}),(0,r.jsx)(b.Zp,{className:"border-0 shadow-none mb-12",children:(0,r.jsx)(b.Wu,{className:"p-0 md:p-4",children:(0,r.jsx)("article",{className:"blog-content",dangerouslySetInnerHTML:{__html:f.blogDescription}})})})]}),(0,r.jsx)(h.default,{})]})}},81630:e=>{"use strict";e.exports=require("http")},82080:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},82150:(e,t,s)=>{"use strict";s.d(t,{$5:()=>i,BU:()=>a,c5:()=>n,cc:()=>d,dZ:()=>l,sq:()=>o});var r=s(28527);let a=async(e=1,t=10)=>{try{return(await r.S.get("/blogs/approved",{params:{page:e,limit:t}})).data}catch(e){throw Error(e.response?.data?.message||`Failed to fetch approved blogs: ${e.message}`)}},o=async(e=1,t=10,s)=>{try{return(await r.S.get("/blogs/my-blogs",{params:{page:e,limit:t,status:s}})).data}catch(e){throw Error(e.response?.data?.message||`Failed to fetch your blogs: ${e.message}`)}},l=async e=>{try{return(await r.S.get(`/blogs/${e}`)).data.data}catch(e){throw Error(e.response?.data?.message||`Failed to fetch blog: ${e.message}`)}},i=async e=>{try{let t=new FormData;return t.append("blogTitle",e.blogTitle),t.append("blogDescription",e.blogDescription),e.blogImage&&t.append("blogImage",e.blogImage),(await r.S.post("/blogs",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){throw Error(e.response?.data?.message||`Failed to create blog: ${e.message}`)}},n=async(e,t)=>{try{let s=new FormData;return t.blogTitle&&s.append("blogTitle",t.blogTitle),t.blogDescription&&s.append("blogDescription",t.blogDescription),t.blogImage&&s.append("blogImage",t.blogImage),t.status&&s.append("status",t.status),(await r.S.put(`/blogs/${e}`,s,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){throw Error(e.response?.data?.message||`Failed to update blog: ${e.message}`)}},d=async e=>{try{await r.S.delete(`/blogs/${e}`)}catch(e){throw Error(e.response?.data?.message||`Failed to delete blog: ${e.message}`)}}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,9663,2800],()=>s(42193));module.exports=r})();