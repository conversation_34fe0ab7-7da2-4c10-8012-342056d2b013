[{"G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\page.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\[id]\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-details\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-thoughts\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\data-table.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\layout.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\reviews\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\testimonials\\page.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\page.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\result\\[examId]\\page.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\layout.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\quiz-termination-log\\page.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\examSlice.ts": "14", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\login\\page.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\ReduxProvider.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\store.ts": "19", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\app-sidebar.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\dataTable.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-main.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-user.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\site-header.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ConfirmationDialog.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamForm.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamTable.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\TestimonialTable.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\accordion.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\alert-dialog.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\avatar.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\badge.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\button.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\card.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\chart.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\checkbox.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dialog.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dropdown-menu.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\form.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\input.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\label.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\select.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\separator.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sheet.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sidebar.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\skeleton.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sonner.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\table.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tabs.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle-group.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tooltip.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\hooks\\use-mobile.ts": "53", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\axios.ts": "54", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\types.ts": "55", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\utils.ts": "56", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\examSchema.ts": "57", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\auth.ts": "58", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\blogApi.ts": "59", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesApi.ts": "60", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesThoughtApi.ts": "61", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\email.ts": "62", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApi.ts": "63", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApplicationApi.ts": "64", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionApi.ts": "65", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\quizTerminationLog.ts": "66", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\reviewsApi.ts": "67", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-details\\page.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\studentApi.ts": "69", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\question-bank\\page.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionBankApi.ts": "71", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\testimonialApi.ts": "72", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\rank-price\\[examId]\\page.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\PriceRankTable.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\priceRankSchema.ts": "75", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizPriceRankApi.ts": "76", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-profile\\[id]\\page.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-applicant\\[examId]\\page.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\StudentProfileModal.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizExamApplicantApi.ts": "80", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\page.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\ReferralFilters.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\[linkId]\\referred-users\\page.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\calendar.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\not-found.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\chat\\page.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddCertificateForm.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddEducationForm.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddExperienceForm.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\CertificateTab.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\EducationTab.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ImagesTab.tsx": "92", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ProfileTab.tsx": "93", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassForm.tsx": "94", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassList.tsx": "95", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionTab.tsx": "96", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\WorkTab.tsx": "97", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\page.tsx": "98", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-edit\\[id]\\page.tsx": "99", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminChat.tsx": "100", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\multi-select.tsx": "101", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\classesEditSchema.ts": "102", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\chatApi.ts": "103", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-prefrence\\[examId]\\page.tsx": "104", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-terminated-students\\[examId]\\page.tsx": "105", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\LevelPrenceManager.tsx": "106", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\SubjectPreferenceManager.tsx": "107", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizLevelPrefrenceApi.ts": "108", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizQuizTerminationLogApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizSubjectPrefrenceApi.ts": "110", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-monitoring\\page.tsx": "111", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\StudentPhotoMonitoring.tsx": "112", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examMonitoringApi.ts": "113", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-result\\[examId]\\page.tsx": "114", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\notifications\\page.tsx": "115", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminNotificationBell.tsx": "116", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\notificationService.ts": "117", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-student\\[id]\\page.tsx": "118", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\page.tsx": "119", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\sub-detail\\[subDetailId]\\page.tsx": "120", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\page.tsx": "121", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\page.tsx": "122", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\mock-question-bank\\page.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\ConfirmDialog.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\pagination.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classes-student.ts": "126", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\constantsApi.ts": "127", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\mock-examApi.ts": "128"}, {"size": 6958, "mtime": *************, "results": "129", "hashOfConfig": "130"}, {"size": 6736, "mtime": *************, "results": "131", "hashOfConfig": "130"}, {"size": 27265, "mtime": *************, "results": "132", "hashOfConfig": "130"}, {"size": 8414, "mtime": *************, "results": "133", "hashOfConfig": "130"}, {"size": 14092, "mtime": *************, "results": "134", "hashOfConfig": "130"}, {"size": 144, "mtime": *************, "results": "135", "hashOfConfig": "130"}, {"size": 698, "mtime": *************, "results": "136", "hashOfConfig": "130"}, {"size": 4377, "mtime": 1752465853423, "results": "137", "hashOfConfig": "130"}, {"size": 248, "mtime": 1747650902107, "results": "138", "hashOfConfig": "130"}, {"size": 272, "mtime": 1747818545268, "results": "139", "hashOfConfig": "130"}, {"size": 1666, "mtime": 1752465855077, "results": "140", "hashOfConfig": "130"}, {"size": 698, "mtime": 1747109266172, "results": "141", "hashOfConfig": "130"}, {"size": 4900, "mtime": 1747109266327, "results": "142", "hashOfConfig": "130"}, {"size": 6769, "mtime": 1747624459637, "results": "143", "hashOfConfig": "130"}, {"size": 587, "mtime": 1747109266338, "results": "144", "hashOfConfig": "130"}, {"size": 3150, "mtime": 1747109266339, "results": "145", "hashOfConfig": "130"}, {"size": 111, "mtime": 1747109266341, "results": "146", "hashOfConfig": "130"}, {"size": 236, "mtime": 1747109266328, "results": "147", "hashOfConfig": "130"}, {"size": 325, "mtime": 1747109266342, "results": "148", "hashOfConfig": "130"}, {"size": 3432, "mtime": 1752465850248, "results": "149", "hashOfConfig": "130"}, {"size": 2911, "mtime": 1752465850414, "results": "150", "hashOfConfig": "130"}, {"size": 3329, "mtime": 1747797160821, "results": "151", "hashOfConfig": "130"}, {"size": 3210, "mtime": 1747109266135, "results": "152", "hashOfConfig": "130"}, {"size": 964, "mtime": 1751625230881, "results": "153", "hashOfConfig": "130"}, {"size": 1292, "mtime": 1751276652287, "results": "154", "hashOfConfig": "130"}, {"size": 15071, "mtime": 1747797160859, "results": "155", "hashOfConfig": "130"}, {"size": 8679, "mtime": 1752465855843, "results": "156", "hashOfConfig": "130"}, {"size": 10610, "mtime": 1752465856725, "results": "157", "hashOfConfig": "130"}, {"size": 2125, "mtime": 1747109266363, "results": "158", "hashOfConfig": "130"}, {"size": 4021, "mtime": 1747289688502, "results": "159", "hashOfConfig": "130"}, {"size": 1090, "mtime": 1747109266365, "results": "160", "hashOfConfig": "130"}, {"size": 1634, "mtime": 1747109266367, "results": "161", "hashOfConfig": "130"}, {"size": 2158, "mtime": 1747109266369, "results": "162", "hashOfConfig": "130"}, {"size": 2003, "mtime": 1747109266374, "results": "163", "hashOfConfig": "130"}, {"size": 10019, "mtime": 1747109266376, "results": "164", "hashOfConfig": "130"}, {"size": 1258, "mtime": 1747109266385, "results": "165", "hashOfConfig": "130"}, {"size": 3915, "mtime": 1748363529403, "results": "166", "hashOfConfig": "130"}, {"size": 8407, "mtime": 1747109266407, "results": "167", "hashOfConfig": "130"}, {"size": 3871, "mtime": 1747109266409, "results": "168", "hashOfConfig": "130"}, {"size": 997, "mtime": 1750649653890, "results": "169", "hashOfConfig": "130"}, {"size": 639, "mtime": 1752465856736, "results": "170", "hashOfConfig": "130"}, {"size": 6433, "mtime": 1750649653892, "results": "171", "hashOfConfig": "130"}, {"size": 738, "mtime": 1747109266416, "results": "172", "hashOfConfig": "130"}, {"size": 4227, "mtime": 1747109266425, "results": "173", "hashOfConfig": "130"}, {"size": 22330, "mtime": 1747109266426, "results": "174", "hashOfConfig": "130"}, {"size": 292, "mtime": 1747109266428, "results": "175", "hashOfConfig": "130"}, {"size": 596, "mtime": 1747109266429, "results": "176", "hashOfConfig": "130"}, {"size": 2458, "mtime": 1747109266431, "results": "177", "hashOfConfig": "130"}, {"size": 2016, "mtime": 1747109266432, "results": "178", "hashOfConfig": "130"}, {"size": 1997, "mtime": 1747109266434, "results": "179", "hashOfConfig": "130"}, {"size": 1622, "mtime": 1747109266437, "results": "180", "hashOfConfig": "130"}, {"size": 1953, "mtime": 1747109266453, "results": "181", "hashOfConfig": "130"}, {"size": 595, "mtime": 1747109266455, "results": "182", "hashOfConfig": "130"}, {"size": 1155, "mtime": 1748962020698, "results": "183", "hashOfConfig": "130"}, {"size": 12193, "mtime": 1752490278742, "results": "184", "hashOfConfig": "130"}, {"size": 407, "mtime": 1747289688590, "results": "185", "hashOfConfig": "130"}, {"size": 2333, "mtime": 1750649653906, "results": "186", "hashOfConfig": "130"}, {"size": 281, "mtime": 1747109266546, "results": "187", "hashOfConfig": "130"}, {"size": 1430, "mtime": 1751272204657, "results": "188", "hashOfConfig": "130"}, {"size": 8100, "mtime": 1749201001867, "results": "189", "hashOfConfig": "130"}, {"size": 1510, "mtime": 1751625231028, "results": "190", "hashOfConfig": "130"}, {"size": 354, "mtime": 1749530596920, "results": "191", "hashOfConfig": "130"}, {"size": 1779, "mtime": 1747797160877, "results": "192", "hashOfConfig": "130"}, {"size": 1017, "mtime": 1747109266581, "results": "193", "hashOfConfig": "130"}, {"size": 2988, "mtime": 1747109266582, "results": "194", "hashOfConfig": "130"}, {"size": 795, "mtime": 1747109266583, "results": "195", "hashOfConfig": "130"}, {"size": 612, "mtime": 1747289688605, "results": "196", "hashOfConfig": "130"}, {"size": 15043, "mtime": 1752465854143, "results": "197", "hashOfConfig": "130"}, {"size": 4251, "mtime": 1751523077949, "results": "198", "hashOfConfig": "130"}, {"size": 49538, "mtime": 1752465855153, "results": "199", "hashOfConfig": "130"}, {"size": 6782, "mtime": 1752489573453, "results": "200", "hashOfConfig": "130"}, {"size": 1472, "mtime": 1747797160878, "results": "201", "hashOfConfig": "130"}, {"size": 9128, "mtime": 1752465855203, "results": "202", "hashOfConfig": "130"}, {"size": 1923, "mtime": 1747797160861, "results": "203", "hashOfConfig": "130"}, {"size": 258, "mtime": 1747797160876, "results": "204", "hashOfConfig": "130"}, {"size": 1933, "mtime": 1747797160878, "results": "205", "hashOfConfig": "130"}, {"size": 16693, "mtime": 1749486774218, "results": "206", "hashOfConfig": "130"}, {"size": 2307, "mtime": 1752465854518, "results": "207", "hashOfConfig": "130"}, {"size": 11914, "mtime": 1749486774211, "results": "208", "hashOfConfig": "130"}, {"size": 1489, "mtime": 1748363529488, "results": "209", "hashOfConfig": "130"}, {"size": 27319, "mtime": 1752465853271, "results": "210", "hashOfConfig": "130"}, {"size": 7414, "mtime": 1748768935822, "results": "211", "hashOfConfig": "130"}, {"size": 30405, "mtime": 1752465852869, "results": "212", "hashOfConfig": "130"}, {"size": 4054, "mtime": 1748363529403, "results": "213", "hashOfConfig": "130"}, {"size": 847, "mtime": 1748768935825, "results": "214", "hashOfConfig": "130"}, {"size": 236, "mtime": 1751276652281, "results": "215", "hashOfConfig": "130"}, {"size": 9836, "mtime": 1749201001627, "results": "216", "hashOfConfig": "130"}, {"size": 14847, "mtime": 1749201001637, "results": "217", "hashOfConfig": "130"}, {"size": 12466, "mtime": 1749201001662, "results": "218", "hashOfConfig": "130"}, {"size": 8747, "mtime": 1749201001679, "results": "219", "hashOfConfig": "130"}, {"size": 9707, "mtime": 1749201001681, "results": "220", "hashOfConfig": "130"}, {"size": 6260, "mtime": 1749201001698, "results": "221", "hashOfConfig": "130"}, {"size": 10604, "mtime": 1749201001711, "results": "222", "hashOfConfig": "130"}, {"size": 13037, "mtime": 1749486774214, "results": "223", "hashOfConfig": "130"}, {"size": 4849, "mtime": 1749201001776, "results": "224", "hashOfConfig": "130"}, {"size": 2628, "mtime": 1749201001787, "results": "225", "hashOfConfig": "130"}, {"size": 9446, "mtime": 1749201001792, "results": "226", "hashOfConfig": "130"}, {"size": 18789, "mtime": 1749486774215, "results": "227", "hashOfConfig": "130"}, {"size": 37891, "mtime": 1749486774217, "results": "228", "hashOfConfig": "130"}, {"size": 31141, "mtime": 1752490278248, "results": "229", "hashOfConfig": "130"}, {"size": 3738, "mtime": 1752490278705, "results": "230", "hashOfConfig": "130"}, {"size": 3207, "mtime": 1749486774424, "results": "231", "hashOfConfig": "130"}, {"size": 1365, "mtime": 1751276652291, "results": "232", "hashOfConfig": "130"}, {"size": 1187, "mtime": 1749486774234, "results": "233", "hashOfConfig": "130"}, {"size": 3013, "mtime": 1752465855220, "results": "234", "hashOfConfig": "130"}, {"size": 8011, "mtime": 1752465855888, "results": "235", "hashOfConfig": "130"}, {"size": 8834, "mtime": 1752465856640, "results": "236", "hashOfConfig": "130"}, {"size": 1675, "mtime": 1750649653907, "results": "237", "hashOfConfig": "130"}, {"size": 548, "mtime": 1749486774426, "results": "238", "hashOfConfig": "130"}, {"size": 2118, "mtime": 1750649653908, "results": "239", "hashOfConfig": "130"}, {"size": 554, "mtime": 1750649653852, "results": "240", "hashOfConfig": "130"}, {"size": 13617, "mtime": 1752465856029, "results": "241", "hashOfConfig": "130"}, {"size": 1291, "mtime": 1750649653906, "results": "242", "hashOfConfig": "130"}, {"size": 3822, "mtime": 1752465855204, "results": "243", "hashOfConfig": "130"}, {"size": 21456, "mtime": 1751625230898, "results": "244", "hashOfConfig": "130"}, {"size": 9775, "mtime": 1751625230867, "results": "245", "hashOfConfig": "130"}, {"size": 4673, "mtime": 1751625231663, "results": "246", "hashOfConfig": "130"}, {"size": 5474, "mtime": 1752465851656, "results": "247", "hashOfConfig": "130"}, {"size": 13037, "mtime": 1752489573348, "results": "248", "hashOfConfig": "130"}, {"size": 12800, "mtime": 1752489573359, "results": "249", "hashOfConfig": "130"}, {"size": 13012, "mtime": 1752489573392, "results": "250", "hashOfConfig": "130"}, {"size": 12623, "mtime": 1752489573414, "results": "251", "hashOfConfig": "130"}, {"size": 34882, "mtime": 1752465855130, "results": "252", "hashOfConfig": "130"}, {"size": 1574, "mtime": 1752465850247, "results": "253", "hashOfConfig": "130"}, {"size": 1868, "mtime": 1752465850416, "results": "254", "hashOfConfig": "130"}, {"size": 2012, "mtime": 1752465856751, "results": "255", "hashOfConfig": "130"}, {"size": 5280, "mtime": 1752465856752, "results": "256", "hashOfConfig": "130"}, {"size": 3364, "mtime": 1752465857138, "results": "257", "hashOfConfig": "130"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k18kcd", {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-details\\[id]\\page.tsx", [], ["642"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-thoughts\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\data-table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\reviews\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\testimonials\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\result\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\quiz-termination-log\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\examSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\ReduxProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\store.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\dataTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ConfirmationDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\TestimonialTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\chart.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle-group.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\examSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\auth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\email.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-details\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\studentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\question-bank\\page.tsx", ["643"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\testimonialApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\rank-price\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\PriceRankTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\priceRankSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizPriceRankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-profile\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-applicant\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\StudentProfileModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizExamApplicantApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\ReferralFilters.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\[linkId]\\referred-users\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddCertificateForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddEducationForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddExperienceForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\CertificateTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\EducationTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ImagesTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ProfileTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassList.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\WorkTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\page.tsx", [], ["644"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-edit\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminChat.tsx", ["645", "646"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\classesEditSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\chatApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-prefrence\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-terminated-students\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\LevelPrenceManager.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\SubjectPreferenceManager.tsx", ["647"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizLevelPrefrenceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizQuizTerminationLogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizSubjectPrefrenceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-monitoring\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\StudentPhotoMonitoring.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-result\\[examId]\\page.tsx", ["648"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\notifications\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminNotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\notificationService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-student\\[id]\\page.tsx", ["649", "650"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\page.tsx", ["651"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\sub-detail\\[subDetailId]\\page.tsx", ["652"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\page.tsx", [], ["653"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\page.tsx", [], ["654"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\mock-question-bank\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\ConfirmDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classes-student.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\constantsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\mock-examApi.ts", [], [], {"ruleId": "655", "severity": 1, "message": "656", "line": 67, "column": 6, "nodeType": "657", "endLine": 67, "endColumn": 14, "suggestions": "658", "suppressions": "659"}, {"ruleId": "655", "severity": 1, "message": "660", "line": 254, "column": 6, "nodeType": "657", "endLine": 254, "endColumn": 13, "suggestions": "661"}, {"ruleId": "655", "severity": 1, "message": "662", "line": 231, "column": 6, "nodeType": "657", "endLine": 231, "endColumn": 15, "suggestions": "663", "suppressions": "664"}, {"ruleId": "655", "severity": 1, "message": "665", "line": 107, "column": 8, "nodeType": "657", "endLine": 107, "endColumn": 56, "suggestions": "666"}, {"ruleId": "655", "severity": 1, "message": "667", "line": 117, "column": 8, "nodeType": "657", "endLine": 117, "endColumn": 57, "suggestions": "668"}, {"ruleId": "655", "severity": 1, "message": "669", "line": 127, "column": 6, "nodeType": "657", "endLine": 127, "endColumn": 8, "suggestions": "670"}, {"ruleId": "655", "severity": 1, "message": "671", "line": 62, "column": 6, "nodeType": "657", "endLine": 62, "endColumn": 20, "suggestions": "672"}, {"ruleId": "655", "severity": 1, "message": "673", "line": 107, "column": 6, "nodeType": "657", "endLine": 107, "endColumn": 15, "suggestions": "674"}, {"ruleId": "655", "severity": 1, "message": "675", "line": 111, "column": 6, "nodeType": "657", "endLine": 111, "endColumn": 33, "suggestions": "676"}, {"ruleId": "655", "severity": 1, "message": "677", "line": 228, "column": 6, "nodeType": "657", "endLine": 228, "endColumn": 19, "suggestions": "678"}, {"ruleId": "655", "severity": 1, "message": "679", "line": 225, "column": 6, "nodeType": "657", "endLine": 225, "endColumn": 22, "suggestions": "680"}, {"ruleId": "655", "severity": 1, "message": "681", "line": 234, "column": 6, "nodeType": "657", "endLine": 234, "endColumn": 21, "suggestions": "682", "suppressions": "683"}, {"ruleId": "655", "severity": 1, "message": "684", "line": 225, "column": 6, "nodeType": "657", "endLine": 225, "endColumn": 8, "suggestions": "685", "suppressions": "686"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchTeacher'. Either include it or remove the dependency array.", "ArrayExpression", ["687"], ["688"], "React Hook useEffect has missing dependencies: 'fetchConstants' and 'fetchQuestions'. Either include them or remove the dependency array.", ["689"], "React Hook useEffect has a missing dependency: 'fetchClassData'. Either include it or remove the dependency array.", ["690"], ["691"], "React Hook useEffect has a missing dependency: 'classes.length'. Either include it or remove the dependency array.", ["692"], "React Hook useEffect has a missing dependency: 'students.length'. Either include it or remove the dependency array.", ["693"], "React Hook useCallback has a missing dependency: 'examId'. Either include it or remove the dependency array.", ["694"], "React Hook useEffect has a missing dependency: 'fetchRankings'. Either include it or remove the dependency array.", ["695"], "React Hook useEffect has missing dependencies: 'currentPage', 'getStudents', 'getYears', 'searchTerm', and 'selectedYear'. Either include them or remove the dependency array.", ["696"], "React Hook useEffect has missing dependencies: 'getStudents' and 'searchTerm'. Either include them or remove the dependency array.", ["697"], "React Hook useEffect has a missing dependency: 'fetchSubDetails'. Either include it or remove the dependency array.", ["698"], "React Hook useEffect has a missing dependency: 'fetchValues'. Either include it or remove the dependency array.", ["699"], "React Hook useEffect has a missing dependency: 'fetchDetails'. Either include it or remove the dependency array.", ["700"], ["701"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["702"], ["703"], {"desc": "704", "fix": "705"}, {"kind": "706", "justification": "707"}, {"desc": "708", "fix": "709"}, {"desc": "710", "fix": "711"}, {"kind": "706", "justification": "707"}, {"desc": "712", "fix": "713"}, {"desc": "714", "fix": "715"}, {"desc": "716", "fix": "717"}, {"desc": "718", "fix": "719"}, {"desc": "720", "fix": "721"}, {"desc": "722", "fix": "723"}, {"desc": "724", "fix": "725"}, {"desc": "726", "fix": "727"}, {"desc": "728", "fix": "729"}, {"kind": "706", "justification": "707"}, {"desc": "730", "fix": "731"}, {"kind": "706", "justification": "707"}, "Update the dependencies array to be: [fetchTeacher, userId]", {"range": "732", "text": "733"}, "directive", "", "Update the dependencies array to be: [fetchConstants, fetchQuestions, limit]", {"range": "734", "text": "735"}, "Update the dependencies array to be: [classId, fetchClassData]", {"range": "736", "text": "737"}, "Update the dependencies array to be: [classSearchQuery, classes.length, isAuthenticated, loadClasses]", {"range": "738", "text": "739"}, "Update the dependencies array to be: [studentSearchQuery, selectedClass, loadStudents, students.length]", {"range": "740", "text": "741"}, "Update the dependencies array to be: [examId]", {"range": "742", "text": "743"}, "Update the dependencies array to be: [examId, fetchRankings, page]", {"range": "744", "text": "745"}, "Update the dependencies array to be: [classId, currentPage, getStudents, getYears, searchTerm, selectedYear]", {"range": "746", "text": "747"}, "Update the dependencies array to be: [currentPage, getStudents, searchTerm, selectedYear]", {"range": "748", "text": "749"}, "Update the dependencies array to be: [fetchDetail, fetchSubDetails]", {"range": "750", "text": "751"}, "Update the dependencies array to be: [fetchSubDetail, fetchValues]", {"range": "752", "text": "753"}, "Update the dependencies array to be: [fetchCategory, fetchDetails]", {"range": "754", "text": "755"}, "Update the dependencies array to be: [fetchCategories]", {"range": "756", "text": "757"}, [2175, 2183], "[fetchTeacher, userId]", [9004, 9011], "[fetchConstants, fetchQuestions, limit]", [7433, 7442], "[classId, fetchClassData]", [3971, 4019], "[classSearch<PERSON><PERSON>y, classes.length, isAuthenticated, loadClasses]", [4337, 4386], "[studentSearch<PERSON><PERSON>y, selectedClass, loadStudents, students.length]", [3343, 3345], "[examId]", [1799, 1813], "[examId, fetchRankings, page]", [3073, 3082], "[classId, currentPage, getStudents, getYears, searchTerm, selectedYear]", [3171, 3198], "[currentPage, getStudents, searchTerm, selectedYear]", [7233, 7246], "[fetchDetail, fetchSubDetails]", [6733, 6749], "[fetchSubDetail, fetchValues]", [7331, 7346], "[fetch<PERSON>ate<PERSON>y, fetchDetails]", [7161, 7163], "[fetchCategories]"]