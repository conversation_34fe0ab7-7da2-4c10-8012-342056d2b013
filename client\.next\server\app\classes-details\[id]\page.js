(()=>{var e={};e.id=6968,e.ids=[6968],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8096:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes-details\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx","default")},10521:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["classes-details",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8096)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/classes-details/[id]/page",pathname:"/classes-details/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var r=s(60687);s(43210);var a=s(95732),i=s(78272),l=s(13964),n=s(3589),o=s(4780);function d({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...l}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:s="popper",...i}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(p,{})]})})}function x({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(n.A,{className:"size-4"})})}function p({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25595:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(60687),a=s(43210),i=s(30474),l=s(16189),n=s(33793),o=s(20255),d=s(44255),c=s(90471),u=s(29523),m=s(90269),x=s(46303),h=s(28527),p=s(35817),f=s(54864),g=s(64398),v=s(80462),j=s(88233),w=s(15079),b=s(52581),y=s(93500),N=s(45880),E=s(27605),S=s(63442),A=s(80942);let k=async(e,t=1,s=5)=>{try{return(await h.S.get(`/reviews/class/${e}`,{params:{page:t,limit:s}})).data}catch(e){throw Error(`Failed to fetch reviews for class: ${e.message}`)}},C=async e=>{try{return(await h.S.get(`/reviews/average/${e}`)).data.averageRating}catch(e){throw Error(`Failed to get average rating: ${e.message}`)}},$=async e=>{try{return(await h.S.post("/reviews",e)).data}catch(r){let e=r.response?.data?.message||r.message,t=r.response?.data?.alreadyReviewed||!1,s=Error(e);throw s.alreadyReviewed=t,s}},R=async e=>{try{return(await h.S.delete(`/reviews/${e}`)).data}catch(e){throw Error(`Failed to delete review: ${e.message}`)}};var P=s(4780),_=s(32584);let I=N.z.object({message:N.z.string().min(10,"Message must be at least 10 characters").max(500,"Message cannot exceed 500 characters"),rating:N.z.number().min(1,"Please select a rating"),classId:N.z.string().min(1,"Class ID is required")}),F=({classId:e,userData:t,onReviewSubmit:s})=>{let[i,l]=(0,a.useState)(0),[n,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(!1),[m,x]=(0,a.useState)(null),[h,p]=(0,a.useState)("ALL"),[f,N]=(0,a.useState)([]),[C,F]=(0,a.useState)(1),[L,z]=(0,a.useState)(!1),[D,O]=(0,a.useState)(!1),[T,q]=(0,a.useState)(!1);(0,a.useEffect)(()=>{q((0,P.xh)());let e=()=>{q((0,P.xh)())};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[]);let M=(0,E.mN)({resolver:(0,S.u)(I),defaultValues:{message:"",rating:0,classId:e}}),V=(0,a.useCallback)(async(s=1,r="ALL")=>{try{let a=await k(e,s,5),i="",l=localStorage.getItem("student_data");if(l)try{let e=JSON.parse(l);e.firstName&&e.lastName&&(i=`${e.firstName} ${e.lastName}`)}catch(e){console.error("Error parsing student data:",e)}!i&&t&&t.firstName&&t.lastName&&(i=`${t.firstName} ${t.lastName}`);let n=a.reviews.map(e=>e.studentName&&!e.message.startsWith(e.studentName)?{...e,message:`${e.studentName}: ${e.message}`}:e);if(1===s){if("ALL"===r)N(n);else if("ME"===r&&i){let e=n.filter(e=>{if(!e.studentId)return e.message.split(":")[0]===i;{let t=JSON.parse(l||"{}");return e.studentId===t.id}});N(e)}}else if("ALL"===r)N(e=>[...e,...n]);else if("ME"===r&&i){let e=n.filter(e=>{if(!e.studentId)return e.message.split(":")[0]===i;{let t=JSON.parse(l||"{}");return e.studentId===t.id}});N(t=>[...t,...e])}return z(a.hasMore),F(a.currentPage),a}catch(e){return console.error("Error fetching reviews:",e),b.oR.error("Failed to fetch reviews"),null}},[e,t]),B=async()=>{O(!0);try{await V(C+1,h)}finally{O(!1)}};(0,a.useEffect)(()=>{F(1),V(1,h)},[h,e,V]);let G=e=>{x(e),c(!0)},U=async()=>{if(!m){console.error("No review ID to delete");return}if(!(0,P.xh)()){b.oR.error("You must be logged in to delete a review"),c(!1),x(null);return}try{await R(m),b.oR.success("Review deleted successfully!"),await V(1,h),s&&s()}catch(e){console.error("Error deleting review:",e),b.oR.error(e.message||"Failed to delete review")}finally{c(!1),x(null)}},W=e=>{l(e),M.setValue("rating",e)},J=async r=>{o(!0);try{let a=localStorage.getItem("student_data"),i="",n="";if(a)try{let e=JSON.parse(a);e.firstName&&e.lastName&&(i=`${e.firstName} ${e.lastName}`,n=e.id)}catch(e){console.error("Error parsing student data:",e)}if(!i&&t&&t.firstName&&t.lastName&&(i=`${t.firstName} ${t.lastName}`),!(0,P.xh)()){b.oR.error("Only students can submit reviews");return}let o={classId:r.classId,rating:r.rating,message:r.message,studentName:i,studentId:n};await $(o),b.oR.success("Review submitted successfully! The class owner will be notified."),M.reset({message:"",rating:0,classId:e}),l(0),await V(1,h),s&&s()}catch(e){console.error("Error submitting review:",e),b.oR.error(e.message||"Failed to submit review")}finally{o(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.Lt,{open:d,onOpenChange:c,children:(0,r.jsxs)(y.EO,{className:"dark:bg-siderbar",children:[(0,r.jsxs)(y.wd,{children:[(0,r.jsx)(y.r7,{children:"Are you sure?"}),(0,r.jsx)(y.$v,{children:"This action cannot be undone. This will permanently delete your testimonial."})]}),(0,r.jsxs)(y.ck,{children:[(0,r.jsx)(y.Zr,{className:"dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",children:"Cancel"}),(0,r.jsx)(y.Rx,{onClick:U,className:"bg-red-500 text-white hover:bg-red-600",children:"Delete"})]})]})}),(0,r.jsxs)("div",{className:"w-full max-w-9xl mx-auto",children:[T?(0,r.jsxs)("div",{className:"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Write a Review"}),(0,r.jsx)(A.lV,{...M,children:(0,r.jsxs)("form",{onSubmit:M.handleSubmit(J),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Your Rating"}),(0,r.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(e=>(0,r.jsx)("button",{type:"button",onClick:()=>W(e),className:"focus:outline-none",children:(0,r.jsx)(g.A,{className:`w-8 h-8 ${e<=i?"fill-[#FD904B] text-[#FD904B]":"text-gray-300"}`})},e))}),M.formState.errors.rating&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.formState.errors.rating.message})]}),(0,r.jsx)(A.zB,{control:M.control,name:"message",render:({field:e})=>(0,r.jsxs)(A.eI,{className:"mb-6",children:[(0,r.jsx)(A.lR,{className:"block text-sm font-medium mb-2",children:"Your Message"}),(0,r.jsx)(A.MJ,{children:(0,r.jsx)("textarea",{...e,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FD904B] focus:border-transparent",rows:4,placeholder:"Share your experience (10-500 characters)..."})}),(0,r.jsx)(A.C5,{})]})}),(0,r.jsx)("button",{type:"submit",disabled:n,className:"w-full bg-[#FD904B] text-white py-2 px-4 rounded-lg hover:bg-[#FD904B]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?"Submitting...":"Submit Review"})]})})]}):(0,r.jsx)("div",{className:"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8 text-center",children:(0,r.jsx)("p",{className:"text-lg mb-4",children:"Please log in as a student to write a review"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Class Reviews"}),T&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsxs)(w.l6,{value:h,onValueChange:e=>p(e),children:[(0,r.jsx)(w.bq,{className:"w-[120px]",children:(0,r.jsx)(w.yv,{placeholder:"Filter"})}),(0,r.jsxs)(w.gC,{children:[(0,r.jsx)(w.eb,{value:"ALL",children:"All Reviews"}),(0,r.jsx)(w.eb,{value:"ME",children:"My Reviews"})]})]})]})]}),f.length>0?(0,r.jsxs)("div",{className:"space-y-4",children:[f.map(e=>(0,r.jsxs)("div",{className:"dark:bg-slidebar border border-gray-400 rounded-lg shadow-sm p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 rounded-full overflow-hidden border-2 border-[#FD904B]/80 flex justify-center items-center",children:(0,r.jsx)(_.eu,{children:(0,r.jsx)(_.q5,{className:"bg-white text-black",children:e?.student?.firstName&&e?.student?.lastName?`${e.student.firstName[0]}${e.student.lastName[0]}`.toUpperCase():"ST"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-600 dark:text-white",children:e.studentName||e.message.split(":")[0]}),T&&(()=>{let t="",s="";try{let e=localStorage.getItem("student_data");if(e){let r=JSON.parse(e);r.firstName&&r.lastName&&(t=`${r.firstName} ${r.lastName}`,s=r.id)}}catch(e){console.error("Error parsing student data:",e)}return e.studentId&&s&&e.studentId===s||t&&(e.studentName&&e.studentName===t||e.message.split(":")[0]===t)?(0,r.jsx)("button",{onClick:()=>G(e.id),className:"text-red-500 hover:text-red-700",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})}):null})()]}),(0,r.jsx)("div",{className:"flex items-center gap-1 mt-1",children:[1,2,3,4,5].map((t,s)=>(0,r.jsx)(g.A,{className:`w-4 h-4 ${s<e.rating?"fill-[#FD904B] text-[#FD904B]":"text-gray-300"}`},s))})]})]}),(0,r.jsx)("p",{className:"text-gray-700 dark:text-gray-300 break-words mb-3",children:e.message.includes(":")?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("span",{children:e.message.split(":").slice(1).join(":").trim()})}):e.message}),(0,r.jsx)("div",{className:"flex justify-between items-center text-sm text-gray-500",children:(0,r.jsxs)("span",{children:["Posted on ",new Date(e.createdAt).toLocaleDateString()]})})]},e.id)),L&&(0,r.jsx)("div",{className:"flex justify-center mt-6",children:(0,r.jsx)(u.$,{onClick:B,variant:"outline",className:"px-6 py-2 border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10",disabled:D,children:D?"Loading...":"Load More"})})]}):(0,r.jsx)("p",{className:"text-muted-foreground",children:"ALL"===h?"No reviews yet.":"You haven't written any reviews yet."})]})]})]})};var L=s(53774);let z=async(e,t)=>{try{return(await h.S.post("/class-view-log/log-view",{classId:e,studentId:t})).data}catch(e){return console.error("Error logging class view:",e),{success:!1,message:e.response?.data?.message||"Failed to log class view"}}};var D=s(63503),O=s(41862);let T=[{key:"education",label:"Education",icon:(0,r.jsx)(n.VHr,{})},{key:"work",label:"Work Experience",icon:(0,r.jsx)(o.C12,{})},{key:"certifications",label:"Certifications",icon:(0,r.jsx)(c.VqV,{})},{key:"tuition",label:"Tuition Classes",icon:(0,r.jsx)(d.VQk,{})}],q=()=>{let[e,t]=(0,a.useState)("education"),[s,o]=(0,a.useState)(null),[g,v]=(0,a.useState)(0),[j,w]=(0,a.useState)(0),[y,N]=(0,a.useState)(!1),[E,S]=(0,a.useState)(null),[A,$]=(0,a.useState)(!1),[R,_]=(0,a.useState)(!1),{id:I}=(0,l.useParams)(),q=(0,l.useRouter)(),M=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=async()=>{try{let e=await C(I);v(e)}catch(e){console.error("Failed to fetch average rating",e)}},t=async()=>{try{let e=await k(I,1,1);w(e.total)}catch(e){console.error("Failed to fetch review count",e)}},s=async()=>{let e=(0,P.xh)();if($(e),e&&I&&!M.current){M.current=!0;try{let e=localStorage.getItem("student_data");if(e){let t=JSON.parse(e);await z(I,t.id),console.log("Class view logged successfully")}}catch(e){console.error("Error logging class view:",e)}}};(async()=>{try{let e=await h.S.get(`classes/details/${I}`);o(e.data)}catch(e){console.error("Failed to fetch teacher data",e)}})(),e(),t(),s()},[I]),(0,a.useEffect)(()=>{(async()=>{if(A&&I)try{let e=await (0,L.Kh)(I);N(e.inWishlist),e.wishlistItem&&S(e.wishlistItem.id)}catch(e){console.error("Error checking wishlist status:",e)}})()},[A,I]);let V=(0,f.d4)(e=>e.user.user),B=async()=>{try{let e=await C(I);v(e);let t=await k(I,1,1);w(t.total)}catch(e){console.error("Failed to update review stats",e)}},G=async()=>{if(!A){_(!0);return}try{if(y&&E)await (0,L.Qg)(E),N(!1),S(null),b.oR.success("Removed from wishlist");else{let e=await (0,L.U4)(I);N(!0),e.data?.id&&S(e.data.id),b.oR.success("Added to wishlist")}}catch(e){b.oR.error(e.message||"Failed to update wishlist")}};if(!s)return(0,r.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,r.jsx)(O.A,{className:"w-8 h-8 animate-spin text-orange-500"})});let{firstName:U,lastName:W,education:J=[],experience:Z=[],certificates:Y=[],ClassAbout:H={},tuitionClasses:K=[],status:Q={}}=s,X=`${U} ${W}`,ee=H?.profilePhoto?`http://localhost:4005/${H.profilePhoto}`:"/teacher-profile.jpg",et=H?.classesLogo?`http://localhost:4005/${H.classesLogo}`:"/teacher-profile.jpg";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12",children:[(0,r.jsxs)("section",{className:"grid md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"md:col-span-3 space-y-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 p-6 rounded-2xl shadow-sm border",children:[(0,r.jsx)("div",{className:"relative w-full sm:w-64 h-64 rounded-xl overflow-hidden shadow-lg",children:(0,r.jsx)(i.default,{src:et,alt:"Teacher",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[X,Q?.status==="APPROVED"&&(0,r.jsx)(c.VqV,{className:"text-green-500"})]}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground font-medium",children:H?.catchyHeadline||"Professional Educator"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:H?.tutorBio||"No bio available."})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Resume"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-4 border-b pb-2",children:T.map(({key:s,label:a,icon:i})=>(0,r.jsxs)("button",{className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${e===s?"bg-orange-100 text-orange-600 font-semibold":"text-muted-foreground hover:bg-gray-100"}`,onClick:()=>t(s),children:[i,a]},s))}),(0,r.jsxs)("div",{className:"space-y-4",children:["education"===e&&(0,r.jsx)("div",{className:"grid gap-4",children:J.length>0&&J[0].isDegree?J.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,r.jsxs)("div",{className:"p-4 rounded-lg shadow-sm border",children:[(0,r.jsx)("p",{className:"font-semibold text-foreground",children:e.university}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.degree," — ",e.degreeType]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Passout Year: ",e.passoutYear]})]},t)):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No education details available."})}),"work"===e&&(0,r.jsx)("div",{className:"grid gap-4",children:Z.length&&Z[0].isExperience?Z.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,r.jsxs)("div",{className:"p-4 rounded-lg shadow-sm border",children:[(0,r.jsx)("p",{className:"font-semibold text-foreground",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["From: ",new Date(e.from).toLocaleDateString()," — To:"," ",new Date(e.to).toLocaleDateString()]})]},t)):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No work experience available."})}),"certifications"===e&&(0,r.jsx)("div",{className:"grid gap-4",children:Y.length>0&&Y[0].isCertificate?Y.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,r.jsx)("div",{className:"p-4 rounded-lg shadow-sm border",children:(0,r.jsx)("p",{className:"font-semibold text-foreground",children:e.title})},t)):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No certifications available."})}),"tuition"===e&&(0,r.jsx)("div",{className:"grid gap-4",children:K.length>0?K.map((e,t)=>(0,r.jsxs)("div",{className:"p-6 rounded-lg shadow-sm border",children:[(0,r.jsxs)("p",{className:"text-lg font-semibold text-foreground",children:["Tuition #",t+1]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Category:"})," ",e.education||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Coaching Type:"})," ",(0,p.sA)(e.coachingType)]}),"Education"===e.education?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Board:"})," ",(0,p.sA)(e.boardType)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Medium:"})," ",(0,p.sA)(e.medium)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Section:"})," ",(0,p.sA)(e.section)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Subject:"})," ",(0,p.sA)(e.subject)]})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Details:"})," ",(0,p.sA)(e.details)]})]}),e.timeSlots?.length>0&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Time Slots:"}),(0,r.jsx)("ul",{className:"list-disc ml-6 mt-1 text-sm text-muted-foreground",children:e.timeSlots.map((e,t)=>(0,r.jsxs)("li",{children:[e.from," — ",e.to]},t))})]})]},t)):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No tuition classes listed yet."})})]})]})]}),(0,r.jsxs)("aside",{className:"sticky top-24 rounded-2xl p-6 shadow-sm border space-y-6",children:[(0,r.jsx)("div",{className:"relative w-full h-48 rounded-xl overflow-hidden",children:(0,r.jsx)(i.default,{src:ee,alt:"Profile",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:"text-2xl font-bold text-yellow-500",children:["★ ",g.toFixed(1)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[j," reviews"]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(u.$,{variant:"default",className:"w-full flex gap-2 bg-orange-500 hover:bg-orange-600 transition-colors",onClick:()=>{if(!A){_(!0);return}let e=`${s.firstName} ${s.lastName}`;q.push(`/student/chat?userId=${s.id}&userName=${encodeURIComponent(e)}`)},children:[(0,r.jsx)(d.VQk,{})," Send Message"]}),(0,r.jsxs)(u.$,{variant:"outline",className:`w-full flex gap-2 hover:bg-orange-50 transition-colors ${y?"bg-orange-50 text-orange-600":""}`,onClick:G,children:[y?(0,r.jsx)(n.Mbv,{className:"text-orange-500"}):(0,r.jsx)(n.sOK,{}),y?"Saved to Wishlist":"Save to My List"]})]})]})]}),(0,r.jsx)(F,{classId:I,userData:V,onReviewSubmit:B})]}),(0,r.jsx)(x.default,{}),(0,r.jsx)(D.lG,{open:R,onOpenChange:_,children:(0,r.jsxs)(D.Cf,{className:"sm:max-w-md",children:[(0,r.jsx)(D.c7,{children:(0,r.jsx)(D.L3,{className:"text-center",children:"Login Required"})}),(0,r.jsx)("div",{className:"space-y-4 py-4",children:(0,r.jsx)("p",{className:"text-center text-muted-foreground",children:"Please login as a student to add this class to your wishlist or send a message."})})]})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35817:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>l,RO:()=>d,Wz:()=>i,sA:()=>n});var r=s(50346),a=s(63766);let i=(e,t)=>{e.contactNo&&t((0,r.ac)(r._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&t((0,r.ac)(r._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&t((0,r.ac)(r._3.PHOTO_LOGO)),e.education?.length>0&&t((0,r.ac)(r._3.EDUCATION)),e.certificates?.length>0&&t((0,r.ac)(r._3.CERTIFICATES)),e.experience?.length>0&&t((0,r.ac)(r._3.EXPERIENCE)),e.tuitionClasses?.length>0&&t((0,r.ac)(r._3.TUTIONCLASS)),e.address&&t((0,r.ac)(r._3.ADDRESS))},l=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch{return[e]}},n=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}},o=new TextEncoder().encode("secret123");async function d(e){try{let{payload:t}=await (0,a.V)(e,o);return t}catch(e){return console.error("Invalid token:",e),null}}},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53774:(e,t,s)=>{"use strict";s.d(t,{Kh:()=>n,Qg:()=>l,U4:()=>i,o3:()=>o});var r=s(4780);let a=process.env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1",i=async e=>{try{let t=(0,r.ZO)();if(!t)throw Error("Authentication required");let s=await fetch(`${a}/student-wishlist`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({classId:e}),credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to add to wishlist")}return await s.json()}catch(e){throw console.error("Error adding to wishlist:",e),e}},l=async e=>{try{let t=(0,r.ZO)();if(!t)throw Error("Authentication required");let s=await fetch(`${a}/student-wishlist/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`},credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to remove from wishlist")}return await s.json()}catch(e){throw console.error("Error removing from wishlist:",e),e}},n=async e=>{try{let t=(0,r.ZO)();if(!t)return{inWishlist:!1};let s=await fetch(`${a}/student-wishlist/check/${e}`,{method:"GET",headers:{Authorization:`Bearer ${t}`},credentials:"include"});if(!s.ok)return{inWishlist:!1};return(await s.json()).data}catch(e){return console.error("Error checking wishlist status:",e),{inWishlist:!1}}},o=async(e=1,t=10)=>{try{let s=(0,r.ZO)();if(!s)throw Error("Authentication required");let i=await fetch(`${a}/student-wishlist?page=${e}&limit=${t}`,{method:"GET",headers:{Authorization:`Bearer ${s}`},credentials:"include"});if(!i.ok){let e=await i.json();throw Error(e.message||"Failed to fetch wishlist")}return await i.json()}catch(e){throw console.error("Error fetching wishlist:",e),e}}},54991:(e,t,s)=>{Promise.resolve().then(s.bind(s,8096))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61170:(e,t,s)=>{"use strict";s.d(t,{b:()=>c});var r=s(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}s(51215);var i=s(60687),l=Symbol("radix.slottable");function n(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let s=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...i}=e;if(r.isValidElement(s)){var l;let e,n;let o=(l=s,(n=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(n=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,t){let s={...t};for(let r in t){let a=e[r],i=t[r];/^on[A-Z]/.test(r)?a&&i?s[r]=(...e)=>{i(...e),a(...e)}:a&&(s[r]=a):"style"===r?s[r]={...a,...i}:"className"===r&&(s[r]=[a,i].filter(Boolean).join(" "))}return{...e,...s}}(i,s.props);return s.type!==r.Fragment&&(d.ref=t?function(...e){return t=>{let s=!1,r=e.map(e=>{let r=a(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():a(e[t],null)}}}}(t,o):o),r.cloneElement(s,d)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:a,...l}=e,o=r.Children.toArray(a),d=o.find(n);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...l,ref:s,children:a})});return s.displayName=`${e}.Slot`,s}(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?s:t,{...l,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),d=r.forwardRef((e,t)=>(0,i.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,s)=>{Promise.resolve().then(s.bind(s,25595))},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>x,L3:()=>h,c7:()=>m,lG:()=>n,rr:()=>p,zM:()=>o});var r=s(60687);s(43210);var a=s(6491),i=s(11860),l=s(4780);function n({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,...s}){return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function h({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...t})}function p({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},80942:(e,t,s)=>{"use strict";s.d(t,{lV:()=>c,MJ:()=>g,Rr:()=>v,zB:()=>m,eI:()=>p,lR:()=>f,C5:()=>j});var r=s(60687),a=s(43210),i=s(11329),l=s(27605),n=s(4780),o=s(61170);function d({className:e,...t}){return(0,r.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let c=l.Op,u=a.createContext({}),m=({...e})=>(0,r.jsx)(u.Provider,{value:{name:e.name},children:(0,r.jsx)(l.xI,{...e})}),x=()=>{let e=a.useContext(u),t=a.useContext(h),{getFieldState:s}=(0,l.xW)(),r=(0,l.lN)({name:e.name}),i=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},h=a.createContext({});function p({className:e,...t}){let s=a.useId();return(0,r.jsx)(h.Provider,{value:{id:s},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",e),...t})})}function f({className:e,...t}){let{error:s,formItemId:a}=x();return(0,r.jsx)(d,{"data-slot":"form-label","data-error":!!s,className:(0,n.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t})}function g({...e}){let{error:t,formItemId:s,formDescriptionId:a,formMessageId:l}=x();return(0,r.jsx)(i.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?`${a} ${l}`:`${a}`,"aria-invalid":!!t,...e})}function v({className:e,...t}){let{formDescriptionId:s}=x();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function j({className:e,...t}){let{error:s,formMessageId:a}=x(),i=s?String(s?.message??""):t.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:a,className:(0,n.cn)("text-destructive text-sm",e),...t,children:i}):null}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,2105,3766,9528,3793,471,255,4255,2800],()=>s(10521));module.exports=r})();