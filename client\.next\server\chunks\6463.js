exports.id=6463,exports.ids=[6463],exports.modules={12814:(e,s,t)=>{"use strict";t.d(s,{A:()=>S});var r=t(60687),a=t(43210),l=t(32584),n=t(29523),i=t(89667),o=t(78122),d=t(11860),c=t(99270),x=t(41312),m=t(58887),h=t(28559),u=t(40945),g=t(13964),b=t(27900);t(57405);var p=t(79663),f=t(52581),w=t(30474),y=t(82120),j=t(28527);let v=async(e,s)=>{try{return(await j.S.get(`chat/messages/private?userId1=${e}&userId2=${s}`,{withCredentials:!0})).data}catch(e){console.error("Error fetching private messages:",e)}},N=async(e,s)=>{try{return(await j.S.get(`chat/messages/users?userId=${e}&userType=${s}`,{withCredentials:!0})).data}catch(e){console.error("Error fetching message users:",e)}};var $=t(16189),k=t(51289);function S({userType:e,isAuthenticated:s,username:t,userId:j,initialSelectedUser:S,initialSelectedUserId:A,initialSelectedUserName:C}){let[I,T]=(0,a.useState)([]),[U,R]=(0,a.useState)(""),[M,O]=(0,a.useState)(!!t),[z,D]=(0,a.useState)([]),[E,L]=(0,a.useState)([]),[P,q]=(0,a.useState)(""),[F,Y]=(0,a.useState)(S||null),[G,W]=(0,a.useState)(null),[B,H]=(0,a.useState)(null),[J,K]=(0,a.useState)(!0),[Q,V]=(0,a.useState)(!1),[X,Z]=(0,a.useState)(new Set),[_,ee]=(0,a.useState)(!1),[es,et]=(0,a.useState)(new Map),[er,ea]=(0,a.useState)("all"),el=(0,$.useRouter)(),en=(0,a.useRef)(null),ei=(0,a.useRef)(null),eo=(0,a.useRef)(null),ed=(0,y.a)(),ec=async s=>{if(s.preventDefault(),!U.trim()||!F||!j)return;let t=U.trim();R("");try{if(!G){f.oR.error("No recipient selected. Please select a user first."),R(t);return}let s="student"===e?"class":"student";"student"==e&&el.replace("/student/chat"),ei.current?.emit("sendPrivateMessage",{text:t,senderId:j,recipientId:G,senderType:e,recipientType:s,recipientUsername:F}),eg(F)||f.oR.info(`${F} is offline. Your message will be delivered when they come online.`)}catch{f.oR.error("Failed to send message. Please try again."),R(t)}},ex=async e=>{em(),T([]),Y(e.username),Z(new Set),ee(!1);let s=e.userId||e.username;if(W(s),et(e=>{let t=new Map(e);return t.delete(s),t}),ed&&K(!1),s&&j)try{let e=await v(j,s);T(e||[])}catch{f.oR.error("Failed to load conversation history.")}},em=()=>{B&&j&&G&&ei.current&&ei.current.emit("leaveChatRoom",{userId:j,recipientId:G})},eh=(0,a.useMemo)(()=>e=>(0,p.GP)(new Date(e),"h:mm a"),[]),eu=(0,a.useMemo)(()=>{let s=new Map;return E.forEach(t=>{s.set(t.userId,{username:t.username,userType:"student"===e?"class":"student",userId:t.userId})}),z.forEach(r=>{r.username!==t&&r.userType!==e&&s.set(r.userId||r.username,{username:r.username,userType:r.userType,userId:r.userId||r.username})}),A&&C&&s.set(A,{username:C,userType:"student"===e?"class":"student",userId:A}),Array.from(s.values()).filter(s=>{let r=s.username.toLowerCase().includes(P.toLowerCase()),a=s.username!==t,l=s.userType!==e;return r&&a&&l})},[E,z,P,t,e,A,C]),eg=(0,a.useMemo)(()=>{let e=new Set(z.map(e=>e.userId));return s=>{let t=eu.find(e=>e.username===s);return t?e.has(t.userId):s===C&&A?e.has(A):!!z.find(e=>e.username===s)}},[z,eu,C,A]),eb=(0,a.useMemo)(()=>eu.filter(e=>{let s=e.userId;return es.has(s)}).length,[eu,es]),ep=(0,a.useMemo)(()=>"unread"===er?eu.filter(e=>{let s=e.userId;return es.has(s)}):eu,[eu,er,es]);return s?(0,r.jsxs)("div",{className:"flex h-[calc(100vh-64px)] bg-background text-foreground relative overflow-hidden",children:[J&&(0,r.jsxs)("aside",{className:`border-r border-gray-200 flex flex-col bg-gradient-to-b from-white to-gray-50 shadow-lg ${ed?"absolute inset-0 z-50 w-full":"relative w-80 min-w-80 lg:w-96 lg:min-w-96"}`,children:[(0,r.jsxs)("div",{className:"p-4 flex items-center justify-between border-b border-gray-200 bg-white/80 backdrop-blur-sm",children:[(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(w.default,{src:"/logo.png",alt:"Uest Logo",width:ed?100:140,height:ed?25:35,className:"object-contain cursor-pointer hover:opacity-80 transition-all duration-300 hover:scale-105",onClick:()=>el.push("/")})})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:ed?"sm":"default",onClick:async()=>{if(ei.current?.emit("getOnlineUsers"),ei.current?.emit("getUnreadCounts",{userId:j,userType:e}),M&&j)try{let s=await N(j,e),t=Array.from(new Map(s.map(e=>[e.userId,e])).values());L(t)}catch{}},className:`bg-white/90 border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md ${ed?"px-3 py-2 text-xs":"px-4 py-2 text-sm"}`,title:"Refresh chat list",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),!ed&&(0,r.jsx)("span",{className:"ml-2",children:"Refresh"})]}),ed&&(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"rounded-xl h-10 w-10 hover:bg-gray-100 transition-all duration-300",onClick:()=>K(!1),children:(0,r.jsx)(d.A,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("div",{className:`${ed?"p-3":"p-4"} bg-white/50`,children:(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(c.A,{className:`absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 group-focus-within:text-gray-600 transition-colors duration-300 ${ed?"h-4 w-4":"h-5 w-5"}`}),(0,r.jsx)(i.p,{placeholder:"Search conversations...",className:`pl-12 pr-4 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-2 focus:ring-gray-100 transition-all duration-300 shadow-sm hover:shadow-md ${ed?"py-2.5 text-sm":"py-3 text-base"}`,value:P,onChange:e=>q(e.target.value)})]})}),(0,r.jsx)("div",{className:`${ed?"px-3 pb-3":"px-4 pb-4"}`,children:(0,r.jsx)("div",{className:"bg-gray-100/80 rounded-2xl p-1.5 shadow-inner",children:(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)("button",{onClick:()=>ea("all"),className:`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${"all"===er?"bg-white text-gray-900 shadow-lg border border-gray-200":"text-gray-600 hover:text-gray-900 hover:bg-white/50"} ${ed?"px-3 py-2.5 text-xs":"px-4 py-3 text-sm"}`,children:(0,r.jsxs)("div",{className:`flex items-center gap-2 ${ed?"flex-col gap-1":""}`,children:[(0,r.jsx)(x.A,{className:`${ed?"h-3 w-3":"h-4 w-4"}`}),(0,r.jsx)("span",{className:ed?"text-[10px] leading-tight":"",children:ed?"All":"All Users"}),(0,r.jsx)("span",{className:`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${"all"===er?"bg-black text-white":"bg-gray-200 text-gray-600"}`,children:eu.length})]})}),(0,r.jsx)("button",{onClick:()=>ea("unread"),className:`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${"unread"===er?"bg-white text-gray-900 shadow-lg border border-gray-200":"text-gray-600 hover:text-gray-900 hover:bg-white/50"} ${ed?"px-3 py-2.5 text-xs":"px-4 py-3 text-sm"}`,children:(0,r.jsxs)("div",{className:`flex items-center gap-2 ${ed?"flex-col gap-1":""}`,children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:`${ed?"h-3 w-3":"h-4 w-4"}`}),eb>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-[8px] rounded-full h-2 w-2 animate-pulse"})]}),(0,r.jsx)("span",{className:ed?"text-[10px] leading-tight":"",children:ed?"Unread":"Unread Only"}),(0,r.jsx)("span",{className:`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${"unread"===er?eb>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-600":eb>0?"bg-red-500 text-white animate-pulse":"bg-gray-200 text-gray-600"}`,children:eb})]})})]})})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto overscroll-contain",children:(0,r.jsx)("div",{className:`space-y-2 ${ed?"px-2 pb-2":"px-3 pb-3"}`,children:ep.length>0||F&&A?(0,r.jsxs)(r.Fragment,{children:[F&&A&&C&&!ep.find(e=>e.userId===A)&&(0,r.jsx)("div",{className:`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${ed?"p-3":"p-4"} bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700`,onClick:()=>ex({username:C,userType:"student"===e?"class":"student",userId:A}),children:(0,r.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.eu,{className:`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${ed?"h-10 w-10":"h-12 w-12"} border-white/50`,children:(0,r.jsx)(l.q5,{className:`font-semibold transition-colors duration-300 ${ed?"text-xs":"text-sm"} bg-white text-black`,children:C.substring(0,2).toUpperCase()})}),(0,r.jsx)("div",{className:`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${ed?"h-4 w-4":"h-5 w-5"} border-white ${eg(C)?"bg-green-500 shadow-lg":"bg-gray-400"}`,children:(0,r.jsx)("div",{className:`rounded-full transition-all duration-300 ${ed?"h-2 w-2":"h-2.5 w-2.5"} ${eg(C)?"bg-green-300 animate-pulse":"bg-gray-300"}`})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h3",{className:`font-semibold truncate transition-colors duration-300 ${ed?"text-sm":"text-base"} text-white`,children:C})}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,r.jsx)("div",{className:"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white",children:"Tutor"}),(0,r.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full ${eg(C)?"bg-green-500":"bg-gray-400"}`}),eg(C)?"Online":"Offline"]})]})]})]})},A),ep.map(e=>(0,r.jsx)("div",{className:`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${ed?"p-3":"p-4"} ${F===e.username?"bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700":"bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300"}`,onClick:()=>ex(e),children:(0,r.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.eu,{className:`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${ed?"h-10 w-10":"h-12 w-12"} ${F===e.username?"border-white/50":"border-gray-300 group-hover:border-gray-400"}`,children:(0,r.jsx)(l.q5,{className:`font-semibold transition-colors duration-300 ${ed?"text-xs":"text-sm"} ${F===e.username?"bg-white text-black":"bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 group-hover:from-gray-200 group-hover:to-gray-300"}`,children:e.username.substring(0,2).toUpperCase()})}),(0,r.jsx)("div",{className:`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${ed?"h-4 w-4":"h-5 w-5"} ${(e.username,"border-white")} ${eg(e.username)?"bg-green-500 shadow-lg":"bg-gray-400"}`,children:(0,r.jsx)("div",{className:`rounded-full transition-all duration-300 ${ed?"h-2 w-2":"h-2.5 w-2.5"} ${eg(e.username)?"bg-green-300 animate-pulse":"bg-gray-300"}`})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h3",{className:`font-semibold truncate transition-colors duration-300 ${ed?"text-sm":"text-base"} ${F===e.username?"text-white":"text-gray-900 group-hover:text-black"}`,children:e.username}),es.has(e.userId)&&(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse",children:es.get(e.userId)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,r.jsx)("div",{className:`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${F===e.username?"bg-white/20 text-white":(e.userType,"bg-gray-100 text-gray-700 group-hover:bg-gray-200")}`,children:"student"===e.userType?"Student":"Tutor"}),(0,r.jsxs)("div",{className:`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${F===e.username?"bg-white/20 text-white":eg(e.username)?"bg-green-100 text-green-700 group-hover:bg-green-200":"bg-gray-100 text-gray-600 group-hover:bg-gray-200"}`,children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full ${eg(e.username)?"bg-green-500":"bg-gray-400"}`}),eg(e.username)?"Online":"Offline"]})]})]})]})},e.userId))]}):(0,r.jsx)("div",{className:"p-6 text-center",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:"unread"===er?(0,r.jsx)(m.A,{className:"h-8 w-8 text-gray-400"}):(0,r.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("h3",{className:`font-semibold text-gray-900 mb-2 ${ed?"text-sm":"text-base"}`,children:"unread"===er?"No unread messages":"No users found"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"unread"===er?"All messages have been read or no conversations yet":`You can only chat with ${"student"===e?"tutors":"students"} who have exchanged messages with you`}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"unread"===er?'Switch to "All Users" to see all conversations':"Users will appear here when you exchange messages with them"})]})})})})]}),ed&&J&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20 z-40",onClick:()=>K(!1)}),(0,r.jsxs)("main",{className:"flex-1 flex flex-col min-w-0 bg-white",children:[(0,r.jsxs)("div",{className:`border-b-2 border-gray-200 flex items-center gap-3 bg-white ${ed?"p-3":"p-4"}`,children:[ed&&!J&&(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:`flex-shrink-0 rounded-xl hover:bg-gray-100 ${ed?"h-8 w-8":"h-10 w-10"}`,onClick:()=>{em(),K(!0),ed&&(Y(null),W(null))},children:(0,r.jsx)(h.A,{className:`${ed?"h-4 w-4":"h-5 w-5"}`})}),(0,r.jsxs)("div",{className:"flex gap-3 items-center min-w-0 flex-1",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.eu,{className:`border-2 border-gray-300 flex-shrink-0 shadow-md ${ed?"h-9 w-9":"h-12 w-12"}`,children:F?(0,r.jsx)(l.q5,{className:`font-semibold bg-gray-100 text-black ${ed?"text-xs":"text-sm"}`,children:F.substring(0,2).toUpperCase()}):(0,r.jsx)(l.q5,{className:"bg-gray-100",children:(0,r.jsx)(x.A,{className:`text-gray-500 ${ed?"h-4 w-4":"h-6 w-6"}`})})}),F&&(0,r.jsx)("div",{className:`absolute -bottom-1 -right-1 rounded-full border-2 border-white flex items-center justify-center ${ed?"h-3 w-3":"h-4 w-4"} ${eg(F)?"bg-green-500":"bg-gray-400"}`,children:(0,r.jsx)("div",{className:`rounded-full ${ed?"h-1.5 w-1.5":"h-2 w-2"} ${eg(F)?"bg-green-400 animate-pulse":"bg-gray-300"}`})})]}),(0,r.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,r.jsx)("h1",{className:`font-semibold flex items-center gap-2 truncate text-black ${ed?"text-base":"text-lg"}`,children:F?(0,r.jsx)("span",{className:"truncate",children:F}):"Select a user"}),(0,r.jsx)("p",{className:`text-gray-600 truncate ${ed?"text-xs":"text-sm"}`,children:F?eg(F)?"Online":"Offline (messages will be delivered when online)":"Choose someone to chat with"})]})]})]}),(0,r.jsx)("div",{className:`flex-1 overflow-y-auto bg-gray-50 ${ed?"p-3":"p-4"}`,children:(0,r.jsxs)("div",{className:`mx-auto ${ed?"space-y-3 max-w-full":"space-y-4 max-w-4xl"}`,children:[F?I.length>0?I.map(e=>{let s=e.sender===t,a=e.sender||"Unknown";return(0,r.jsxs)("div",{className:`flex items-end ${s?"justify-end":""} ${ed?"gap-2":"gap-3"}`,children:[!s&&(0,r.jsx)(l.eu,{className:`border-2 border-gray-300 shadow-sm ${ed?"h-6 w-6":"h-8 w-8"}`,children:(0,r.jsx)(l.q5,{className:"bg-gray-200 text-black font-semibold text-xs",children:a.substring(0,2).toUpperCase()})}),(0,r.jsx)("div",{className:`${s?"text-right":""} ${ed?"max-w-[80%]":"max-w-[70%]"}`,children:(0,r.jsxs)("div",{className:`${s?"bg-black text-white":"bg-white text-black border-2 border-gray-200"} rounded-2xl shadow-lg break-words ${ed?"p-3":"p-4"}`,children:[(0,r.jsx)("div",{className:`leading-relaxed ${ed?"text-sm":"text-base"}`,children:e.text}),(0,r.jsxs)("div",{className:`text-xs mt-2 flex items-end justify-end gap-1 ${s?"text-gray-300":"text-gray-500"}`,children:[eh(e.timestamp),s&&(0,r.jsx)("span",{children:X.has(e.id)?(0,r.jsx)(u.A,{className:`${ed?"h-3 w-3":"h-4 w-4"} text-blue-500`}):_?(0,r.jsx)(u.A,{className:`${ed?"h-3 w-3":"h-4 w-4"} text-blue-500`}):eg(F)?(0,r.jsx)(u.A,{className:`${ed?"h-3 w-3":"h-4 w-4"} text-gray-400`}):(0,r.jsx)(g.A,{className:`${ed?"h-3 w-3":"h-4 w-4"} text-gray-400`})})]})]})})]},e.id)}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 text-center",children:[(0,r.jsx)(m.A,{className:"text-gray-400 mb-4 h-16 w-16"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg font-medium",children:"No messages yet"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Send a message to start the conversation"})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 text-center",children:[(0,r.jsx)(m.A,{className:"text-gray-400 mb-4 h-16 w-16"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg font-medium",children:"Select a user to start chatting"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Choose a user from the sidebar to start a private conversation"}),(0,r.jsxs)("p",{className:"text-gray-500 text-sm mt-4 max-w-md",children:["Note: You can only chat with ","student"===e?"tutors":"students"," who have exchanged messages with you.",0===ep.length&&(0,r.jsx)("span",{className:"block mt-2",children:"There are currently no users to chat with. When you exchange messages with someone, they will appear in the sidebar."})]})]}),(0,r.jsx)("div",{ref:en})]})}),(0,r.jsx)("form",{onSubmit:ec,className:`border-t-2 border-gray-200 bg-white ${ed?"p-3":"p-4"}`,children:(0,r.jsxs)("div",{className:`flex items-center mx-auto ${ed?"gap-2 max-w-full":"gap-3 max-w-4xl"}`,children:[(0,r.jsx)("button",{type:"button",onClick:e=>{e.preventDefault(),V(e=>!e)},className:`bg-white border-2 border-gray-200 text-black hover:bg-gray-100 rounded-xl font-medium transition-all duration-300 ${ed?"text-lg px-2 py-1":"text-2xl px-3 py-1"}`,children:"\uD83D\uDE0A"}),Q&&(0,r.jsx)("div",{ref:eo,className:`absolute z-10 ${ed?"bottom-12 left-4 right-4":"bottom-12 left-96"}`,children:(0,r.jsx)(k.Ay,{onEmojiClick:e=>{R(s=>s+e.emoji)},emojiStyle:k.Ai.APPLE,searchDisabled:!0,width:ed?"100%":void 0})}),(0,r.jsx)(i.p,{placeholder:F?"Type your message...":"Select a user to start chatting",className:`flex-1 bg-gray-50 border-2 border-gray-200 text-black placeholder:text-gray-500 focus:border-black rounded-xl transition-all duration-300 ${ed?"px-3 py-2 text-sm":"px-4 py-3 text-base"}`,value:U,onChange:e=>R(e.target.value),disabled:!F,maxLength:250}),(0,r.jsxs)(n.$,{type:"submit",size:ed?"default":"lg",disabled:!U.trim()||!F,className:`bg-black text-white hover:bg-gray-800 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 ${ed?"px-4 py-2":"px-6 py-3"}`,children:[(0,r.jsx)(b.A,{className:`${ed?"h-4 w-4 mr-1":"h-5 w-5 mr-2"}`}),"Send"]})]})})]})]}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[calc(100vh-64px)] bg-background p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md p-4 sm:p-6 bg-card rounded-lg shadow-lg",children:[(0,r.jsx)("h2",{className:"text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center",children:"Login Required"}),(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center text-center",children:[(0,r.jsx)("p",{className:"text-center text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"Please login as a student to access the chat feature."}),(0,r.jsx)(n.$,{onClick:()=>el.push("/"),className:"w-full bg-orange-500 hover:bg-orange-600",children:"Go to Login"})]})]})})}},39727:()=>{},47990:()=>{}};