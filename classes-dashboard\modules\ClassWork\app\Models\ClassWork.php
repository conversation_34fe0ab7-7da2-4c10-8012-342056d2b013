<?php

namespace ClassWork\Models;

use Illuminate\Database\Eloquent\Model;
use Classroom\Models\Classroom;
use Subject\Models\Subject;


class ClassWork extends Model
{
    public $table = 'classworks';
    protected $fillable = [
        'class_uuid',
        'classroom_id',
        'subject_id',
        'classwork_date',
        'title',
        'description',
    ];

    public function classroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom_id');
    }
    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }
}
