(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1554],{7583:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var s=a(95155);a(12115);var l=a(6874),r=a.n(l),i=a(66766),n=a(29911);let o=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(r(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:l}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(r(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:l,children:(0,s.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},l)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(r(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(r(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(r(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},20185:(e,t,a)=>{"use strict";a.d(t,{Ow:()=>i,RO:()=>c,Wz:()=>r,sA:()=>n});var s=a(94314),l=a(18159);let r=(e,t)=>{var a,l,r,i,n,o,c,d;e.contactNo&&t((0,s.ac)(s._3.PROFILE)),(null===(l=e.ClassAbout)||void 0===l?void 0:null===(a=l.tutorBio)||void 0===a?void 0:a.length)>50&&t((0,s.ac)(s._3.DESCRIPTION)),(null===(r=e.ClassAbout)||void 0===r?void 0:r.profilePhoto)&&(null===(i=e.ClassAbout)||void 0===i?void 0:i.classesLogo)&&t((0,s.ac)(s._3.PHOTO_LOGO)),(null===(n=e.education)||void 0===n?void 0:n.length)>0&&t((0,s.ac)(s._3.EDUCATION)),(null===(o=e.certificates)||void 0===o?void 0:o.length)>0&&t((0,s.ac)(s._3.CERTIFICATES)),(null===(c=e.experience)||void 0===c?void 0:c.length)>0&&t((0,s.ac)(s._3.EXPERIENCE)),(null===(d=e.tuitionClasses)||void 0===d?void 0:d.length)>0&&t((0,s.ac)(s._3.TUTIONCLASS)),e.address&&t((0,s.ac)(s._3.ADDRESS))},i=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch(t){return[e]}},n=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch(t){return e||"N/A"}},o=new TextEncoder().encode("secret123");async function c(e){try{let{payload:t}=await (0,l.V)(e,o);return t}catch(e){return console.error("Invalid token:",e),null}}},38564:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},42355:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},46102:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>i,ZI:()=>c,k$:()=>o,m_:()=>n});var s=a(95155);a(12115);var l=a(71739),r=a(59434);function i(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(l.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function n(e){let{...t}=e;return(0,s.jsx)(i,{children:(0,s.jsx)(l.bL,{"data-slot":"tooltip",...t})})}function o(e){let{...t}=e;return(0,s.jsx)(l.l9,{"data-slot":"tooltip-trigger",...t})}function c(e){let{className:t,sideOffset:a=0,children:i,...n}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,r.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...n,children:[i,(0,s.jsx)(l.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>x,L3:()=>h,c7:()=>m,lG:()=>n,rr:()=>g,zM:()=>o});var s=a(95155);a(12115);var l=a(4033),r=a(54416),i=a(59434);function n(e){let{...t}=e;return(0,s.jsx)(l.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,s.jsx)(l.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,s.jsx)(l.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(l.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(l.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...n,children:[a,(0,s.jsxs)(l.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(l.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(l.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>r,aR:()=>i,wL:()=>d});var s=a(95155);a(12115);var l=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,l.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},66932:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68856:(e,t,a)=>{"use strict";a.d(t,{E:()=>r});var s=a(95155),l=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,l.cn)("bg-accent animate-pulse rounded-md",t),...a})}},69244:(e,t,a)=>{Promise.resolve().then(a.bind(a,75580))},74436:(e,t,a)=>{"use strict";a.d(t,{k5:()=>d});var s=a(12115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=s.createContext&&s.createContext(l),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(this,arguments)}function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,s)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach(function(t){var s,l,r;s=e,l=t,r=a[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(l))in s?Object.defineProperty(s,l,{value:r,enumerable:!0,configurable:!0,writable:!0}):s[l]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function d(e){return t=>s.createElement(u,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,a)=>s.createElement(t.tag,c({key:a},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var a,{attr:l,size:r,title:o}=e,d=function(e,t){if(null==e)return{};var a,s,l=function(e,t){if(null==e)return{};var a={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;a[s]=e[s]}return a}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(s=0;s<r.length;s++)a=r[s],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}(e,i),u=r||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),s.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,d,{className:a,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==r?s.createElement(r.Consumer,null,e=>t(e)):t(l)}},75580:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>A});var s=a(95155),l=a(12115),r=a(35695),i=a(19320),n=a(66695),o=a(30285),c=a(66932),d=a(54416),u=a(38564),m=a(42355),x=a(13052),h=a(55077),g=a(70347),p=a(7583),f=a(56671),j=a(66766),v=a(68856);let b=e=>{let{label:t,value:a,onChange:r}=e,i=l.useRef(null);return(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-muted-foreground mb-1",children:t}),(0,s.jsx)("input",{ref:i,type:"text",placeholder:"Enter ".concat(t),className:"border bg-white dark:bg-black rounded-lg px-3 py-2 text-sm text-black dark:text-white   focus:border-customOrange focus:ring focus:ring-customOrange/20 focus:outline-none   transition-all duration-200",value:a,onChange:e=>{let t=e.target.value.replace(/\s+/g," ").trimStart();e.target.value=t,r(e)},maxLength:50})]})};var N=a(20185),y=a(54165),w=a(46102),C=a(51013),k=a(29911);let O=()=>{let e=(0,r.useRouter)(),[t,a]=(0,l.useState)([]),[g,p]=(0,l.useState)(null),[O,A]=(0,l.useState)(!0),[S,E]=(0,l.useState)(1),[P,T]=(0,l.useState)(1),[_,R]=(0,l.useState)(!1),[F]=(0,l.useState)(!1),[D,L]=(0,l.useState)(!1),I=(0,r.useSearchParams)(),[z,U]=(0,l.useState)(!1),[B,Z]=(0,l.useState)(null),[$,M]=(0,l.useState)(1e3),[H,V]=(0,l.useState)(null),[W,G]=(0,l.useState)({education:I.get("education")||"",details:I.get("details")||"",boardType:I.get("boardType")||"",medium:I.get("medium")||"",section:I.get("section")||"",coachingType:I.get("coachingType")||"",subject:I.get("subject")||"",firstName:I.get("firstName")||"",lastName:I.get("lastName")||"",className:I.get("className")||"",sortByRating:!0,sortByReviewCount:!0}),Y=e=>{if(!g)return[];let t=g.details.find(e=>"Education"===e.name);if(!t)return[];let a=t.subDetails.find(t=>t.name===e);return a?a.values.map(e=>({id:e.id,value:e.name})):[]},q=async()=>{try{let e=await h.S.get("/constant/TuitionClasses");p(e.data)}catch(e){f.oR.error("Failed to fetch tuition class categories")}},J=()=>{if(!navigator.geolocation){V("Geolocation is not supported by your browser.");return}navigator.geolocation.getCurrentPosition(e=>{Z({lat:e.coords.latitude,lng:e.coords.longitude}),V(null)},()=>{V("Unable to retrieve your location.")})},Q=async()=>{if(B){A(!0);try{let e=await h.S.get("/classes/nearby",{params:{lat:B.lat,lng:B.lng,radius:$}});a(e.data.data||[]),T(1)}catch(e){f.oR.error("Failed to fetch nearby classes")}finally{A(!1)}}},X=async e=>{A(!0);try{let t=e||W,s=await h.S.get("/classes/approved-tutors",{params:{page:S,limit:9,...t}}),l=s.data.data;a(l),T(s.data.totalPages)}catch(e){f.oR.error("Failed to fetch tutors")}finally{A(!1)}};(0,l.useEffect)(()=>{q()},[]),(0,l.useEffect)(()=>{z&&B?Q():X()},[S,z,B,$]);let K={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},ee={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100}}},et=e=>{let{label:t,value:a,onChange:l,options:r}=e;return(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-muted-foreground mb-1",children:t}),(0,s.jsxs)("select",{className:"border rounded-lg px-3 py-2 text-sm   dark:bg-black   text-gray-900 dark:text-white",value:a,onChange:l,children:[(0,s.jsxs)("option",{value:"",className:"bg-white dark:bg-zinc-900",children:["All ",t]}),r.map(e=>(0,s.jsx)("option",{value:e.value,className:"bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100",children:e.value},e.id))]})]})};return(0,l.useEffect)(()=>{z&&!B&&J()},[z,B]),(0,s.jsxs)(i.P.section,{initial:"hidden",animate:"visible",variants:K,className:"container mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold",children:"Find Your Perfect Tutor"}),(0,s.jsx)("p",{className:"mt-4 text-lg font-medium bg-gradient-to-r from-gray-700 to-gray-500 dark:from-gray-300 dark:to-gray-400 bg-clip-text text-transparent",children:"Discover experienced tutors who can help you achieve your learning goals"})]}),(0,s.jsxs)(i.P.div,{className:"mb-8 bg-white/30 dark:bg-black/30 backdrop-blur-lg rounded-xl p-6",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 text-customOrange"}),(0,s.jsx)("h3",{className:"text-xl font-semibold",children:"Filters"})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsx)(o.$,{variant:"outline",className:"hover:bg-customOrange/10",onClick:()=>R(!_),children:_?(0,s.jsx)(d.A,{className:"w-4 h-4"}):(0,s.jsx)(c.A,{className:"w-4 h-4"})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 transition-all duration-300 ".concat(_?"block":"hidden"),children:[(0,s.jsx)(et,{label:"Category",value:W.education,onChange:e=>{G(t=>({...t,education:e.target.value,details:"",boardType:"Education"!==e.target.value?"":t.boardType,medium:"Education"!==e.target.value?"":t.medium,section:"Education"!==e.target.value?"":t.section,subject:"Education"!==e.target.value?"":t.subject}))},options:g?g.details.map(e=>({id:e.id,value:e.name})):[]}),"Education"===W.education&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(et,{label:"Board Type",value:W.boardType,onChange:e=>G(t=>({...t,boardType:e.target.value})),options:Y("Board Type")}),(0,s.jsx)(et,{label:"Medium",value:W.medium,onChange:e=>G(t=>({...t,medium:e.target.value})),options:Y("Medium")}),(0,s.jsx)(et,{label:"Section",value:W.section,onChange:e=>G(t=>({...t,section:e.target.value})),options:Y("Section")}),(0,s.jsx)(et,{label:"Subject",value:W.subject,onChange:e=>G(t=>({...t,subject:e.target.value})),options:Y("Subject")})]}),W.education&&"Education"!==W.education&&(0,s.jsx)(et,{label:"Details",value:W.details,onChange:e=>G(t=>({...t,details:e.target.value})),options:(e=>{if(!g)return[];let t=g.details.find(t=>t.name===e);return t?"Education"===e?[]:t.subDetails.map(e=>({id:e.id,value:e.name})):[]})(W.education)}),(0,s.jsx)(et,{label:"Coaching Type",value:W.coachingType,onChange:e=>G(t=>({...t,coachingType:e.target.value})),options:[{id:"personal",value:"Personal"},{id:"group",value:"Group"},{id:"online",value:"Online"},{id:"hybrid",value:"Hybrid"}]}),(0,s.jsx)(b,{label:"First Name",value:W.firstName,onChange:e=>G(t=>({...t,firstName:e.target.value}))}),(0,s.jsx)(b,{label:"Class Name",value:W.className,onChange:e=>G(t=>({...t,className:e.target.value}))})]}),(0,s.jsxs)("div",{className:"flex gap-4 mt-4 ".concat(_?"block":"hidden"),children:[(0,s.jsx)(o.$,{className:"w-[200px] bg-customOrange hover:bg-customOrange/90",onClick:()=>{E(1),X()},children:"Apply Filters"}),(0,s.jsx)(o.$,{variant:"default",className:"w-[200px]",onClick:()=>{let e={education:"",details:"",boardType:"",medium:"",section:"",coachingType:"",subject:"",firstName:"",lastName:"",className:"",sortByRating:!0,sortByReviewCount:!0};G(e),E(1),X(e)},children:"Reset Filters"})]})]}),(0,s.jsxs)("div",{className:"flex flex-nowrap items-center gap-6 w-full mb-5",children:[(0,s.jsxs)("label",{className:"flex items-center gap-2 mb-0 whitespace-nowrap flex-shrink-0",children:[(0,s.jsx)("input",{type:"checkbox",checked:z,onChange:()=>U(e=>!e),className:"accent-customOrange"}),(0,s.jsx)("span",{children:"Show Nearby Classes"})]}),z&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,s.jsxs)("select",{className:"border rounded px-2 py-1 min-w-[90px]",value:$,onChange:e=>M(Number(e.target.value)),children:[(0,s.jsx)("option",{value:100,children:"100m"}),(0,s.jsx)("option",{value:500,children:"500m"}),(0,s.jsx)("option",{value:1e3,children:"1km"}),(0,s.jsx)("option",{value:5e3,children:"5km"}),(0,s.jsx)("option",{value:1e4,children:"10km"}),(0,s.jsx)("option",{value:1e4,children:"60km"})]}),(0,s.jsx)("span",{className:"whitespace-nowrap",children:$>=1e3?"".concat($/1e3,"km Radius"):"".concat($,"m Radius")})]}),H&&(0,s.jsx)("span",{className:"text-red-500 ml-2 flex-shrink-0",children:H})]})]}),O?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsx)(v.E,{className:"h-96 w-full rounded-xl"},t))}):0===t.length?(0,s.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-10",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No tutors found. Try adjusting your filters."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.P.div,{variants:K,initial:"hidden",animate:"visible",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map((t,a)=>{var l;return(0,s.jsx)(i.P.div,{variants:ee,whileHover:{y:-5},className:"h-full",children:(0,s.jsxs)(n.Zp,{className:"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center gap-4",children:[(0,s.jsx)(i.P.div,{className:"relative w-30 h-30 rounded-full overflow-hidden ring-2 ring-customOrange/20",whileHover:{scale:1.05},children:(0,s.jsx)(j.default,{src:t.ClassAbout&&t.ClassAbout.classesLogo?"".concat("http://localhost:4005/").concat(t.ClassAbout.classesLogo):"/default-profile.jpg",alt:t.firstName,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold hover:text-customOrange transition-colors",children:[t.firstName," ",t.lastName]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t.className})]}),(0,s.jsxs)(w.m_,{children:[(0,s.jsx)(w.k$,{asChild:!0,children:(0,s.jsx)("div",{className:"relative group cursor-pointer",children:(0,s.jsx)(C.VqV,{className:"text-green-500"})})}),(0,s.jsx)(w.ZI,{className:"text-xs",children:"Verified by Uest"})]})]}),(0,s.jsxs)(n.Wu,{className:"flex-1 space-y-4",children:[(0,s.jsxs)(w.m_,{children:[(0,s.jsx)(w.k$,{asChild:!0,children:(null===(l=t.education)||void 0===l?void 0:l.filter(e=>e.isDegree&&"APPROVED"===e.status).length)>0&&(0,s.jsxs)("div",{className:"text-xs text-muted-foreground mt-1 flex items-center gap-1 flex-wrap",children:[(0,s.jsx)(k.YNd,{className:"text-customOrange text-lg"}),(0,s.jsxs)("span",{className:"text-md",children:["Degrees:"," ",t.education.filter(e=>e.isDegree&&"APPROVED"===e.status).map(e=>e.degree).join(", ")]})]})}),(0,s.jsx)(w.ZI,{className:"text-xs",children:"Verified by Uest"})]}),(0,s.jsx)("p",{className:"line-clamp-2 text-sm text-muted-foreground",children:t.ClassAbout&&t.ClassAbout.tutorBio||"No bio available."}),Array.isArray(t.tuitionClasses)&&t.tuitionClasses.length>0&&(0,s.jsx)("div",{className:"space-y-2 p-4 rounded-lg bg-black/5 dark:bg-white/5",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"font-medium",children:"Category"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,N.sA)(t.tuitionClasses[0].education)||"N/A"})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"font-medium",children:"Coaching Type"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,N.sA)(t.tuitionClasses[0].coachingType)||"N/A"})]}),"Education"===t.tuitionClasses[0].education&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"font-medium",children:"Board"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,N.sA)(t.tuitionClasses[0].boardType)||"N/A"})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"font-medium",children:"Medium"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,N.sA)(t.tuitionClasses[0].medium)||"N/A"})]})]})]})}),void 0!==t.distance&&(0,s.jsxs)("div",{className:"text-sm text-customOrange font-semibold",children:["Distance: ",(t.distance/1e3).toFixed(2)," km"]})]}),(0,s.jsxs)(n.wL,{className:"flex flex-col items-start gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 pt-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,s.jsx)("span",{className:"font-semibold text-foreground",children:t.averageRating?t.averageRating.toFixed(1):"0"}),(0,s.jsxs)("span",{children:["(",t.reviewCount||0," reviews)"]})]}),(0,s.jsxs)("div",{className:"flex gap-2 w-full",children:[(0,s.jsx)(o.$,{className:"flex-1 bg-customOrange hover:bg-[#E88143]",onClick:()=>e.push("/classes-details/".concat(t.id)),children:"View Profile"}),(0,s.jsx)(o.$,{variant:"outline",className:"flex-1 hover:bg-orange-50",onClick:()=>{if(!F){L(!0);return}let a="".concat(t.firstName," ").concat(t.lastName);e.push("/student/chat?userId=".concat(t.id,"&userName=").concat(encodeURIComponent(a)))},children:"Message"})]})]})]})},a)})}),(0,s.jsxs)("div",{className:"flex justify-center items-center mt-8 gap-4",children:[(0,s.jsxs)(o.$,{variant:"outline",disabled:1===S,onClick:()=>E(S-1),className:"flex gap-2",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"})," Previous"]}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",S," of ",P]}),(0,s.jsxs)(o.$,{variant:"outline",disabled:S===P,onClick:()=>E(S+1),className:"flex gap-2",children:["Next ",(0,s.jsx)(x.A,{className:"h-4 w-4"})]})]}),(0,s.jsx)(y.lG,{open:D,onOpenChange:L,children:(0,s.jsxs)(y.Cf,{className:"sm:max-w-md",children:[(0,s.jsx)(y.c7,{children:(0,s.jsx)(y.L3,{className:"text-center",children:"Login Required"})}),(0,s.jsx)("div",{className:"space-y-4 py-4",children:(0,s.jsx)("p",{className:"text-center text-muted-foreground",children:"Please login as a student to add this class to send a message."})})]})})]})]})},A=()=>(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsx)(g.default,{}),(0,s.jsx)(l.Suspense,{fallback:(0,s.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsx)(v.E,{className:"h-96 w-full rounded-xl"},t))})}),children:(0,s.jsx)(O,{})}),(0,s.jsx)(p.default,{})]})},94314:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,_3:()=>l,ac:()=>i});var s=a(51990),l=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let r=(0,s.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let a=t.payload;e.completedForms[a]||(e.completedForms[a]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:n}=r.actions,o=r.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,512,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,8159,1739,347,8441,1684,7358],()=>t(69244)),_N_E=e.O()}]);