(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7127],{2773:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(95155),l=s(12115),r=s(30285),n=s(55077);let d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let a=await n.S.get("/mock-exam-leaderboard/leaderboard/".concat(e,"?page=").concat(t,"&limit=").concat(s),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data}}catch(e){var a,l;return{success:!1,error:"Failed to get mock exam leaderboard data: ".concat((null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||e.message)}}},i=(0,s(19946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var c=s(19320),o=s(70347),m=s(7583),x=s(66766),g=s(29911),u=s(95811);let p=["Today","Weekly","All time"];function h(){let[e,t]=(0,l.useState)("Today"),[s,n]=(0,l.useState)([]),[h,b]=(0,l.useState)(1),[f,w]=(0,l.useState)(1),[j,v]=(0,l.useState)(!1),[N,y]=(0,l.useState)(null),_=e=>e>=100&&e<=499?"/scholer.svg":e>=500&&e<=999?"/Mastermind.svg":e>=1e3?"/Achiever.svg":null;(0,l.useEffect)(()=>{(async()=>{var t;v(!0),y(null);let s=e.toLowerCase().replace(" ","-"),a=await d(s,f,10);a.success&&(null===(t=a.data)||void 0===t?void 0:t.data)?(n(a.data.data),b(Math.ceil(a.data.total/10))):y(a.error||"Failed to fetch leaderboard"),v(!1)})()},[e,f]);let k=s.slice(0,3),z=s.slice(3),O=function(e){var t,s;let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:96,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.profileImage&&""!==e.profileImage.trim()?(0,a.jsx)(x.default,{src:e.profileImage,alt:"".concat(e.firstName||""," ").concat(e.lastName||""),width:l,height:l,className:"rounded-full object-cover"}):(0,a.jsx)("div",{style:{width:l,height:l},className:"flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-lg sm:text-xl md:text-2xl border-4 border-customOrange",children:((null===(t=e.firstName)||void 0===t?void 0:t.charAt(0))||"")+((null===(s=e.lastName)||void 0===s?void 0:s.charAt(0))||"")});return r?(0,a.jsx)(c.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:n}):n};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.default,{}),(0,a.jsx)("div",{className:"min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center",children:(0,a.jsxs)("div",{className:"w-full max-w-5xl space-y-6 sm:space-y-8 pt-8",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange",children:"Daily Quiz Leaderboard"}),(0,a.jsx)("div",{className:"flex justify-center gap-4 sm:gap-10 overflow-x-auto",children:p.map(s=>(0,a.jsx)(r.$,{variant:e===s?"default":"outline",className:"rounded-full px-4 sm:px-6 py-1 sm:py-2 text-sm sm:text-base font-semibold ".concat(e===s?"bg-customOrange text-white":"border-orange-400 text-orange-400"," whitespace-nowrap"),"aria-label":"Select ".concat(s," leaderboard"),onClick:()=>{t(s),w(1)},children:s},s))}),j&&(0,a.jsx)("p",{className:"text-center text-gray-500",children:"Loading..."}),N&&(0,a.jsx)("p",{className:"text-center text-red-500",children:N}),!j&&!N&&(0,a.jsx)("div",{className:"flex flex-col sm:flex-row justify-around items-center sm:items-end gap-4 sm:gap-6 mt-6 sm:mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg",children:k.map((e,t)=>(0,a.jsx)(c.P.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2*t},className:"flex flex-col items-center ".concat(0===t?"order-2":1===t?"order-1":"order-3"),children:(0,a.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"relative rounded-full border-4 p-2 ".concat(0===t?"shadow-2xl scale-110 border-customOrange":"border-orange-500"),children:[0===t&&(0,a.jsx)(i,{className:"absolute -top-6 sm:-top-8 left-1/2 -translate-x-1/2 text-customOrange w-6 sm:w-8 h-6 sm:h-8"}),O(e,64,0===t),(0,a.jsx)("div",{className:"absolute -bottom-4 sm:-bottom-5 left-1/2 -translate-x-1/2 rounded-full flex items-center justify-center font-bold ".concat(0===t?"w-7 h-7 sm:w-9 sm:h-9 bg-orange-500 text-white shadow-lg border-4 border-orange-500":1===t?"w-6 h-6 sm:w-8 sm:h-8 bg-orange-500 text-white shadow border-4 border-orange-500":"w-5 h-5 sm:w-7 sm:h-7 bg-orange-500 text-white border-4 border-orange-500"),children:e.rank})]}),(0,a.jsxs)("p",{className:"mt-6 sm:mt-8 font-semibold text-base sm:text-lg text-center",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"mt-2 w-full flex justify-center",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 sm:gap-3",children:[_(e.coinEarnings)&&(0,a.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,a.jsx)(x.default,{src:_(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,a.jsx)(u.A,{badge:e.badge})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-2 sm:gap-5 mt-2",children:[(0,a.jsx)("div",{className:"px-3 sm:px-4 py-1 rounded-full border border-orange-300 text-orange-600 font-bold text-xs sm:text-sm ".concat(0===t?"bg-green-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.lHQ,{})," ",e.score]})}),(0,a.jsxs)("div",{className:"px-3 sm:px-4 py-1 rounded-full border border-green-300 text-green-600 font-bold text-xs sm:text-sm flex items-center gap-1 ".concat(0===t?"bg-green-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"),children:[(0,a.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,a.jsxs)("div",{className:"px-3 sm:px-4 py-1 rounded-full border border-blue-300 text-blue-600 font-bold text-xs sm:text-sm ".concat(0===t?"bg-blue-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-blue-100"),children:["\uD83D\uDD25 ",e.streakCount]})]})]})},e.studentId))}),!j&&!N&&(0,a.jsx)("div",{className:"rounded-lg mt-6 sm:mt-10 bg-white space-y-3 sm:space-y-4",children:z.map(e=>(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between p-3 sm:p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 sm:gap-4 w-full sm:w-auto",children:[(0,a.jsx)("div",{className:"relative flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-orange-100 text-orange-500 font-bold text-sm sm:text-lg",children:e.rank}),O(e,48),(0,a.jsxs)("p",{className:"font-semibold text-base sm:text-lg text-black",children:[e.firstName," ",e.lastName]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3 sm:gap-5 mt-3 sm:mt-0 w-full sm:w-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[_(e.coinEarnings)&&(0,a.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,a.jsx)(x.default,{src:_(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,a.jsx)(u.A,{badge:e.badge})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap justify-center",children:[(0,a.jsx)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-orange-300 bg-orange-100 text-orange-600",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.lHQ,{className:"mr-1"})," ",e.score]})}),(0,a.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-green-300 bg-green-100 text-green-700 flex items-center gap-1",children:[(0,a.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,a.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-blue-300 bg-blue-100 text-blue-700",children:["\uD83D\uDD25 ",e.streakCount]})]})]})]},e.studentId))}),!j&&!N&&s.length>0&&(0,a.jsxs)("div",{className:"flex justify-center gap-4 mt-6 sm:mt-8",children:[(0,a.jsx)(r.$,{disabled:1===f,onClick:()=>w(e=>e-1),className:"px-4 sm:px-6 py-1 sm:py-2 rounded-full bg-customOrange text-white disabled:bg-gray-300 text-sm sm:text-base","aria-label":"Go to previous page",children:"Previous"}),(0,a.jsxs)("span",{className:"flex items-center text-sm sm:text-lg",children:["Page ",f," of ",h]}),(0,a.jsx)(r.$,{disabled:f===h,onClick:()=>w(e=>e+1),className:"px-4 sm:px-6 py-1 sm:py-2 rounded-full bg-customOrange text-white disabled:bg-gray-300 text-sm sm:text-base","aria-label":"Go to next page",children:"Next"})]})]})}),(0,a.jsx)(m.default,{})]})}},8989:(e,t,s)=>{Promise.resolve().then(s.bind(s,2773))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,347,6764,8441,1684,7358],()=>t(8989)),_N_E=e.O()}]);