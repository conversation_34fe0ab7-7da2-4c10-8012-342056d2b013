(()=>{var e={};e.id=3612,e.ids=[3612],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var s=r(60687);r(43210);var a=r(95732),l=r(78272),n=r(13964),i=r(3589),o=r(4780);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...n}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:r="popper",...l}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...l,children:[(0,s.jsx)(h,{}),(0,s.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function x({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}function p({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}},15585:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),n=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["student",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59006)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/student/profile/page",pathname:"/student/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},18333:(e,t,r)=>{Promise.resolve().then(r.bind(r,59006))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var s=r(60687);r(43210);var a=r(4780);function l({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},36893:(e,t,r)=>{Promise.resolve().then(r.bind(r,83726))},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n,wL:()=>c});var s=r(60687);r(43210);var a=r(4780);function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},51361:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59006:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx","default")},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var l=r(60687),n=Symbol("radix.slottable");function i(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===n}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...l}=e;if(s.isValidElement(r)){var n;let e,i;let o=(n=r,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,t){let r={...t};for(let s in t){let a=e[s],l=t[s];/^on[A-Z]/.test(s)?a&&l?r[s]=(...e)=>{l(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...l}:"className"===s&&(r[s]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...n}=e,o=s.Children.toArray(a),d=o.find(i);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,l.jsx)(t,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?r:t,{...n,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),d=s.forwardRef((e,t)=>(0,l.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83726:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>B});var s=r(60687),a=r(43210),l=r.n(a),n=r(63442),i=r(27605),o=r(45880),d=r(52581),c=r(16189),u=r(79663),m=r(40228),x=r(51361),h=r(13964),p=r(11860),f=r(62688);let g=(0,f.A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),b=(0,f.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var j=r(54864),y=r(74004),v=r(45201),N=r(80942),w=r(89667),k=r(29523),C=r(34729),z=r(15079),P=r(44493),R=r(40988),M=r(11095),S=r(4780),A=r(30474),_=r(90269);let E=o.z.object({firstName:o.z.string().min(2,"First name must be at least 2 characters."),lastName:o.z.string().min(2,"Last name must be at least 2 characters."),contact:o.z.string().min(10,"Contact number must be at least 10 digits.").max(15,"Contact number must not exceed 15 digits.").regex(/^\d+$/,"Contact number must contain only digits."),medium:o.z.string().min(1,"Medium of instruction is required"),classroom:o.z.string().min(1,"Classroom standard is required"),school:o.z.string().min(2,"School name must be at least 2 characters."),birthday:o.z.date({required_error:"Please select your birthday"}),address:o.z.string().min(5,"Address must be at least 5 characters."),photo:o.z.any().optional(),document:o.z.any().optional()}),q=()=>{let e=(0,c.useRouter)(),t=(0,j.wA)(),r=(0,c.useSearchParams)(),o="true"===r.get("quiz"),f=r.get("examId"),[q,B]=(0,a.useState)(null),[I,V]=(0,a.useState)(!1),[D,T]=(0,a.useState)(!1),[U,W]=(0,a.useState)(null),[F,$]=(0,a.useState)(null),[J,L]=(0,a.useState)(!1),{profileData:O,loading:G}=(0,j.d4)(e=>e.studentProfile),Z=O?.profile||null,H=O?.classroomOptions||[],Y=(0,a.useRef)(null),X=(0,a.useRef)(null),K=(0,i.mN)({resolver:(0,n.u)(E),defaultValues:{firstName:"",lastName:"",contact:"",medium:"",classroom:"",birthday:void 0,school:"",address:""},mode:"onSubmit"});(0,a.useEffect)(()=>{localStorage.getItem("studentToken")||(d.oR.error("Please login to access your profile"),e.push("/"))},[e]),(0,a.useEffect)(()=>{localStorage.getItem("studentToken")&&t((0,y.N)())},[t]),(0,a.useEffect)(()=>{if(I||!O)return;let e=O.profile,t=e?.student||JSON.parse(localStorage.getItem("student_data")||"{}"),r={firstName:t?.firstName||"",lastName:t?.lastName||"",contact:t?.contact||"",medium:e?.medium||"",classroom:e?.classroom||"",birthday:e?.birthday?new Date(e.birthday):void 0,school:e?.school||"",address:e?.address||""};if(e?.photo&&!q&&(B(e.photo),K.setValue("photo",e.photo)),e?.documentUrl&&!F&&!J){let t=e.documentUrl.startsWith("http")?e.documentUrl:`http://localhost:4005/${e.documentUrl}`,r={name:t.split("/").pop()||"Uploaded Document",size:0,url:t,type:"application/octet-stream"};$(r),K.setValue("document",r)}let s=K.getValues(),a=!s.firstName&&!s.lastName&&!s.contact,l=!s.medium||!s.classroom;(a||l)&&K.reset(r)},[O,K,I,q,F,J]);let Q=async()=>{W(null);try{if(!navigator.mediaDevices?.getUserMedia)throw Error("Camera not supported on this device");V(!0);let e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});Y.current&&(Y.current.srcObject=e,Y.current.onloadedmetadata=()=>{Y.current?.play().catch(()=>d.oR.error("Error starting camera preview"))})}catch(t){V(!1);let e="NotAllowedError"===t.name?"Please allow camera access in your browser settings.":"Could not access camera. Please check your camera settings.";W(e),d.oR.error(e)}},ee=(e,t=800,r=.6)=>{if(!e.getContext("2d"))return"";let s=e.width,a=e.height,l=s,n=a;s>t&&(l=t,n=a*t/s);let i=document.createElement("canvas");i.width=l,i.height=n;let o=i.getContext("2d");return o?(o.drawImage(e,0,0,l,n),i.toDataURL("image/jpeg",r)):""},et=()=>{if(!Y.current||!X.current)return;let e=Y.current,r=X.current,s=r.getContext("2d");r.width=e.videoWidth,r.height=e.videoHeight,s?.clearRect(0,0,r.width,r.height),s?.save(),s?.scale(-1,1),s?.drawImage(e,-r.width,0,r.width,r.height),s?.restore();let a=ee(r,800,.6);if(3*a.split(",")[1].length/4/1024>5120){d.oR.error("Photo size exceeds 5MB limit. Please try again.");return}B(a),K.setValue("photo",a),t((0,v.XY)(a)),er()},er=()=>{Y.current?.srcObject&&(Y.current.srcObject.getTracks().forEach(e=>e.stop()),Y.current.srcObject=null),V(!1),W(null)},es=()=>{F&&"url"in F&&F.url.startsWith("blob:")&&URL.revokeObjectURL(F.url),$(null),L(!0);let e=document.getElementById("document");e&&(e.value=""),K.setValue("document",null)},ea=e=>e<1024?e+" bytes":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",el=l().useMemo(()=>{let e=!!(q||O?.profile?.photo),t=!!F&&!J;return!!(K.getValues().firstName&&K.getValues().lastName&&K.getValues().contact&&K.getValues().medium&&K.getValues().classroom&&K.getValues().birthday&&K.getValues().school&&K.getValues().address&&e&&t)},[K,q,F,O?.profile?.photo,J]),en=async r=>{T(!0);try{if(!(q||O?.profile?.photo)){d.oR.error("Please capture a photo for your profile"),T(!1);return}if(!F||J){d.oR.error("Identity document is required. Please upload a document."),T(!1);return}if(!await K.trigger()){d.oR.error("Please fill in all required fields correctly"),T(!1);return}let s={firstName:r.firstName,lastName:r.lastName,contact:r.contact,medium:r.medium,classroom:r.classroom,birthday:r.birthday?.toISOString()||"",school:r.school,address:r.address};if(q?.startsWith("data:")){let e=q.split(",")[1];if(3*e.length/4/1024>5120){d.oR.error("Photo size exceeds 5MB limit.");return}s.photo=e,s.photoMimeType="image/jpeg"}if(F instanceof File||F&&"url"in F&&F.url.startsWith("blob:")){let e=F instanceof File?F:await fetch(F.url).then(e=>e.blob()).then(e=>new File([e],F.name,{type:F.type})),t=await new Promise((t,r)=>{let s=new FileReader;s.onload=()=>t(s.result.split(",")[1]),s.onerror=r,s.readAsDataURL(e)});if(3*t.length/4/1024>5120){d.oR.error("Document size exceeds 5MB limit.");return}s.document=t,s.documentMimeType=e.type,s.documentName=e.name}if(J&&O?.profile?.documentUrl&&(s.removeDocument=!0),!localStorage.getItem("studentToken")){d.oR.error("Please login to submit your profile"),e.push("/");return}let a=await t((0,y.A)(s));if("fulfilled"===a.meta.requestStatus){d.oR.success(`Profile ${Z?"updated":"created"} successfully!`);let s=JSON.parse(localStorage.getItem("student_data")||"{}"),a={...s,id:s.id||O?.profile?.student?.id||"",firstName:r.firstName,lastName:r.lastName,email:s.email||O?.profile?.student?.email||"",contact:r.contact};localStorage.setItem("student_data",JSON.stringify(a)),L(!1),await t((0,y.N)()),o?f?e.push(`/uwhiz-exam/${f}`):e.push("/mock-test"):e.push("/")}else if("rejected"===a.meta.requestStatus){let t=a.payload;t.includes("401")||t.includes("Unauthorized")?(d.oR.error("Your session has expired. Please login again."),localStorage.removeItem("studentToken"),e.push("/")):d.oR.error(t||"Failed to update profile")}}catch{d.oR.error("Failed to submit profile information")}finally{T(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(_.default,{}),(0,s.jsx)("div",{className:"min-h-screen py-12",children:(0,s.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 max-w-3xl",children:(0,s.jsxs)("div",{className:"bg-white shadow-xl rounded-2xl overflow-hidden",children:[(0,s.jsxs)("div",{className:"bg-black p-6 sm:p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Student Profile"}),(0,s.jsx)("p",{className:"text-white/80 mt-2",children:"Complete your profile information"})]}),(0,s.jsx)("div",{className:"p-6 sm:p-8",children:G?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,s.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading profile information..."})]}):(0,s.jsx)(N.lV,{...K,children:(0,s.jsxs)("form",{onSubmit:K.handleSubmit(en),className:"space-y-8",children:[(0,s.jsxs)(P.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsx)(P.ZB,{className:"text-lg font-medium",children:"Personal Information"}),(0,s.jsx)(P.BT,{children:"Update your personal details"})]}),(0,s.jsxs)(P.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(N.zB,{control:K.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"First Name"}),(0,s.jsx)(N.MJ,{children:(0,s.jsx)(w.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your first name"})}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})}),(0,s.jsx)(N.zB,{control:K.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Last Name"}),(0,s.jsx)(N.MJ,{children:(0,s.jsx)(w.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your last name"})}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})})]}),(()=>{let e=O?.profile?.student?.email;return e?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Email"}),(0,s.jsx)("div",{className:"p-2 bg-gray-50 border border-gray-200 rounded-md",children:(0,s.jsx)("p",{className:"text-gray-700",children:e})}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Your email address cannot be changed"})]}):null})(),(0,s.jsx)(N.zB,{control:K.control,name:"contact",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Contact Number"}),(0,s.jsx)(N.MJ,{children:(0,s.jsx)(w.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your contact number",type:"tel",inputMode:"numeric",pattern:"[0-9]*",onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:t=>{let r=t.target.value.replace(/\D/g,"");e.onChange(r)}})}),(0,s.jsx)(N.Rr,{className:"text-xs text-gray-500",children:"Your contact number will be used for important notifications"}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})}),(0,s.jsx)(N.zB,{control:K.control,name:"birthday",render:({field:e})=>(0,s.jsxs)(N.eI,{className:"flex flex-col",children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Birthday"}),(0,s.jsxs)(R.AM,{children:[(0,s.jsx)(R.Wv,{asChild:!0,children:(0,s.jsx)(N.MJ,{children:(0,s.jsxs)(k.$,{variant:"outline",className:(0,S.cn)("w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg",!e.value&&"text-muted-foreground"),children:[e.value&&e.value instanceof Date&&!isNaN(e.value.getTime())?(0,u.GP)(e.value,"PPP"):(0,s.jsx)("span",{children:"Select your birthday"}),(0,s.jsx)(m.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,s.jsxs)(R.hl,{className:"w-auto p-0 bg-white border border-gray-300 shadow-lg",align:"start",children:[(0,s.jsx)("div",{className:"p-3 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,s.jsxs)(z.l6,{value:e.value?e.value.getFullYear().toString():"",onValueChange:t=>{let r=new Date(e.value||new Date);r.setFullYear(parseInt(t)),e.onChange(r)},children:[(0,s.jsx)(z.bq,{className:"w-24",children:(0,s.jsx)(z.yv,{placeholder:"Year"})}),(0,s.jsx)(z.gC,{className:"max-h-48",children:Array.from({length:125},(e,t)=>{let r=new Date().getFullYear()-t;return(0,s.jsx)(z.eb,{value:r.toString(),children:r},r)})})]}),(0,s.jsxs)(z.l6,{value:e.value?e.value.getMonth().toString():"",onValueChange:t=>{let r=new Date(e.value||new Date);r.setMonth(parseInt(t)),e.onChange(r)},children:[(0,s.jsx)(z.bq,{className:"w-32",children:(0,s.jsx)(z.yv,{placeholder:"Month"})}),(0,s.jsx)(z.gC,{children:["January","February","March","April","May","June","July","August","September","October","November","December"].map((e,t)=>(0,s.jsx)(z.eb,{value:t.toString(),children:e},t))})]})]})}),(0,s.jsx)(M.V,{mode:"single",selected:e.value,onSelect:e.onChange,disabled:e=>e>new Date||e<new Date("1900-01-01"),month:e.value||new Date,className:"rounded-md border-0"})]})]}),(0,s.jsx)(N.Rr,{className:"text-xs text-gray-500",children:"Your date of birth will be verified with your documents"}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})}),(0,s.jsx)(N.zB,{control:K.control,name:"address",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Address"}),(0,s.jsx)(N.MJ,{children:(0,s.jsx)(C.T,{...e,rows:3,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none",placeholder:"Enter your full address"})}),(0,s.jsx)(N.Rr,{className:"text-xs text-gray-500",children:"Provide your complete residential address"}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})}),(0,s.jsx)(N.zB,{control:K.control,name:"school",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"School Name"}),(0,s.jsx)(N.MJ,{children:(0,s.jsx)(w.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your school name"})}),(0,s.jsx)(N.Rr,{className:"text-xs text-gray-500",children:"Enter the full name of your school or educational institution"}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})})]})]}),(0,s.jsxs)(P.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsx)(P.ZB,{className:"text-lg font-medium",children:"Educational Information"}),(0,s.jsx)(P.BT,{children:"Select your medium of instruction and classroom standard"})]}),(0,s.jsx)(P.Wu,{className:"space-y-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(N.zB,{control:K.control,name:"medium",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Medium of Instruction"}),(0,s.jsxs)(z.l6,{onValueChange:t=>{e.onChange(t)},value:e.value||void 0,children:[(0,s.jsx)(N.MJ,{children:(0,s.jsx)(z.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,s.jsx)(z.yv,{placeholder:"Select Medium"})})}),(0,s.jsxs)(z.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,s.jsx)(z.eb,{value:"english",children:"English"}),(0,s.jsx)(z.eb,{value:"gujarati",children:"Gujarati"})]})]}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})}),(0,s.jsx)(N.zB,{control:K.control,name:"classroom",render:({field:e})=>(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.lR,{className:"text-black font-medium",children:"Classroom (Standard)"}),(0,s.jsxs)(z.l6,{onValueChange:t=>{e.onChange(t)},value:e.value||void 0,children:[(0,s.jsx)(N.MJ,{children:(0,s.jsx)(z.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,s.jsx)(z.yv,{placeholder:"Select Classroom"})})}),(0,s.jsx)(z.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:G?(0,s.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,s.jsxs)("svg",{className:"animate-spin h-5 w-5 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}):H.length>0?H.map(e=>(0,s.jsx)(z.eb,{value:e.value,children:e.value},e.id)):(0,s.jsx)("div",{className:"p-2 text-center text-gray-500",children:"No classroom options available"})})]}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})})]})})]}),(0,s.jsx)(N.zB,{control:K.control,name:"photo",render:()=>(0,s.jsxs)(P.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsx)(P.ZB,{className:"text-lg font-medium",children:"Profile Photo"}),(0,s.jsx)(P.BT,{children:"Take a clear photo of your face for your profile (MAX. 5MB)"})]}),(0,s.jsx)(P.Wu,{children:(0,s.jsxs)(N.eI,{children:[(0,s.jsx)(N.MJ,{children:(0,s.jsxs)("div",{children:[U&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-700 text-sm",children:U})}),!I&&!q&&(0,s.jsxs)(k.$,{type:"button",onClick:Q,className:"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Open Camera"]}),I&&(0,s.jsxs)("div",{className:"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm",children:[(0,s.jsx)("video",{ref:Y,autoPlay:!0,playsInline:!0,className:"w-full h-auto transform scale-x-[-1]"}),(0,s.jsxs)("div",{className:"flex p-4 bg-gray-50",children:[(0,s.jsxs)(k.$,{type:"button",onClick:et,variant:"default",className:"flex-1 mr-2 bg-black hover:bg-gray-800 text-white",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Capture"]}),(0,s.jsxs)(k.$,{type:"button",onClick:er,variant:"outline",className:"flex-1 border-gray-300",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Cancel"]})]})]}),!I&&(O?.profile?.photo||q)&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4",children:[(0,s.jsx)("div",{className:"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full",children:(0,s.jsx)("div",{className:"flex justify-center",children:(()=>{let e=q||O?.profile?.photo;return e?(0,s.jsx)(A.default,{src:e.startsWith("data:")?e:e.startsWith("http")?e:`http://localhost:4005/${e}?t=${new Date().getTime()}`,alt:"Student Photo",height:1e3,width:1e3,className:"max-w-full max-h-80 object-contain rounded-lg",style:{height:"auto",width:"auto"},unoptimized:e.startsWith("data:")}):(0,s.jsx)("div",{className:"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg",children:(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400"})})})()})}),(0,s.jsxs)(k.$,{type:"button",onClick:()=>{B(null),W(null),t((0,v.XY)(void 0)),K.setValue("photo",null),Q()},variant:"outline",className:"border-gray-300",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Retake Photo"]})]}),(0,s.jsx)("canvas",{ref:X,style:{display:"none"}})]})}),(0,s.jsx)(N.Rr,{className:"text-xs text-gray-500 mt-2",children:"A clear photo helps us identify you and personalize your profile"}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})})]})}),(0,s.jsx)(N.zB,{control:K.control,name:"document",render:({field:e})=>(0,s.jsxs)(P.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsx)(P.ZB,{className:"text-lg font-medium",children:"Identity Document"}),(0,s.jsx)(P.BT,{children:"Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate"})]}),(0,s.jsx)(P.Wu,{children:(0,s.jsxs)(N.eI,{children:[F?(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 bg-[#fff8f3] rounded-full",children:(0,s.jsx)(b,{className:"h-5 w-5 text-black"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:F.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:F instanceof File?ea(F.size):"Previously uploaded document"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[F&&"url"in F&&(0,s.jsx)(k.$,{type:"button",variant:"outline",size:"sm",onClick:()=>window.open(F.url,"_blank"),className:"h-8 px-3 border-gray-200",children:"View"}),(0,s.jsx)(k.$,{type:"button",variant:"outline",size:"sm",onClick:es,className:"h-8 w-8 p-0 border-gray-200",children:(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-500"})})]})]})}):(0,s.jsx)(N.MJ,{children:(0,s.jsx)("div",{className:"flex items-center justify-center w-full",children:(0,s.jsxs)("label",{className:"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,s.jsx)(g,{className:"w-10 h-10 mb-3 text-black"}),(0,s.jsxs)("p",{className:"mb-2 text-sm text-gray-700",children:[(0,s.jsx)("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"PDF, PNG, JPG or JPEG (MAX. 5MB)"})]}),(0,s.jsx)(w.p,{id:"document",type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",onChange:t=>{let r=t.target.files?.[0];if(r){if(r.size>5242880){d.oR.error("File size exceeds 5MB limit");return}$({name:r.name,size:r.size,type:r.type,url:URL.createObjectURL(r)}),L(!1),e.onChange(r)}}})]})})}),(0,s.jsx)(N.Rr,{className:"text-xs text-gray-500 mt-2",children:"This document will serve to verify your identity and date of birth."}),(0,s.jsx)(N.C5,{className:"text-red-500"})]})})]})}),(0,s.jsx)(P.Zp,{className:"shadow-sm border-none",children:(0,s.jsx)(P.Wu,{className:"pt-6",children:(0,s.jsx)(k.$,{type:"submit",className:`w-full font-medium py-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 text-lg ${el&&!D?"bg-black text-white hover:bg-gray-800":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,disabled:D||!el,children:D?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),O?"Updating Profile...":"Creating Profile..."]}):O?"Update Profile":"Save Profile"})})})]})})})]})})})]})},B=()=>(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),children:(0,s.jsx)(q,{})})},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(60687);r(43210);var a=r(4780);function l({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,8721,2105,9663,9528,2800,2489],()=>r(15585));module.exports=s})();