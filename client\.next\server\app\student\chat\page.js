(()=>{var e={};e.id=569,e.ids=[569],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8541:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student\\\\chat\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11022:(e,t,r)=>{Promise.resolve().then(r.bind(r,8541))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},49299:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>c,tree:()=>p});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),u={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let p={children:["",{children:["student",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8541)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/student/chat/page",pathname:"/student/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74070:(e,t,r)=>{Promise.resolve().then(r.bind(r,77399))},74075:e=>{"use strict";e.exports=require("zlib")},77399:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),n=r(16189),a=r(12814);function o(){let[e,t]=(0,i.useState)(!1),[r,o]=(0,i.useState)(""),[u,p]=(0,i.useState)(""),d=(0,n.useSearchParams)(),l=d.get("userId"),c=d.get("userName");return(0,s.jsx)(a.A,{userType:"student",isAuthenticated:e,username:r,userId:u,loginPath:"/",initialSelectedUserId:l||void 0,initialSelectedUserName:c||void 0})}function u(){return(0,s.jsx)(i.Suspense,{children:(0,s.jsx)(o,{})})}r(4780)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82120:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var s=r(43210);function i(){let[e,t]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var i=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,8721,9663,6426,2800,6463],()=>r(49299));module.exports=s})();