@extends('layouts.app')
@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Testimonial</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('create testimonial')
                            <a id="addTestimonialEntry" data-toggle="modal" data-target="#newTestimonialEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New Testimonial</a>
                            @endcan
                        </div>
                        <table id="testimonial_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Testimonial</th>
                                    <th>Rating</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Testimonial</th>
                                    <th>Rating</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newTestimonialEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script>
     var testimonialRoute = {
        index: "{{ route('testimonial.index') }}",
        create: "{{ route('testimonial.create') }}",
        delete: "{{ route('testimonial.destroy',':did') }}",
        edit: "{{ route('testimonial.edit',':editid') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/Testimonial/index.js')) }}"></script>
@endsection