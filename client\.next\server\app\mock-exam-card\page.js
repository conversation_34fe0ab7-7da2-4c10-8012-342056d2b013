(()=>{var e={};e.id=2540,e.ids=[2540],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25314:(e,t,a)=>{Promise.resolve().then(a.bind(a,46900))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var r=a(60687);a(43210);var s=a(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},46900:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\mock-exam-card\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57022:(e,t,a)=>{"use strict";a.d(t,{S:()=>i,q:()=>s});var r=a(28527);let s=async e=>{try{let t=await r.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){return{success:!1,error:`Failed to save mock exam result: ${e.response?.data?.message||e.message}`}}},i=async(e,t=1,a=10)=>{try{let s=await r.S.get(`/mock-exam-result/${e}?page=${t}&limit=${a}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){return{success:!1,error:`Failed to get mock exam result: ${e.response?.data?.message||e.message}`}}}},59818:(e,t,a)=>{Promise.resolve().then(a.bind(a,99046))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88689:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d={children:["",{children:["mock-exam-card",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,46900)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/mock-exam-card/page",pathname:"/mock-exam-card",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},99046:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(60687),s=a(90269),i=a(46303),n=a(44493),o=a(29523),l=a(30474);let d={src:"/_next/static/media/mockExamImage.b1be346b.svg"};var c=a(33793),x=a(30656),u=a(16189),p=a(92449),m=a(52581),h=a(43210);function g(){let e=(0,u.useRouter)(),[t,a]=(0,h.useState)(!1),[s,i]=(0,h.useState)(null),[n,l]=(0,h.useState)(null);(0,h.useRef)(null),(0,h.useRef)(null);let d=()=>{if(null===s)return null;let e=Math.floor(s/3600),t=Math.floor(s%3600/60),a=s%60;return`${e.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`};return(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(o.$,{className:"w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>{if(!n){m.oR.error("Please log in to attempt the exam."),e.push("/login");return}if(t&&s){m.oR.error(`You can attempt the exam again in ${d()}.`);return}e.push("/mock-test")},disabled:t||!n,children:"Try Daily Quiz"}),t&&s&&(0,r.jsxs)("p",{className:"text-center mt-2 text-gray-500 text-sm",children:["Next attempt available in: ",d()]})]})}a(57022);let y=()=>{let e=(0,u.useRouter)(),t=null;try{let e=localStorage.getItem("student_data");t=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),t=null}return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-800 dark:text-gray-100 transition-colors duration-500",children:[(0,r.jsx)(s.default,{}),(0,r.jsx)("section",{className:"bg-gradient-to-r from-gray-900 to-gray-700 py-12 flex justify-center",children:(0,r.jsx)(p.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,ease:"easeOut"},children:(0,r.jsx)(l.default,{height:200,width:300,src:d.src,alt:"Current Affairs Quiz Logo",priority:!0,quality:100,className:"object-contain rounded-lg shadow-lg"})})}),(0,r.jsxs)("section",{className:"text-center mt-10 px-4 pt-7",children:[(0,r.jsxs)(p.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-4xl md:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white",children:["Daily Current Affairs ",(0,r.jsx)("span",{className:"text-amber-500",children:"Quiz"})]}),(0,r.jsx)(p.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"mt-3 text-lg text-gray-600 dark:text-gray-300",children:"Stay informed, test your knowledge, and earn exclusive rewards!"})]}),(0,r.jsx)("section",{className:"flex justify-center p-4 sm:p-6 md:p-10 lg:p-16",children:(0,r.jsx)(p.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7,delay:.6},children:(0,r.jsxs)(n.Zp,{className:"max-w-4xl w-full mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-3xl shadow-xl transition-all duration-500 p-8 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between border-b border-gray-200 dark:border-gray-700/50 pb-5 mb-8",children:[(0,r.jsxs)("h2",{className:"text-2xl md:text-3xl font-extrabold text-gray-900 dark:text-white",children:["Daily Current Affairs ",(0,r.jsx)("span",{className:"text-amber-500",children:"Quiz"})]}),(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 hidden sm:inline",children:"Stay updated & earn rewards daily"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 shadow-sm transition-all duration-300",children:[(0,r.jsxs)(p.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.8},whileHover:{scale:1.02},children:[(0,r.jsx)(c.O6N,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Questions:"})," 10"]})]}),(0,r.jsxs)(p.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:1},whileHover:{scale:1.02},children:[(0,r.jsx)(x.gPQ,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Badges:"})," Streak & Rank-Based"]})]}),(0,r.jsxs)(p.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:1.1},whileHover:{scale:1.02},children:[(0,r.jsx)(c.w_X,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Duration:"})," 8 min"]})]}),(0,r.jsxs)(p.P.div,{className:"flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:1.2},whileHover:{scale:1.02},children:[(0,r.jsx)(c.cEG,{className:"text-amber-500 text-2xl"}),(0,r.jsxs)("span",{className:"text-lg",children:[(0,r.jsx)("strong",{children:"Earn Up to:"})," 5 Coins"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col justify-between space-y-6",children:[(0,r.jsx)(p.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.3},children:(0,r.jsx)(g,{})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 text-center",children:"Attempt once every 24 hours to earn coins & build your streak!"}),(0,r.jsxs)(p.P.div,{className:"text-sm bg-gray-100/50 dark:bg-gray-700/50 rounded-xl px-6 py-5 space-y-4 text-gray-800 dark:text-gray-200 backdrop-blur-sm shadow-sm",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.4},children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Coin Rewards:"}),(0,r.jsxs)("ul",{className:"list-none space-y-2",children:[(0,r.jsxs)(p.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"0 coins for 50% score"]}),(0,r.jsxs)(p.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"1 coins for 60% score"]}),(0,r.jsxs)(p.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"2 coins for 70% score"]}),(0,r.jsxs)(p.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"3 coins for 80% score"]}),(0,r.jsxs)(p.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"4 coins for 90% score"]}),(0,r.jsxs)(p.P.li,{className:"flex items-center gap-2 hover:text-amber-500 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-500 rounded-full"}),"5 coins for 100% score"]})]}),(0,r.jsxs)("p",{className:"font-semibold",children:[(0,r.jsx)("strong",{children:"Streak Bonus:"})," +1 coin per daily attempt"]})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-row flex-wrap gap-4 justify-center",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(l.default,{src:"/scholer.svg",alt:"100 Coins",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"100 Coins"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(l.default,{src:"/Mastermind.svg",alt:"1000 Coins",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"500 Coins"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(l.default,{src:"/Achiever.svg",alt:"10000 Coins",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"1000 Coins"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(l.default,{src:"/Perfect Month.svg",alt:"30 Days Streak",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"30 Days Streak"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(l.default,{src:"/Perfect Year.svg",alt:"365 Days Streak",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"365 Days Streak"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full",children:[(0,r.jsx)(l.default,{src:"/Streak.svg",alt:"Daily Streak",width:48,height:48}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis",children:"Daily Streak"})]})]}),(0,r.jsxs)("div",{className:"mt-10 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(p.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.4},children:(0,r.jsx)(o.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-customOrange hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-lg py-4",onClick:()=>e.push(`/mock-exam-result/${t}`),children:"View Quiz Results & Earned Coins"})}),(0,r.jsx)(p.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.5},children:(0,r.jsx)(o.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-customOrange hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-lg py-4",onClick:()=>e.push("/Leader-Board"),children:"View Leaderboards"})})]})]})})}),(0,r.jsx)(i.default,{})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,8721,3793,656,2800],()=>a(88689));module.exports=r})();