(()=>{var e={};e.id=2328,e.ids=[2328],e.modules={1508:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\mock-test\\\\mockExam.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23031:(e,t,s)=>{Promise.resolve().then(s.bind(s,45777))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35817:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>n,RO:()=>c,Wz:()=>l,sA:()=>i});var r=s(50346),a=s(63766);let l=(e,t)=>{e.contactNo&&t((0,r.ac)(r._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&t((0,r.ac)(r._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&t((0,r.ac)(r._3.PHOTO_LOGO)),e.education?.length>0&&t((0,r.ac)(r._3.EDUCATION)),e.certificates?.length>0&&t((0,r.ac)(r._3.CERTIFICATES)),e.experience?.length>0&&t((0,r.ac)(r._3.EXPERIENCE)),e.tuitionClasses?.length>0&&t((0,r.ac)(r._3.TUTIONCLASS)),e.address&&t((0,r.ac)(r._3.ADDRESS))},n=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch{return[e]}},i=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}},o=new TextEncoder().encode("secret123");async function c(e){try{let{payload:t}=await (0,a.V)(e,o);return t}catch(e){return console.error("Invalid token:",e),null}}},43125:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},45777:(e,t,s)=>{"use strict";s.d(t,{default:()=>v});var r=s(60687),a=s(29523),l=s(43210),n=s.n(l),i=s(16189),o=s(28527);let c=async e=>{try{return(await o.S.get(`/uwhizStudentData/${e}`)).data}catch(e){return{success:!1,error:`Failed To Get Student Detail: ${e.response?.data?.message||e.message}`}}};s(57022);let d=async e=>{try{return(await o.S.post("/mock-exam-terminate",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed to save termination log: ${e.response?.data?.message||e.message}`}}},m=async e=>{try{return(await o.S.get(`mock-exam-terminate/count?studentId=${e}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed To Get Count of termination: ${e.response?.data?.message||e.message}`}}};var u=s(43125),x=s(48730),h=s(52581),p=s(30474),g=s(71062);let f=async e=>{try{return(await o.S.get(`mock-exam/${e}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){throw Error(`Failed To Get Question: ${e.response?.data?.message||e.message}`)}},b=e=>{let t=Date.now(),s=localStorage.getItem("examAttempts"),r=s?JSON.parse(s):{};r[e]=t,localStorage.setItem("examAttempts",JSON.stringify(r))};s(90971);var w=s(35817);let y=()=>{let e=(0,i.useSearchParams)(),t=(0,i.useRouter)(),[s,r]=(0,l.useState)(null);return(0,l.useEffect)(()=>{(async()=>{let s=e.get("token");if(s){let e=await (0,w.RO)(s);if(e?.id){let s={id:e.id};localStorage.setItem("student_data",JSON.stringify(s)),r(e.id);let a=window.location.pathname;t.replace(a)}}else{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e):null;r(t?.id||null)}})()},[e]),s},j=n().memo(()=>(0,r.jsx)("header",{className:"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)(p.default,{height:60,width:60,src:g.A.src,alt:"Uwhiz Logo",quality:100,className:"object-contain sm:h-20 sm:w-20"}),(0,r.jsx)("h1",{className:"text-lg sm:text-2xl font-bold tracking-tight",children:"U-whiz MOCK EXAM"})]})}));function v(){let e=(0,i.useRouter)(),t=y(),[s,n]=(0,l.useState)(!1),[o,p]=(0,l.useState)(!1),[g,w]=(0,l.useState)(!1),[v,k]=(0,l.useState)(!1),[N,S]=(0,l.useState)(!1),[A,E]=(0,l.useState)(!1),[C,F]=(0,l.useState)([]),[z,R]=(0,l.useState)(0),[O,q]=(0,l.useState)(0),[T,D]=(0,l.useState)([]),[P,_]=(0,l.useState)(!1),[$,I]=(0,l.useState)(null),[G,Q]=(0,l.useState)(!1);(0,l.useRef)(null);let[U,M]=(0,l.useState)(""),[L,K]=(0,l.useState)(0),[J,Y]=(0,l.useState)(!1),[B,X]=(0,l.useState)(null),[H,W]=(0,l.useState)(!1),[V,Z]=(0,l.useState)(null),ee=(0,l.useCallback)(async()=>{let e=C[z];if($){let t=$===e.correctAnswer;D(s=>[...s,{questionId:e.id,selectedAnswer:$,isCorrect:t}]),Y(!0),setTimeout(()=>{Y(!1),I(null),z<C.length-1?R(e=>e+1):_(!0)},1e3)}else D(t=>[...t,{questionId:e.id,selectedAnswer:"skipped",isCorrect:!1}]),h.oR.warning("Question skipped."),z<C.length-1?R(e=>e+1):_(!0)},[$,C,z]),et=(0,l.useMemo)(()=>T.reduce((e,t)=>e+ +!!t.isCorrect,0),[T]),es=(0,l.useMemo)(()=>{let e=et/C.length*100;return e>=100?5:e>=90?4:e>=80?3:e>=70?2:+(e>=60)},[et,C.length]);(0,l.useCallback)(async()=>{if(!t){n(!0);return}try{let e=await c(t);if(!e.success){h.oR.error(e.error),p(!0);return}let s=await f(t);s&&Array.isArray(s)?(F(s),q(45),w(!0)):h.oR.error("No questions found or invalid response.")}catch(e){h.oR.error(e)}},[t]);let er=async(e=3,t=1)=>{try{if(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement){if(document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen&&await document.mozCancelFullScreen(),await new Promise(e=>setTimeout(e,100)),!document.fullscreenElement&&!document.webkitFullscreenElement&&!document.mozFullScreenElement)return!0;if(t<e)return await er(e,t+1);throw Error("Max attempts reached")}return!0}catch(s){if(console.error(`Failed to exit full-screen mode (attempt ${t}):`,s),t<e)return await new Promise(e=>setTimeout(e,500)),await er(e,t+1);return h.oR.error("Failed to exit full-screen mode. Please press Esc to exit manually."),!1}},ea=()=>{let e=document.documentElement;e.requestFullscreen&&e.requestFullscreen().catch(e=>console.error("Failed to enter fullscreen:",e))};(0,l.useCallback)(async e=>{if(g||s||o||N||G||v)return;let r=["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"],a=e.ctrlKey&&e.shiftKey&&("I"===e.key||"J"===e.key||"C"===e.key)||e.metaKey&&e.altKey&&"I"===e.key||"F12"===e.key,l=(e.ctrlKey||e.metaKey)&&("c"===e.key||"C"===e.key);if(["Alt","Control","Tab","Shift","Enter"].includes(e.key)||r.includes(e.key)||a||l){if(e.preventDefault(),l){h.oR.warning("Copying is disabled during the quiz.");return}if(!t){K(0);return}Q(!0);try{let s=a?"DevTools shortcut":r.includes(e.key)?`Function key "${e.key}"`:`Restricted key "${e.key}"`;await d({studentId:t,reason:s});let l=await m(t);K(l),1===l?(S(!0),h.oR.warning(`${s} detected.`)):2===l?(S(!0),h.oR.warning(`${s} detected. One more violation will terminate the quiz.`)):l>=3&&(E(!0),M("Quiz terminated due to multiple cheating attempts."),t&&b(t),h.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{Q(!1)}}},[t,g,s,o,N,G,v]),(0,l.useCallback)(async()=>{if(!g&&!s&&!o&&!N&&!G&&!v&&document.hidden){Q(!0);try{if(await d({studentId:t,reason:"Tab switch"}),!t){K(0);return}let e=await m(t);K(e),1===e?(S(!0),h.oR.warning("Tab switch detected.")):2===e?(S(!0),h.oR.warning("Again tab switch detected. One more violation will terminate the quiz.")):e>=3&&(E(!0),M("Quiz terminated due to multiple cheating attempts."),t&&b(t),h.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{Q(!1)}}},[t,g,s,o,N,G,v]),(0,l.useCallback)(async e=>{g||s||o||N||G||v||(e.preventDefault(),h.oR.warning("Right-click is disabled during the quiz."))},[t,g,s,o,N,G,v]),(0,l.useCallback)(async()=>{if(!g&&!s&&!o&&!N&&!G&&!v){Q(!0);try{await d({studentId:t,reason:"Window blur"});let e=await m(t);K(e),1===e?(S(!0),h.oR.warning("Window focus lost.")):2==e?(S(!0),h.oR.warning("Window focus lost again. One more violation will terminate the quiz.")):e>=3&&(E(!0),M("Quiz terminated due to multiple cheating attempts."),t&&b(t),h.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{Q(!1)}}},[t,g,s,o,N,G,v]),(0,l.useCallback)(async()=>{if(!g&&!s&&!o&&!N&&!G&&!v&&!document.fullscreenElement){Q(!0);try{if(await d({studentId:t,reason:"Full-screen exit"}),!t){K(0);return}let e=await m(t);K(e),1===e?(S(!0),h.oR.warning("You have exited full-screen mode.")):2===e?(S(!0),h.oR.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.")):e>=3&&(E(!0),M("Quiz terminated due to multiple cheating attempts."),t&&b(t),h.oR.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.oR.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{Q(!1)}}},[t,g,s,o,N,G,v]);let el=async()=>{E(!1),(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)&&(await er()||h.oR.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.")),e.push("/mock-exam-card"),setTimeout(()=>{e.push("/mock-exam-card")},1e3)},en=(0,l.useCallback)(e=>{let t=Math.floor(e/60);return`${t.toString().padStart(2,"0")}:${(e%60).toString().padStart(2,"0")}`},[]),ei=(0,l.useMemo)(()=>C.length>0?(z+1)/C.length*100:0,[z,C]),eo=e=>{let t="w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white";return $===e?`${t} bg-orange-100 border-orange-500`:t},ec=e=>{J||I(e)};if(s)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Login Required"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Please log in as a student to access the quiz."}),(0,r.jsx)(a.$,{onClick:()=>e.push("/student/login?redirect=/mock-test"),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Login to Continue"})]})});if(o)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Complete Your Profile"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Your profile is incomplete. Please complete your profile to proceed."}),(0,r.jsx)(a.$,{onClick:()=>{e.push("/student/profile")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Complete Profile"})]})});if(v)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-black",children:"Exam Already Taken"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have already attempted the mock exam today. Please try again tomorrow."}),(0,r.jsx)(a.$,{onClick:()=>{k(!1),e.push("/mock-exam-card")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Go to Home"})]})});if(0===C.length)return(0,r.jsxs)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:[(0,r.jsx)("p",{className:"text-base sm:text-xl font-medium mr-4",children:"Loading questions..."}),(0,r.jsx)(u.A,{className:"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"})]});if(P)return(0,r.jsxs)("div",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 overflow-hidden text-gray-900 dark:text-white px-4",children:[(0,r.jsx)("div",{className:"absolute inset-0 z-0 pointer-events-none",children:(0,r.jsx)("div",{className:"animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full"})}),(0,r.jsxs)("div",{className:"relative z-10 text-center p-8 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl max-w-md w-full space-y-6 transition-all border border-orange-100 dark:border-gray-700",children:[(0,r.jsx)("h1",{className:"text-3xl font-extrabold text-orange-600 dark:text-orange-400",children:"Quiz Completed \uD83C\uDF89"}),(0,r.jsxs)("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:["You ",(0,r.jsx)("span",{className:"text-orange-500 font-bold",children:"rocked it"}),"! Keep up the momentum. \uD83D\uDCAA"]}),(0,r.jsxs)("p",{className:"text-base text-gray-800 dark:text-gray-200",children:["Final Score: ",(0,r.jsx)("span",{className:"font-bold",children:et})," / ",C.length]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,r.jsx)("div",{className:"absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping"}),(0,r.jsx)("div",{className:"absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse"}),(0,r.jsx)("div",{className:"absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce"}),(0,r.jsx)("div",{className:"relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning",children:(0,r.jsx)("span",{className:"text-4xl",children:"\uD83D\uDD25"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400",children:H?"Loading Streak...":V?"Error Loading Streak":`🔥 Streak: ${B?.streak||0} Days`})]})}),(0,r.jsxs)("div",{className:"text-sm text-gray-800 dark:text-gray-300 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Coins Earned:"})," ",es+(B?.streak||0)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Bonus:"})," +",B?.streak||0," for Streak!"]})]}),(0,r.jsx)("button",{className:"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-3 rounded-xl text-base shadow-lg",onClick:el,children:"\uD83D\uDE80 Continue Learning"})]})]});let ed=C[z];return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-100 text-gray-900",children:[g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Start Quiz"}),(0,r.jsx)("p",{className:"font-semibold mb-4 text-sm sm:text-base text-gray-600",children:"Note: This is a mock exam for testing purposes only."}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",children:[(0,r.jsx)("p",{className:"font-semibold mb-2",children:"Instructions (English):"}),(0,r.jsxs)("ul",{className:"list-disc list-inside mb-4 text-gray-600",children:[(0,r.jsx)("li",{children:"Do not switch tabs during the quiz."}),(0,r.jsx)("li",{children:"Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."}),(0,r.jsx)("li",{children:"Do not open Developer Tools."}),(0,r.jsx)("li",{children:"Do not exit full-screen mode."}),(0,r.jsx)("li",{children:"Do not interact with other windows or applications."}),(0,r.jsx)("li",{children:"Do not change the screen or minimize the quiz window."}),(0,r.jsx)("li",{children:"Do not receive or make calls during the quiz."}),(0,r.jsx)("li",{children:"Do not use split screen or floating windows on your device."})]}),(0,r.jsx)("p",{className:"font-semibold mb-2",children:"સૂચનાઓ (ગુજરાતી):"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."}),(0,r.jsx)("li",{children:"પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."}),(0,r.jsx)("li",{children:"ડેવલપર ટૂલ્સ ખોલશો નહીં."}),(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."}),(0,r.jsx)("li",{children:"ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."}),(0,r.jsx)("li",{children:"અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."}),(0,r.jsx)("li",{children:"સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."}),(0,r.jsx)("li",{children:"ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."}),(0,r.jsx)("li",{children:"તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."})]})]}),(0,r.jsx)(a.$,{onClick:()=>{w(!1),ea(),C.length>0&&q(45)},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"Start Quiz"})]})}),N&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-customOrange",children:"Warning"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have performed a restricted action. Repeating this will terminate the quiz."}),(0,r.jsx)(a.$,{onClick:()=>S(!1),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"OK"})]})}),A&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-red-500",children:"Quiz Terminated"}),(0,r.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:U||"Your quiz has been terminated due to multiple cheating attempts."}),(0,r.jsx)(a.$,{onClick:el,className:"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",children:"Go to Home"})]})}),!g&&!s&&!o&&!v&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j,{}),(0,r.jsx)("div",{className:"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",children:(0,r.jsx)("div",{className:"h-3.5 bg-customOrange rounded-r-full transition-all duration-300",style:{width:`${ei}%`}})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full max-w-3xl",children:[(0,r.jsxs)("div",{className:"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"}),(0,r.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-customOrange",children:en(O)})]}),(0,r.jsxs)("div",{className:"w-full text-center flex flex-col items-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-3 sm:mb-4",children:(0,r.jsxs)("span",{className:"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",children:["Question ",z+1," of ",C.length]})}),(0,r.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",children:ed.question}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",children:[(0,r.jsxs)(a.$,{variant:"outline",className:eo("optionOne"),onClick:()=>ec("optionOne"),disabled:A||J,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"A"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ed.optionOne})]}),(0,r.jsxs)(a.$,{variant:"outline",className:eo("optionTwo"),onClick:()=>ec("optionTwo"),disabled:A||J,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"B"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ed.optionTwo})]}),(0,r.jsxs)(a.$,{variant:"outline",className:eo("optionThree"),onClick:()=>ec("optionThree"),disabled:A||J,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"C"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ed.optionThree})]}),(0,r.jsxs)(a.$,{variant:"outline",className:eo("optionFour"),onClick:()=>ec("optionFour"),disabled:A||J,children:[(0,r.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"D"}),(0,r.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:ed.optionFour})]})]})]}),(0,r.jsx)(a.$,{className:"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>ee(),disabled:A||J,children:z===C.length-1?"Finish":"Next Question"})]}),(0,r.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",children:[(0,r.jsx)("span",{children:"Powered by"}),(0,r.jsx)("span",{className:"font-semibold",children:"UEST EdTech"})]})]})})]})]})}j.displayName="QuizHeader"},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57022:(e,t,s)=>{"use strict";s.d(t,{S:()=>l,q:()=>a});var r=s(28527);let a=async e=>{try{let t=await r.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){return{success:!1,error:`Failed to save mock exam result: ${e.response?.data?.message||e.message}`}}},l=async(e,t=1,s=10)=>{try{let a=await r.S.get(`/mock-exam-result/${e}?page=${t}&limit=${s}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data}}catch(e){return{success:!1,error:`Failed to get mock exam result: ${e.response?.data?.message||e.message}`}}}},62325:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c={children:["",{children:["mock-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68454)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/mock-test/page",pathname:"/mock-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68454:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(61120),l=s(1508);function n(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(l.default,{})})}},71062:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r={src:"/_next/static/media/uwhizExam.5364baa3.png",height:626,width:798,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAElBMVEURCwcCAgEcEQogIB49Pj0lJSX5PC0XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nCXKuREAMAgEsb2H/lv2GDIFQtg2YsGHBIfkwGRP2k7KAwYIAEmvy1CUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86079:(e,t,s)=>{Promise.resolve().then(s.bind(s,1508))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,3766,2800],()=>s(62325));module.exports=r})();