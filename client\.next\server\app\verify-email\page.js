(()=>{var e={};e.id=7839,e.ids=[7839],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20681:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(60687),a=s(46303),i=s(90269),o=s(63503),n=s(58759),l=s(41831),d=s(16189),c=s(43210);let u=()=>{let e=(0,d.useRouter)(),t=(0,d.useSearchParams)(),s=t.get("token"),a=t.get("userType")||"teacher",[i,u]=(0,c.useState)("loading"),[p,x]=(0,c.useState)("");return console.log("Token that is",s),console.log("User type:",a),(0,c.useEffect)(()=>{(async()=>{if(!s){u("error"),x("No verification token");return}try{let t;t="student"===a?await (0,l.Xc)(s):await (0,n.A$)(s),console.log("Verification response:",t),u("success"),x(t.message||"Email verified successfully!"),setTimeout(()=>{e.push("/")},2e3)}catch(e){console.error("Verification error:",e),u("error"),x("Failed to verify email. The token is invalid or expired.")}})()},[s,a,e]),(0,r.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,r.jsxs)(o.lG,{children:["loading"===i&&(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Verifying your email..."}),"success"===i&&(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["Your Email is",(0,r.jsx)("span",{className:"text-orange-500 italic",children:" Verified"}),(0,r.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:p})]}),"error"===i&&(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["Verification",(0,r.jsx)("span",{className:"text-red-500 italic",children:" Failed"}),(0,r.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:p})]})]})})},p=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.default,{}),(0,r.jsx)(c.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading verification..."}),children:(0,r.jsx)(u,{})}),(0,r.jsx)(a.default,{})]})},21820:e=>{"use strict";e.exports=require("os")},23539:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\verify-email\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx","default")},26575:(e,t,s)=>{Promise.resolve().then(s.bind(s,20681))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>x,L3:()=>f,c7:()=>p,lG:()=>n,rr:()=>m,zM:()=>l});var r=s(60687);s(43210);var a=s(6491),i=s(11860),o=s(4780);function n({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,...s}){return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t})}function m({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},73431:(e,t,s)=>{Promise.resolve().then(s.bind(s,23539))},74075:e=>{"use strict";e.exports=require("zlib")},77323:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23539)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/verify-email/page",pathname:"/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,2800],()=>s(77323));module.exports=r})();