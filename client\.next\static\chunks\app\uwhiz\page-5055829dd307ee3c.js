(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7742],{12767:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},42355:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},52278:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>d});var a=s(95155);s(12115);var r=s(59434);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},74454:(e,t,s)=>{Promise.resolve().then(s.bind(s,99067))},99067:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var a=s(95155),r=s(70347);let n={src:"/_next/static/media/exam-logo.367af320.jpg"};var l=s(66695),i=s(30285),c=s(46523),o=s(55077);let d=async(e,t,s)=>{try{return(await o.S.post("/examApplicantEmail/send-exam-applicant-email",{examId:e,exam_name:t,email:s})).data}catch(e){var a,r;throw Error((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||"Failed to send exam applicant email")}};var x=s(31291),m=s(12115),u=s(64315),h=s(56671),g=s(8019),p=s(51154);let f=(0,s(19946).A)("coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);var j=s(52278),w=s(42355),N=s(13052),y=s(12767),v=s(7583),b=s(93588),k=s(24944),_=s(93347),A=s(25703),O=s(89447),C=s(50958),S=s(61672);let M=(0,m.memo)(e=>{let{exam:t}=e,[s,r]=(0,m.useState)("registration"),[n,l]=(0,m.useState)({days:0,hours:0,minutes:0,seconds:0}),[i,c]=(0,m.useState)({minutes:10,seconds:0,isLate:!1});return(0,m.useEffect)(()=>{let e=()=>{let e=t.start_registration_date?new Date(t.start_registration_date):null,s=new Date(t.start_date);if(isNaN(s.getTime())){console.error("Invalid start_date for exam ".concat(t.id,": ").concat(t.start_date)),r("expired"),l({days:0,hours:0,minutes:0,seconds:0}),c({minutes:0,seconds:0,isLate:!0});return}let a=(0,S.L_)(new Date,"Asia/Kolkata"),n=(0,S.L_)(s,"Asia/Kolkata"),i=n.getTime(),o=(function(e,t,s){let a=(0,O.a)(e,void 0);return a.setTime(a.getTime()+t*A.Cg),a})(n,t.duration).getTime(),d=a.getTime();if(e&&!isNaN(e.getTime())&&d<e.getTime()){let t=(0,C.O)(e,a),s=Math.floor(t/86400),n=Math.floor(t%86400/3600),i=Math.floor(t%3600/60);r("registration"),l({days:s,hours:n,minutes:i,seconds:t%60}),c({minutes:10,seconds:0,isLate:!1})}else if(d<i){let e=(0,C.O)(n,a),t=Math.floor(e/86400),s=Math.floor(e%86400/3600),i=Math.floor(e%3600/60);r("application"),l({days:t,hours:s,minutes:i,seconds:e%60}),c({minutes:10,seconds:0,isLate:!1})}else if(d>=i&&d<=o){let e=(0,C.O)(new Date(o),a),t=Math.floor(e/60);r("exam"),l({days:0,hours:0,minutes:0,seconds:0}),c({minutes:t,seconds:e%60,isLate:!1})}else r("late"),l({days:0,hours:0,minutes:0,seconds:0}),c({minutes:0,seconds:0,isLate:!0})};e();let s=setInterval(e,1e3);return()=>clearInterval(s)},[t.start_date,t.start_registration_date,t.id]),(0,a.jsxs)("div",{className:"flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300",children:[(0,a.jsx)(_.MzU,{className:"text-2xl text-customOrange animate-pulse"}),(0,a.jsx)("span",{className:"font-semibold text-customOrange text-sm",children:"registration"===s?(0,a.jsxs)("span",{children:["Registration Starts in: ",n.days,"d ",n.hours,"h ",n.minutes,"m"," ",n.seconds,"s"]}):"application"===s?(0,a.jsxs)("span",{children:["Exam Starts in: ",n.days,"d ",n.hours,"h ",n.minutes,"m"," ",n.seconds,"s"]}):"exam"===s?(0,a.jsxs)("span",{children:["You May Starts In: ",i.minutes,"m ",i.seconds,"s"]}):"late"===s?(0,a.jsx)("span",{children:"You Are Late"}):(0,a.jsx)("span",{children:"Expired"})})]})});var z=s(39414),E=s(66766),L=s(35695),R=s(22429);function P(e){let{open:t,onClose:s}=e,r=(0,m.useRef)(null);return(0,m.useEffect)(()=>{let e=r.current;e&&(t&&!e.open?e.showModal():!t&&e.open&&e.close())},[t]),(0,a.jsx)("dialog",{ref:r,className:"w-[600px] h-[600px] p-0 border-none shadow-xl rounded-lg backdrop:bg-black/50 fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",onClose:s,children:(0,a.jsxs)("div",{className:"relative w-full h-full",children:[(0,a.jsx)("button",{className:"absolute top-3 right-3 text-gray-600 hover:text-red-500 text-2xl z-10",onClick:s,children:"\xd7"}),(0,a.jsx)(E.default,{src:"/MathsMarvelWinner.png",alt:"Uwhiz Winner",width:400,height:250,className:"w-full h-full object-cover rounded-lg"})]})})}let q=()=>{let[e,t]=(0,m.useState)([]),[s,_]=(0,m.useState)(!1),[A,O]=(0,m.useState)(!1),[C,S]=(0,m.useState)(null),[q,D]=(0,m.useState)(1),[T,F]=(0,m.useState)(1),[$]=(0,m.useState)(9),[I,J]=(0,m.useState)(!1),[U,Y]=(0,m.useState)(null),[V,W]=(0,m.useState)(null),[B,K]=(0,m.useState)(!1),[Z,Q]=(0,m.useState)(0),[X,G]=(0,m.useState)(!1),[H,ee]=(0,m.useState)(!1),et=()=>{try{let e=localStorage.getItem("student_data");return e?JSON.parse(e).id:""}catch(e){return""}},es=()=>{ee(!0)},ea=()=>{ee(!1)},er=(0,m.useMemo)(()=>e,[e]),en=(0,L.useRouter)(),el=et(),ei=er.filter(e=>{let t=new Date,s=new Date(e.start_date),a=6e4*e.duration;return new Date(s.getTime()+a).getTime()>t.getTime()}),ec=er.filter(e=>{let t=new Date,s=new Date(e.start_date),a=6e4*e.duration;return new Date(s.getTime()+a).getTime()<t.getTime()});(0,m.useEffect)(()=>{(async()=>{if(el)try{let e=await (0,R.J)();e.success&&W(e.data)}catch(e){console.error("Error fetching discount info:",e)}})()},[el]),(0,m.useEffect)(()=>{(async()=>{J(!0);try{let e=await (0,c.Dl)(q,$,el),s=e.exams;s=el?await Promise.all(s.map(async e=>{try{let t=await (0,g.o)(el,e.id);return{...e,hasAttempted:!1!==t.success&&t}}catch(t){return console.error("Error checking attempt for exam ".concat(e.id,":"),t),{...e,hasAttempted:!1}}})):s.map(e=>({...e,hasAttempted:!1})),t(s),F(e.totalPages||1)}catch(e){console.error("Error fetching exams:",e),h.oR.error(e.message||"Failed to load exams")}finally{J(!1)}})()},[q,$,el]);let eo=e=>{S(e),Y(null),O(!0)},ed=async()=>{try{return(await o.S.get("/coins/get-total-coins/student")).data.coins}catch(e){h.oR.error("Failed to load coin data. Please try again."),console.error("Error fetching data",e)}},ex=async()=>{if(!C)return;let e=et();if(!e){h.oR.error("Please log in as a student to apply for an exam");return}let s=await ed();try{let s=await (0,x.n)(C.id,e);if(s.application){try{let e=localStorage.getItem("student_data");if(e){let t=JSON.parse(e).email;t&&await d(C.id,C.exam_name,t)}}catch(e){console.log("Email sending failed",e)}t(e=>e.map(e=>e.id===C.id?{...e,joinedClassesCount:e.joinedClassesCount+1,totalApplicants:(e.totalApplicants||0)+1,hasApplied:!0}:e)),O(!1),_(!0),h.oR.success(s.message||"Successfully applied for the exam"),Y(null)}}catch(t){let e=t.message||"Error applying for exam";if(h.oR.error(e),e.includes("Required Coin for Applying in Exam")){var a;Y(e);let t=null!==(a=Number(C.coins_required))&&void 0!==a?a:0;(null==V?void 0:V.hasDiscount)&&(t*=1-V.discountPercentage/100),Q(Math.floor(Math.floor(t)-s))}else O(!1)}finally{K(!1)}},em=()=>{_(!1),O(!1),S(null),Y(null)},eu=e=>{window.location.href="/uwhiz-info/".concat(e)},eh=(e,t)=>0===t?0:Math.min(100,Math.max(0,e/t*100)),eg=async()=>{K(!0);try{let{order:e}=(await o.S.post("/coins/create-order",{amount:100*Z})).data,t={key:"rzp_test_Opr6M8CKpK12pF",amount:e.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:e.id,handler:async function(e){try{G(!0),await o.S.post("/coins/verify",{razorpay_order_id:e.razorpay_order_id,razorpay_payment_id:e.razorpay_payment_id,razorpay_signature:e.razorpay_signature,amount:100*Z}),h.oR.success("Coins added successfully!"),ex(),G(!1)}catch(e){h.oR.error("Payment verification failed")}finally{G(!1)}},theme:{color:"#f97316"}};new window.Razorpay(t).open()}catch(e){h.oR.error("Payment initialization failed")}finally{K(!1)}};return(0,m.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("div",{className:"flex justify-center bg-black pb-10",children:(0,a.jsx)(E.default,{height:400,width:400,src:n.src,alt:"Exam Logo",priority:!0,quality:100})}),s&&C&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-xl max-w-md w-full",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-green-600 mb-4",children:"Application Successful!"}),(0,a.jsxs)("p",{className:"text-gray-700 mb-6",children:["You have successfully applied for"," ",(0,a.jsx)("strong",{children:C.exam_name}),"."]}),(0,a.jsx)(i.$,{onClick:em,className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg",children:"Close"})]})}),A&&C&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100",children:[(0,a.jsx)("h2",{className:"text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2",children:"Are You Sure?"}),(0,a.jsxs)("p",{className:"text-gray-700 text-lg mb-6 leading-relaxed",children:["Do you want to apply for"," ",(0,a.jsx)("strong",{className:"text-customOrange",children:C.exam_name}),"?",null!=C.coins_required&&(0,a.jsxs)("span",{children:[" ","This will cost"," ",(null==V?void 0:V.hasDiscount)?(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"line-through text-gray-500",children:C.coins_required})," ",(0,a.jsx)("strong",{className:"text-green-600",children:(0,R.w)(C.coins_required,V.discountPercentage)})," ",(0,a.jsxs)("span",{className:"text-green-600 text-sm",children:["(",V.discountPercentage,"% discount applied)"]})]}):(0,a.jsx)("strong",{className:"text-customOrange",children:C.coins_required})," ","coins."]})]}),U&&(0,a.jsxs)("div",{className:"flex-col justify-center items-start gap-2 bg-red-50 p-4 rounded-lg mb-6 border border-red-200",children:[(0,a.jsxs)("div",{className:"flex gap-5 items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600 mt-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-red-600 text-sm font-medium",children:U})]}),(0,a.jsx)(i.$,{onClick:()=>eg(),className:"mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:X,children:X?(0,a.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(p.A,{className:"animate-spin w-5 h-5"}),"Processing..."]}):"Add Coins"})]}),et()?(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(i.$,{onClick:ex,className:"w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:!!U||B,children:B?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Yes, Apply"}),(0,a.jsx)(i.$,{onClick:em,className:"w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg",children:"Cancel"})]}):(0,a.jsx)(i.$,{onClick:()=>en.push("/student/login?redirect=/uwhiz"),className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",children:"Login to Apply"})]})}),I?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"Loading exams..."}):(0,a.jsxs)(a.Fragment,{children:[ei.length>0&&(0,a.jsxs)(a.Fragment,{children:[ei.length>0&&(0,a.jsx)("div",{className:"flex justify-center items-center mt-10",children:(0,a.jsxs)("h1",{className:"text-3xl font-bold ml-4",children:["Upcoming ",(0,a.jsx)("span",{className:"text-customOrange",children:"Exams "})]})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center items-start gap-6 p-4 sm:p-6 md:p-10 lg:p-20",children:ei&&ei.map(e=>{var t,s,r,n,c;let o=null!==(s=e.total_student_intake)&&void 0!==s?s:0,d=null!==(r=null===(t=e.UwhizPriceRank.find(e=>1===e.rank))||void 0===t?void 0:t.price)&&void 0!==r?r:0;return(0,a.jsxs)(l.Zp,{className:"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center px-3 py-2 space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105",children:e.exam_name}),4!==e.id&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xl font-bold",children:[(0,a.jsx)(b.qjb,{className:"text-xl text-customOrange"}),(0,a.jsxs)("span",{className:"dark:text-white",children:["1st Prize: ",(0,a.jsx)("span",{className:"text-customOrange",children:d})]})]})]}),(0,a.jsx)(M,{exam:e}),(0,a.jsxs)("div",{className:"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.lfF,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Total Questions: ",e.total_questions]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.O6N,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Marks: ",e.marks]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.w_X,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Duration: ",e.duration]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(f,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Coins: ",null!=e.coins_required?(null==V?void 0:V.hasDiscount)?(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"line-through text-gray-500 text-xs",children:e.coins_required}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:(0,R.w)(e.coins_required,V.discountPercentage)}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",V.discountPercentage,"% off)"]})]}):e.coins_required:"Free"]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Student Joined"}),1===e.id&&(0,a.jsx)(k.k,{value:eh((null!==(n=e.totalApplicants)&&void 0!==n?n:0)+25,o),className:"[&>*]:bg-customOrange bg-slate-300"}),3===e.id&&(0,a.jsx)(k.k,{value:eh((null!==(c=e.totalApplicants)&&void 0!==c?c:0)+10,o),className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Limited Seats Available"})})]}),(0,a.jsxs)("div",{className:"flex justify-center px-3",children:[1===e.id&&(0,a.jsx)(i.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>eu(String(e.id)),children:"View details"}),(0,a.jsx)(z.A,{exam:e,hasApplied:e.hasApplied,isMaxLimitReached:e.isMaxLimitReached,hasAttempted:e.hasAttempted,onApplyClick:()=>eo(e)})]}),1===e.id&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-customOrange text-white font-semibold text-sm text-center p-3 flex items-center justify-center gap-2  border-orange-600 shadow-md ",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M12 20.5a8.5 8.5 0 100-17 8.5 8.5 0 000 17z"})}),(0,a.jsx)("span",{children:"Your Exam Just Got Rescheduled"})]})}),1===e.id?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-300 pt-2 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("span",{children:"Sponsored by"}),(0,a.jsx)(E.default,{src:"/nalanda.png",alt:"Nalanda Logo",height:60,width:60,className:"object-contain h-5 w-5"}),(0,a.jsx)("span",{className:"font-semibold",children:"Nalanda Vidhyalay"})]}):null]},e.id)})})]}),ec.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("h1",{className:"text-center text-4xl font-bold dark:text-white mt-10",children:["Past ",(0,a.jsx)("span",{className:"text-customOrange",children:"Exams"})]}),I?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"Loading exams..."}):0===ec.length?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"No past exams found."}):(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20",children:ec.map(e=>{var t,s;let r=null!==(s=null===(t=e.UwhizPriceRank.find(e=>1===e.rank))||void 0===t?void 0:t.price)&&void 0!==s?s:0;return(0,a.jsxs)(l.Zp,{className:"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center px-3 py-2 space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105",children:e.exam_name}),4!==e.id&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xl font-bold",children:[(0,a.jsx)(b.qjb,{className:"text-xl text-customOrange"}),(0,a.jsxs)("span",{className:"dark:text-white",children:["1st Prize: ",(0,a.jsx)("span",{className:"text-customOrange",children:r})]})]})]}),(0,a.jsx)(M,{exam:e}),(0,a.jsxs)("div",{className:"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.lfF,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Total Questions: ",e.total_questions]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.O6N,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Marks: ",e.marks]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.w_X,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Duration: ",e.duration]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(f,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Coins: ",null!=e.coins_required?(null==V?void 0:V.hasDiscount)?(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"line-through text-gray-500 text-xs",children:e.coins_required}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:(0,R.w)(e.coins_required,V.discountPercentage)}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",V.discountPercentage,"% off)"]})]}):e.coins_required:"Free"]})]})]})]}),5===e.id?(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Classes Joined"}),(0,a.jsx)(k.k,{value:100,className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Seats Full"})})]}):(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Student Joined"}),(0,a.jsx)(k.k,{value:100,className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Seats Full"})})]}),(0,a.jsxs)("div",{className:"flex justify-center px-3",children:[1===e.id&&(0,a.jsx)(i.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>eu(String(e.id)),children:"View details"}),3===e.id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.$,{className:"bg-customOrange mx-5",onClick:es,children:"View Result"}),(0,a.jsx)(P,{open:H,onClose:ea})]}),5===e.id?(0,a.jsx)(i.$,{className:"bg-customOrange mx-5",onClick:()=>en.push("/uwhiz-details/".concat(5)),children:"View Result"}):3!==e.id?(0,a.jsx)(z.A,{exam:e,hasApplied:e.hasApplied,isMaxLimitReached:e.isMaxLimitReached,hasAttempted:e.hasAttempted,onApplyClick:()=>eo(e)}):null]})]},e.id)})})]}),(0,a.jsx)("div",{className:"flex items-center justify-center px-4 py-6 dark:text-white",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>D(1),disabled:1===q,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(j.A,{})}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>D(e=>Math.max(e-1,1)),disabled:1===q,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(w.A,{})}),(0,a.jsxs)("span",{className:"text-sm",children:["Page ",q," of ",T]}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>D(e=>Math.min(e+1,T)),disabled:q===T,className:"hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(N.A,{})}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>D(T),disabled:q===T,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(y.A,{})})]})})]}),(0,a.jsx)(v.default,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6446,7672,9204,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1682,2823,347,8903,8441,1684,7358],()=>t(74454)),_N_E=e.O()}]);