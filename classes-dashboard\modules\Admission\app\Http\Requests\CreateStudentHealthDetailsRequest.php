<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentHealthDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'eye_sight' => 'required|string|max:200',
            "hear_ability"    => "required|string|max:200",
            "allergy_1"    => "required|string|max:200",
            "allergy_2"    => "required|string|max:200",
            "any_health_issue"    => "required|string|max:200",
            "doctors_name"    => "required|string|max:200",
        ];
    }
}
