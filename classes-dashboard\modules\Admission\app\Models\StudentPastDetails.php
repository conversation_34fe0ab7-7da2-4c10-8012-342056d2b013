<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentPastDetails extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_past_details';

    protected $fillable = [
        'student_id',
        'prev_standard',
        'prev_school',
        'prev_passing_year',
        'prev_school_left_date',
        'left_reason',
    ];
}
