"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[818],{13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},32944:(e,t,n)=>{n.d(t,{p:()=>o});var r=n(89447);function o(e,t){let n=(0,r.a)(e,null==t?void 0:t.in),o=n.getMonth();return n.setFullYear(n.getFullYear(),o+1,0),n.setHours(23,59,59,999),n}},64261:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(7239);function o(e){return(0,r.w)(e,Date.now())}},67140:(e,t,n)=>{n.d(t,{UC:()=>eF,ZL:()=>eT,bL:()=>eL,l9:()=>eD});var r,o=n(12115),i=n.t(o,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return o.useCallback(u(...e),e)}var d=n(95155);function c(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,s=o.useMemo(()=>a,Object.values(a));return(0,d.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var f=n(47650);function p(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){var i;let e,l;let a=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(s.ref=t?u(t,a):a),o.cloneElement(n,s)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),a=l.find(m);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,d.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,d.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var v=Symbol("radix.slottable");function m(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===v}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=p(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function y(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var g="dismissableLayer.update",E=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,b=o.useContext(E),[C,N]=o.useState(null),P=null!==(i=null==C?void 0:C.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,A]=o.useState({}),O=s(t,e=>N(e)),R=Array.from(b.layers),[S]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),j=R.indexOf(S),L=C?R.indexOf(C):-1,D=b.layersWithOutsidePointerEventsDisabled.size>0,T=L>=j,F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=y(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!T||n||(null==c||c(e),null==p||p(e),e.defaultPrevented||null==v||v())},P),_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=y(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},P);return!function(e,t=globalThis?.document){let n=y(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L===b.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},P),o.useEffect(()=>{if(C)return a&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(C)),b.layers.add(C),w(),()=>{a&&1===b.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[C,P,a,b]),o.useEffect(()=>()=>{C&&(b.layers.delete(C),b.layersWithOutsidePointerEventsDisabled.delete(C),w())},[C,b]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,d.jsx)(h.div,{...m,ref:O,style:{pointerEvents:D?T?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,_.onFocusCapture),onBlurCapture:l(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,F.onPointerDownCapture)})});function w(){let e=new CustomEvent(g);document.dispatchEvent(e)}function x(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&f.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(E),r=o.useRef(null),i=s(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,d.jsx)(h.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var C=0;function N(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",O={bubbles:!1,cancelable:!0},R=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...a}=e,[u,c]=o.useState(null),f=y(i),p=y(l),v=o.useRef(null),m=s(t,e=>c(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(g.paused||!u)return;let t=e.target;u.contains(t)?v.current=t:L(v.current,{select:!0})},t=function(e){if(g.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||L(v.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&L(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,g.paused]),o.useEffect(()=>{if(u){D.add(g);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(P,O);u.addEventListener(P,f),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(L(r,{select:t}),document.activeElement!==n)return}(S(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&L(u))}return()=>{u.removeEventListener(P,f),setTimeout(()=>{let t=new CustomEvent(A,O);u.addEventListener(A,p),u.dispatchEvent(t),t.defaultPrevented||L(null!=e?e:document.body,{select:!0}),u.removeEventListener(A,p),D.remove(g)},0)}}},[u,f,p,g]);let E=o.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=S(e);return[j(t,e),j(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&L(i,{select:!0})):(e.preventDefault(),n&&L(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,d.jsx)(h.div,{tabIndex:-1,...a,ref:m,onKeyDown:E})});function S(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function j(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function L(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}R.displayName="FocusScope";var D=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=T(e,t)).unshift(t)},remove(t){var n;null===(n=(e=T(e,t))[0])||void 0===n||n.resume()}}}();function T(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=globalThis?.document?o.useLayoutEffect:()=>{},_=i[" useId ".trim().toString()]||(()=>void 0),I=0,k=n(84945),M=n(22475),W=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,d.jsx)(h.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});W.displayName="Arrow";var U="Popper",[z,B]=c(U),[H,$]=z(U),K=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,d.jsx)(H,{scope:t,anchor:r,onAnchorChange:i,children:n})};K.displayName=U;var Y="PopperAnchor",V=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=$(Y,n),a=o.useRef(null),u=s(t,a);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,d.jsx)(h.div,{...i,ref:u})});V.displayName=Y;var q="PopperContent",[X,Z]=z(q),G=o.forwardRef((e,t)=>{var n,r,i,l,a,u,c,f;let{__scopePopper:p,side:v="bottom",sideOffset:m=0,align:g="center",alignOffset:E=0,arrowPadding:b=0,avoidCollisions:w=!0,collisionBoundary:x=[],collisionPadding:C=0,sticky:N="partial",hideWhenDetached:P=!1,updatePositionStrategy:A="optimized",onPlaced:O,...R}=e,S=$(q,p),[j,L]=o.useState(null),D=s(t,e=>L(e)),[T,_]=o.useState(null),I=function(e){let[t,n]=o.useState(void 0);return F(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(T),W=null!==(c=null==I?void 0:I.width)&&void 0!==c?c:0,U=null!==(f=null==I?void 0:I.height)&&void 0!==f?f:0,z="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(x)?x:[x],H=B.length>0,K={padding:z,boundary:B.filter(et),altBoundary:H},{refs:Y,floatingStyles:V,placement:Z,isPositioned:G,middlewareData:J}=(0,k.we)({strategy:"fixed",placement:v+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,M.ll)(...t,{animationFrame:"always"===A})},elements:{reference:S.anchor},middleware:[(0,k.cY)({mainAxis:m+U,alignmentAxis:E}),w&&(0,k.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===N?(0,k.ER)():void 0,...K}),w&&(0,k.UU)({...K}),(0,k.Ej)({...K,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),T&&(0,k.UE)({element:T,padding:b}),en({arrowWidth:W,arrowHeight:U}),P&&(0,k.jD)({strategy:"referenceHidden",...K})]}),[Q,ee]=er(Z),eo=y(O);F(()=>{G&&(null==eo||eo())},[G,eo]);let ei=null===(n=J.arrow)||void 0===n?void 0:n.x,el=null===(r=J.arrow)||void 0===r?void 0:r.y,ea=(null===(i=J.arrow)||void 0===i?void 0:i.centerOffset)!==0,[eu,es]=o.useState();return F(()=>{j&&es(window.getComputedStyle(j).zIndex)},[j]),(0,d.jsx)("div",{ref:Y.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:G?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null===(l=J.transformOrigin)||void 0===l?void 0:l.x,null===(a=J.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(u=J.hide)||void 0===u?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(X,{scope:p,placedSide:Q,onArrowChange:_,arrowX:ei,arrowY:el,shouldHideArrow:ea,children:(0,d.jsx)(h.div,{"data-side":Q,"data-align":ee,...R,ref:D,style:{...R.style,animation:G?void 0:"none"}})})})});G.displayName=q;var J="PopperArrow",Q={top:"bottom",right:"left",bottom:"top",left:"right"},ee=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=Z(J,n),i=Q[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(W,{...r,ref:t,style:{...r.style,display:"block"}})})});function et(e){return null!==e}ee.displayName=J;var en=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,d=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=er(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(i=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+c/2,y=(null!==(l=null===(o=s.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,g="",E="";return"bottom"===p?(g=d?m:"".concat(h,"px"),E="".concat(-f,"px")):"top"===p?(g=d?m:"".concat(h,"px"),E="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),E=d?m:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),E=d?m:"".concat(y,"px")),{data:{x:g,y:E}}}});function er(e){let[t,n="center"]=e.split("-");return[t,n]}var eo=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);F(()=>u(!0),[]);let s=i||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?f.createPortal((0,d.jsx)(h.div,{...l,ref:t}),s):null});eo.displayName="Portal";var ei=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef({}),a=o.useRef(e),u=o.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=el(l.current);u.current="mounted"===s?e:"none"},[s]),F(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=el(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),F(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=el(l.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=el(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=s(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function el(e){return(null==e?void 0:e.animationName)||"none"}ei.displayName="Presence";var ea=n(38168),eu=n(31114),es="Popover",[ed,ec]=c(es,[B]),ef=B(),[ep,ev]=ed(es),em=e=>{let{__scopePopover:t,children:n,open:r,defaultOpen:i,onOpenChange:l,modal:a=!1}=e,u=ef(t),s=o.useRef(null),[c,f]=o.useState(!1),[p=!1,v]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,i]=function({defaultProp:e,onChange:t}){let n=o.useState(e),[r]=n,i=o.useRef(r),l=y(t);return o.useEffect(()=>{i.current!==r&&(l(r),i.current=r)},[r,i,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,a=l?e:r,u=y(n);return[a,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else i(t)},[l,e,i,u])]}({prop:r,defaultProp:i,onChange:l});return(0,d.jsx)(K,{...u,children:(0,d.jsx)(ep,{scope:t,contentId:function(e){let[t,n]=o.useState(_());return F(()=>{n(e=>e??String(I++))},[void 0]),e||(t?`radix-${t}`:"")}(),triggerRef:s,open:p,onOpenChange:v,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:c,onCustomAnchorAdd:o.useCallback(()=>f(!0),[]),onCustomAnchorRemove:o.useCallback(()=>f(!1),[]),modal:a,children:n})})};em.displayName=es;var eh="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=ev(eh,n),l=ef(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:u}=i;return o.useEffect(()=>(a(),()=>u()),[a,u]),(0,d.jsx)(V,{...l,...r,ref:t})}).displayName=eh;var ey="PopoverTrigger",eg=o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=ev(ey,n),i=ef(n),a=s(t,o.triggerRef),u=(0,d.jsx)(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ej(o.open),...r,ref:a,onClick:l(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?u:(0,d.jsx)(V,{asChild:!0,...i,children:u})});eg.displayName=ey;var eE="PopoverPortal",[eb,ew]=ed(eE,{forceMount:void 0}),ex=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,i=ev(eE,t);return(0,d.jsx)(eb,{scope:t,forceMount:n,children:(0,d.jsx)(ei,{present:n||i.open,children:(0,d.jsx)(eo,{asChild:!0,container:o,children:r})})})};ex.displayName=eE;var eC="PopoverContent",eN=o.forwardRef((e,t)=>{let n=ew(eC,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,i=ev(eC,e.__scopePopover);return(0,d.jsx)(ei,{present:r||i.open,children:i.modal?(0,d.jsx)(eA,{...o,ref:t}):(0,d.jsx)(eO,{...o,ref:t})})});eN.displayName=eC;var eP=p("PopoverContent.RemoveScroll"),eA=o.forwardRef((e,t)=>{let n=ev(eC,e.__scopePopover),r=o.useRef(null),i=s(t,r),a=o.useRef(!1);return o.useEffect(()=>{let e=r.current;if(e)return(0,ea.Eq)(e)},[]),(0,d.jsx)(eu.A,{as:eP,allowPinchZoom:!0,children:(0,d.jsx)(eR,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),a.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;a.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),eO=o.forwardRef((e,t)=>{let n=ev(eC,e.__scopePopover),r=o.useRef(!1),i=o.useRef(!1);return(0,d.jsx)(eR,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eR=o.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:s,onFocusOutside:c,onInteractOutside:f,...p}=e,v=ev(eC,n),m=ef(n);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:N()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:N()),C++,()=>{1===C&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),C--}},[]),(0,d.jsx)(R,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,d.jsx)(b,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:u,onPointerDownOutside:s,onFocusOutside:c,onDismiss:()=>v.onOpenChange(!1),children:(0,d.jsx)(G,{"data-state":ej(v.open),role:"dialog",id:v.contentId,...m,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),eS="PopoverClose";function ej(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=ev(eS,n);return(0,d.jsx)(h.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})}).displayName=eS,o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=ef(n);return(0,d.jsx)(ee,{...o,...r,ref:t})}).displayName="PopoverArrow";var eL=em,eD=eg,eT=ex,eF=eN}}]);