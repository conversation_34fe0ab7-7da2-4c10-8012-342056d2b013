@extends('layouts.app')
@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Thoughts</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('create thoughts')
                            <a id="addThoughtsEntry" data-toggle="modal" data-target="#newThoughtsEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New Thoughts</a>
                            @endcan
                        </div>
                        <table id="thoughts_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Thoughts</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Thoughts</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newThoughtsEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script>
     var thoughtsRoute = {
        index: "{{ route('thoughts.index') }}",
        create: "{{ route('thoughts.create') }}",
        delete: "{{ route('thoughts.destroy',':did') }}",
        edit: "{{ route('thoughts.edit',':editid') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/Thoughts/index.js')) }}"></script>
@endsection