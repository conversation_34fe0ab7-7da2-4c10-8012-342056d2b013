(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6976],{4258:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(95155),a=r(70347),l=r(7583),i=r(12115),n=r(30285),o=r(55594),d=r(62177),c=r(90221),m=r(62523),u=r(55365),x=r(85339),h=r(51154),f=r(75937),g=r(56671),v=r(60723),p=r(35695);let j=o.z.object({otp:o.z.string().regex(/^\d{6}$/,"OTP must be a 6-digit number"),contactNo:o.z.string().regex(/^\d{10}$/,"Invalid mobile number"),email:o.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address"),firstName:o.z.string().optional().refine(e=>!e||/^[a-zA-Z]+$/.test(e),"Invalid first name"),lastName:o.z.string().optional().refine(e=>!e||/^[a-zA-Z]+$/.test(e),"Invalid last name"),referralCode:o.z.string().optional()}),b=e=>{let{message:t}=e;return t?(0,s.jsxs)(u.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)(u.TN,{className:"text-red-500",children:t})]}):null};function N(){let e=(0,p.useRouter)(),t=(0,p.useSearchParams)(),[r,o]=(0,i.useState)(!1),[u,x]=(0,i.useState)(""),[N,w]=(0,i.useState)(!1),[y,I]=(0,i.useState)(120),T=t.get("contactNo")||"",k=t.get("email")||"",P=t.get("flow")||"login",S=t.get("firstName")||"",C=t.get("lastName")||"",R=t.get("referralCode")||"",O="register"===P,F="otpTimer_".concat(T,"_").concat(P),z=(0,d.mN)({resolver:(0,c.u)(j),defaultValues:{otp:"",contactNo:T,email:k,firstName:S,lastName:C,referralCode:R},mode:"onChange"}),{formState:A,trigger:_,setValue:E}=z;(0,i.useEffect)(()=>{if(!T||!/^\d{10}$/.test(T)){x("Invalid mobile number. Please try again."),g.oR.error("Invalid mobile number. Redirecting to login."),setTimeout(()=>e.push("/student-login"),2e3);return}E("contactNo",T,{shouldValidate:!0}),E("email",k,{shouldValidate:!0}),E("firstName",S,{shouldValidate:!0}),E("lastName",C,{shouldValidate:!0}),E("referralCode",R,{shouldValidate:!0}),_(),localStorage.removeItem(F),I(120)},[T,k,S,C,R,e,E,_,F]),(0,i.useEffect)(()=>{let e=null;return y>0&&(e=setInterval(()=>{I(t=>{let r=t-1;return r<=0?(localStorage.removeItem(F),clearInterval(e),0):(localStorage.setItem(F,r.toString()),r)})},1e3)),()=>{e&&clearInterval(e)}},[F,y]);let V=async r=>{o(!0),x("");try{let s=await (0,v.RY)({contactNo:r.contactNo,otp:r.otp,...r.email&&{email:r.email},...O&&{firstName:r.firstName,lastName:r.lastName,referralCode:r.referralCode}});if(!1===s.success){x(s.message||"OTP verification failed"),g.oR.error(s.message||"OTP verification failed");return}if(s.data){let{userId:a,contactNo:l,firstName:i,lastName:n,token:o}=s.data;localStorage.setItem("studentToken",o),localStorage.setItem("student_data",JSON.stringify({id:a,contactNo:l,firstName:i,lastName:n})),localStorage.removeItem(F),g.oR.success(r.email&&!O?"Contact number updated and login successful":O?"Registration successful":"Login successful");let d=t.get("redirect")||"/";e.push(d)}}catch(t){var s,a;let e=(null==t?void 0:null===(a=t.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||"Something went wrong";x(e),g.oR.error(e)}finally{o(!1)}},B=async()=>{w(!0),x("");try{let e=await (0,v.Ty)({contactNo:T,firstName:S||"User"});if(!1===e.success){x(e.message||"Failed to resend OTP"),g.oR.error(e.message||"Failed to resend OTP");return}I(120),localStorage.setItem(F,"120"),g.oR.success("New OTP sent successfully")}catch(s){var e,t;let r=(null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||"Something went wrong";r.includes("Too many OTP requests")?g.oR.error("Too many OTP requests. Please wait before trying again."):g.oR.error(r),x(r)}finally{w(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Verify OTP"}),(0,s.jsxs)("p",{className:"text-[#ff914d]",children:["Enter the 6-digit OTP sent to ",T]}),k&&(0,s.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Linked to email: ",k]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(f.lV,{...z,children:(0,s.jsxs)("form",{onSubmit:z.handleSubmit(V),className:"space-y-6",children:[u&&(0,s.jsx)(b,{message:u}),(0,s.jsx)(f.zB,{control:z.control,name:"otp",render:e=>{let{field:t}=e;return(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-gray-700 font-medium",children:"OTP"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)("div",{className:"flex space-x-2 justify-center",children:[...Array(6)].map((e,r)=>(0,s.jsx)(m.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-lg font-medium border-gray-200 focus:border-[#ff914d] focus:ring-[#ff914d]/20 rounded-lg",value:t.value[r]||"",onChange:e=>{let s=(t.value||"").split("");if(s[r]=e.target.value.replace(/\D/g,""),t.onChange(s.join("")),_("otp"),e.target.value&&r<5){var a;null===(a=document.getElementById("otp-".concat(r+1)))||void 0===a||a.focus()}},onKeyDown:e=>{if("Backspace"===e.key&&!t.value[r]&&r>0){var s;null===(s=document.getElementById("otp-".concat(r-1)))||void 0===s||s.focus()}},id:"otp-".concat(r)},r))})}),(0,s.jsx)(f.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(f.zB,{control:z.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,s.jsx)(f.eI,{hidden:!0,children:(0,s.jsx)(f.MJ,{children:(0,s.jsx)(m.p,{...t})})})}}),(0,s.jsx)(f.zB,{control:z.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsx)(f.eI,{hidden:!0,children:(0,s.jsx)(f.MJ,{children:(0,s.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,s.jsx)(f.zB,{control:z.control,name:"firstName",render:e=>{let{field:t}=e;return(0,s.jsx)(f.eI,{hidden:!0,children:(0,s.jsx)(f.MJ,{children:(0,s.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,s.jsx)(f.zB,{control:z.control,name:"lastName",render:e=>{let{field:t}=e;return(0,s.jsx)(f.eI,{hidden:!0,children:(0,s.jsx)(f.MJ,{children:(0,s.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,s.jsx)(f.zB,{control:z.control,name:"referralCode",render:e=>{let{field:t}=e;return(0,s.jsx)(f.eI,{hidden:!0,children:(0,s.jsx)(f.MJ,{children:(0,s.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,s.jsx)(n.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:r||!A.isValid,children:r?(0,s.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):"Verify OTP"}),(0,s.jsx)(n.$,{type:"button",variant:"ghost",className:"w-full text-[#ff914d] hover:text-[#ff914d]/90",disabled:N||y>0,onClick:B,children:N?(0,s.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):y>0?"Resend OTP in ".concat(y,"s"):"Resend OTP"})]})})})]})}),(0,s.jsx)(l.default,{})]})}function w(){return(0,s.jsx)(i.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}),children:(0,s.jsx)(N,{})})}},7583:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(95155);r(12115);var a=r(6874),l=r.n(a),i=r(66766),n=r(29911);let o=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:r,label:a}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,s.jsx)(r,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},42262:(e,t,r)=>{Promise.resolve().then(r.bind(r,4258))},55365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>d});var s=r(95155),a=r(12115),l=r(74466),i=r(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,...l}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(n({variant:a}),r),...l})});o.displayName="Alert",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})}).displayName="AlertTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",r),...a})});d.displayName="AlertDescription"},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(95155);r(12115);var a=r(59434);function l(e){let{className:t,type:r,...l}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},75937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>c,MJ:()=>v,Rr:()=>p,zB:()=>u,eI:()=>f,lR:()=>g,C5:()=>j});var s=r(95155),a=r(12115),l=r(66634),i=r(62177),n=r(59434),o=r(24265);function d(e){let{className:t,...r}=e;return(0,s.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}let c=i.Op,m=a.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(m.Provider,{value:{name:t.name},children:(0,s.jsx)(i.xI,{...t})})},x=()=>{let e=a.useContext(m),t=a.useContext(h),{getFieldState:r}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),l=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},h=a.createContext({});function f(e){let{className:t,...r}=e,l=a.useId();return(0,s.jsx)(h.Provider,{value:{id:l},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",t),...r})})}function g(e){let{className:t,...r}=e,{error:a,formItemId:l}=x();return(0,s.jsx)(d,{"data-slot":"form-label","data-error":!!a,className:(0,n.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...r})}function v(e){let{...t}=e,{error:r,formItemId:a,formDescriptionId:i,formMessageId:n}=x();return(0,s.jsx)(l.DX,{"data-slot":"form-control",id:a,"aria-describedby":r?"".concat(i," ").concat(n):"".concat(i),"aria-invalid":!!r,...t})}function p(e){let{className:t,...r}=e,{formDescriptionId:a}=x();return(0,s.jsx)("p",{"data-slot":"form-description",id:a,className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function j(e){var t;let{className:r,...a}=e,{error:l,formMessageId:i}=x(),o=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):a.children;return o?(0,s.jsx)("p",{"data-slot":"form-message",id:i,className:(0,n.cn)("text-destructive text-sm",r),...a,children:o}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1342,6919,347,8441,1684,7358],()=>t(42262)),_N_E=e.O()}]);