(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3980],{18717:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(95155),l=a(12115),n=a(59434),r=a(86214),i=a(35695),d=a(70347),o=a(7583),c=a(19320),x=a(66766);let m=e=>{let{totalCoins:t,badgeSrc:a,badgeAlt:l}=e;return null===t?null:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm",children:[(0,s.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:20,height:20,className:"w-5 h-5"}),t," Coins"]}),a&&l&&(0,s.jsx)(c.P.div,{animate:{scale:[1,1.05,1]},transition:{repeat:1/0,duration:1.5,ease:"easeInOut"},className:"w-12 h-12 flex items-center justify-center",children:(0,s.jsx)(x.default,{src:a,alt:l,width:48,height:48,className:"object-contain"})})]})};var u=a(95811);let g=function(){var e,t,a,g;let h=(0,i.useParams)().studentId,[v,f]=(0,l.useState)(null),[b,y]=(0,l.useState)(!1),[p,j]=(0,l.useState)(null),[w,N]=(0,l.useState)(null),[k,S]=(0,l.useState)(null),[P,C]=(0,l.useState)(null),[D,E]=(0,l.useState)(1);(0,l.useEffect)(()=>{(async()=>{if(!h){j("Student ID is required"),y(!1);return}y(!0),j(null);try{var e;let t=await (0,r.S)(h,D,10);if(t.success&&(null===(e=t.data)||void 0===e?void 0:e.data)){f(t.data.data);let{mockExamResults:e}=t.data.data,a=e.reduce((e,t)=>e+(t.coinEarnings||0),0);N(a);let s=null,l=null;a>=100&&a<=499?(s="/scholer.svg",l="Scholar Badge"):a>=500&&a<=999?(s="/Mastermind.svg",l="Mastermind Badge"):a>=1e3&&(s="/Achiever.svg",l="Achiever Badge"),S(s),C(l)}else j(t.error||"Failed to fetch mock exam results")}catch(e){j(e.message||"An unexpected error occurred")}finally{y(!1)}})()},[h,D]);let _=e=>new Date(e).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit",year:"numeric"}).split("/").join("-");return b?(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center text-xl font-medium text-gray-700 animate-pulse",children:"Loading daily quiz results..."})}):p?(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center text-xl font-medium text-red-600 bg-red-50 p-4 rounded-lg shadow-md",children:["Error: ",p]})}):v?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.default,{}),(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen py-16 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,s.jsx)("h2",{className:"text-4xl font-extrabold text-gray-800 text-center mb-10 tracking-tight",children:"Daily Quiz Results"}),(0,s.jsx)(c.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"mb-10 rounded-3xl p-[2px] bg-gradient-to-tr from-orange-400 via-yellow-400 to-amber-500",children:(0,s.jsx)("div",{className:"rounded-3xl bg-white/80 backdrop-blur-md p-6 md:p-8 shadow-xl",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-start justify-between gap-6 relative",children:[(0,s.jsx)(c.P.div,{animate:{scale:[1,1.1,1],rotate:[0,1,-1,0]},transition:{repeat:1/0,duration:2,ease:"easeInOut"},className:"w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-inner ring-2 ring-amber-200",children:(0,s.jsx)("span",{className:"text-3xl",children:"\uD83D\uDD25"})}),(0,s.jsxs)("div",{className:"text-center sm:text-left flex-1",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-neutral-800 mb-1 tracking-tight",children:"Daily Streak"}),(0,s.jsxs)("p",{className:"text-4xl font-extrabold text-amber-600 tracking-wider",children:[v.streak.streakCount," ",1===v.streak.streakCount?"Day":"Days"]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1 italic",children:"Stay consistent and keep growing"}),(0,s.jsx)(u.A,{badge:v.badge})]}),(0,s.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,s.jsx)("div",{className:"px-4 py-2 bg-white rounded-full shadow ring-1 ring-amber-300 text-sm font-medium text-amber-600",children:"Consistency Reward"}),(0,s.jsx)(m,{totalCoins:w,badgeSrc:k,badgeAlt:P})]})]})})}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("div",{className:"space-y-4",children:0===v.mockExamResults.length?(0,s.jsx)("div",{className:"text-center text-gray-500 text-lg font-medium py-8",children:"No daily quiz results found."}):v.mockExamResults.map((e,t)=>(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-5 rounded-xl transition-all duration-200",0===t?"bg-orange-50 border-l-4 border-orange-500":"bg-gray-50 hover:bg-gray-100 hover:shadow-md"),children:[(0,s.jsxs)("div",{className:"flex items-center gap-5",children:[(0,s.jsxs)("div",{className:(0,n.cn)("w-12 h-12 flex items-center justify-center rounded-full font-semibold text-sm shadow-sm",0===t?"bg-orange-500 text-white":"bg-gray-200 text-gray-700"),children:["#",(D-1)*10+t+1]}),(0,s.jsx)("div",{children:(0,s.jsx)("p",{className:(0,n.cn)("font-semibold",0===t?"text-lg text-gray-800":"text-base text-gray-700"),children:_(e.createdAt)})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[e.coinEarnings>=0&&(0,s.jsxs)("div",{className:"flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm",children:[(0,s.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:20,height:20,className:"w-5 h-5"}),e.coinEarnings]}),(0,s.jsxs)("div",{className:(0,n.cn)("font-semibold text-orange-600 bg-orange-100 px-4 py-1.5 rounded-full shadow-sm",0===t?"text-base":"text-sm"),children:[e.score,"/10"]})]})]},e.id))}),(null==v?void 0:null===(e=v.pagination)||void 0===e?void 0:e.totalPages)>1&&(0,s.jsxs)("div",{className:"flex justify-center gap-6 mt-8",children:[(0,s.jsx)("button",{onClick:()=>{D>1&&E(D-1)},disabled:1===D,className:(0,n.cn)("bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",1===D?"opacity-50 cursor-not-allowed":"hover:bg-orange-600 hover:scale-105"),children:"Previous"}),(0,s.jsxs)("span",{className:"self-center text-gray-600 font-medium",children:["Page ",D," of ",(null==v?void 0:null===(t=v.pagination)||void 0===t?void 0:t.totalPages)||1]}),(0,s.jsx)("button",{onClick:()=>{(null==v?void 0:v.pagination)&&D<v.pagination.totalPages&&E(D+1)},disabled:D===((null==v?void 0:null===(a=v.pagination)||void 0===a?void 0:a.totalPages)||1),className:(0,n.cn)("bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",D===((null==v?void 0:null===(g=v.pagination)||void 0===g?void 0:g.totalPages)||1)?"opacity-50 cursor-not-allowed":"hover:bg-orange-600 hover:scale-105"),children:"Next"})]})]})]})}),(0,s.jsx)(o.default,{})]}):(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center text-xl font-medium text-gray-700",children:"No data available"})})}},50240:(e,t,a)=>{Promise.resolve().then(a.bind(a,18717))},86214:(e,t,a)=>{"use strict";a.d(t,{S:()=>n,q:()=>l});var s=a(55077);let l=async e=>{try{let t=await s.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam result: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}},n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let l=await s.S.get("/mock-exam-result/".concat(e,"?page=").concat(t,"&limit=").concat(a),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:l.data}}catch(e){var l,n;return{success:!1,error:"Failed to get mock exam result: ".concat((null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message)||e.message)}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,347,6764,8441,1684,7358],()=>t(50240)),_N_E=e.O()}]);