(()=>{var e={};e.id=523,e.ids=[523],e.modules={1769:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\coins\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},49463:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),a=s(43210),n=s(96834),i=s(29523),o=s(44493),d=s(79663),l=s(28527),c=s(54864),u=s(90269),p=s(46303),m=s(30474),x=s(85726),g=s(3589),h=s(78272),f=s(78122),v=s(80462),b=s(52581),y=s(89667),j=s(4780),w=s(45880),N=s(27605),k=s(63442);let _=w.z.object({amount:w.z.string().min(1,"Amount is required").refine(e=>{let t=parseInt(e,10);return!isNaN(t)&&t>=1&&t<=1e4},{message:"Amount must be between ₹1 and ₹10,000"})}),A=()=>{let[e,t]=(0,a.useState)(0),[s,w]=(0,a.useState)([]),[A,C]=(0,a.useState)(!0),[E,S]=(0,a.useState)("all"),[q,z]=(0,a.useState)("desc"),{user:P}=(0,c.d4)(e=>e.user),[T,G]=(0,a.useState)(!1),{register:R,handleSubmit:D,formState:{errors:M,isSubmitting:U},reset:O}=(0,N.mN)({resolver:(0,k.u)(_)}),I=async e=>{await $(parseInt(e.amount))},$=async e=>{let t=null!==localStorage.getItem("studentToken"),s=P?.role==="STUDENT"||t;G(!0);try{let{order:t}=(await l.S.post(s?"/coins/create-order":"/coins/create-order/class",{amount:100*e})).data,r={key:"rzp_test_Opr6M8CKpK12pF",amount:t.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:t.id,prefill:{name:P?.firstName+" "+P?.lastName,email:P?.email},handler:async function(t){try{await l.S.post(s?"/coins/verify":"/coins/verify/class",{razorpay_order_id:t.razorpay_order_id,razorpay_payment_id:t.razorpay_payment_id,razorpay_signature:t.razorpay_signature,amount:100*e}),b.oR.success("Coins added successfully!"),L(),O()}catch{b.oR.error("Payment verification failed")}},theme:{color:"#f97316"}};new window.Razorpay(r).open()}catch{b.oR.error("Payment initialization failed")}finally{G(!1)}};(0,a.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]);let L=(0,a.useCallback)(async()=>{C(!0);try{let e=null!==localStorage.getItem("studentToken"),s=P?.role==="STUDENT"||e,r=await l.S.get(s?"/coins/get-total-coins/student":"/coins/get-total-coins"),a=await l.S.get(s?"/coins/transaction-history/student":"/coins/transaction-history");t(r.data.coins),w(a.data.transactions)}catch(e){b.oR.error("Failed to load coin data. Please try again."),console.error("Error fetching data",e)}finally{C(!1)}},[P?.role]);(0,a.useEffect)(()=>{L()},[P?.id,L]),(0,a.useEffect)(()=>{let e=()=>{L()};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[L]);let Z=s.filter(e=>"all"===E||e.type.toLowerCase()===E).sort((e,t)=>{let s=new Date(e.createdAt).getTime(),r=new Date(t.createdAt).getTime();return"desc"===q?r-s:s-r}),F=({txn:e})=>{let[t,s]=(0,a.useState)(!1);return(0,r.jsxs)(o.Zp,{className:"p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200",onClick:()=>s(!t),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("h3",{className:"text-base font-semibold text-foreground",children:"CREDIT"===e.type?(0,r.jsx)("span",{className:"text-green-500",children:"Credit"}):(0,r.jsx)("span",{className:"text-red-500",children:"Debit"})}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.amount," Coins • ",(0,d.GP)(new Date(e.createdAt),"MMM dd, yyyy")]})]}),t?(0,r.jsx)(g.A,{className:"h-5 w-5 text-muted-foreground"}):(0,r.jsx)(h.A,{className:"h-5 w-5 text-muted-foreground"})]}),t&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Reason:"})," ",e.reason]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Time:"})," ",(0,d.GP)(new Date(e.createdAt),"p")]})]})]})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.default,{}),(0,r.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8 py-12 max-w-7xl mx-auto space-y-6",children:[(0,r.jsx)("div",{className:"sticky top-16 z-10 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60 py-4",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 rounded-full bg-orange-100 p-2",children:(0,r.jsx)(m.default,{src:"/uest_coin.png",alt:"Coin Icon",fill:!0,className:"object-contain"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:["Class"," Uest Coin Balance"]}),(0,r.jsxs)(n.E,{variant:"outline",className:"text-customOrange text-xl font-semibold border-customOrange mt-1",children:[e," Coins"]})]})]}),(0,r.jsxs)(i.$,{variant:"outline",onClick:L,disabled:A,className:"flex gap-2 w-full sm:w-auto",children:[(0,r.jsx)(f.A,{className:`h-4 w-4 ${A?"animate-spin":""}`}),"Refresh"]})]})}),(0,r.jsx)("form",{onSubmit:D(I),children:(0,r.jsxs)(o.Zp,{className:"p-6 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-foreground mb-4",children:"Add Uest Coins"}),(0,r.jsx)("div",{className:"grid gap-4 sm:grid-cols-2",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y.p,{type:"number",placeholder:"Enter amount (₹1 - ₹10,000)",className:"border-customOrange/30 focus:border-customOrange pr-10",disabled:T||U,...R("amount")}),(0,r.jsx)("span",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm",children:"₹"}),M.amount&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:M.amount.message})]}),(0,r.jsxs)(i.$,{type:"submit",disabled:T||U,className:(0,j.cn)("w-full bg-customOrange hover:bg-orange-600 text-white",(T||U)&&"opacity-75 cursor-not-allowed"),children:[(T||U)&&(0,r.jsx)(f.A,{className:"h-4 w-4 animate-spin mr-2"}),T||U?"Processing...":"Add Coins"]})]})})]})}),A?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(x.E,{className:"h-20 w-full rounded-lg"}),[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(x.E,{className:"h-16 w-full rounded-lg"},t))]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-center",children:[(0,r.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,r.jsx)(i.$,{variant:"all"===E?"default":"outline",size:"sm",onClick:()=>S("all"),className:"all"===E?"bg-customOrange hover:bg-orange-600":"",children:"All"}),(0,r.jsx)(i.$,{variant:"credit"===E?"default":"outline",size:"sm",onClick:()=>S("credit"),className:"credit"===E?"bg-customOrange hover:bg-orange-600":"",children:"Credit"}),(0,r.jsx)(i.$,{variant:"debit"===E?"default":"outline",size:"sm",onClick:()=>S("debit"),className:"debit"===E?"bg-customOrange hover:bg-orange-600":"",children:"Debit"})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>z("desc"===q?"asc":"desc"),className:"flex gap-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"Sort by Date ","desc"===q?"↓":"↑"]})]}),Z.length>0?(0,r.jsx)("div",{className:"grid gap-4",children:Z.map(e=>(0,r.jsx)(F,{txn:e},e.id))}):(0,r.jsxs)(o.Zp,{className:"p-8 bg-white rounded-lg shadow-sm border text-center",children:[(0,r.jsx)("div",{className:"relative w-24 h-24 mx-auto mb-4 opacity-50",children:(0,r.jsx)(m.default,{src:"/uest_coin.png",alt:"No Transactions",fill:!0,className:"object-contain"})}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"No transactions found. Start earning or spending coins!"}),(0,r.jsx)(i.$,{variant:"outline",className:"bg-orange-50 text-customOrange hover:bg-orange-100",onClick:L,children:"Refresh Data"})]})]})]}),(0,r.jsx)(p.default,{})]})}},51139:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let l={children:["",{children:["coins",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1769)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/coins/page",pathname:"/coins",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76133:(e,t,s)=>{Promise.resolve().then(s.bind(s,1769))},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78272:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(60687),a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",e),...t})}},89285:(e,t,s)=>{Promise.resolve().then(s.bind(s,49463))},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(60687);s(43210);var a=s(11329),n=s(24224),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...n}){let d=s?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,2105,9663,2800],()=>s(51139));module.exports=r})();