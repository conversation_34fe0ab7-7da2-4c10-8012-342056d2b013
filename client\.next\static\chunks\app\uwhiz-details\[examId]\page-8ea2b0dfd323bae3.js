(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8712],{7583:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var s=a(95155);a(12115);var i=a(6874),r=a.n(i),l=a(66766),n=a(29911);let c=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(r(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(l.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:i}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(r(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:i,children:(0,s.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},i)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(r(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(l.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(r(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(r(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},33821:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var s=a(95155),i=a(70347),r=a(15305),l=a(51013),n=a(30285),c=a(7509),o=a(12115),d=a(56671),x=a(46523),m=a(55077);async function h(e){try{return(await m.S.get("/ranking/".concat(e))).data}catch(e){var t,a;return{success:!1,error:"Failed to fetch rankings: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}}let g=async(e,t)=>{try{return(await m.S.get("/certificate/exam-participants?examId=".concat(e,"&classId=").concat(t))).data}catch(e){var a,s;return{success:!1,error:"Failed to get participants: ".concat((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||e.message)}}};async function u(e,t){try{return(await m.S.get("/ranking/".concat(e,"/").concat(t))).data}catch(e){var a,s;return{success:!1,error:"Failed to fetch rankings: ".concat((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||e.message)}}}var f=a(35695),p=a(26597),v=a(34540),j=a(93588),b=a(64315),y=a(19320),N=a(7583),w=a(66766);let k=()=>{var e;let{examId:t}=(0,f.useParams)(),[a,m]=(0,o.useState)(null),[k,S]=(0,o.useState)(!1),[F,E]=(0,o.useState)([]),[P,C]=(0,o.useState)(0),[L,I]=(0,o.useState)(2),[R,T]=(0,o.useState)(""),_=(0,o.useRef)(null),O=(null===(e=(0,v.d4)(e=>e.user).user)||void 0===e?void 0:e.id)||"";(0,o.useEffect)(()=>{let e=async()=>{S(!0);try{var e;let a=await (0,x.LP)(Number(t));if(!1===a.success)throw Error(a.error);m(a);let s=await u(Number(t),O);C(s.rank);let i=await h(Number(t));E(i);let r=await g(Number(t),O);if(r.success&&(null===(e=r.data)||void 0===e?void 0:e.length)){let e=r.data[0];T("".concat(e.firstName," ").concat(e.lastName))}}catch(e){console.error("Error fetching data:",e),d.oR.error(e.message||"Failed to load data")}finally{S(!1)}};t&&e()},[t,O]);let D=(e,t,a)=>{let s=_.current;if(!s){d.oR.error("Canvas not available");return}let i=s.getContext("2d",{alpha:!1});if(!i){d.oR.error("Failed to get canvas context");return}let r=document.createElement("img");r.src=e,r.onload=async()=>{try{await document.fonts.ready;s.width=2245.3199999999997,s.height=1587.6,i.scale(2,2),i.imageSmoothingEnabled=!0,i.imageSmoothingQuality="high",i.clearRect(0,0,s.width,s.height),i.drawImage(r,0,0,s.width/2,s.height/2),i.textRendering="geometricPrecision",i.fontKerning="normal",i.textBaseline="middle",i.font="48px Balsamiq Sans",i.textAlign="center",i.strokeStyle="rgba(0, 0, 0, 0.4)",i.lineWidth=3,i.strokeText(t,560,458),i.fillStyle="#FFFFFF",i.fillText(t,560,458),null!=a&&(i.font="22px Balsamiq Sans",i.strokeText(a.toString(),359,503),i.fillText("#".concat(a),359,503));let e=new p.uE("landscape","mm","a4"),l=s.toDataURL("image/jpeg",1);e.addImage(l,"JPEG",0,0,e.internal.pageSize.width,e.internal.pageSize.height,void 0,"NONE"),e.save("UWHIZ_Certificate_".concat(t,".pdf"))}catch(e){console.error("Error generating certificate:",e),d.oR.error("Failed to generate certificate")}},r.onerror=()=>{console.error("Failed to load image: ".concat(e)),d.oR.error("Failed to load certificate image")}};if(k)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100",children:(0,s.jsx)(y.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-lg font-semibold text-gray-600",children:"Loading exam details..."})});if(!a)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100",children:(0,s.jsx)(y.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-lg font-semibold text-red-600",children:"Error: Failed to load exam details"})});let q=F.find(e=>e.userId===O),z=F.filter(e=>e.userId!==O);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)(y.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5},className:"bg-white rounded-2xl shadow-xl overflow-hidden mb-12",children:[(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-customOrange mb-6",children:"U whiz - Super Tutors"}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 text-gray-700",children:[{icon:l.qxq,label:"Marks",value:100},{icon:l.Gm4,label:"Start Time",value:"10:00 AM"},{icon:r.WEI,label:"Date",value:"27-4-2025"},{icon:j.XLK,label:"Duration",value:"90 min"},{icon:b.CgP,label:"Questions",value:100}].map((e,t)=>(0,s.jsxs)(y.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"flex items-center gap-3",children:[(0,s.jsx)(e.icon,{className:"text-customOrange text-2xl"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:e.label}),(0,s.jsx)("p",{className:"text-lg font-semibold",children:e.value})]})]},t))})]}),(0,s.jsxs)("div",{className:"p-6 bg-gray-50 flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(n.$,{onClick:()=>D("/RankCertificate.png",R,P),disabled:""===R,className:"bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-full transition-all transform hover:scale-105 disabled:opacity-50",children:"Download Result"}),(0,s.jsxs)(n.$,{onClick:()=>D("/participantCertificate.png",R),disabled:""===R,className:"bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-full transition-all transform hover:scale-105 disabled:opacity-50 flex items-center gap-2",children:[(0,s.jsx)(c.QbB,{className:"text-xl"})," Download Certificate"]})]})]}),(0,s.jsxs)(y.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,delay:.2},className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-customOrange text-center mb-8",children:"Exam Rankings"}),(0,s.jsxs)("div",{className:"space-y-4",children:[q&&(0,s.jsxs)(y.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"flex items-center justify-between p-4 bg-orange-100 rounded-xl shadow-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"w-10 h-10 flex items-center justify-center bg-orange-500 text-white rounded-full font-bold text-base shadow",children:["#",q.rank]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[q.classesLogo?(0,s.jsx)(w.default,{src:"".concat("http://localhost:4005/").concat(q.classesLogo),alt:"".concat(q.name,"'s logo"),className:"rounded-sm object-cover",height:80,width:80}):(0,s.jsx)("div",{className:"w-12 h-12 flex items-center justify-center bg-gray-200 rounded-full text-xs text-gray-500",children:"No Logo"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-base font-semibold text-orange-800",children:q.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["(",q.classesName,")"]}),1===q.rank&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-orange-600 text-sm mt-1",children:[(0,s.jsx)(j.qjb,{className:"text-base"}),(0,s.jsx)("span",{className:"font-medium text-xl",children:"₹21,000"})]})]})]})]}),(0,s.jsxs)("div",{className:"text-base font-bold text-green-600 bg-green-100 px-3 py-1 rounded-full shadow-sm",children:[q.score,"/",q.totalQuestions]})]}),z.slice(0,L).map(e=>(0,s.jsxs)(y.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"flex items-center justify-between p-4 rounded-xl shadow-md ".concat(1===e.rank?"bg-orange-50 border-l-4 border-orange-500":"hover:bg-gray-50"),children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"w-10 h-10 flex items-center justify-center rounded-full ".concat(1===e.rank?"bg-orange-500 text-white":"bg-orange-100 text-orange-600"," font-bold text-sm shadow"),children:["#",e.rank]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[e.classesLogo?(0,s.jsx)(w.default,{src:"".concat("http://localhost:4005/").concat(e.classesLogo),alt:"".concat(e.name,"'s logo"),className:"rounded-sm object-cover",width:80,height:80}):(0,s.jsx)("div",{className:"w-12 h-12 flex items-center justify-center bg-gray-200 rounded-full text-xs text-gray-500",children:"No Logo"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold ".concat(1===e.rank?"text-lg text-orange-800":"text-base text-gray-700"),children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["(",e.classesName,")"]}),1===e.rank&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-orange-500 mt-1",children:[(0,s.jsx)(j.qjb,{className:"text-base"}),(0,s.jsx)("span",{className:"font-medium text-sm",children:"₹21,000"})]})]})]})]}),(0,s.jsxs)("div",{className:"font-semibold text-green-600 bg-green-100 px-3 py-1 rounded-full ".concat(1===e.rank?"text-base":"text-sm"),children:[e.score,"/",e.totalQuestions]})]},e.userId))]}),L<z.length&&(0,s.jsx)("div",{className:"flex justify-center mt-6",children:(0,s.jsx)(n.$,{onClick:()=>I(F.length),className:"bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-full transition-all transform hover:scale-105",children:"Load More"})})]})]})}),(0,s.jsx)(N.default,{}),(0,s.jsx)("canvas",{ref:_,style:{display:"none"}})]})}},46407:(e,t,a)=>{Promise.resolve().then(a.bind(a,33821))},46523:(e,t,a)=>{"use strict";a.d(t,{Dl:()=>i,LP:()=>r});var s=a(55077);let i=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0;try{return(await s.S.get("/exams?page=".concat(e,"&limit=").concat(t).concat(a?"&applicantId=".concat(a):""),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var i,r;return{success:!1,error:"Failed to fetch Exam: ".concat((null===(r=e.response)||void 0===r?void 0:null===(i=r.data)||void 0===i?void 0:i.message)||e.message)}}},r=async(e,t)=>{try{return(await s.S.get("/exams/".concat(e).concat(t?"?applicantId=".concat(t):""),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var a,i;return{success:!1,error:"Failed to fetch Exam: ".concat((null===(i=e.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.message)||e.message)}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6446,512,7672,4506,8087,3930,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,2676,347,8441,1684,7358],()=>t(46407)),_N_E=e.O()}]);