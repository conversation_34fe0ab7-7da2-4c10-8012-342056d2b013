<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentSiblingsDetails extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_siblings_details';

    protected $fillable = [
        'student_id',
        'sibling_name',
        'sibling_date_of_birth',
        'studying_std',
        'school_name',
    ];
}
