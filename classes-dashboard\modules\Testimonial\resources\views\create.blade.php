<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::open(['route' => 'testimonial.store','id'=>'createtestimonial_form']) !!}
                    @include('Testimonial::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('Testimonial\Http\Requests\CreateTestimonialRequest', '#createtestimonial_form') !!}
<script>
     var createtestimonialRoute = {
        store: "{{ route('testimonial.store') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/Testimonial/create.js')) }}"></script>