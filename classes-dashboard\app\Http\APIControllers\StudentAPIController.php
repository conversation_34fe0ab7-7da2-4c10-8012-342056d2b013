<?php
namespace App\Http\APIControllers;

use Admission\Models\StudentDetails;
use Admission\Models\StudentDetailsView;
use Admission\Repositories\AdmissionRepository;
use Fees\Repositories\StudentFeePaymentRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use PDF;
use StudentAttendance\Repositories\AttendanceRepository;
use Timetable\Repositories\MasterTimetableRepository;

class StudentAPIController extends Controller
{
    protected $admissionRepository;
    protected $studentFeePaymentRepository;
    protected $masterTimetableRepository;
    protected $attendanceRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        StudentFeePaymentRepository $studentFeePaymentRepository,
        MasterTimetableRepository $masterTimetableRepository,
        AttendanceRepository $attendanceRepository
    ) {
        $this->admissionRepository = $admissionRepository;
        $this->studentFeePaymentRepository = $studentFeePaymentRepository;
        $this->masterTimetableRepository = $masterTimetableRepository;
        $this->attendanceRepository = $attendanceRepository;
    }

    public function getProfiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetailsView::where('email', $request->email)
            ->select('gr_no', 'first_name', 'middle_name', 'last_name', 'photo', 'contact_no', 'date_of_birth', 'id', 'email')
            ->orderBy('id', 'DESC')
            ->where('status', 'ACTIVE')
            ->get()->unique('gr_no');

        if ($student->isEmpty()) {
            return response()->json(apiErrorResonse('No profiles found for this email', ''), 422);
        }

        return response()->json(apiResonse("Profiles Fetched Successfully", $student));
    }

    public function selectProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'gr_no' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)
            ->where('gr_no', $request->gr_no)
            ->orderBy('id', 'DESC')
            ->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Invalid email or GR number', ''), 422);
        }

        return response()->json(apiResonse("Profile Selected Successfully", $student));
    }

    public function getStudentDetails(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)
            ->orderBy('id', 'DESC')
            ->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found for this email', ''), 422);
        }

        $studentDetails = $this->admissionRepository->findByGrno($student->gr_no);

        if (!$studentDetails) {
            return response()->json(apiErrorResonse('Student details not found', ''), 422);
        }

        return response()->json(apiResonse("Student Details Fetched Successfully", $studentDetails));
    }

    public function logout(Request $request)
    {
        return response()->json(apiResonse("Successfully logged out", ''));
    }

    public function getFeesDetails(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', ''), 422);
        }

        $feesDetail = $this->studentFeePaymentRepository->getFeesByStudent($student->id);
        return response()->json(apiResonse("Fees Detail Fetched Successfully", $feesDetail));
    }

    public function getTimetable(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'date' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', ''), 422);
        }

        $classroom_id = getStudentAcademicById($student->id)->classroom;
        $timetable = $this->masterTimetableRepository->getTimetableByClassroom($classroom_id, $request->date);

        return response()->json(apiResonse("Timetable Fetched Successfully", $timetable));
    }

    public function getFeesPaymentLogs(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'installment' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', ''), 422);
        }

        $timetable = $this->studentFeePaymentRepository->getPaidFeesLogs($request,

 $student->id, $request->input('installment'));
        return response()->json(apiResonse("Fees Logs Fetched Successfully", $timetable));
    }

    public function getAttendance(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', ''), 422);
        }

        $attendance = $this->attendanceRepository->getAttendanceForAPI($request->all(), $student->id);
        return response()->json(apiResonse("Attendance Fetched Successfully", $attendance));
    }

    public function getDisciplineIssue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', ''), 422);
        }

        $attendance = $this->attendanceRepository->getDisciplineIssue($request->all(), $student->id);
        return response()->json(apiResonse("Discipline Issue Fetched Successfully", $attendance));
    }

    public function downloadSlips(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $student = StudentDetails::where('email', $request->email)->first();

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', ''), 422);
        }

        $studentdata = $this->admissionRepository->findByid($student->id);
        $paymentdetails = $this->studentFeePaymentRepository->getStudentPaymentLogsByPaymentid($id);

        $tenantData = session('tenant') ?? [
            'tenantLogo' => asset("images/logo.png"),
            'tenantAddress' => "UEST, Morbi - 363641",
            'tenantName' => "UEST",
            'subdomain' => "uest",
        ];

        $pdf = PDF::loadView(
            'Admission::payslip',
            [
                'paymentdetails' => $paymentdetails,
                'studentdata' => $studentdata,
                'tenantLogo' => $tenantData['tenantLogo'],
                'tenantAddress' => $tenantData['tenantAddress'],
                'tenantName' => $tenantData['tenantName'],
            ]
        )->setPaper('a4', 'landscape');

        return response($pdf->output(), 200)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="fees-receipt-' . $id . '.pdf"');
    }
}