(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6980],{3159:(e,t,o)=>{"use strict";o.d(t,{Ay:()=>g});var r=o(39249),n=o(12115),i=o(76879),a=o.n(i);function s(e,t,o,r,n){void 0===n&&(n=0);var i=f(t.width,t.height,n),a=i.width,s=i.height;return{x:c(e.x,a,o.width,r),y:c(e.y,s,o.height,r)}}function c(e,t,o,r){var n=t*r/2-o/2;return v(e,-n,n)}function p(e,t){return Math.sqrt(Math.pow(e.y-t.y,2)+Math.pow(e.x-t.x,2))}function h(e,t){return 180*Math.atan2(t.y-e.y,t.x-e.x)/Math.PI}function u(e,t){return Math.min(e,Math.max(0,t))}function l(e,t){return t}function d(e,t){return{x:(t.x+e.x)/2,y:(t.y+e.y)/2}}function f(e,t,o){var r=o*Math.PI/180;return{width:Math.abs(Math.cos(r)*e)+Math.abs(Math.sin(r)*t),height:Math.abs(Math.sin(r)*e)+Math.abs(Math.cos(r)*t)}}function v(e,t,o){return Math.min(Math.max(e,t),o)}function m(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.filter(function(e){return"string"==typeof e&&!!(e.length>0)}).join(" ").trim()}var g=function(e){function t(){var o=null!==e&&e.apply(this,arguments)||this;return o.cropperRef=n.createRef(),o.imageRef=n.createRef(),o.videoRef=n.createRef(),o.containerPosition={x:0,y:0},o.containerRef=null,o.styleRef=null,o.containerRect=null,o.mediaSize={width:0,height:0,naturalWidth:0,naturalHeight:0},o.dragStartPosition={x:0,y:0},o.dragStartCrop={x:0,y:0},o.gestureZoomStart=0,o.gestureRotationStart=0,o.isTouching=!1,o.lastPinchDistance=0,o.lastPinchRotation=0,o.rafDragTimeout=null,o.rafPinchTimeout=null,o.wheelTimer=null,o.currentDoc="undefined"!=typeof document?document:null,o.currentWindow="undefined"!=typeof window?window:null,o.resizeObserver=null,o.state={cropSize:null,hasWheelJustStarted:!1,mediaObjectFit:void 0},o.initResizeObserver=function(){if(void 0!==window.ResizeObserver&&o.containerRef){var e=!0;o.resizeObserver=new window.ResizeObserver(function(t){if(e){e=!1;return}o.computeSizes()}),o.resizeObserver.observe(o.containerRef)}},o.preventZoomSafari=function(e){return e.preventDefault()},o.cleanEvents=function(){o.currentDoc&&(o.currentDoc.removeEventListener("mousemove",o.onMouseMove),o.currentDoc.removeEventListener("mouseup",o.onDragStopped),o.currentDoc.removeEventListener("touchmove",o.onTouchMove),o.currentDoc.removeEventListener("touchend",o.onDragStopped),o.currentDoc.removeEventListener("gesturemove",o.onGestureMove),o.currentDoc.removeEventListener("gestureend",o.onGestureEnd),o.currentDoc.removeEventListener("scroll",o.onScroll))},o.clearScrollEvent=function(){o.containerRef&&o.containerRef.removeEventListener("wheel",o.onWheel),o.wheelTimer&&clearTimeout(o.wheelTimer)},o.onMediaLoad=function(){var e=o.computeSizes();e&&(o.emitCropData(),o.setInitialCrop(e)),o.props.onMediaLoaded&&o.props.onMediaLoaded(o.mediaSize)},o.setInitialCrop=function(e){if(o.props.initialCroppedAreaPercentages){var t,r,n,i,a,s,c,p=(t=o.props.initialCroppedAreaPercentages,r=o.mediaSize,n=o.props.rotation,i=o.props.minZoom,a=o.props.maxZoom,s=f(r.width,r.height,n),{crop:{x:(c=v(e.width/s.width*(100/t.width),i,a))*s.width/2-e.width/2-s.width*c*(t.x/100),y:c*s.height/2-e.height/2-s.height*c*(t.y/100)},zoom:c}),h=p.crop,u=p.zoom;o.props.onCropChange(h),o.props.onZoomChange&&o.props.onZoomChange(u)}else if(o.props.initialCroppedAreaPixels){var l,d,m,g,w,y,C,S,R,b=(l=o.props.initialCroppedAreaPixels,d=o.mediaSize,m=o.props.rotation,g=o.props.minZoom,w=o.props.maxZoom,void 0===m&&(m=0),y=f(d.naturalWidth,d.naturalHeight,m),S=v((C=d.width>d.height?d.width/d.naturalWidth:d.height/d.naturalHeight,e.height>e.width?e.height/(l.height*C):e.width/(l.width*C)),g,w),R=e.height>e.width?e.height/l.height:e.width/l.width,{crop:{x:((y.width-l.width)/2-l.x)*R,y:((y.height-l.height)/2-l.y)*R},zoom:S}),h=b.crop,u=b.zoom;o.props.onCropChange(h),o.props.onZoomChange&&o.props.onZoomChange(u)}},o.computeSizes=function(){var e,t,n,i,a,s,c=o.imageRef.current||o.videoRef.current;if(c&&o.containerRef){o.containerRect=o.containerRef.getBoundingClientRect(),o.saveContainerPosition();var p,h,u,l,d,v,m,g,w,y,C,S=o.containerRect.width/o.containerRect.height,R=(null===(e=o.imageRef.current)||void 0===e?void 0:e.naturalWidth)||(null===(t=o.videoRef.current)||void 0===t?void 0:t.videoWidth)||0,b=(null===(n=o.imageRef.current)||void 0===n?void 0:n.naturalHeight)||(null===(i=o.videoRef.current)||void 0===i?void 0:i.videoHeight)||0,x=c.offsetWidth<R||c.offsetHeight<b,z=R/b,D=void 0;if(x)switch(o.state.mediaObjectFit){default:case"contain":D=S>z?{width:o.containerRect.height*z,height:o.containerRect.height}:{width:o.containerRect.width,height:o.containerRect.width/z};break;case"horizontal-cover":D={width:o.containerRect.width,height:o.containerRect.width/z};break;case"vertical-cover":D={width:o.containerRect.height*z,height:o.containerRect.height}}else D={width:c.offsetWidth,height:c.offsetHeight};o.mediaSize=(0,r.Cl)((0,r.Cl)({},D),{naturalWidth:R,naturalHeight:b}),o.props.setMediaSize&&o.props.setMediaSize(o.mediaSize);var P=o.props.cropSize?o.props.cropSize:(p=o.mediaSize.width,h=o.mediaSize.height,u=o.containerRect.width,l=o.containerRect.height,d=o.props.aspect,void 0===(v=o.props.rotation)&&(v=0),g=(m=f(p,h,v)).width,w=m.height,(y=Math.min(g,u))>(C=Math.min(w,l))*d?{width:C*d,height:C}:{width:y,height:y/d});return((null===(a=o.state.cropSize)||void 0===a?void 0:a.height)!==P.height||(null===(s=o.state.cropSize)||void 0===s?void 0:s.width)!==P.width)&&o.props.onCropSizeChange&&o.props.onCropSizeChange(P),o.setState({cropSize:P},o.recomputeCropPosition),o.props.setCropSize&&o.props.setCropSize(P),P}},o.saveContainerPosition=function(){if(o.containerRef){var e=o.containerRef.getBoundingClientRect();o.containerPosition={x:e.left,y:e.top}}},o.onMouseDown=function(e){o.currentDoc&&(e.preventDefault(),o.currentDoc.addEventListener("mousemove",o.onMouseMove),o.currentDoc.addEventListener("mouseup",o.onDragStopped),o.saveContainerPosition(),o.onDragStart(t.getMousePoint(e)))},o.onMouseMove=function(e){return o.onDrag(t.getMousePoint(e))},o.onScroll=function(e){o.currentDoc&&(e.preventDefault(),o.saveContainerPosition())},o.onTouchStart=function(e){o.currentDoc&&(o.isTouching=!0,(!o.props.onTouchRequest||o.props.onTouchRequest(e))&&(o.currentDoc.addEventListener("touchmove",o.onTouchMove,{passive:!1}),o.currentDoc.addEventListener("touchend",o.onDragStopped),o.saveContainerPosition(),2===e.touches.length?o.onPinchStart(e):1===e.touches.length&&o.onDragStart(t.getTouchPoint(e.touches[0]))))},o.onTouchMove=function(e){e.preventDefault(),2===e.touches.length?o.onPinchMove(e):1===e.touches.length&&o.onDrag(t.getTouchPoint(e.touches[0]))},o.onGestureStart=function(e){o.currentDoc&&(e.preventDefault(),o.currentDoc.addEventListener("gesturechange",o.onGestureMove),o.currentDoc.addEventListener("gestureend",o.onGestureEnd),o.gestureZoomStart=o.props.zoom,o.gestureRotationStart=o.props.rotation)},o.onGestureMove=function(e){if(e.preventDefault(),!o.isTouching){var r=t.getMousePoint(e),n=o.gestureZoomStart-1+e.scale;if(o.setNewZoom(n,r,{shouldUpdatePosition:!0}),o.props.onRotationChange){var i=o.gestureRotationStart+e.rotation;o.props.onRotationChange(i)}}},o.onGestureEnd=function(e){o.cleanEvents()},o.onDragStart=function(e){var t,n;o.dragStartPosition={x:e.x,y:e.y},o.dragStartCrop=(0,r.Cl)({},o.props.crop),null===(n=(t=o.props).onInteractionStart)||void 0===n||n.call(t)},o.onDrag=function(e){var t=e.x,r=e.y;o.currentWindow&&(o.rafDragTimeout&&o.currentWindow.cancelAnimationFrame(o.rafDragTimeout),o.rafDragTimeout=o.currentWindow.requestAnimationFrame(function(){if(o.state.cropSize&&void 0!==t&&void 0!==r){var e=t-o.dragStartPosition.x,n=r-o.dragStartPosition.y,i={x:o.dragStartCrop.x+e,y:o.dragStartCrop.y+n},a=o.props.restrictPosition?s(i,o.mediaSize,o.state.cropSize,o.props.zoom,o.props.rotation):i;o.props.onCropChange(a)}}))},o.onDragStopped=function(){var e,t;o.isTouching=!1,o.cleanEvents(),o.emitCropData(),null===(t=(e=o.props).onInteractionEnd)||void 0===t||t.call(e)},o.onWheel=function(e){if(o.currentWindow&&(!o.props.onWheelRequest||o.props.onWheelRequest(e))){e.preventDefault();var r=t.getMousePoint(e),n=a()(e).pixelY,i=o.props.zoom-n*o.props.zoomSpeed/200;o.setNewZoom(i,r,{shouldUpdatePosition:!0}),o.state.hasWheelJustStarted||o.setState({hasWheelJustStarted:!0},function(){var e,t;return null===(t=(e=o.props).onInteractionStart)||void 0===t?void 0:t.call(e)}),o.wheelTimer&&clearTimeout(o.wheelTimer),o.wheelTimer=o.currentWindow.setTimeout(function(){return o.setState({hasWheelJustStarted:!1},function(){var e,t;return null===(t=(e=o.props).onInteractionEnd)||void 0===t?void 0:t.call(e)})},250)}},o.getPointOnContainer=function(e,t){var r=e.x,n=e.y;if(!o.containerRect)throw Error("The Cropper is not mounted");return{x:o.containerRect.width/2-(r-t.x),y:o.containerRect.height/2-(n-t.y)}},o.getPointOnMedia=function(e){var t=e.x,r=e.y,n=o.props,i=n.crop,a=n.zoom;return{x:(t+i.x)/a,y:(r+i.y)/a}},o.setNewZoom=function(e,t,r){var n=(void 0===r?{}:r).shouldUpdatePosition;if(o.state.cropSize&&o.props.onZoomChange){var i=v(e,o.props.minZoom,o.props.maxZoom);if(void 0===n||n){var a=o.getPointOnContainer(t,o.containerPosition),c=o.getPointOnMedia(a),p={x:c.x*i-a.x,y:c.y*i-a.y},h=o.props.restrictPosition?s(p,o.mediaSize,o.state.cropSize,i,o.props.rotation):p;o.props.onCropChange(h)}o.props.onZoomChange(i)}},o.getCropData=function(){var e,t,n,i,a,c,p,h,d,v,m,g,w,y,C;return o.state.cropSize?(e=o.props.restrictPosition?s(o.props.crop,o.mediaSize,o.state.cropSize,o.props.zoom,o.props.rotation):o.props.crop,t=o.mediaSize,n=o.state.cropSize,i=o.getAspect(),a=o.props.zoom,c=o.props.rotation,p=o.props.restrictPosition,void 0===c&&(c=0),void 0===p&&(p=!0),h=p?u:l,d=f(t.width,t.height,c),v=f(t.naturalWidth,t.naturalHeight,c),m={x:h(100,((d.width-n.width/a)/2-e.x/a)/d.width*100),y:h(100,((d.height-n.height/a)/2-e.y/a)/d.height*100),width:h(100,n.width/d.width*100/a),height:h(100,n.height/d.height*100/a)},g=Math.round(h(v.width,m.width*v.width/100)),w=Math.round(h(v.height,m.height*v.height/100)),y=v.width>=v.height*i?{width:Math.round(w*i),height:w}:{width:g,height:Math.round(g/i)},C=(0,r.Cl)((0,r.Cl)({},y),{x:Math.round(h(v.width-y.width,m.x*v.width/100)),y:Math.round(h(v.height-y.height,m.y*v.height/100))}),{croppedAreaPercentages:m,croppedAreaPixels:C}):null},o.emitCropData=function(){var e=o.getCropData();if(e){var t=e.croppedAreaPercentages,r=e.croppedAreaPixels;o.props.onCropComplete&&o.props.onCropComplete(t,r),o.props.onCropAreaChange&&o.props.onCropAreaChange(t,r)}},o.emitCropAreaChange=function(){var e=o.getCropData();if(e){var t=e.croppedAreaPercentages,r=e.croppedAreaPixels;o.props.onCropAreaChange&&o.props.onCropAreaChange(t,r)}},o.recomputeCropPosition=function(){if(o.state.cropSize){var e=o.props.restrictPosition?s(o.props.crop,o.mediaSize,o.state.cropSize,o.props.zoom,o.props.rotation):o.props.crop;o.props.onCropChange(e),o.emitCropData()}},o.onKeyDown=function(e){var t,n,i=o.props,a=i.crop,c=i.onCropChange,p=i.keyboardStep,h=i.zoom,u=i.rotation,l=p;if(o.state.cropSize){e.shiftKey&&(l*=.2);var d=(0,r.Cl)({},a);switch(e.key){case"ArrowUp":d.y-=l,e.preventDefault();break;case"ArrowDown":d.y+=l,e.preventDefault();break;case"ArrowLeft":d.x-=l,e.preventDefault();break;case"ArrowRight":d.x+=l,e.preventDefault();break;default:return}o.props.restrictPosition&&(d=s(d,o.mediaSize,o.state.cropSize,h,u)),e.repeat||null===(n=(t=o.props).onInteractionStart)||void 0===n||n.call(t),c(d)}},o.onKeyUp=function(e){var t,r;switch(e.key){case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":e.preventDefault();break;default:return}o.emitCropData(),null===(r=(t=o.props).onInteractionEnd)||void 0===r||r.call(t)},o}return(0,r.C6)(t,e),t.prototype.componentDidMount=function(){this.currentDoc&&this.currentWindow&&(this.containerRef&&(this.containerRef.ownerDocument&&(this.currentDoc=this.containerRef.ownerDocument),this.currentDoc.defaultView&&(this.currentWindow=this.currentDoc.defaultView),this.initResizeObserver(),void 0===window.ResizeObserver&&this.currentWindow.addEventListener("resize",this.computeSizes),this.props.zoomWithScroll&&this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}),this.containerRef.addEventListener("gesturestart",this.onGestureStart)),this.currentDoc.addEventListener("scroll",this.onScroll),this.props.disableAutomaticStylesInjection||(this.styleRef=this.currentDoc.createElement("style"),this.styleRef.setAttribute("type","text/css"),this.props.nonce&&this.styleRef.setAttribute("nonce",this.props.nonce),this.styleRef.innerHTML=".reactEasyCrop_Container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  user-select: none;\n  touch-action: none;\n  cursor: move;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.reactEasyCrop_Image,\n.reactEasyCrop_Video {\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\n}\n\n.reactEasyCrop_Contain {\n  max-width: 100%;\n  max-height: 100%;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n.reactEasyCrop_Cover_Horizontal {\n  width: 100%;\n  height: auto;\n}\n.reactEasyCrop_Cover_Vertical {\n  width: auto;\n  height: 100%;\n}\n\n.reactEasyCrop_CropArea {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  box-sizing: border-box;\n  box-shadow: 0 0 0 9999em;\n  color: rgba(0, 0, 0, 0.5);\n  overflow: hidden;\n}\n\n.reactEasyCrop_CropAreaRound {\n  border-radius: 50%;\n}\n\n.reactEasyCrop_CropAreaGrid::before {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 0;\n  bottom: 0;\n  left: 33.33%;\n  right: 33.33%;\n  border-top: 0;\n  border-bottom: 0;\n}\n\n.reactEasyCrop_CropAreaGrid::after {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 33.33%;\n  bottom: 33.33%;\n  left: 0;\n  right: 0;\n  border-left: 0;\n  border-right: 0;\n}\n",this.currentDoc.head.appendChild(this.styleRef)),this.imageRef.current&&this.imageRef.current.complete&&this.onMediaLoad(),this.props.setImageRef&&this.props.setImageRef(this.imageRef),this.props.setVideoRef&&this.props.setVideoRef(this.videoRef),this.props.setCropperRef&&this.props.setCropperRef(this.cropperRef))},t.prototype.componentWillUnmount=function(){var e,t;this.currentDoc&&this.currentWindow&&(void 0===window.ResizeObserver&&this.currentWindow.removeEventListener("resize",this.computeSizes),null===(e=this.resizeObserver)||void 0===e||e.disconnect(),this.containerRef&&this.containerRef.removeEventListener("gesturestart",this.preventZoomSafari),this.styleRef&&(null===(t=this.styleRef.parentNode)||void 0===t||t.removeChild(this.styleRef)),this.cleanEvents(),this.props.zoomWithScroll&&this.clearScrollEvent())},t.prototype.componentDidUpdate=function(e){e.rotation!==this.props.rotation?(this.computeSizes(),this.recomputeCropPosition()):e.aspect!==this.props.aspect?this.computeSizes():e.objectFit!==this.props.objectFit?this.computeSizes():e.zoom!==this.props.zoom?this.recomputeCropPosition():(null===(t=e.cropSize)||void 0===t?void 0:t.height)!==(null===(o=this.props.cropSize)||void 0===o?void 0:o.height)||(null===(r=e.cropSize)||void 0===r?void 0:r.width)!==(null===(n=this.props.cropSize)||void 0===n?void 0:n.width)?this.computeSizes():((null===(i=e.crop)||void 0===i?void 0:i.x)!==(null===(a=this.props.crop)||void 0===a?void 0:a.x)||(null===(s=e.crop)||void 0===s?void 0:s.y)!==(null===(c=this.props.crop)||void 0===c?void 0:c.y))&&this.emitCropAreaChange(),e.zoomWithScroll!==this.props.zoomWithScroll&&this.containerRef&&(this.props.zoomWithScroll?this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}):this.clearScrollEvent()),e.video!==this.props.video&&(null===(p=this.videoRef.current)||void 0===p||p.load());var t,o,r,n,i,a,s,c,p,h=this.getObjectFit();h!==this.state.mediaObjectFit&&this.setState({mediaObjectFit:h},this.computeSizes)},t.prototype.getAspect=function(){var e=this.props,t=e.cropSize,o=e.aspect;return t?t.width/t.height:o},t.prototype.getObjectFit=function(){var e,t,o,r;if("cover"===this.props.objectFit){if((this.imageRef.current||this.videoRef.current)&&this.containerRef){this.containerRect=this.containerRef.getBoundingClientRect();var n=this.containerRect.width/this.containerRect.height;return((null===(e=this.imageRef.current)||void 0===e?void 0:e.naturalWidth)||(null===(t=this.videoRef.current)||void 0===t?void 0:t.videoWidth)||0)/((null===(o=this.imageRef.current)||void 0===o?void 0:o.naturalHeight)||(null===(r=this.videoRef.current)||void 0===r?void 0:r.videoHeight)||0)<n?"horizontal-cover":"vertical-cover"}return"horizontal-cover"}return this.props.objectFit},t.prototype.onPinchStart=function(e){var o=t.getTouchPoint(e.touches[0]),r=t.getTouchPoint(e.touches[1]);this.lastPinchDistance=p(o,r),this.lastPinchRotation=h(o,r),this.onDragStart(d(o,r))},t.prototype.onPinchMove=function(e){var o=this;if(this.currentDoc&&this.currentWindow){var r=t.getTouchPoint(e.touches[0]),n=t.getTouchPoint(e.touches[1]),i=d(r,n);this.onDrag(i),this.rafPinchTimeout&&this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout),this.rafPinchTimeout=this.currentWindow.requestAnimationFrame(function(){var e=p(r,n),t=o.props.zoom*(e/o.lastPinchDistance);o.setNewZoom(t,i,{shouldUpdatePosition:!1}),o.lastPinchDistance=e;var a=h(r,n),s=o.props.rotation+(a-o.lastPinchRotation);o.props.onRotationChange&&o.props.onRotationChange(s),o.lastPinchRotation=a})}},t.prototype.render=function(){var e,t=this,o=this.props,i=o.image,a=o.video,s=o.mediaProps,c=o.cropperProps,p=o.transform,h=o.crop,u=h.x,l=h.y,d=o.rotation,f=o.zoom,v=o.cropShape,g=o.showGrid,w=o.style,y=w.containerStyle,C=w.cropAreaStyle,S=w.mediaStyle,R=o.classes,b=R.containerClassName,x=R.cropAreaClassName,z=R.mediaClassName,D=null!==(e=this.state.mediaObjectFit)&&void 0!==e?e:this.getObjectFit();return n.createElement("div",{onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,ref:function(e){return t.containerRef=e},"data-testid":"container",style:y,className:m("reactEasyCrop_Container",b)},i?n.createElement("img",(0,r.Cl)({alt:"",className:m("reactEasyCrop_Image","contain"===D&&"reactEasyCrop_Contain","horizontal-cover"===D&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===D&&"reactEasyCrop_Cover_Vertical",z)},s,{src:i,ref:this.imageRef,style:(0,r.Cl)((0,r.Cl)({},S),{transform:p||"translate(".concat(u,"px, ").concat(l,"px) rotate(").concat(d,"deg) scale(").concat(f,")")}),onLoad:this.onMediaLoad})):a&&n.createElement("video",(0,r.Cl)({autoPlay:!0,playsInline:!0,loop:!0,muted:!0,className:m("reactEasyCrop_Video","contain"===D&&"reactEasyCrop_Contain","horizontal-cover"===D&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===D&&"reactEasyCrop_Cover_Vertical",z)},s,{ref:this.videoRef,onLoadedMetadata:this.onMediaLoad,style:(0,r.Cl)((0,r.Cl)({},S),{transform:p||"translate(".concat(u,"px, ").concat(l,"px) rotate(").concat(d,"deg) scale(").concat(f,")")}),controls:!1}),(Array.isArray(a)?a:[{src:a}]).map(function(e){return n.createElement("source",(0,r.Cl)({key:e.src},e))})),this.state.cropSize&&n.createElement("div",(0,r.Cl)({ref:this.cropperRef,style:(0,r.Cl)((0,r.Cl)({},C),{width:this.state.cropSize.width,height:this.state.cropSize.height}),tabIndex:0,onKeyDown:this.onKeyDown,onKeyUp:this.onKeyUp,"data-testid":"cropper",className:m("reactEasyCrop_CropArea","round"===v&&"reactEasyCrop_CropAreaRound",g&&"reactEasyCrop_CropAreaGrid",x)},c)))},t.defaultProps={zoom:1,rotation:0,aspect:4/3,maxZoom:3,minZoom:1,cropShape:"rect",objectFit:"contain",showGrid:!0,style:{},classes:{},mediaProps:{},cropperProps:{},zoomSpeed:1,restrictPosition:!0,zoomWithScroll:!0,keyboardStep:1},t.getMousePoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},t.getTouchPoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},t}(n.Component)},9125:e=>{"use strict";var t=!!("undefined"!=typeof window&&window.document&&window.document.createElement);e.exports={canUseDOM:t,canUseWorkers:"undefined"!=typeof Worker,canUseEventListeners:t&&!!(window.addEventListener||window.attachEvent),canUseViewport:t&&!!window.screen,isInWorker:!t}},14050:(e,t,o)=>{"use strict";o.d(t,{b:()=>h});var r=o(12115);o(47650);var n=o(66634),i=o(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let o=(0,n.TL)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?o:t,{...a,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),s="horizontal",c=["horizontal","vertical"],p=r.forwardRef((e,t)=>{var o;let{decorative:r,orientation:n=s,...p}=e,h=(o=n,c.includes(o))?n:s;return(0,i.jsx)(a.div,{"data-orientation":h,...r?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...p,ref:t})});p.displayName="Separator";var h=p},24265:(e,t,o)=>{"use strict";o.d(t,{b:()=>h});var r=o(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}o(47650);var i=o(95155),a=Symbol("radix.slottable");function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let o=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:o,...i}=e;if(r.isValidElement(o)){var a;let e,s;let c=(a=o,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),p=function(e,t){let o={...t};for(let r in t){let n=e[r],i=t[r];/^on[A-Z]/.test(r)?n&&i?o[r]=(...e)=>{i(...e),n(...e)}:n&&(o[r]=n):"style"===r?o[r]={...n,...i}:"className"===r&&(o[r]=[n,i].filter(Boolean).join(" "))}return{...e,...o}}(i,o.props);return o.type!==r.Fragment&&(p.ref=t?function(...e){return t=>{let o=!1,r=e.map(e=>{let r=n(e,t);return o||"function"!=typeof r||(o=!0),r});if(o)return()=>{for(let t=0;t<r.length;t++){let o=r[t];"function"==typeof o?o():n(e[t],null)}}}}(t,c):c),r.cloneElement(o,p)}return r.Children.count(o)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),o=r.forwardRef((e,o)=>{let{children:n,...a}=e,c=r.Children.toArray(n),p=c.find(s);if(p){let e=p.props.children,n=c.map(t=>t!==p?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:o,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...a,ref:o,children:n})});return o.displayName=`${e}.Slot`,o}(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?o:t,{...a,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),p=r.forwardRef((e,t)=>(0,i.jsx)(c.label,{...e,ref:t,onMouseDown:t=>{var o;t.target.closest("button, input, select, textarea")||(null===(o=e.onMouseDown)||void 0===o||o.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));p.displayName="Label";var h=p},39249:(e,t,o)=>{"use strict";o.d(t,{C6:()=>n,Cl:()=>i,Tt:()=>a,fX:()=>s});var r=function(e,t){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])})(e,t)};function n(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function o(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(o.prototype=t.prototype,new o)}var i=function(){return(i=Object.assign||function(e){for(var t,o=1,r=arguments.length;o<r;o++)for(var n in t=arguments[o])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function a(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(o[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(o[r[n]]=e[r[n]]);return o}Object.create;function s(e,t,o){if(o||2==arguments.length)for(var r,n=0,i=t.length;n<i;n++)!r&&n in t||(r||(r=Array.prototype.slice.call(t,0,n)),r[n]=t[n]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},57988:(e,t,o)=>{"use strict";var r,n=o(9125);n.canUseDOM&&(r=document.implementation&&document.implementation.hasFeature&&!0!==document.implementation.hasFeature("","")),e.exports=function(e,t){if(!n.canUseDOM||t&&!("addEventListener"in document))return!1;var o="on"+e,i=o in document;if(!i){var a=document.createElement("div");a.setAttribute(o,"return;"),i="function"==typeof a[o]}return!i&&r&&"wheel"===e&&(i=document.implementation.hasFeature("Events.wheel","3.0")),i}},66318:e=>{var t,o,r,n,i,a,s,c,p,h,u,l,d,f,v,m=!1;function g(){if(!m){m=!0;var e=navigator.userAgent,g=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e),w=/(Mac OS X)|(Windows)|(Linux)/.exec(e);if(l=/\b(iPhone|iP[ao]d)/.exec(e),d=/\b(iP[ao]d)/.exec(e),h=/Android/i.exec(e),f=/FBAN\/\w+;/i.exec(e),v=/Mobile/i.exec(e),u=!!/Win64/.exec(e),g){(t=g[1]?parseFloat(g[1]):g[5]?parseFloat(g[5]):NaN)&&document&&document.documentMode&&(t=document.documentMode);var y=/(?:Trident\/(\d+.\d+))/.exec(e);a=y?parseFloat(y[1])+4:t,o=g[2]?parseFloat(g[2]):NaN,r=g[3]?parseFloat(g[3]):NaN,i=(n=g[4]?parseFloat(g[4]):NaN)&&(g=/(?:Chrome\/(\d+\.\d+))/.exec(e))&&g[1]?parseFloat(g[1]):NaN}else t=o=r=i=n=NaN;if(w){if(w[1]){var C=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);s=!C||parseFloat(C[1].replace("_","."))}else s=!1;c=!!w[2],p=!!w[3]}else s=c=p=!1}}var w={ie:function(){return g()||t},ieCompatibilityMode:function(){return g()||a>t},ie64:function(){return w.ie()&&u},firefox:function(){return g()||o},opera:function(){return g()||r},webkit:function(){return g()||n},safari:function(){return w.webkit()},chrome:function(){return g()||i},windows:function(){return g()||c},osx:function(){return g()||s},linux:function(){return g()||p},iphone:function(){return g()||l},mobile:function(){return g()||l||d||h||v},nativeApp:function(){return g()||f},android:function(){return g()||h},ipad:function(){return g()||d}};e.exports=w},76879:(e,t,o)=>{e.exports=o(92824)},92824:(e,t,o)=>{"use strict";var r=o(66318),n=o(57988);function i(e){var t=0,o=0,r=0,n=0;return"detail"in e&&(o=e.detail),"wheelDelta"in e&&(o=-e.wheelDelta/120),"wheelDeltaY"in e&&(o=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=o,o=0),r=10*t,n=10*o,"deltaY"in e&&(n=e.deltaY),"deltaX"in e&&(r=e.deltaX),(r||n)&&e.deltaMode&&(1==e.deltaMode?(r*=40,n*=40):(r*=800,n*=800)),r&&!t&&(t=r<1?-1:1),n&&!o&&(o=n<1?-1:1),{spinX:t,spinY:o,pixelX:r,pixelY:n}}i.getEventType=function(){return r.firefox()?"DOMMouseScroll":n("wheel")?"wheel":"mousewheel"},e.exports=i}}]);