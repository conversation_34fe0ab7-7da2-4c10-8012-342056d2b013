<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::model($data, ['id'=>'editthoughts_form','route' => ['thoughts.update', $data->id]]) !!}
                    @include('Thoughts::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('Thoughts\Http\Requests\CreateThoughtsRequest', '#editthoughts_form') !!}
<script>
    $("#editthoughts_form").submit(function() {
        event.preventDefault();
        var form = $(this);
        if ($(this).valid()) {
            var url = "{{ route('thoughts.update',$data->id) }}";
            ajaxHandler(form, url, 'PATCH', '#editthoughts_form', '#savethoughts', '#newThoughtsEntry', '#thoughts_table');
            return false;
        }
    });
</script>