(()=>{var e={};e.id=5344,e.ids=[5344],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5913:(e,t,s)=>{Promise.resolve().then(s.bind(s,49561)),Promise.resolve().then(s.bind(s,12304))},9768:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(37413),r=s(12304),n=s(49561);function i(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Tuition Class Setup"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add details about your tuition classes including subjects, standards, timings, and pricing to attract the right students."})]}),(0,a.jsx)(r.Separator,{}),(0,a.jsx)(n.TuitionClassForm,{})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12304:(e,t,s)=>{"use strict";s.d(t,{Separator:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx","Separator")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>c,yv:()=>d});var a=s(60687);s(43210);var r=s(95732),n=s(78272),i=s(13964),l=s(3589),o=s(4780);function c({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function d({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(f,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function f({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},17887:(e,t,s)=>{"use strict";s.d(t,{TuitionClassForm:()=>P});var a=s(60687),r=s(27605),n=s(63442),i=s(45880),l=s(52581),o=s(29523),c=s(80942),d=s(15079);s(59863);var u=s(43210),p=s(11860),m=s(62688);let x=(0,m.A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);var f=s(13964),h=s(4780);function g({options:e,value:t,onChange:s,placeholder:r="Select options...",className:n}){let[i,l]=u.useState(!1),[o,c]=u.useState(""),d=e.filter(e=>e.label.toLowerCase().includes(o.toLowerCase()));return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:(0,h.cn)("flex min-h-[40px] w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",n),onClick:()=>l(!i),children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:t.length>0?t.map(r=>{let n=e.find(e=>e.value===r);return(0,a.jsxs)("div",{className:"flex items-center gap-1 rounded-md bg-secondary px-2 py-1 text-sm",children:[n?.label,(0,a.jsx)(p.A,{className:"h-3 w-3 cursor-pointer",onClick:e=>{e.stopPropagation(),s(t.filter(e=>e!==r))}})]},r)}):(0,a.jsx)("span",{className:"text-muted-foreground",children:r})}),(0,a.jsx)(x,{className:"h-4 w-4 opacity-50"})]}),i&&(0,a.jsxs)("div",{className:"absolute z-50 mt-1 w-full rounded-md border bg-popover p-1 shadow-md",children:[(0,a.jsx)("div",{className:"flex items-center border-b px-2",children:(0,a.jsx)("input",{type:"text",placeholder:"Search...",className:"h-8 w-full bg-transparent px-2 py-1 text-sm outline-none",value:o,onChange:e=>c(e.target.value)})}),(0,a.jsx)("div",{className:"max-h-[200px] overflow-y-auto",children:0===d.length?(0,a.jsx)("div",{className:"px-2 py-1.5 text-sm text-muted-foreground",children:"No options found."}):d.map(e=>(0,a.jsxs)("div",{className:(0,h.cn)("flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent",t.includes(e.value)&&"bg-accent"),onClick:()=>{s(t.includes(e.value)?t.filter(t=>t!==e.value):[...t,e.value])},children:[(0,a.jsx)(f.A,{className:(0,h.cn)("h-4 w-4",t.includes(e.value)?"opacity-100":"opacity-0")}),e.label]},e.value))})]})]})}var v=s(28527),j=s(54864),b=s(50346),y=s(20672),w=s(88233);let N=(0,m.A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var C=s(44493),S=s(63503),T=s(35817);let z=i.z.object({tuitionDetails:i.z.array(i.z.object({education:i.z.string().min(1,"Category is required"),coachingType:i.z.array(i.z.string()).optional(),boardType:i.z.array(i.z.string()).optional(),subject:i.z.array(i.z.string()).optional(),medium:i.z.array(i.z.string()).optional(),section:i.z.array(i.z.string()).optional(),details:i.z.array(i.z.string()).optional()})).refine(e=>e.every(e=>"Education"===e.education?e.boardType?.length&&e.subject?.length&&e.medium?.length&&e.section?.length:!e.boardType?.length&&!e.subject?.length&&!e.medium?.length&&!e.section?.length),{message:"For 'Education', boardType, subject, medium, and section are required. For other categories, these fields must be empty.",path:["tuitionDetails"]})});function A({form:e,index:t,removeTuition:s,constants:r,tuitionFieldsLength:n}){let i=(e,t)=>{let s=r.find(t=>t.name===e),a=s?.subDetails.find(e=>e.name===t);return a?.values||[]},l=e=>i("Education",e),u=e=>{let t=r.find(t=>t.name===e);return t?.subDetails.map(e=>({id:e.id,name:e.name,isActive:!0,subDetailId:e.id}))||[]},p=e.watch(`tuitionDetails.${t}.education`);return(0,a.jsxs)("div",{className:"relative rounded-2xl border p-6 bg-muted/40 shadow-sm space-y-6",children:[n>1&&(0,a.jsx)(o.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-4 right-4 text-destructive hover:bg-destructive/10",onClick:()=>s(t),children:(0,a.jsx)(w.A,{size:20})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.education`,render:({field:s})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsx)(c.lR,{children:"Category"}),(0,a.jsx)(c.MJ,{children:(0,a.jsxs)(d.l6,{onValueChange:a=>{s.onChange(a),e.setValue(`tuitionDetails.${t}.boardType`,[]),e.setValue(`tuitionDetails.${t}.subject`,[]),e.setValue(`tuitionDetails.${t}.medium`,[]),e.setValue(`tuitionDetails.${t}.section`,[]),e.setValue(`tuitionDetails.${t}.details`,[])},value:s.value,children:[(0,a.jsx)(d.bq,{className:"w-full",children:(0,a.jsx)(d.yv,{placeholder:"Select Category"})}),(0,a.jsx)(d.gC,{children:r.map(e=>(0,a.jsx)(d.eb,{value:e.name,children:e.name},e.id))})]})}),(0,a.jsx)(c.C5,{})]})}),(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.coachingType`,render:({field:e})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsx)(c.lR,{children:"Coaching Type"}),(0,a.jsx)(c.MJ,{children:(0,a.jsx)(g,{options:[{label:"Personal",value:"Personal"},{label:"Group",value:"Group"},{label:"Online",value:"Online"},{label:"Hybrid",value:"Hybrid"}],value:e.value||[],onChange:t=>e.onChange(t),placeholder:"Select Coaching Type"})}),(0,a.jsx)(c.C5,{})]})}),"Education"===p&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.boardType`,render:({field:e})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsx)(c.lR,{children:"Board Type"}),(0,a.jsx)(c.MJ,{children:(0,a.jsx)(g,{options:l("Board Type").map(e=>({label:e.name,value:e.name})),value:e.value||[],onChange:t=>e.onChange(t),placeholder:"Select Board Type"})}),(0,a.jsx)(c.C5,{})]})}),(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.medium`,render:({field:e})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsx)(c.lR,{children:"Medium"}),(0,a.jsx)(c.MJ,{children:(0,a.jsx)(g,{options:l("Medium").map(e=>({label:e.name,value:e.name})),value:e.value||[],onChange:t=>e.onChange(t),placeholder:"Select Medium"})}),(0,a.jsx)(c.C5,{})]})}),(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.section`,render:({field:e})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsx)(c.lR,{children:"Section"}),(0,a.jsx)(c.MJ,{children:(0,a.jsx)(g,{options:l("Section").map(e=>({label:e.name,value:e.name})),value:e.value||[],onChange:t=>e.onChange(t),placeholder:"Select Section"})}),(0,a.jsx)(c.C5,{})]})}),(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.subject`,render:({field:e})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsx)(c.lR,{children:"Subject"}),(0,a.jsx)(c.MJ,{children:(0,a.jsx)(g,{options:l("Subject").map(e=>({label:e.name,value:e.name})),value:e.value||[],onChange:t=>e.onChange(t),placeholder:"Select Subject"})}),(0,a.jsx)(c.C5,{})]})})]}),p&&"Education"!==p&&(0,a.jsx)(c.zB,{control:e.control,name:`tuitionDetails.${t}.details`,render:({field:e})=>(0,a.jsxs)(c.eI,{className:"w-full",children:[(0,a.jsxs)(c.lR,{children:[p," Details"]}),(0,a.jsx)(c.MJ,{children:(0,a.jsx)(g,{options:u(p).map(e=>({label:e.name,value:e.name})),value:e.value||[],onChange:t=>e.onChange(t),placeholder:`Select ${p} Details`})}),(0,a.jsx)(c.C5,{})]})})]})]})}function P(){let e=(0,j.wA)(),[t,s]=(0,u.useState)([]),i=(0,j.d4)(e=>e.class.classData),d=(0,r.mN)({resolver:(0,n.u)(z),defaultValues:{tuitionDetails:[{education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}]}}),{fields:p,append:m,remove:x}=(0,r.jz)({control:d.control,name:"tuitionDetails"}),f=async t=>{try{let s={tuitionDetails:t.tuitionDetails.map(e=>({...e,boardType:e.boardType?JSON.stringify(e.boardType):null,subject:e.subject?JSON.stringify(e.subject):null,medium:e.medium?JSON.stringify(e.medium):null,section:e.section?JSON.stringify(e.section):null,details:e.details?JSON.stringify(e.details):null}))};await v.S.post("/classes-profile/tuition-classes",s),await e((0,y.V)(i.id)),l.oR.success("Tuition class details uploaded successfully"),e((0,b.ac)(b._3.TUTIONCLASS)),d.reset({tuitionDetails:[{education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}]}),l.oR.success("Now you can send for review your profile")}catch{l.oR.error("Something went wrong")}},h=async(t,s)=>{try{await v.S.delete(`/classes-profile/tuition-class/${t}`,{data:{classId:s}}),l.oR.success("Tuition class deleted successfully"),await e((0,y.V)(s)),d.reset({tuitionDetails:[{education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}]})}catch{l.oR.error("Failed to delete tuition class")}};return(0,a.jsxs)(a.Fragment,{children:[i?.tuitionClasses?.length>0&&(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Tuition Classes"}),i.tuitionClasses.map((e,t)=>(0,a.jsxs)(C.Zp,{className:"bg-muted/30 relative",children:[(0,a.jsxs)(C.aR,{className:"flex flex-row items-start justify-between",children:[(0,a.jsxs)(C.ZB,{className:"text-base font-semibold",children:["Tuition #",t+1]}),(0,a.jsxs)(S.lG,{children:[(0,a.jsx)(S.zM,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",size:"icon",className:"text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(S.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(S.c7,{children:[(0,a.jsx)(S.L3,{children:"Delete Tuition Class"}),(0,a.jsx)(S.rr,{children:"Are you sure you want to delete this tuition class? This action cannot be undone. All associated time slots will also be deleted."})]}),(0,a.jsxs)(S.Es,{className:"gap-2",children:[(0,a.jsx)(o.$,{variant:"outline",onClick:()=>document.querySelector('button[data-state="open"]')?.click(),children:"Cancel"}),(0,a.jsx)(o.$,{variant:"destructive",onClick:()=>{h(e.id,i.id),document.querySelector('button[data-state="open"]')?.click()},children:"Delete"})]})]})]})]}),(0,a.jsxs)(C.Wu,{className:"space-y-3 text-sm",children:[e.education&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Category:"})," ",e.education]}),e.coachingType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Coaching Type:"})," ",(0,T.Ow)(e.coachingType).join(", ")]}),"Education"===e.education&&(0,a.jsxs)(a.Fragment,{children:[e.boardType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Board:"})," ",(0,T.Ow)(e.boardType).join(", ")]}),e.medium&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Medium:"})," ",(0,T.Ow)(e.medium).join(", ")]}),e.section&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Section:"})," ",(0,T.Ow)(e.section).join(", ")]}),e.subject&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Subject:"})," ",(0,T.Ow)(e.subject).join(", ")]})]}),"Education"!==e.education&&e.details&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Details:"})," ",(0,T.Ow)(e.details).join(", ")]})]})]},t))]}),(0,a.jsx)(c.lV,{...d,children:(0,a.jsxs)("form",{onSubmit:d.handleSubmit(f),className:"space-y-8",children:[p.map((e,s)=>(0,a.jsx)(A,{form:d,index:s,removeTuition:x,constants:t,tuitionFieldsLength:p.length},e.id)),(0,a.jsxs)(o.$,{type:"button",onClick:()=>m({education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}),className:"flex items-center gap-2",children:[(0,a.jsx)(N,{size:18}),"Add Another Tuition"]}),(0,a.jsx)(o.$,{type:"submit",children:"Save Tuition Class Details"})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23546:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\profile\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx","default")},23562:(e,t,s)=>{"use strict";s.d(t,{k:()=>i});var a=s(60687);s(43210);var r=s(25177),n=s(4780);function i({className:e,value:t,...s}){return(0,a.jsx)(r.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,a.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},25177:(e,t,s)=>{"use strict";s.d(t,{C1:()=>y,bL:()=>b});var a=s(43210),r=s(11273),n=s(3416),i=s(60687),l="Progress",[o,c]=(0,r.A)(l),[d,u]=o(l),p=a.forwardRef((e,t)=>{var s,a;let{__scopeProgress:r,value:l=null,max:o,getValueLabel:c=f,...u}=e;(o||0===o)&&!v(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=v(o)?o:100;null===l||j(l,p)||console.error((a=`${l}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=j(l,p)?l:null,x=g(m)?c(m,p):void 0;return(0,i.jsx)(d,{scope:r,value:m,max:p,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":h(m,p),"data-value":m??void 0,"data-max":p,...u,ref:t})})});p.displayName=l;var m="ProgressIndicator",x=a.forwardRef((e,t)=>{let{__scopeProgress:s,...a}=e,r=u(m,s);return(0,i.jsx)(n.sG.div,{"data-state":h(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...a,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function j(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=m;var b=p,y=x},27910:e=>{"use strict";e.exports=require("stream")},28029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(60687),r=s(35950),n=s(85814),i=s.n(n),l=s(16189),o=s(54864),c=s(5336);function d({items:e}){let t=(0,l.usePathname)(),{completedForms:s}=(0,o.d4)(e=>e.formProgress),r=e=>e.toLowerCase().replace(/ & /g,"_").replace(/\s+/g,"_");return(0,a.jsx)("nav",{className:"space-y-1",children:e.map((n,l)=>{let o=r(n.title),d=t===n.href,u=l>0&&!s[r(e[l-1].title)];return(0,a.jsxs)(i(),{href:u?"#":n.href,className:`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${d?"bg-muted text-primary":u?"text-gray-400 cursor-not-allowed":"text-muted-foreground hover:text-primary"}`,onClick:e=>{u&&e.preventDefault()},children:[(0,a.jsx)("span",{children:n.title}),s[o]&&(0,a.jsx)(c.A,{size:16,className:"text-green-500"})]},n.href)})})}var u=s(23562),p=s(43210),m=s(28527);s(36097),s(35817);var x=s(29523),f=s(90269),h=s(46303);let g=[{title:"About",href:"/classes/profile"},{title:"Description",href:"/classes/profile/description"},{title:"Address",href:"/classes/profile/address"},{title:"Photo & Logo",href:"/classes/profile/photo-and-logo"},{title:"Education",href:"/classes/profile/education"},{title:"Experience",href:"/classes/profile/experience"},{title:"Certificates",href:"/classes/profile/certificates"},{title:"Tution Class",href:"/classes/profile/tution-class"}];function v({children:e}){let{completedSteps:t,totalSteps:s}=(0,o.d4)(e=>e.formProgress),{user:n}=function(){let e=(0,o.d4)(e=>e.user.isAuthenticated);return(0,l.useRouter)(),console.log(e),{user:e}}(),{user:i}=(0,o.d4)(e=>e.user);(0,o.wA)();let[c,v]=(0,p.useState)(!1),[j,b]=(0,p.useState)(!1),[y,w]=(0,p.useState)("");if(!n)return null;let N=t/s*100,C=100===Math.round(N),S=async()=>{try{v(!0),await m.S.post(`/classes-profile/send-for-review/${i.id}`),b(!0)}catch(e){console.error("Error sending for review:",e)}finally{v(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.default,{}),(0,a.jsxs)("div",{className:"space-y-6 p-10 pb-4 md:block",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Edit Profile"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Start creating your public profile. Your progress will be automatically saved as you complete each section. You can return at any time to finish your registration."})]}),(0,a.jsx)(u.k,{value:N,className:"h-2"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[Math.round(N),"% complete"]}),C&&(0,a.jsx)("div",{className:"mt-4",children:j?(0,a.jsx)(x.$,{className:"bg-gray-400 text-white cursor-not-allowed",disabled:!0,children:"APPROVED"===y?"Profile Approved ✅":"Profile Sent for Review"}):(0,a.jsx)(x.$,{className:"bg-green-600 hover:bg-green-700 text-white",disabled:c,onClick:S,children:"Send for Review"})}),(0,a.jsx)(r.Separator,{className:"my-6"}),(0,a.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[(0,a.jsx)("aside",{className:"-mx-4 lg:w-1/6 pb-12",children:(0,a.jsx)(d,{items:g})}),(0,a.jsx)("div",{className:"flex justify-center w-full",children:(0,a.jsx)("div",{className:"flex-1 lg:max-w-2xl pb-12",children:e})})]})]}),(0,a.jsx)(h.default,{})]})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35817:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>i,RO:()=>c,Wz:()=>n,sA:()=>l});var a=s(50346),r=s(63766);let n=(e,t)=>{e.contactNo&&t((0,a.ac)(a._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&t((0,a.ac)(a._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&t((0,a.ac)(a._3.PHOTO_LOGO)),e.education?.length>0&&t((0,a.ac)(a._3.EDUCATION)),e.certificates?.length>0&&t((0,a.ac)(a._3.CERTIFICATES)),e.experience?.length>0&&t((0,a.ac)(a._3.EXPERIENCE)),e.tuitionClasses?.length>0&&t((0,a.ac)(a._3.TUTIONCLASS)),e.address&&t((0,a.ac)(a._3.ADDRESS))},i=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch{return[e]}},l=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}},o=new TextEncoder().encode("secret123");async function c(e){try{let{payload:t}=await (0,r.V)(e,o);return t}catch(e){return console.error("Invalid token:",e),null}}},36161:(e,t,s)=>{Promise.resolve().then(s.bind(s,23546))},42123:(e,t,s)=>{"use strict";s.d(t,{b:()=>d});var a=s(43210);s(51215);var r=s(11329),n=s(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,r.TL)(`Primitive.${t}`),i=a.forwardRef((e,a)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(r?s:t,{...i,ref:a})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),l="horizontal",o=["horizontal","vertical"],c=a.forwardRef((e,t)=>{var s;let{decorative:a,orientation:r=l,...c}=e,d=(s=r,o.includes(s))?r:l;return(0,n.jsx)(i.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var d=c},42361:(e,t,s)=>{Promise.resolve().then(s.bind(s,17887)),Promise.resolve().then(s.bind(s,35950))},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>d});var a=s(60687);s(43210);var r=s(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},49561:(e,t,s)=>{"use strict";s.d(t,{TuitionClassForm:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call TuitionClassForm() from the server but TuitionClassForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx","TuitionClassForm")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59863:()=>{},61170:(e,t,s)=>{"use strict";s.d(t,{b:()=>d});var a=s(43210);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}s(51215);var n=s(60687),i=Symbol("radix.slottable");function l(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let s=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:s,...n}=e;if(a.isValidElement(s)){var i;let e,l;let o=(i=s,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let s={...t};for(let a in t){let r=e[a],n=t[a];/^on[A-Z]/.test(a)?r&&n?s[a]=(...e)=>{n(...e),r(...e)}:r&&(s[a]=r):"style"===a?s[a]={...r,...n}:"className"===a&&(s[a]=[r,n].filter(Boolean).join(" "))}return{...e,...s}}(n,s.props);return s.type!==a.Fragment&&(c.ref=t?function(...e){return t=>{let s=!1,a=e.map(e=>{let a=r(e,t);return s||"function"!=typeof a||(s=!0),a});if(s)return()=>{for(let t=0;t<a.length;t++){let s=a[t];"function"==typeof s?s():r(e[t],null)}}}}(t,o):o),a.cloneElement(s,c)}return a.Children.count(s)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=a.forwardRef((e,s)=>{let{children:r,...i}=e,o=a.Children.toArray(r),c=o.find(l);if(c){let e=c.props.children,r=o.map(t=>t!==c?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,n.jsx)(t,{...i,ref:s,children:r})});return s.displayName=`${e}.Slot`,s}(`Primitive.${t}`),i=a.forwardRef((e,a)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(r?s:t,{...i,ref:a})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),c=a.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},62129:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["classes",{children:["profile",{children:["tution-class",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9768)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23546)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/classes/profile/tution-class/page",pathname:"/classes/profile/tution-class",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>m,L3:()=>x,c7:()=>p,lG:()=>l,rr:()=>f,zM:()=>o});var a=s(60687);s(43210);var r=s(6491),n=s(11860),i=s(4780);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,...s}){return(0,a.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,a.jsx)(d,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function f({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80942:(e,t,s)=>{"use strict";s.d(t,{lV:()=>d,MJ:()=>g,Rr:()=>v,zB:()=>p,eI:()=>f,lR:()=>h,C5:()=>j});var a=s(60687),r=s(43210),n=s(11329),i=s(27605),l=s(4780),o=s(61170);function c({className:e,...t}){return(0,a.jsx)(o.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let d=i.Op,u=r.createContext({}),p=({...e})=>(0,a.jsx)(u.Provider,{value:{name:e.name},children:(0,a.jsx)(i.xI,{...e})}),m=()=>{let e=r.useContext(u),t=r.useContext(x),{getFieldState:s}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),n=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},x=r.createContext({});function f({className:e,...t}){let s=r.useId();return(0,a.jsx)(x.Provider,{value:{id:s},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...t})})}function h({className:e,...t}){let{error:s,formItemId:r}=m();return(0,a.jsx)(c,{"data-slot":"form-label","data-error":!!s,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:r,...t})}function g({...e}){let{error:t,formItemId:s,formDescriptionId:r,formMessageId:i}=m();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?`${r} ${i}`:`${r}`,"aria-invalid":!!t,...e})}function v({className:e,...t}){let{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}function j({className:e,...t}){let{error:s,formMessageId:r}=m(),n=s?String(s?.message??""):t.children;return n?(0,a.jsx)("p",{"data-slot":"form-message",id:r,className:(0,l.cn)("text-destructive text-sm",e),...t,children:n}):null}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},94990:(e,t,s)=>{Promise.resolve().then(s.bind(s,28029))}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,8721,2105,9191,3766,9528,2800,7200],()=>s(62129));module.exports=a})();