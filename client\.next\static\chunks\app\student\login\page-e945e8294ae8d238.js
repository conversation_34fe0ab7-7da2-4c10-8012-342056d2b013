(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8504],{24031:(e,t,a)=>{Promise.resolve().then(a.bind(a,25458))},25458:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>R});var s=a(95155),r=a(70347),l=a(7583),o=a(12115),n=a(30285),i=a(55594),c=a(62177),d=a(90221),m=a(10351),u=a(62523),f=a(55365),x=a(85339),g=a(51154),h=a(75937),N=a(56671),b=a(60723),p=a(35695),j=a(60760),v=a(19320);let y=i.z.object({contactNo:i.z.string().optional().refine(e=>!e||/^\d{10}$/.test(e),"Please enter a valid 10-digit mobile number"),email:i.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}).refine(e=>!e.contactNo&&!e.email||!!e.contactNo||!!e.email,{message:"Either mobile number or email is required",path:["contactNo","email"]}),w=i.z.object({firstName:i.z.string().min(2,"First name is required").regex(/^[a-zA-Z]+$/,"Invalid first name"),lastName:i.z.string().min(2,"Last name is required").regex(/^[a-zA-Z]+$/,"Invalid last name"),contactNo:i.z.string().regex(/^\d{10}$/,"Please enter a valid 10-digit mobile number"),referralCode:i.z.string().optional(),email:i.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}),C=e=>{let{message:t}=e;return t?(0,s.jsxs)(f.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)(f.TN,{className:"text-red-500",children:t})]}):null};function S(){let e=(0,p.useRouter)(),t=(0,p.useSearchParams)(),[a,i]=(0,o.useState)(!0),[f,x]=(0,o.useState)(!1),[S,R]=(0,o.useState)(""),[P,I]=(0,o.useState)(null),[z,k]=(0,o.useState)("mobile"),[E,F]=(0,o.useState)(!1),[T,U]=(0,o.useState)(null),A=(0,c.mN)({resolver:(0,d.u)(y),defaultValues:{contactNo:"",email:""},mode:"onChange"}),O=(0,c.mN)({resolver:(0,d.u)(w),defaultValues:{firstName:"",lastName:"",contactNo:"",referralCode:t.get("ref")||localStorage.getItem("referralCode")||"",email:""},mode:"onChange"});(0,o.useEffect)(()=>{localStorage.getItem("user")&&(e.push("/"),setTimeout(()=>{N.oR.error("You are already logged in as a tutor. Please logout first to login as student.")},500))},[e]),(0,o.useEffect)(()=>{localStorage.getItem("studentToken")&&(e.push("/"),setTimeout(()=>{N.oR.error("You are already logged in as a student.")},500))},[e]),(0,o.useEffect)(()=>{let e=t.get("ref");e&&(I(e),localStorage.setItem("referralCode",e),O.setValue("referralCode",e))},[t,O]),(0,o.useEffect)(()=>{a?(A.reset({contactNo:"mobile"===z?A.getValues().contactNo:"",email:"email"===z?A.getValues().email:""}),A.setFocus("mobile"===z?"contactNo":"email")):(O.reset({firstName:"",lastName:"",contactNo:"",referralCode:P||"",email:""}),O.setFocus("firstName"))},[a,z,E,P,A,O]);let $=async(a,s)=>{x(!0),R("");try{if(s){let r=await (0,b.bZ)({contactNo:s,email:a});if(!1===r.success){R(r.message||"Authentication failed"),N.oR.error(r.message||"Authentication failed");return}let l=t.get("redirect")||"",o=l?"&redirect=".concat(encodeURIComponent(l)):"";e.push("/student-verify-otp?contactNo=".concat(s,"&flow=login&email=").concat(encodeURIComponent(a)).concat(o)),N.oR.success("OTP sent successfully. Please check your phone."),A.reset({contactNo:"",email:""}),F(!1),U(null)}else{let s=await (0,b.iM)({email:a});if(!1===s.success){R(s.message||"Email check failed"),N.oR.error(s.message||"Email check failed");return}if(U(s.data),F(s.data.isOldUser),A.setValue("contactNo",s.data.contactNo||""),A.setValue("email",a),A.trigger(),!s.data.isOldUser&&s.data.otpSent){let r=t.get("redirect")||"",l=r?"&redirect=".concat(encodeURIComponent(r)):"";e.push("/student-verify-otp?contactNo=".concat(s.data.contactNo,"&flow=login&email=").concat(encodeURIComponent(a)).concat(l)),N.oR.success("OTP sent successfully. Please check your phone."),A.reset({contactNo:"",email:""}),F(!1),U(null)}else s.data.isOldUser&&N.oR.info("This email is not linked to a mobile number. Please enter a mobile number to proceed.")}}catch(t){var r,l;let e=(null==t?void 0:null===(l=t.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||"Something went wrong";R(e),N.oR.error(e)}finally{x(!1)}},M=async a=>{x(!0),R("");try{if("email"===z)await $(a.email,E?a.contactNo:void 0);else if("mobile"===z){let s=await (0,b.bZ)({contactNo:a.contactNo});if(!1===s.success){R(s.message||"Authentication failed"),N.oR.error(s.message||"Authentication failed");return}let r=t.get("redirect")||"",l=r?"&redirect=".concat(encodeURIComponent(r)):"";e.push("/student-verify-otp?contactNo=".concat(a.contactNo,"&flow=login").concat(l)),N.oR.success("OTP sent successfully. Please check your phone."),A.reset({contactNo:"",email:""}),F(!1),U(null)}}catch(t){var s,r;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.message)||"Something went wrong";R(e),N.oR.error(e)}finally{x(!1)}},B=async a=>{x(!0),R("");try{let s={...a,...P?{referralCode:P}:{}},r=await (0,b.zy)(s);if(!1===r.success){R(r.message||"Authentication failed"),N.oR.error(r.message||"Authentication failed");return}let l=t.get("redirect")||"",o=l?"&redirect=".concat(encodeURIComponent(l)):"";e.push("/student-verify-otp?contactNo=".concat(a.contactNo,"&flow=register&firstName=").concat(encodeURIComponent(a.firstName),"&lastName=").concat(encodeURIComponent(a.lastName)).concat(a.referralCode?"&referralCode=".concat(encodeURIComponent(a.referralCode)):"").concat(a.email?"&email=".concat(encodeURIComponent(a.email)):"").concat(o)),N.oR.success("OTP sent successfully. Please check your phone."),O.reset({firstName:"",lastName:"",contactNo:"",referralCode:"",email:""}),localStorage.removeItem("referralCode"),I(null),F(!1),U(null)}catch(t){var s,r;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.message)||"Something went wrong";R(e),N.oR.error(e)}finally{x(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.default,{}),(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4",children:(0,s.jsx)("p",{className:"text-center text-orange-700 font-medium",children:a?"Student Login Portal":"Student Registration Portal"})}),(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)(n.$,{variant:a?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat(a?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{i(!0),R(""),k("mobile"),F(!1),U(null),A.reset({contactNo:"",email:""})},children:"Student Login"}),(0,s.jsx)(n.$,{variant:a?"ghost":"default",className:"px-4 py-2 rounded-lg ".concat(a?"text-gray-600 hover:text-[#ff914d]":"bg-[#ff914d] text-white hover:bg-[#ff914d]/90"),onClick:()=>{i(!1),R(""),k("mobile"),F(!1),U(null),O.reset({firstName:"",lastName:"",contactNo:"",referralCode:P||"",email:""})},children:"Student Sign Up"})]})}),a&&(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)(n.$,{variant:"mobile"===z?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat("mobile"===z?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{k("mobile"),R(""),F(!1),U(null),A.reset({contactNo:"",email:""})},children:"Mobile"}),(0,s.jsx)(n.$,{variant:"email"===z?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat("email"===z?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{k("email"),R(""),F(!1),U(null),A.reset({contactNo:"",email:""})},children:"Email"})]})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:a?"Welcome Back to Your Student Portal":"Register as a Student"}),(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 mr-3"}),(0,s.jsx)("span",{className:"text-[#ff914d] font-medium",children:"STUDENT PORTAL"}),(0,s.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 ml-3"})]}),P&&!a&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-700 text-center",children:["\uD83C\uDF89 You' joining via referral code: ",(0,s.jsx)("span",{className:"font-semibold",children:P})]})})]}),(0,s.jsx)("div",{children:a?(0,s.jsx)(h.lV,{...A,children:(0,s.jsxs)("form",{onSubmit:A.handleSubmit(M),className:"space-y-6",children:[S&&(0,s.jsx)(C,{message:S}),"email"===z&&(0,s.jsx)(h.zB,{control:A.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{className:"text-gray-700 font-medium",children:"Email"}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.pHD,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{type:"email",placeholder:"Enter email address",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),A.formState.touchedFields.email&&(0,s.jsx)(h.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(j.N,{children:("mobile"===z||E)&&(0,s.jsx)(v.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,s.jsx)(h.zB,{control:A.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...t,disabled:!!(null==T?void 0:T.contactNo)&&!E&&"email"===z,autoFocus:E})]})}),A.formState.touchedFields.contactNo&&(0,s.jsx)(h.C5,{className:"text-red-500"})]})}})})}),(0,s.jsx)(n.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:f,children:f?(0,s.jsx)(g.A,{className:"h-5 w-5 animate-spin"}):"email"!==z||E?"Send OTP to Login":"Check Email"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,s.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})}):(0,s.jsx)(h.lV,{...O,children:(0,s.jsxs)("form",{onSubmit:O.handleSubmit(B),className:"space-y-6",children:[S&&(0,s.jsx)(C,{message:S}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsx)(h.zB,{control:O.control,name:"firstName",render:e=>{let{field:t}=e;return(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{className:"text-gray-700 font-medium",children:"First Name"}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{placeholder:"First Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),O.formState.touchedFields.firstName&&(0,s.jsx)(h.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(h.zB,{control:O.control,name:"lastName",render:e=>{let{field:t}=e;return(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{className:"text-gray-700 font-medium",children:"Last Name"}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{placeholder:"Last Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),O.formState.touchedFields.lastName&&(0,s.jsx)(h.C5,{className:"text-red-500"})]})}})]}),(0,s.jsx)(h.zB,{control:O.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...t})]})}),O.formState.touchedFields.contactNo&&(0,s.jsx)(h.C5,{className:"text-red-500"})]})}}),P&&(0,s.jsx)(h.zB,{control:O.control,name:"referralCode",render:e=>{let{field:t}=e;return(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{className:"text-gray-700 font-medium",children:"Referral Code"}),(0,s.jsx)(h.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(u.p,{placeholder:"Referral Code",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t,disabled:!0})})}),O.formState.touchedFields.referralCode&&(0,s.jsx)(h.C5,{className:"text-red-500"})]})}}),(0,s.jsx)(n.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:f,children:f?(0,s.jsx)(g.A,{className:"h-5 w-5 animate-spin"}):"Send OTP to Register"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,s.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})})})]})}),(0,s.jsx)(l.default,{})]})}function R(){return(0,s.jsx)(o.Suspense,{fallback:(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 mx-auto mb-4 rounded"}),(0,s.jsx)("div",{className:"bg-gray-200 h-8 w-full mb-4 rounded"}),(0,s.jsx)("div",{className:"bg-gray-200 h-12 w-full rounded"})]})})})}),children:(0,s.jsx)(S,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,844,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1342,5433,347,9216,8441,1684,7358],()=>t(24031)),_N_E=e.O()}]);