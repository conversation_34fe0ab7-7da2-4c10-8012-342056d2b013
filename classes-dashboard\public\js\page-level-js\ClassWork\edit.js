/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/ClassWork/resources/views/js/edit.js ***!
  \******************************************************/
$("#editclassWorks_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandler(form, editClassWorkRoute.update, 'PATCH', '#editclassWorks_form', '#saveClassWorks', '#newClassWorkEntry', '#classWork_table');
    return false;
  }
});
/******/ })()
;