"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5881],{7583:(e,t,a)=>{a.d(t,{default:()=>n});var s=a(95155);a(12115);var l=a(6874),r=a.n(l),i=a(66766),o=a(29911);let n=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(r(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:o.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:o.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:o.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:o.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:o.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:o.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:o.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:l}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(r(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:l,children:(0,s.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},l)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(r(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(r(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(r(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(r(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},36754:(e,t,a)=>{a.d(t,{$5:()=>o,BU:()=>l,c5:()=>n,cc:()=>c,dZ:()=>i,sq:()=>r});var s=a(55077);let l=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{return(await s.S.get("/blogs/approved",{params:{page:e,limit:t}})).data}catch(e){var a,l;throw Error((null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)||"Failed to fetch approved blogs: ".concat(e.message))}},r=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0;try{return(await s.S.get("/blogs/my-blogs",{params:{page:e,limit:t,status:a}})).data}catch(e){var l,r;throw Error((null===(r=e.response)||void 0===r?void 0:null===(l=r.data)||void 0===l?void 0:l.message)||"Failed to fetch your blogs: ".concat(e.message))}},i=async e=>{try{return(await s.S.get("/blogs/".concat(e))).data.data}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to fetch blog: ".concat(e.message))}},o=async e=>{try{let t=new FormData;return t.append("blogTitle",e.blogTitle),t.append("blogDescription",e.blogDescription),e.blogImage&&t.append("blogImage",e.blogImage),(await s.S.post("/blogs",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to create blog: ".concat(e.message))}},n=async(e,t)=>{try{let a=new FormData;return t.blogTitle&&a.append("blogTitle",t.blogTitle),t.blogDescription&&a.append("blogDescription",t.blogDescription),t.blogImage&&a.append("blogImage",t.blogImage),t.status&&a.append("status",t.status),(await s.S.put("/blogs/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,l;throw Error((null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)||"Failed to update blog: ".concat(e.message))}},c=async e=>{try{await s.S.delete("/blogs/".concat(e))}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to delete blog: ".concat(e.message))}}},54568:(e,t,a)=>{a.d(t,{A:()=>h});var s=a(95155);a(12115);var l=a(6874),r=a.n(l),i=a(66766),o=a(30285),n=a(23562),c=a(71007),d=a(5040);let h=e=>{let{blog:t}=e,a="http://localhost:4005/".replace(/\/$/,""),l=t.blogImage?t.blogImage.startsWith("/")?t.blogImage:"/".concat(t.blogImage):"",h=t.blogImage?"".concat(a).concat(l):"",m=new Date(t.createdAt).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"});return(0,s.jsxs)("div",{className:"h-full overflow-hidden flex flex-col group rounded-xl bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsxs)("div",{className:"relative h-52 w-full overflow-hidden rounded-t-xl",children:[t.blogImage?(0,s.jsxs)("div",{className:"relative w-full h-full",children:[(0,s.jsx)(i.default,{src:h,alt:t.blogTitle,className:"object-cover transition-transform duration-500 group-hover:scale-110",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",priority:!0}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}):(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-400 dark:text-gray-500 text-sm",children:"No image available"})}),(0,s.jsxs)("div",{className:"absolute top-3 right-3 bg-white/90 dark:bg-gray-800/90 text-xs font-medium py-1.5 px-3 rounded-full shadow-md backdrop-blur-sm flex items-center gap-1.5",children:[(0,s.jsx)(n.A,{className:"w-3.5 h-3.5 text-[#FD904B]"}),m]})]}),(0,s.jsxs)("div",{className:"p-6 flex flex-col flex-grow",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-3 line-clamp-2 group-hover:text-[#FD904B] transition-colors duration-300",children:t.blogTitle}),t.class&&(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(c.A,{className:"w-3.5 h-3.5 text-[#FD904B]"}),(0,s.jsxs)("span",{children:[t.class.firstName," ",t.class.lastName]})]}),(0,s.jsx)("span",{className:"text-gray-300",children:"•"}),(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(d.A,{className:"w-3.5 h-3.5 text-[#FD904B]"}),(0,s.jsx)("span",{children:t.class.className})]})]}),(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)("p",{className:"text-muted-foreground line-clamp-3 text-sm leading-relaxed",children:(e=>{let t=e.replace(/<[^>]*>/g,"");return t.length>100?t.substring(0,100)+"...":t})(t.blogDescription)}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white dark:from-gray-900 to-transparent"})]}),(0,s.jsx)("div",{className:"mt-auto pt-2",children:(0,s.jsx)(r(),{href:"/blogs/".concat(t.id),passHref:!0,children:(0,s.jsx)(o.$,{variant:"outline",className:"w-full bg-transparent border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B] hover:text-white transition-all duration-300 font-medium",children:"Read More"})})})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FD904B] to-[#FD904B]/60"})]})}}}]);