(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3612],{59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var r=a(95155);a(12115);var s=a(79899),l=a(66474),n=a(5196),o=a(47863),i=a(59434);function d(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[n,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:l="popper",...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...n,children:[(0,r.jsx)(h,{}),(0,r.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(g,{})]})})}function x(e){let{className:t,children:a,...l}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.A,{className:"size-4"})})}},61286:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>B});var r=a(95155),s=a(12115),l=a(90221),n=a(62177),o=a(55594),i=a(56671),d=a(35695),c=a(7632),u=a(69074),m=a(84355),x=a(5196),h=a(54416),g=a(29869),p=a(57434),f=a(34540),v=a(56762),b=a(93457),j=a(75937),y=a(62523),N=a(30285),w=a(88539),k=a(59409),C=a(66695),z=a(14636),M=a(85511),R=a(59434),S=a(66766),P=a(70347);let I=o.z.object({firstName:o.z.string().min(2,"First name must be at least 2 characters."),lastName:o.z.string().min(2,"Last name must be at least 2 characters."),contact:o.z.string().min(10,"Contact number must be at least 10 digits.").max(15,"Contact number must not exceed 15 digits.").regex(/^\d+$/,"Contact number must contain only digits."),medium:o.z.string().min(1,"Medium of instruction is required"),classroom:o.z.string().min(1,"Classroom standard is required"),school:o.z.string().min(2,"School name must be at least 2 characters."),birthday:o.z.date({required_error:"Please select your birthday"}),address:o.z.string().min(5,"Address must be at least 5 characters."),photo:o.z.any().optional(),document:o.z.any().optional()}),A=()=>{var e;let t=(0,d.useRouter)(),a=(0,f.wA)(),o=(0,d.useSearchParams)(),A="true"===o.get("quiz"),B=o.get("examId"),[D,F]=(0,s.useState)(null),[V,E]=(0,s.useState)(!1),[T,W]=(0,s.useState)(!1),[_,U]=(0,s.useState)(null),[J,L]=(0,s.useState)(null),[O,q]=(0,s.useState)(!1),{profileData:Y,loading:$}=(0,f.d4)(e=>e.studentProfile),Z=(null==Y?void 0:Y.profile)||null,G=(null==Y?void 0:Y.classroomOptions)||[],H=(0,s.useRef)(null),X=(0,s.useRef)(null),K=(0,n.mN)({resolver:(0,l.u)(I),defaultValues:{firstName:"",lastName:"",contact:"",medium:"",classroom:"",birthday:void 0,school:"",address:""},mode:"onSubmit"});(0,s.useEffect)(()=>{localStorage.getItem("studentToken")||(i.oR.error("Please login to access your profile"),t.push("/"))},[t]),(0,s.useEffect)(()=>{localStorage.getItem("studentToken")&&a((0,v.N)())},[a]),(0,s.useEffect)(()=>{if(V||!Y)return;let e=Y.profile,t=(null==e?void 0:e.student)||JSON.parse(localStorage.getItem("student_data")||"{}"),a={firstName:(null==t?void 0:t.firstName)||"",lastName:(null==t?void 0:t.lastName)||"",contact:(null==t?void 0:t.contact)||"",medium:(null==e?void 0:e.medium)||"",classroom:(null==e?void 0:e.classroom)||"",birthday:(null==e?void 0:e.birthday)?new Date(e.birthday):void 0,school:(null==e?void 0:e.school)||"",address:(null==e?void 0:e.address)||""};if((null==e?void 0:e.photo)&&!D&&(F(e.photo),K.setValue("photo",e.photo)),(null==e?void 0:e.documentUrl)&&!J&&!O){let t=e.documentUrl.startsWith("http")?e.documentUrl:"".concat("http://localhost:4005/").concat(e.documentUrl),a={name:t.split("/").pop()||"Uploaded Document",size:0,url:t,type:"application/octet-stream"};L(a),K.setValue("document",a)}let r=K.getValues(),s=!r.firstName&&!r.lastName&&!r.contact,l=!r.medium||!r.classroom;(s||l)&&K.reset(a)},[Y,K,V,D,J,O]);let Q=async()=>{U(null);try{var e;if(!(null===(e=navigator.mediaDevices)||void 0===e?void 0:e.getUserMedia))throw Error("Camera not supported on this device");E(!0);let t=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});H.current&&(H.current.srcObject=t,H.current.onloadedmetadata=()=>{var e;null===(e=H.current)||void 0===e||e.play().catch(()=>i.oR.error("Error starting camera preview"))})}catch(t){E(!1);let e="NotAllowedError"===t.name?"Please allow camera access in your browser settings.":"Could not access camera. Please check your camera settings.";U(e),i.oR.error(e)}},ee=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:800,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.6;if(!e.getContext("2d"))return"";let r=e.width,s=e.height,l=r,n=s;r>t&&(l=t,n=s*t/r);let o=document.createElement("canvas");o.width=l,o.height=n;let i=o.getContext("2d");return i?(i.drawImage(e,0,0,l,n),o.toDataURL("image/jpeg",a)):""},et=()=>{if(!H.current||!X.current)return;let e=H.current,t=X.current,r=t.getContext("2d");t.width=e.videoWidth,t.height=e.videoHeight,null==r||r.clearRect(0,0,t.width,t.height),null==r||r.save(),null==r||r.scale(-1,1),null==r||r.drawImage(e,-t.width,0,t.width,t.height),null==r||r.restore();let s=ee(t,800,.6);if(3*s.split(",")[1].length/4/1024>5120){i.oR.error("Photo size exceeds 5MB limit. Please try again.");return}F(s),K.setValue("photo",s),a((0,b.XY)(s)),ea()},ea=()=>{var e;(null===(e=H.current)||void 0===e?void 0:e.srcObject)&&(H.current.srcObject.getTracks().forEach(e=>e.stop()),H.current.srcObject=null),E(!1),U(null)},er=()=>{J&&"url"in J&&J.url.startsWith("blob:")&&URL.revokeObjectURL(J.url),L(null),q(!0);let e=document.getElementById("document");e&&(e.value=""),K.setValue("document",null)},es=e=>e<1024?e+" bytes":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",el=s.useMemo(()=>{var e;let t=!!(D||(null==Y?void 0:null===(e=Y.profile)||void 0===e?void 0:e.photo)),a=!!J&&!O;return!!(K.getValues().firstName&&K.getValues().lastName&&K.getValues().contact&&K.getValues().medium&&K.getValues().classroom&&K.getValues().birthday&&K.getValues().school&&K.getValues().address&&t&&a)},[K,D,J,null==Y?void 0:null===(e=Y.profile)||void 0===e?void 0:e.photo,O]),en=async e=>{W(!0);try{var r,s,l,n,o,d,c;if(!(D||(null==Y?void 0:null===(r=Y.profile)||void 0===r?void 0:r.photo))){i.oR.error("Please capture a photo for your profile"),W(!1);return}if(!J||O){i.oR.error("Identity document is required. Please upload a document."),W(!1);return}if(!await K.trigger()){i.oR.error("Please fill in all required fields correctly"),W(!1);return}let u={firstName:e.firstName,lastName:e.lastName,contact:e.contact,medium:e.medium,classroom:e.classroom,birthday:(null===(s=e.birthday)||void 0===s?void 0:s.toISOString())||"",school:e.school,address:e.address};if(null==D?void 0:D.startsWith("data:")){let e=D.split(",")[1];if(3*e.length/4/1024>5120){i.oR.error("Photo size exceeds 5MB limit.");return}u.photo=e,u.photoMimeType="image/jpeg"}if(J instanceof File||J&&"url"in J&&J.url.startsWith("blob:")){let e=J instanceof File?J:await fetch(J.url).then(e=>e.blob()).then(e=>new File([e],J.name,{type:J.type})),t=await new Promise((t,a)=>{let r=new FileReader;r.onload=()=>t(r.result.split(",")[1]),r.onerror=a,r.readAsDataURL(e)});if(3*t.length/4/1024>5120){i.oR.error("Document size exceeds 5MB limit.");return}u.document=t,u.documentMimeType=e.type,u.documentName=e.name}if(O&&(null==Y?void 0:null===(l=Y.profile)||void 0===l?void 0:l.documentUrl)&&(u.removeDocument=!0),!localStorage.getItem("studentToken")){i.oR.error("Please login to submit your profile"),t.push("/");return}let m=await a((0,v.A)(u));if("fulfilled"===m.meta.requestStatus){i.oR.success("Profile ".concat(Z?"updated":"created"," successfully!"));let r=JSON.parse(localStorage.getItem("student_data")||"{}"),s={...r,id:r.id||(null==Y?void 0:null===(o=Y.profile)||void 0===o?void 0:null===(n=o.student)||void 0===n?void 0:n.id)||"",firstName:e.firstName,lastName:e.lastName,email:r.email||(null==Y?void 0:null===(c=Y.profile)||void 0===c?void 0:null===(d=c.student)||void 0===d?void 0:d.email)||"",contact:e.contact};localStorage.setItem("student_data",JSON.stringify(s)),q(!1),await a((0,v.N)()),A?B?t.push("/uwhiz-exam/".concat(B)):t.push("/mock-test"):t.push("/")}else if("rejected"===m.meta.requestStatus){let e=m.payload;e.includes("401")||e.includes("Unauthorized")?(i.oR.error("Your session has expired. Please login again."),localStorage.removeItem("studentToken"),t.push("/")):i.oR.error(e||"Failed to update profile")}}catch(e){i.oR.error("Failed to submit profile information")}finally{W(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(P.default,{}),(0,r.jsx)("div",{className:"min-h-screen py-12",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 max-w-3xl",children:(0,r.jsxs)("div",{className:"bg-white shadow-xl rounded-2xl overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-black p-6 sm:p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Student Profile"}),(0,r.jsx)("p",{className:"text-white/80 mt-2",children:"Complete your profile information"})]}),(0,r.jsx)("div",{className:"p-6 sm:p-8",children:$?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,r.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading profile information..."})]}):(0,r.jsx)(j.lV,{...K,children:(0,r.jsxs)("form",{onSubmit:K.handleSubmit(en),className:"space-y-8",children:[(0,r.jsxs)(C.Zp,{className:"shadow-sm",children:[(0,r.jsxs)(C.aR,{children:[(0,r.jsx)(C.ZB,{className:"text-lg font-medium",children:"Personal Information"}),(0,r.jsx)(C.BT,{children:"Update your personal details"})]}),(0,r.jsxs)(C.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(j.zB,{control:K.control,name:"firstName",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"First Name"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(y.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your first name"})}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(j.zB,{control:K.control,name:"lastName",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Last Name"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(y.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your last name"})}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}})]}),(()=>{var e,t;let a=null==Y?void 0:null===(t=Y.profile)||void 0===t?void 0:null===(e=t.student)||void 0===e?void 0:e.email;return a?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Email"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 border border-gray-200 rounded-md",children:(0,r.jsx)("p",{className:"text-gray-700",children:a})}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Your email address cannot be changed"})]}):null})(),(0,r.jsx)(j.zB,{control:K.control,name:"contact",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Contact Number"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(y.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your contact number",type:"tel",inputMode:"numeric",pattern:"[0-9]*",onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:e=>{let a=e.target.value.replace(/\D/g,"");t.onChange(a)}})}),(0,r.jsx)(j.Rr,{className:"text-xs text-gray-500",children:"Your contact number will be used for important notifications"}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(j.zB,{control:K.control,name:"birthday",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{className:"flex flex-col",children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Birthday"}),(0,r.jsxs)(z.AM,{children:[(0,r.jsx)(z.Wv,{asChild:!0,children:(0,r.jsx)(j.MJ,{children:(0,r.jsxs)(N.$,{variant:"outline",className:(0,R.cn)("w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg",!t.value&&"text-muted-foreground"),children:[t.value&&t.value instanceof Date&&!isNaN(t.value.getTime())?(0,c.GP)(t.value,"PPP"):(0,r.jsx)("span",{children:"Select your birthday"}),(0,r.jsx)(u.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,r.jsxs)(z.hl,{className:"w-auto p-0 bg-white border border-gray-300 shadow-lg",align:"start",children:[(0,r.jsx)("div",{className:"p-3 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,r.jsxs)(k.l6,{value:t.value?t.value.getFullYear().toString():"",onValueChange:e=>{let a=new Date(t.value||new Date);a.setFullYear(parseInt(e)),t.onChange(a)},children:[(0,r.jsx)(k.bq,{className:"w-24",children:(0,r.jsx)(k.yv,{placeholder:"Year"})}),(0,r.jsx)(k.gC,{className:"max-h-48",children:Array.from({length:125},(e,t)=>{let a=new Date().getFullYear()-t;return(0,r.jsx)(k.eb,{value:a.toString(),children:a},a)})})]}),(0,r.jsxs)(k.l6,{value:t.value?t.value.getMonth().toString():"",onValueChange:e=>{let a=new Date(t.value||new Date);a.setMonth(parseInt(e)),t.onChange(a)},children:[(0,r.jsx)(k.bq,{className:"w-32",children:(0,r.jsx)(k.yv,{placeholder:"Month"})}),(0,r.jsx)(k.gC,{children:["January","February","March","April","May","June","July","August","September","October","November","December"].map((e,t)=>(0,r.jsx)(k.eb,{value:t.toString(),children:e},t))})]})]})}),(0,r.jsx)(M.V,{mode:"single",selected:t.value,onSelect:t.onChange,disabled:e=>e>new Date||e<new Date("1900-01-01"),month:t.value||new Date,className:"rounded-md border-0"})]})]}),(0,r.jsx)(j.Rr,{className:"text-xs text-gray-500",children:"Your date of birth will be verified with your documents"}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(j.zB,{control:K.control,name:"address",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Address"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(w.T,{...t,rows:3,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none",placeholder:"Enter your full address"})}),(0,r.jsx)(j.Rr,{className:"text-xs text-gray-500",children:"Provide your complete residential address"}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(j.zB,{control:K.control,name:"school",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"School Name"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(y.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter your school name"})}),(0,r.jsx)(j.Rr,{className:"text-xs text-gray-500",children:"Enter the full name of your school or educational institution"}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}})]})]}),(0,r.jsxs)(C.Zp,{className:"shadow-sm",children:[(0,r.jsxs)(C.aR,{children:[(0,r.jsx)(C.ZB,{className:"text-lg font-medium",children:"Educational Information"}),(0,r.jsx)(C.BT,{children:"Select your medium of instruction and classroom standard"})]}),(0,r.jsx)(C.Wu,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(j.zB,{control:K.control,name:"medium",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Medium of Instruction"}),(0,r.jsxs)(k.l6,{onValueChange:e=>{t.onChange(e)},value:t.value||void 0,children:[(0,r.jsx)(j.MJ,{children:(0,r.jsx)(k.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,r.jsx)(k.yv,{placeholder:"Select Medium"})})}),(0,r.jsxs)(k.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,r.jsx)(k.eb,{value:"english",children:"English"}),(0,r.jsx)(k.eb,{value:"gujarati",children:"Gujarati"})]})]}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(j.zB,{control:K.control,name:"classroom",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{className:"text-black font-medium",children:"Classroom (Standard)"}),(0,r.jsxs)(k.l6,{onValueChange:e=>{t.onChange(e)},value:t.value||void 0,children:[(0,r.jsx)(j.MJ,{children:(0,r.jsx)(k.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,r.jsx)(k.yv,{placeholder:"Select Classroom"})})}),(0,r.jsx)(k.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:$?(0,r.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}):G.length>0?G.map(e=>(0,r.jsx)(k.eb,{value:e.value,children:e.value},e.id)):(0,r.jsx)("div",{className:"p-2 text-center text-gray-500",children:"No classroom options available"})})]}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})}})]})})]}),(0,r.jsx)(j.zB,{control:K.control,name:"photo",render:()=>{var e;return(0,r.jsxs)(C.Zp,{className:"shadow-sm",children:[(0,r.jsxs)(C.aR,{children:[(0,r.jsx)(C.ZB,{className:"text-lg font-medium",children:"Profile Photo"}),(0,r.jsx)(C.BT,{children:"Take a clear photo of your face for your profile (MAX. 5MB)"})]}),(0,r.jsx)(C.Wu,{children:(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.MJ,{children:(0,r.jsxs)("div",{children:[_&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-700 text-sm",children:_})}),!V&&!D&&(0,r.jsxs)(N.$,{type:"button",onClick:Q,className:"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Open Camera"]}),V&&(0,r.jsxs)("div",{className:"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm",children:[(0,r.jsx)("video",{ref:H,autoPlay:!0,playsInline:!0,className:"w-full h-auto transform scale-x-[-1]"}),(0,r.jsxs)("div",{className:"flex p-4 bg-gray-50",children:[(0,r.jsxs)(N.$,{type:"button",onClick:et,variant:"default",className:"flex-1 mr-2 bg-black hover:bg-gray-800 text-white",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Capture"]}),(0,r.jsxs)(N.$,{type:"button",onClick:ea,variant:"outline",className:"flex-1 border-gray-300",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Cancel"]})]})]}),!V&&((null==Y?void 0:null===(e=Y.profile)||void 0===e?void 0:e.photo)||D)&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4",children:[(0,r.jsx)("div",{className:"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full",children:(0,r.jsx)("div",{className:"flex justify-center",children:(()=>{var e;let t=D||(null==Y?void 0:null===(e=Y.profile)||void 0===e?void 0:e.photo);return t?(0,r.jsx)(S.default,{src:t.startsWith("data:")?t:t.startsWith("http")?t:"".concat("http://localhost:4005/").concat(t,"?t=").concat(new Date().getTime()),alt:"Student Photo",height:1e3,width:1e3,className:"max-w-full max-h-80 object-contain rounded-lg",style:{height:"auto",width:"auto"},unoptimized:t.startsWith("data:")}):(0,r.jsx)("div",{className:"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-12 w-12 text-gray-400"})})})()})}),(0,r.jsxs)(N.$,{type:"button",onClick:()=>{F(null),U(null),a((0,b.XY)(void 0)),K.setValue("photo",null),Q()},variant:"outline",className:"border-gray-300",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Retake Photo"]})]}),(0,r.jsx)("canvas",{ref:X,style:{display:"none"}})]})}),(0,r.jsx)(j.Rr,{className:"text-xs text-gray-500 mt-2",children:"A clear photo helps us identify you and personalize your profile"}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})})]})}}),(0,r.jsx)(j.zB,{control:K.control,name:"document",render:e=>{let{field:t}=e;return(0,r.jsxs)(C.Zp,{className:"shadow-sm",children:[(0,r.jsxs)(C.aR,{children:[(0,r.jsx)(C.ZB,{className:"text-lg font-medium",children:"Identity Document"}),(0,r.jsx)(C.BT,{children:"Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate"})]}),(0,r.jsx)(C.Wu,{children:(0,r.jsxs)(j.eI,{children:[J?(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-[#fff8f3] rounded-full",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-black"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:J.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:J instanceof File?es(J.size):"Previously uploaded document"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[J&&"url"in J&&(0,r.jsx)(N.$,{type:"button",variant:"outline",size:"sm",onClick:()=>window.open(J.url,"_blank"),className:"h-8 px-3 border-gray-200",children:"View"}),(0,r.jsx)(N.$,{type:"button",variant:"outline",size:"sm",onClick:er,className:"h-8 w-8 p-0 border-gray-200",children:(0,r.jsx)(h.A,{className:"h-4 w-4 text-gray-500"})})]})]})}):(0,r.jsx)(j.MJ,{children:(0,r.jsx)("div",{className:"flex items-center justify-center w-full",children:(0,r.jsxs)("label",{className:"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,r.jsx)(g.A,{className:"w-10 h-10 mb-3 text-black"}),(0,r.jsxs)("p",{className:"mb-2 text-sm text-gray-700",children:[(0,r.jsx)("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"PDF, PNG, JPG or JPEG (MAX. 5MB)"})]}),(0,r.jsx)(y.p,{id:"document",type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",onChange:e=>{var a;let r=null===(a=e.target.files)||void 0===a?void 0:a[0];if(r){if(r.size>5242880){i.oR.error("File size exceeds 5MB limit");return}L({name:r.name,size:r.size,type:r.type,url:URL.createObjectURL(r)}),q(!1),t.onChange(r)}}})]})})}),(0,r.jsx)(j.Rr,{className:"text-xs text-gray-500 mt-2",children:"This document will serve to verify your identity and date of birth."}),(0,r.jsx)(j.C5,{className:"text-red-500"})]})})]})}}),(0,r.jsx)(C.Zp,{className:"shadow-sm border-none",children:(0,r.jsx)(C.Wu,{className:"pt-6",children:(0,r.jsx)(N.$,{type:"submit",className:"w-full font-medium py-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 text-lg ".concat(el&&!T?"bg-black text-white hover:bg-gray-800":"bg-gray-300 text-gray-500 cursor-not-allowed"),disabled:T||!el,children:T?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),Y?"Updating Profile...":"Creating Profile..."]}):Y?"Update Profile":"Save Profile"})})})]})})})]})})})]})},B=()=>(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),children:(0,r.jsx)(A,{})})},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,type:a,...l}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},66587:(e,t,a)=>{Promise.resolve().then(a.bind(a,61286))},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>c});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>c,MJ:()=>f,Rr:()=>v,zB:()=>m,eI:()=>g,lR:()=>p,C5:()=>b});var r=a(95155),s=a(12115),l=a(66634),n=a(62177),o=a(59434),i=a(24265);function d(e){let{className:t,...a}=e;return(0,r.jsx)(i.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let c=n.Op,u=s.createContext({}),m=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(n.xI,{...t})})},x=()=>{let e=s.useContext(u),t=s.useContext(h),{getFieldState:a}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),l=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...l}},h=s.createContext({});function g(e){let{className:t,...a}=e,l=s.useId();return(0,r.jsx)(h.Provider,{value:{id:l},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...a})})}function p(e){let{className:t,...a}=e,{error:s,formItemId:l}=x();return(0,r.jsx)(d,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...a})}function f(e){let{...t}=e,{error:a,formItemId:s,formDescriptionId:n,formMessageId:o}=x();return(0,r.jsx)(l.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!a,...t})}function v(e){let{className:t,...a}=e,{formDescriptionId:s}=x();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function b(e){var t;let{className:a,...s}=e,{error:l,formMessageId:n}=x(),i=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):s.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",a),...s,children:i}):null}},85511:(e,t,a)=>{"use strict";a.d(t,{V:()=>j});var r=a(95155),s=a(12115),l=a(42355),n=a(13052),o=a(53231),i=a(32944),d=a(84423),c=a(72794),u=a(7632),m=a(70542),x=a(3898),h=a(66835),g=a(40714),p=a(48882),f=a(37223),v=a(59434),b=a(30285);function j(e){let{className:t,selected:a,onSelect:j,disabled:y,month:N,onMonthChange:w,fromYear:k,toYear:C,captionLayout:z="buttons",classNames:M,...R}=e,[S,P]=s.useState(N||a||new Date);s.useEffect(()=>{N&&P(N)},[N]);let I=(0,o.w)(S),A=(0,i.p)(I),B=(0,d.k)(I),D=(0,c.$)(A),F=[],V=[],E=B,T="";for(;E<=D;){for(let e=0;e<7;e++){T=(0,u.GP)(E,"d");let e=E;V.push((0,r.jsx)("div",{className:(0,v.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer","h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground",{"text-muted-foreground":!(0,m.t)(E,I),"bg-primary text-primary-foreground":a&&(0,x.r)(E,a),"bg-accent text-accent-foreground":(0,h.c)(E)&&(!a||!(0,x.r)(E,a)),"opacity-50 cursor-not-allowed":y&&y(E)}),onClick:()=>{y&&y(e)||null==j||j(e)},children:(0,r.jsx)("span",{className:"font-normal",children:T})},E.toString())),E=(0,g.f)(E,1)}F.push((0,r.jsx)("div",{className:"flex w-full mt-2",children:V},E.toString())),V=[]}return(0,r.jsx)("div",{className:(0,v.cn)("p-3",t),...R,children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("div",{className:(0,v.cn)("flex justify-center pt-1 relative items-center w-full",null==M?void 0:M.caption),children:"dropdown"===z?(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("select",{value:S.getMonth(),onChange:e=>{let t=new Date(S.getFullYear(),parseInt(e.target.value),1);P(t),null==w||w(t)},className:(0,v.cn)("mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",null==M?void 0:M.dropdown),children:Array.from({length:12},(e,t)=>(0,r.jsx)("option",{value:t,children:(0,u.GP)(new Date(2e3,t,1),"MMMM")},t))}),(0,r.jsx)("select",{value:S.getFullYear(),onChange:e=>{let t=new Date(parseInt(e.target.value),S.getMonth(),1);P(t),null==w||w(t)},className:(0,v.cn)("mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",null==M?void 0:M.dropdown),children:Array.from({length:(C||new Date().getFullYear())-(k||1950)+1},(e,t)=>{let a=(k||1950)+t;return(0,r.jsx)("option",{value:a,children:a},a)})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.$,{variant:"outline",size:"sm",className:"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100",onClick:()=>{let e=(0,f.a)(S,1);P(e),null==w||w(e)},children:(0,r.jsx)(l.A,{className:"size-4"})}),(0,r.jsx)("div",{className:(0,v.cn)("text-sm font-medium",null==M?void 0:M.caption_label),children:(0,u.GP)(S,"MMMM yyyy")}),(0,r.jsx)(b.$,{variant:"outline",size:"sm",className:"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100",onClick:()=>{let e=(0,p.P)(S,1);P(e),null==w||w(e)},children:(0,r.jsx)(n.A,{className:"size-4"})})]})}),(0,r.jsxs)("div",{className:"w-full border-collapse space-x-1",children:[(0,r.jsx)("div",{className:"flex",children:["Su","Mo","Tu","We","Th","Fr","Sa"].map(e=>(0,r.jsx)("div",{className:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center",children:e},e))}),F]})]})})}},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>l});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1342,7632,4211,352,347,8441,1684,7358],()=>t(66587)),_N_E=e.O()}]);