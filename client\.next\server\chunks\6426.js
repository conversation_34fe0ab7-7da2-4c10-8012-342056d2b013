exports.id=6426,exports.ids=[6426],exports.modules={2666:(e,f,t)=>{"use strict";let n;let a=t(74075),i=t(10257),r=t(44400),{kStatusCode:s}=t(91813),o=Buffer[Symbol.species],u=Buffer.from([0,0,255,255]),c=Symbol("permessage-deflate"),d=Symbol("total-length"),l=Symbol("callback"),h=Symbol("buffers"),g=Symbol("error");class w{constructor(e,f,t){this._maxPayload=0|t,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!f,this._deflate=null,this._inflate=null,this.params=null,n||(n=new r(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[l];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let f=this._options,t=e.find(e=>(!1!==f.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==f.serverMaxWindowBits&&("number"!=typeof f.serverMaxWindowBits||!(f.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof f.clientMaxWindowBits||!!e.client_max_window_bits));if(!t)throw Error("None of the extension offers can be accepted");return f.serverNoContextTakeover&&(t.server_no_context_takeover=!0),f.clientNoContextTakeover&&(t.client_no_context_takeover=!0),"number"==typeof f.serverMaxWindowBits&&(t.server_max_window_bits=f.serverMaxWindowBits),"number"==typeof f.clientMaxWindowBits?t.client_max_window_bits=f.clientMaxWindowBits:(!0===t.client_max_window_bits||!1===f.clientMaxWindowBits)&&delete t.client_max_window_bits,t}acceptAsClient(e){let f=e[0];if(!1===this._options.clientNoContextTakeover&&f.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(f.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&f.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(f.client_max_window_bits=this._options.clientMaxWindowBits);return f}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(f=>{let t=e[f];if(t.length>1)throw Error(`Parameter "${f}" must have only a single value`);if(t=t[0],"client_max_window_bits"===f){if(!0!==t){let e=+t;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${f}": ${t}`);t=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${f}": ${t}`)}else if("server_max_window_bits"===f){let e=+t;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${f}": ${t}`);t=e}else if("client_no_context_takeover"===f||"server_no_context_takeover"===f){if(!0!==t)throw TypeError(`Invalid value for parameter "${f}": ${t}`)}else throw Error(`Unknown parameter "${f}"`);e[f]=t})}),e}decompress(e,f,t){n.add(n=>{this._decompress(e,f,(e,f)=>{n(),t(e,f)})})}compress(e,f,t){n.add(n=>{this._compress(e,f,(e,f)=>{n(),t(e,f)})})}_decompress(e,f,t){let n=this._isServer?"client":"server";if(!this._inflate){let e=`${n}_max_window_bits`,f="number"!=typeof this.params[e]?a.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=a.createInflateRaw({...this._options.zlibInflateOptions,windowBits:f}),this._inflate[c]=this,this._inflate[d]=0,this._inflate[h]=[],this._inflate.on("error",L),this._inflate.on("data",M)}this._inflate[l]=t,this._inflate.write(e),f&&this._inflate.write(u),this._inflate.flush(()=>{let e=this._inflate[g];if(e){this._inflate.close(),this._inflate=null,t(e);return}let a=i.concat(this._inflate[h],this._inflate[d]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[d]=0,this._inflate[h]=[],f&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),t(null,a)})}_compress(e,f,t){let n=this._isServer?"server":"client";if(!this._deflate){let e=`${n}_max_window_bits`,f="number"!=typeof this.params[e]?a.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=a.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:f}),this._deflate[d]=0,this._deflate[h]=[],this._deflate.on("data",p)}this._deflate[l]=t,this._deflate.write(e),this._deflate.flush(a.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[h],this._deflate[d]);f&&(e=new o(e.buffer,e.byteOffset,e.length-4)),this._deflate[l]=null,this._deflate[d]=0,this._deflate[h]=[],f&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),t(null,e)})}}function p(e){this[h].push(e),this[d]+=e.length}function M(e){if(this[d]+=e.length,this[c]._maxPayload<1||this[d]<=this[c]._maxPayload){this[h].push(e);return}this[g]=RangeError("Max payload size exceeded"),this[g].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[g][s]=1009,this.removeListener("data",M),this.reset()}function L(e){this[c]._inflate=null,e[s]=1007,this[l](e)}e.exports=w},3890:(e,f,t)=>{let n=t(83997),a=t(28354);f.init=function(e){e.inspectOpts={};let t=Object.keys(f.inspectOpts);for(let n=0;n<t.length;n++)e.inspectOpts[t[n]]=f.inspectOpts[t[n]]},f.log=function(...e){return process.stderr.write(a.formatWithOptions(f.inspectOpts,...e)+"\n")},f.formatArgs=function(t){let{namespace:n,useColors:a}=this;if(a){let f=this.color,a="\x1b[3"+(f<8?f:"8;5;"+f),i=`  ${a};1m${n} \u001B[0m`;t[0]=i+t[0].split("\n").join("\n"+i),t.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else t[0]=(f.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+t[0]},f.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},f.load=function(){return process.env.DEBUG},f.useColors=function(){return"colors"in f.inspectOpts?!!f.inspectOpts.colors:n.isatty(process.stderr.fd)},f.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),f.colors=[6,2,3,4,5,1];try{let e=t(39228);e&&(e.stderr||e).level>=2&&(f.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}f.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,f)=>{let t=f.substring(6).toLowerCase().replace(/_([a-z])/g,(e,f)=>f.toUpperCase()),n=process.env[f];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[t]=n,e},{}),e.exports=t(43095)(f);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},9680:(e,f,t)=>{e.exports=function(e){function f(e){let t,a,i;let r=null;function s(...e){if(!s.enabled)return;let n=Number(new Date);s.diff=n-(t||n),s.prev=t,s.curr=n,t=n,e[0]=f.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,n)=>{if("%%"===t)return"%";a++;let i=f.formatters[n];if("function"==typeof i){let f=e[a];t=i.call(s,f),e.splice(a,1),a--}return t}),f.formatArgs.call(s,e),(s.log||f.log).apply(s,e)}return s.namespace=e,s.useColors=f.useColors(),s.color=f.selectColor(e),s.extend=n,s.destroy=f.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(a!==f.namespaces&&(a=f.namespaces,i=f.enabled(e)),i),set:e=>{r=e}}),"function"==typeof f.init&&f.init(s),s}function n(e,t){let n=f(this.namespace+(void 0===t?":":t)+e);return n.log=this.log,n}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return f.debug=f,f.default=f,f.coerce=function(e){return e instanceof Error?e.stack||e.message:e},f.disable=function(){let e=[...f.names.map(a),...f.skips.map(a).map(e=>"-"+e)].join(",");return f.enable(""),e},f.enable=function(e){let t;f.save(e),f.namespaces=e,f.names=[],f.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),a=n.length;for(t=0;t<a;t++)n[t]&&("-"===(e=n[t].replace(/\*/g,".*?"))[0]?f.skips.push(RegExp("^"+e.slice(1)+"$")):f.names.push(RegExp("^"+e+"$")))},f.enabled=function(e){let t,n;if("*"===e[e.length-1])return!0;for(t=0,n=f.skips.length;t<n;t++)if(f.skips[t].test(e))return!1;for(t=0,n=f.names.length;t<n;t++)if(f.names[t].test(e))return!0;return!1},f.humanize=t(67802),f.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(t=>{f[t]=e[t]}),f.names=[],f.skips=[],f.formatters={},f.selectColor=function(e){let t=0;for(let f=0;f<e.length;f++)t=(t<<5)-t+e.charCodeAt(f)|0;return f.colors[Math.abs(t)%f.colors.length]},f.enable(f.load()),f}},10257:(e,f,t)=>{"use strict";let{EMPTY_BUFFER:n}=t(91813),a=Buffer[Symbol.species];function i(e,f,t,n,a){for(let i=0;i<a;i++)t[n+i]=e[i]^f[3&i]}function r(e,f){for(let t=0;t<e.length;t++)e[t]^=f[3&t]}function s(e){let f;return(s.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?f=new a(e):ArrayBuffer.isView(e)?f=new a(e.buffer,e.byteOffset,e.byteLength):(f=Buffer.from(e),s.readOnly=!1),f)}if(e.exports={concat:function(e,f){if(0===e.length)return n;if(1===e.length)return e[0];let t=Buffer.allocUnsafe(f),i=0;for(let f=0;f<e.length;f++){let n=e[f];t.set(n,i),i+=n.length}return i<f?new a(t.buffer,t.byteOffset,i):t},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:s,unmask:r},!process.env.WS_NO_BUFFER_UTIL)try{let f=t(39727);e.exports.mask=function(e,t,n,a,r){r<48?i(e,t,n,a,r):f.mask(e,t,n,a,r)},e.exports.unmask=function(e,t){e.length<32?r(e,t):f.unmask(e,t)}}catch(e){}},13964:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25133:(e,f,t)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=t(88573):e.exports=t(67461)},27900:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},28559:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29940:(e,f,t)=>{e.exports=function(e){function f(e){let t,a,i;let r=null;function s(...e){if(!s.enabled)return;let n=Number(new Date);s.diff=n-(t||n),s.prev=t,s.curr=n,t=n,e[0]=f.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,n)=>{if("%%"===t)return"%";a++;let i=f.formatters[n];if("function"==typeof i){let f=e[a];t=i.call(s,f),e.splice(a,1),a--}return t}),f.formatArgs.call(s,e),(s.log||f.log).apply(s,e)}return s.namespace=e,s.useColors=f.useColors(),s.color=f.selectColor(e),s.extend=n,s.destroy=f.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(a!==f.namespaces&&(a=f.namespaces,i=f.enabled(e)),i),set:e=>{r=e}}),"function"==typeof f.init&&f.init(s),s}function n(e,t){let n=f(this.namespace+(void 0===t?":":t)+e);return n.log=this.log,n}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return f.debug=f,f.default=f,f.coerce=function(e){return e instanceof Error?e.stack||e.message:e},f.disable=function(){let e=[...f.names.map(a),...f.skips.map(a).map(e=>"-"+e)].join(",");return f.enable(""),e},f.enable=function(e){let t;f.save(e),f.namespaces=e,f.names=[],f.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),a=n.length;for(t=0;t<a;t++)n[t]&&("-"===(e=n[t].replace(/\*/g,".*?"))[0]?f.skips.push(RegExp("^"+e.slice(1)+"$")):f.names.push(RegExp("^"+e+"$")))},f.enabled=function(e){let t,n;if("*"===e[e.length-1])return!0;for(t=0,n=f.skips.length;t<n;t++)if(f.skips[t].test(e))return!1;for(t=0,n=f.names.length;t<n;t++)if(f.names[t].test(e))return!0;return!1},f.humanize=t(67802),f.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(t=>{f[t]=e[t]}),f.names=[],f.skips=[],f.formatters={},f.selectColor=function(e){let t=0;for(let f=0;f<e.length;f++)t=(t<<5)-t+e.charCodeAt(f)|0;return f.colors[Math.abs(t)%f.colors.length]},f.enable(f.load()),f}},31637:(e,f,t)=>{"use strict";let{tokenChars:n}=t(37293);function a(e,f,t){void 0===e[f]?e[f]=[t]:e[f].push(t)}e.exports={format:function(e){return Object.keys(e).map(f=>{let t=e[f];return Array.isArray(t)||(t=[t]),t.map(e=>[f].concat(Object.keys(e).map(f=>{let t=e[f];return Array.isArray(t)||(t=[t]),t.map(e=>!0===e?f:`${f}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let f,t;let i=Object.create(null),r=Object.create(null),s=!1,o=!1,u=!1,c=-1,d=-1,l=-1,h=0;for(;h<e.length;h++)if(d=e.charCodeAt(h),void 0===f){if(-1===l&&1===n[d])-1===c&&(c=h);else if(0!==h&&(32===d||9===d))-1===l&&-1!==c&&(l=h);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${h}`);-1===l&&(l=h);let t=e.slice(c,l);44===d?(a(i,t,r),r=Object.create(null)):f=t,c=l=-1}else throw SyntaxError(`Unexpected character at index ${h}`)}else if(void 0===t){if(-1===l&&1===n[d])-1===c&&(c=h);else if(32===d||9===d)-1===l&&-1!==c&&(l=h);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${h}`);-1===l&&(l=h),a(r,e.slice(c,l),!0),44===d&&(a(i,f,r),r=Object.create(null),f=void 0),c=l=-1}else if(61===d&&-1!==c&&-1===l)t=e.slice(c,h),c=l=-1;else throw SyntaxError(`Unexpected character at index ${h}`)}else if(o){if(1!==n[d])throw SyntaxError(`Unexpected character at index ${h}`);-1===c?c=h:s||(s=!0),o=!1}else if(u){if(1===n[d])-1===c&&(c=h);else if(34===d&&-1!==c)u=!1,l=h;else if(92===d)o=!0;else throw SyntaxError(`Unexpected character at index ${h}`)}else if(34===d&&61===e.charCodeAt(h-1))u=!0;else if(-1===l&&1===n[d])-1===c&&(c=h);else if(-1!==c&&(32===d||9===d))-1===l&&(l=h);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${h}`);-1===l&&(l=h);let n=e.slice(c,l);s&&(n=n.replace(/\\/g,""),s=!1),a(r,t,n),44===d&&(a(i,f,r),r=Object.create(null),f=void 0),t=void 0,c=l=-1}else throw SyntaxError(`Unexpected character at index ${h}`);if(-1===c||u||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===l&&(l=h);let g=e.slice(c,l);return void 0===f?a(i,g,r):(void 0===t?a(r,g,!0):s?a(r,t,g.replace(/\\/g,"")):a(r,t,g),a(i,f,r)),i}}},36207:(e,f,t)=>{"use strict";let n;let{Duplex:a}=t(27910),{randomFillSync:i}=t(55511),r=t(2666),{EMPTY_BUFFER:s}=t(91813),{isValidStatusCode:o}=t(37293),{mask:u,toBuffer:c}=t(10257),d=Symbol("kByteLength"),l=Buffer.alloc(4),h=8192;class g{constructor(e,f,t){this._extensions=f||{},t&&(this._generateMask=t,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,f){let t,a;let r=!1,s=2,o=!1;f.mask&&(t=f.maskBuffer||l,f.generateMask?f.generateMask(t):(8192===h&&(void 0===n&&(n=Buffer.alloc(8192)),i(n,0,8192),h=0),t[0]=n[h++],t[1]=n[h++],t[2]=n[h++],t[3]=n[h++]),o=(t[0]|t[1]|t[2]|t[3])==0,s=6),"string"==typeof e?a=(!f.mask||o)&&void 0!==f[d]?f[d]:(e=Buffer.from(e)).length:(a=e.length,r=f.mask&&f.readOnly&&!o);let c=a;a>=65536?(s+=8,c=127):a>125&&(s+=2,c=126);let g=Buffer.allocUnsafe(r?a+s:s);return(g[0]=f.fin?128|f.opcode:f.opcode,f.rsv1&&(g[0]|=64),g[1]=c,126===c?g.writeUInt16BE(a,2):127===c&&(g[2]=g[3]=0,g.writeUIntBE(a,4,6)),f.mask)?(g[1]|=128,g[s-4]=t[0],g[s-3]=t[1],g[s-2]=t[2],g[s-1]=t[3],o)?[g,e]:r?(u(e,t,g,s,a),[g]):(u(e,t,e,0,a),[g,e]):[g,e]}close(e,f,t,n){let a;if(void 0===e)a=s;else if("number"==typeof e&&o(e)){if(void 0!==f&&f.length){let t=Buffer.byteLength(f);if(t>123)throw RangeError("The message must not be greater than 123 bytes");(a=Buffer.allocUnsafe(2+t)).writeUInt16BE(e,0),"string"==typeof f?a.write(f,2):a.set(f,2)}else(a=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let i={[d]:a.length,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,a,!1,i,n]):this.sendFrame(g.frame(a,i),n)}ping(e,f,t){let n,a;if("string"==typeof e?(n=Buffer.byteLength(e),a=!1):(n=(e=c(e)).length,a=c.readOnly),n>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[d]:n,fin:!0,generateMask:this._generateMask,mask:f,maskBuffer:this._maskBuffer,opcode:9,readOnly:a,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,t]):this.sendFrame(g.frame(e,i),t)}pong(e,f,t){let n,a;if("string"==typeof e?(n=Buffer.byteLength(e),a=!1):(n=(e=c(e)).length,a=c.readOnly),n>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[d]:n,fin:!0,generateMask:this._generateMask,mask:f,maskBuffer:this._maskBuffer,opcode:10,readOnly:a,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,t]):this.sendFrame(g.frame(e,i),t)}send(e,f,t){let n,a;let i=this._extensions[r.extensionName],s=f.binary?2:1,o=f.compress;if("string"==typeof e?(n=Buffer.byteLength(e),a=!1):(n=(e=c(e)).length,a=c.readOnly),this._firstFragment?(this._firstFragment=!1,o&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=n>=i._threshold),this._compress=o):(o=!1,s=0),f.fin&&(this._firstFragment=!0),i){let i={[d]:n,fin:f.fin,generateMask:this._generateMask,mask:f.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:a,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,i,t]):this.dispatch(e,this._compress,i,t)}else this.sendFrame(g.frame(e,{[d]:n,fin:f.fin,generateMask:this._generateMask,mask:f.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:a,rsv1:!1}),t)}dispatch(e,f,t,n){if(!f){this.sendFrame(g.frame(e,t),n);return}let a=this._extensions[r.extensionName];this._bufferedBytes+=t[d],this._deflating=!0,a.compress(e,t.fin,(e,f)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof n&&n(e);for(let f=0;f<this._queue.length;f++){let t=this._queue[f],n=t[t.length-1];"function"==typeof n&&n(e)}return}this._bufferedBytes-=t[d],this._deflating=!1,t.readOnly=!1,this.sendFrame(g.frame(f,t),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][d],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][d],this._queue.push(e)}sendFrame(e,f){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],f),this._socket.uncork()):this._socket.write(e[0],f)}}e.exports=g},36495:(e,f,t)=>{"use strict";let n=t(94735),a=t(81630),{Duplex:i}=t(27910),{createHash:r}=t(55511),s=t(31637),o=t(2666),u=t(70946),c=t(72635),{GUID:d,kWebSocket:l}=t(91813),h=/^[+/0-9A-Za-z]{22}==$/;class g extends n{constructor(e,f){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:c,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=a.createServer((e,f)=>{let t=a.STATUS_CODES[426];f.writeHead(426,{"Content-Length":t.length,"Content-Type":"text/plain"}),f.end(t)}),this._server.listen(e.port,e.host,e.backlog,f)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,f){for(let t of Object.keys(f))e.on(t,f[t]);return function(){for(let t of Object.keys(f))e.removeListener(t,f[t])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(f,t,n)=>{this.handleUpgrade(f,t,n,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(w,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(w,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{w(this)})}}}shouldHandle(e){if(this.options.path){let f=e.url.indexOf("?");if((-1!==f?e.url.slice(0,f):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,f,t,n){f.on("error",p);let a=e.headers["sec-websocket-key"],i=e.headers.upgrade,r=+e.headers["sec-websocket-version"];if("GET"!==e.method){L(this,e,f,405,"Invalid HTTP method");return}if(void 0===i||"websocket"!==i.toLowerCase()){L(this,e,f,400,"Invalid Upgrade header");return}if(void 0===a||!h.test(a)){L(this,e,f,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==r&&13!==r){L(this,e,f,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){M(f,400);return}let c=e.headers["sec-websocket-protocol"],d=new Set;if(void 0!==c)try{d=u.parse(c)}catch(t){L(this,e,f,400,"Invalid Sec-WebSocket-Protocol header");return}let l=e.headers["sec-websocket-extensions"],g={};if(this.options.perMessageDeflate&&void 0!==l){let t=new o(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=s.parse(l);e[o.extensionName]&&(t.accept(e[o.extensionName]),g[o.extensionName]=t)}catch(t){L(this,e,f,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let i={origin:e.headers[`${8===r?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(i,(i,r,s,o)=>{if(!i)return M(f,r||401,s,o);this.completeUpgrade(g,a,d,e,f,t,n)});return}if(!this.options.verifyClient(i))return M(f,401)}this.completeUpgrade(g,a,d,e,f,t,n)}completeUpgrade(e,f,t,n,a,i,u){if(!a.readable||!a.writable)return a.destroy();if(a[l])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return M(a,503);let c=r("sha1").update(f+d).digest("base64"),h=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${c}`],g=new this.options.WebSocket(null,void 0,this.options);if(t.size){let e=this.options.handleProtocols?this.options.handleProtocols(t,n):t.values().next().value;e&&(h.push(`Sec-WebSocket-Protocol: ${e}`),g._protocol=e)}if(e[o.extensionName]){let f=e[o.extensionName].params,t=s.format({[o.extensionName]:[f]});h.push(`Sec-WebSocket-Extensions: ${t}`),g._extensions=e}this.emit("headers",h,n),a.write(h.concat("\r\n").join("\r\n")),a.removeListener("error",p),g.setSocket(a,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(g),g.on("close",()=>{this.clients.delete(g),this._shouldEmitClose&&!this.clients.size&&process.nextTick(w,this)})),u(g,n)}}function w(e){e._state=2,e.emit("close")}function p(){this.destroy()}function M(e,f,t,n){t=t||a.STATUS_CODES[f],n={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(t),...n},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${f} ${a.STATUS_CODES[f]}\r
`+Object.keys(n).map(e=>`${e}: ${n[e]}`).join("\r\n")+"\r\n\r\n"+t)}function L(e,f,t,n,a){if(e.listenerCount("wsClientError")){let n=Error(a);Error.captureStackTrace(n,L),e.emit("wsClientError",n,t,f)}else M(t,n,a)}e.exports=g},37101:(e,f,t)=>{var n=t(29021),a=t(79551),i=t(79646).spawn;function r(e){"use strict";e=e||{};var f,r,s=this,o=t(81630),u=t(55591),c={},d=!1,l={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},h=Object.assign({},l),g=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],w=["TRACE","TRACK","CONNECT"],p=!1,M=!1,L=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,f,t,n,a){if(this.abort(),M=!1,L=!1,!(e&&-1===w.indexOf(e)))throw Error("SecurityError: Request method not allowed");c={method:e,url:f.toString(),async:"boolean"!=typeof t||t,user:n||null,password:a||null},m(this.OPENED)},this.setDisableHeaderCheck=function(e){d=e},this.setRequestHeader=function(e,f){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!d&&(!e||-1!==g.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(p)throw Error("INVALID_STATE_ERR: send flag is true");return h[e]=f,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&r.headers[e.toLowerCase()]&&!M?r.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||M)return"";var e="";for(var f in r.headers)"set-cookie"!==f&&"set-cookie2"!==f&&(e+=f+": "+r.headers[f]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&h[e]?h[e]:""},this.send=function(t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(p)throw Error("INVALID_STATE_ERR: send has already been called");var d,l=!1,g=!1,w=a.parse(c.url);switch(w.protocol){case"https:":l=!0;case"http:":d=w.hostname;break;case"file:":g=!0;break;case void 0:case"":d="localhost";break;default:throw Error("Protocol not supported.")}if(g){if("GET"!==c.method)throw Error("XMLHttpRequest: Only GET method is supported");if(c.async)n.readFile(unescape(w.pathname),function(e,f){e?s.handleError(e,e.errno||-1):(s.status=200,s.responseText=f.toString("utf8"),s.response=f,m(s.DONE))});else try{this.response=n.readFileSync(unescape(w.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,m(s.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var L=w.port||(l?443:80),b=w.pathname+(w.search?w.search:"");if(h.Host=d,l&&443===L||80===L||(h.Host+=":"+w.port),c.user){void 0===c.password&&(c.password="");var y=new Buffer(c.user+":"+c.password);h.Authorization="Basic "+y.toString("base64")}"GET"===c.method||"HEAD"===c.method?t=null:t?(h["Content-Length"]=Buffer.isBuffer(t)?t.length:Buffer.byteLength(t),Object.keys(h).some(function(e){return"content-type"===e.toLowerCase()})||(h["Content-Type"]="text/plain;charset=UTF-8")):"POST"===c.method&&(h["Content-Length"]=0);var C=e.agent||!1,j={host:d,port:L,path:b,method:c.method,headers:h,agent:C};if(l&&(j.pfx=e.pfx,j.key=e.key,j.passphrase=e.passphrase,j.cert=e.cert,j.ca=e.ca,j.ciphers=e.ciphers,j.rejectUnauthorized=!1!==e.rejectUnauthorized),M=!1,c.async){var I=l?u.request:o.request;p=!0,s.dispatchEvent("readystatechange");var v=function(t){if(302===(r=t).statusCode||303===r.statusCode||307===r.statusCode){c.url=r.headers.location;var n=a.parse(c.url);d=n.hostname;var i={hostname:n.hostname,port:n.port,path:n.path,method:303===r.statusCode?"GET":c.method,headers:h};l&&(i.pfx=e.pfx,i.key=e.key,i.passphrase=e.passphrase,i.cert=e.cert,i.ca=e.ca,i.ciphers=e.ciphers,i.rejectUnauthorized=!1!==e.rejectUnauthorized),(f=I(i,v).on("error",k)).end();return}m(s.HEADERS_RECEIVED),s.status=r.statusCode,r.on("data",function(e){if(e){var f=Buffer.from(e);s.response=Buffer.concat([s.response,f])}p&&m(s.LOADING)}),r.on("end",function(){p&&(p=!1,m(s.DONE),s.responseText=s.response.toString("utf8"))}),r.on("error",function(e){s.handleError(e)})},k=function(e){if(f.reusedSocket&&"ECONNRESET"===e.code)return I(j,v).on("error",k);s.handleError(e)};f=I(j,v).on("error",k),e.autoUnref&&f.on("socket",e=>{e.unref()}),t&&f.write(t),f.end(),s.dispatchEvent("loadstart")}else{var N=".node-xmlhttprequest-content-"+process.pid,S=".node-xmlhttprequest-sync-"+process.pid;n.writeFileSync(S,"","utf8");for(var D="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(l?"s":"")+".request;var options = "+JSON.stringify(j)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+N+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+S+"');});response.on('error', function(error) {fs.writeFileSync('"+N+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+S+"');});}).on('error', function(error) {fs.writeFileSync('"+N+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+S+"');});"+(t?"req.write('"+JSON.stringify(t).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",T=i(process.argv[0],["-e",D]);n.existsSync(S););if(s.responseText=n.readFileSync(N,"utf8"),T.stdin.end(),n.unlinkSync(N),s.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var E=JSON.parse(s.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));s.handleError(E,503)}else{s.status=s.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var x=JSON.parse(s.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));r={statusCode:s.status,headers:x.data.headers},s.responseText=x.data.text,s.response=Buffer.from(x.data.data,"base64"),m(s.DONE,!0)}}},this.handleError=function(e,f){this.status=f||0,this.statusText=e,this.responseText=e.stack,M=!0,m(this.DONE)},this.abort=function(){f&&(f.abort(),f=null),h=Object.assign({},l),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),M=L=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||p)&&this.readyState!==this.DONE&&(p=!1,m(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,f){e in b||(b[e]=[]),b[e].push(f)},this.removeEventListener=function(e,f){e in b&&(b[e]=b[e].filter(function(e){return e!==f}))},this.dispatchEvent=function(e){if("function"==typeof s["on"+e]&&(this.readyState===this.DONE&&c.async?setTimeout(function(){s["on"+e]()},0):s["on"+e]()),e in b)for(let f=0,t=b[e].length;f<t;f++)this.readyState===this.DONE?setTimeout(function(){b[e][f].call(s)},0):b[e][f].call(s)};var m=function(e){if(s.readyState!==e&&(s.readyState!==s.UNSENT||!L)&&(s.readyState=e,(c.async||s.readyState<s.OPENED||s.readyState===s.DONE)&&s.dispatchEvent("readystatechange"),s.readyState===s.DONE)){let e;e=L?"abort":M?"error":"load",s.dispatchEvent(e),s.dispatchEvent("loadend")}}}e.exports=r,r.XMLHttpRequest=r},37293:(e,f,t)=>{"use strict";let{isUtf8:n}=t(79428);function a(e){let f=e.length,t=0;for(;t<f;)if((128&e[t])==0)t++;else if((224&e[t])==192){if(t+1===f||(192&e[t+1])!=128||(254&e[t])==192)return!1;t+=2}else if((240&e[t])==224){if(t+2>=f||(192&e[t+1])!=128||(192&e[t+2])!=128||224===e[t]&&(224&e[t+1])==128||237===e[t]&&(224&e[t+1])==160)return!1;t+=3}else{if((248&e[t])!=240||t+3>=f||(192&e[t+1])!=128||(192&e[t+2])!=128||(192&e[t+3])!=128||240===e[t]&&(240&e[t+1])==128||244===e[t]&&e[t+1]>143||e[t]>244)return!1;t+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:a,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},n)e.exports.isValidUTF8=function(e){return e.length<24?a(e):n(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let f=t(47990);e.exports.isValidUTF8=function(e){return e.length<32?a(e):f(e)}}catch(e){}},40945:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("check-check",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]])},41312:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},42009:(e,f,t)=>{let n=t(83997),a=t(28354);f.init=function(e){e.inspectOpts={};let t=Object.keys(f.inspectOpts);for(let n=0;n<t.length;n++)e.inspectOpts[t[n]]=f.inspectOpts[t[n]]},f.log=function(...e){return process.stderr.write(a.formatWithOptions(f.inspectOpts,...e)+"\n")},f.formatArgs=function(t){let{namespace:n,useColors:a}=this;if(a){let f=this.color,a="\x1b[3"+(f<8?f:"8;5;"+f),i=`  ${a};1m${n} \u001B[0m`;t[0]=i+t[0].split("\n").join("\n"+i),t.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else t[0]=(f.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+t[0]},f.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},f.load=function(){return process.env.DEBUG},f.useColors=function(){return"colors"in f.inspectOpts?!!f.inspectOpts.colors:n.isatty(process.stderr.fd)},f.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),f.colors=[6,2,3,4,5,1];try{let e=t(39228);e&&(e.stderr||e).level>=2&&(f.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}f.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,f)=>{let t=f.substring(6).toLowerCase().replace(/_([a-z])/g,(e,f)=>f.toUpperCase()),n=process.env[f];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[t]=n,e},{}),e.exports=t(9680)(f);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},43095:(e,f,t)=>{e.exports=function(e){function f(e){let t,a,i;let r=null;function s(...e){if(!s.enabled)return;let n=Number(new Date);s.diff=n-(t||n),s.prev=t,s.curr=n,t=n,e[0]=f.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,n)=>{if("%%"===t)return"%";a++;let i=f.formatters[n];if("function"==typeof i){let f=e[a];t=i.call(s,f),e.splice(a,1),a--}return t}),f.formatArgs.call(s,e),(s.log||f.log).apply(s,e)}return s.namespace=e,s.useColors=f.useColors(),s.color=f.selectColor(e),s.extend=n,s.destroy=f.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(a!==f.namespaces&&(a=f.namespaces,i=f.enabled(e)),i),set:e=>{r=e}}),"function"==typeof f.init&&f.init(s),s}function n(e,t){let n=f(this.namespace+(void 0===t?":":t)+e);return n.log=this.log,n}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return f.debug=f,f.default=f,f.coerce=function(e){return e instanceof Error?e.stack||e.message:e},f.disable=function(){let e=[...f.names.map(a),...f.skips.map(a).map(e=>"-"+e)].join(",");return f.enable(""),e},f.enable=function(e){let t;f.save(e),f.namespaces=e,f.names=[],f.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),a=n.length;for(t=0;t<a;t++)n[t]&&("-"===(e=n[t].replace(/\*/g,".*?"))[0]?f.skips.push(RegExp("^"+e.slice(1)+"$")):f.names.push(RegExp("^"+e+"$")))},f.enabled=function(e){let t,n;if("*"===e[e.length-1])return!0;for(t=0,n=f.skips.length;t<n;t++)if(f.skips[t].test(e))return!1;for(t=0,n=f.names.length;t<n;t++)if(f.names[t].test(e))return!0;return!1},f.humanize=t(67802),f.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(t=>{f[t]=e[t]}),f.names=[],f.skips=[],f.formatters={},f.selectColor=function(e){let t=0;for(let f=0;f<e.length;f++)t=(t<<5)-t+e.charCodeAt(f)|0;return f.colors[Math.abs(t)%f.colors.length]},f.enable(f.load()),f}},43121:(e,f,t)=>{f.formatArgs=function(f){if(f[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+f[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;f.splice(1,0,t,"color: inherit");let n=0,a=0;f[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(a=n))}),f.splice(a,0,t)},f.save=function(e){try{e?f.storage.setItem("debug",e):f.storage.removeItem("debug")}catch(e){}},f.load=function(){let e;try{e=f.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},f.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},f.storage=function(){try{return localStorage}catch(e){}}(),f.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),f.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],f.log=console.debug||console.log||(()=>{}),e.exports=t(9680)(f);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},44400:e=>{"use strict";let f=Symbol("kDone"),t=Symbol("kRun");class n{constructor(e){this[f]=()=>{this.pending--,this[t]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[t]()}[t](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[f])}}}e.exports=n},47905:(e,f,t)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=t(43121):e.exports=t(42009)},51289:(e,f,t)=>{"use strict";t.d(f,{Ai:()=>i,Ay:()=>nf});var n,a,i,r,s,o,u,c,d,l,h,g,w,p,M,L=t(43210),b=t.n(L);function m(e){return[].concat(e)}function y(e){return e.startsWith(":")}function C(e){return v(e)&&("*"===e||e.length>1&&":>~.+*".includes(e.slice(0,1))||k(e))}function j(e,f){return(v(f)||"number"==typeof f)&&"--"!==e&&!y(e)&&!I(e)}function I(e){return e.startsWith("@media")}function v(e){return e+""===e}function k(e){return v(e)&&(e.startsWith("&")||y(e))}function N(e,f=""){return e.filter(Boolean).join(f)}function S(e,f){let t=0;if(0===f.length)return t.toString();for(let e=0;e<f.length;e++)t=(t<<5)-t+f.charCodeAt(e),t&=t;return`${e??"cl"}_${t.toString(36)}`}var D=class e{constructor(e,f,t,n){this.sheet=e,this.property=f,this.value=t,this.selector=n,this.property=f,this.value=t,this.joined=`${f}:${t}`;let a=this.selector.preconditions.concat(this.selector.postconditions);this.hash=this.selector.hasConditions?this.selector.scopeClassName:S(this.sheet.name,this.joined),this.key=N([this.joined,a,this.hash])}toString(){let f=T(this.selector.preconditions,{right:this.hash});return f=T(this.selector.postconditions,{left:f}),`${f} {${e.genRule(this.property,this.value)}}`}static genRule(e,f){var t,n;return t=e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),n="content"===e?`"${f}"`:f,`${t}:${n};`}};function T(e,{left:f="",right:t=""}={}){let n=e.reduce((e,f)=>y(f)?e+f:k(f)?e+f.slice(1):N([e,f]," "),f);return N([n,t?`.${t}`:""]," ")}var E=class e{constructor(e,f=null,{preconditions:t,postconditions:n}={}){this.sheet=e,this.preconditions=[],this.scopeClassName=null,this.scopeName=null,this.postconditions=[],this.preconditions=t?m(t):[],this.postconditions=n?m(n):[],this.setScope(f)}setScope(e){return e&&(this.scopeClassName||(this.scopeName=e,this.scopeClassName=S(this.sheet.name,e+this.sheet.count))),this}get hasConditions(){return this.preconditions.length>0||this.postconditions.length>0}addScope(f){return new e(this.sheet,f,{preconditions:this.preconditions,postconditions:this.postconditions})}addPrecondition(f){return new e(this.sheet,this.scopeClassName,{postconditions:this.postconditions,preconditions:this.preconditions.concat(f)})}addPostcondition(f){return new e(this.sheet,this.scopeClassName,{preconditions:this.preconditions,postconditions:this.postconditions.concat(f)})}createRule(e,f){return new D(this.sheet,e,f,this)}},x=class{constructor(e,f){this.name=e,this.rootNode=f,this.storedStyles={},this.storedClasses={},this.style="",this.count=0,this.id=`flairup-${e}`,this.styleTag=this.createStyleTag()}getStyle(){return this.style}append(e){var f;this.style=(f=this.style)?`${f}
${e}`:e}apply(){this.count++,this.styleTag&&(this.styleTag.innerHTML=this.style)}isApplied(){return!!this.styleTag}createStyleTag(){if("undefined"==typeof document||this.isApplied()||null===this.rootNode)return this.styleTag;let e=document.createElement("style");return e.type="text/css",e.id=this.id,(this.rootNode??document.head).appendChild(e),e}addRule(e){let f=this.storedClasses[e.key];return v(f)?f:(this.storedClasses[e.key]=e.hash,this.storedStyles[e.hash]=[e.property,e.value],this.append(e.toString()),e.hash)}};function z(e,f){for(let t in e)f(t.trim(),e[t])}function A(...e){return N(e.reduce((e,f)=>(f instanceof Set?e.push(...f):"string"==typeof f?e.push(f):Array.isArray(f)?e.push(A(...f)):"object"==typeof f&&Object.entries(f).forEach(([f,t])=>{t&&e.push(f)}),e),[])," ").trim()}function _(e,f){return e.forEach(e=>f.add(e)),f}function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var f=1;f<arguments.length;f++){var t=arguments[f];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function P(e,f){return(P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,f){return e.__proto__=f,e})(e,f)}function Q(e,f){if(null==e)return{};var t,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)t=i[n],f.indexOf(t)>=0||(a[t]=e[t]);return a}function Y(e,f){(null==f||f>e.length)&&(f=e.length);for(var t=0,n=Array(f);t<f;t++)n[t]=e[t];return n}function R(){for(var e=arguments.length,f=Array(e),t=0;t<e;t++)f[t]=arguments[t];return f.map(function(e){return"."+e}).join("")}!function(e){e.hiddenOnSearch="epr-hidden-on-search",e.searchActive="epr-search-active",e.hidden="epr-hidden",e.visible="epr-visible",e.active="epr-active",e.emoji="epr-emoji",e.category="epr-emoji-category",e.label="epr-emoji-category-label",e.categoryContent="epr-emoji-category-content",e.emojiHasVariations="epr-emoji-has-variations",e.scrollBody="epr-body",e.emojiList="epr-emoji-list",e.external="__EmojiPicker__",e.emojiPicker="EmojiPickerReact",e.open="epr-open",e.vertical="epr-vertical",e.horizontal="epr-horizontal",e.variationPicker="epr-emoji-variation-picker",e.darkTheme="epr-dark-theme",e.autoTheme="epr-auto-theme"}(n||(n={}));var J=function(e,f){let t=new x("epr",null);return{create:function(e){let f={};return(function e(f,t,n){let a=[];return z(t,(i,r)=>{if(C(i))return e(f,r,n.addPrecondition(i)).forEach(e=>a.push(e));a.push([i,t[i],n.addScope(i)])}),a})(t,e,new E(t)).forEach(([e,n,a])=>{(function e(f,t,n){let a=new Set;return z(t,(t,i)=>{let r=[];if(C(t))r=e(f,i,n.addPostcondition(t));else if("."===t)r=m(i);else if(I(t))r=function(f,t,n,a){f.append(n+" {");let i=e(f,t,a);return f.append("}"),i}(f,i,t,n);else if("--"===t)r=function(f,t,n){let a=new Set,i=[];if(z(t,(t,r)=>{if(j(t,r)){i.push(D.genRule(t,r));return}_(e(f,r??{},n),a)}),!n.scopeClassName)return a;if(i.length){let e=i.join(" ");f.append(`${T(n.preconditions,{right:n.scopeClassName})} {${e}}`)}return a.add(n.scopeClassName),a}(f,i,n);else if(j(t,i)){let e=n.createRule(t,i);f.addRule(e),a.add(e.hash)}return _(r,a)}),a})(t,n,a).forEach(t=>{var n,a;n=e,a=t,f[n]=f[n]??new Set,f[n].add(a)})}),t.apply(),f},getStyle:t.getStyle.bind(t),isApplied:t.isApplied.bind(t)}}(0,0),B={display:"none",opacity:"0",pointerEvents:"none",visibility:"hidden",overflow:"hidden"},U=J.create({hidden:O({".":n.hidden},B)}),G=(0,L.memo)(function(){return(0,L.createElement)("style",{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:J.getStyle()}})}),Z=J.create({".epr-main":{":has(input:not(:placeholder-shown))":{categoryBtn:{":hover":{opacity:"1",backgroundPositionY:"var(--epr-category-navigation-button-size)"}},hiddenOnSearch:O({".":n.hiddenOnSearch},B)},":has(input(:placeholder-shown))":{visibleOnSearchOnly:B}},hiddenOnReactions:{transition:"all 0.5s ease-in-out"},".epr-reactions":{hiddenOnReactions:{height:"0px",width:"0px",opacity:"0",pointerEvents:"none",overflow:"hidden"}},".EmojiPickerReact:not(.epr-search-active)":{categoryBtn:{":hover":{opacity:"1",backgroundPositionY:"var(--epr-category-navigation-button-size)"},"&.epr-active":{opacity:"1",backgroundPositionY:"var(--epr-category-navigation-button-size)"}},visibleOnSearchOnly:O({".":"epr-visible-on-search-only"},B)}});function F(e,f){var t,n;return{".epr-dark-theme":((t={})[e]=f,t),".epr-auto-theme":((n={})[e]={"@media (prefers-color-scheme: dark)":f},n)}}function W(e,f){var t,n,a=null!=(t=e.customEmojis)?t:[],i=null!=(n=f.customEmojis)?n:[];return e.open===f.open&&e.emojiVersion===f.emojiVersion&&e.reactionsDefaultOpen===f.reactionsDefaultOpen&&e.searchPlaceHolder===f.searchPlaceHolder&&e.searchPlaceholder===f.searchPlaceholder&&e.defaultSkinTone===f.defaultSkinTone&&e.skinTonesDisabled===f.skinTonesDisabled&&e.autoFocusSearch===f.autoFocusSearch&&e.emojiStyle===f.emojiStyle&&e.theme===f.theme&&e.suggestedEmojisMode===f.suggestedEmojisMode&&e.lazyLoadEmojis===f.lazyLoadEmojis&&e.className===f.className&&e.height===f.height&&e.width===f.width&&e.style===f.style&&e.searchDisabled===f.searchDisabled&&e.skinTonePickerLocation===f.skinTonePickerLocation&&a.length===i.length}var H=["1f44d","2764-fe0f","1f603","1f622","1f64f","1f44e","1f621"];(function(e){e.RECENT="recent",e.FREQUENT="frequent"})(a||(a={})),function(e){e.NATIVE="native",e.APPLE="apple",e.TWITTER="twitter",e.GOOGLE="google",e.FACEBOOK="facebook"}(i||(i={})),function(e){e.DARK="dark",e.LIGHT="light",e.AUTO="auto"}(r||(r={})),function(e){e.NEUTRAL="neutral",e.LIGHT="1f3fb",e.MEDIUM_LIGHT="1f3fc",e.MEDIUM="1f3fd",e.MEDIUM_DARK="1f3fe",e.DARK="1f3ff"}(s||(s={})),function(e){e.SUGGESTED="suggested",e.CUSTOM="custom",e.SMILEYS_PEOPLE="smileys_people",e.ANIMALS_NATURE="animals_nature",e.FOOD_DRINK="food_drink",e.TRAVEL_PLACES="travel_places",e.ACTIVITIES="activities",e.OBJECTS="objects",e.SYMBOLS="symbols",e.FLAGS="flags"}(o||(o={})),function(e){e.SEARCH="SEARCH",e.PREVIEW="PREVIEW"}(u||(u={}));var V=[o.SUGGESTED,o.CUSTOM,o.SMILEYS_PEOPLE,o.ANIMALS_NATURE,o.FOOD_DRINK,o.TRAVEL_PLACES,o.ACTIVITIES,o.OBJECTS,o.SYMBOLS,o.FLAGS],X={name:"Recently Used",category:o.SUGGESTED},K=((c={})[o.SUGGESTED]={category:o.SUGGESTED,name:"Frequently Used"},c[o.CUSTOM]={category:o.CUSTOM,name:"Custom Emojis"},c[o.SMILEYS_PEOPLE]={category:o.SMILEYS_PEOPLE,name:"Smileys & People"},c[o.ANIMALS_NATURE]={category:o.ANIMALS_NATURE,name:"Animals & Nature"},c[o.FOOD_DRINK]={category:o.FOOD_DRINK,name:"Food & Drink"},c[o.TRAVEL_PLACES]={category:o.TRAVEL_PLACES,name:"Travel & Places"},c[o.ACTIVITIES]={category:o.ACTIVITIES,name:"Activities"},c[o.OBJECTS]={category:o.OBJECTS,name:"Objects"},c[o.SYMBOLS]={category:o.SYMBOLS,name:"Symbols"},c[o.FLAGS]={category:o.FLAGS,name:"Flags"},c);function q(e){return V.map(function(f){return O({},K[f],e&&e[f]&&e[f])})}function $(e){return e.category}function ee(e,f){return void 0===f&&(f={}),Object.assign(K[e],f)}var ef={custom:[],smileys_people:[{n:["grinning","grinning face"],u:"1f600",a:"1.0"},{n:["smiley","smiling face with open mouth"],u:"1f603",a:"0.6"},{n:["smile","smiling face with open mouth and smiling eyes"],u:"1f604",a:"0.6"},{n:["grin","grinning face with smiling eyes"],u:"1f601",a:"0.6"},{n:["laughing","satisfied","smiling face with open mouth and tightly-closed eyes"],u:"1f606",a:"0.6"},{n:["sweat smile","smiling face with open mouth and cold sweat"],u:"1f605",a:"0.6"},{n:["rolling on the floor laughing"],u:"1f923",a:"3.0"},{n:["joy","face with tears of joy"],u:"1f602",a:"0.6"},{n:["slightly smiling face"],u:"1f642",a:"1.0"},{n:["upside-down face","upside down face"],u:"1f643",a:"1.0"},{n:["melting face"],u:"1fae0",a:"14.0"},{n:["wink","winking face"],u:"1f609",a:"0.6"},{n:["blush","smiling face with smiling eyes"],u:"1f60a",a:"0.6"},{n:["innocent","smiling face with halo"],u:"1f607",a:"1.0"},{n:["smiling face with 3 hearts","smiling face with smiling eyes and three hearts"],u:"1f970",a:"11.0"},{n:["heart eyes","smiling face with heart-shaped eyes"],u:"1f60d",a:"0.6"},{n:["star-struck","grinning face with star eyes"],u:"1f929",a:"5.0"},{n:["kissing heart","face throwing a kiss"],u:"1f618",a:"0.6"},{n:["kissing","kissing face"],u:"1f617",a:"1.0"},{n:["relaxed","white smiling face"],u:"263a-fe0f",a:"0.6"},{n:["kissing closed eyes","kissing face with closed eyes"],u:"1f61a",a:"0.6"},{n:["kissing smiling eyes","kissing face with smiling eyes"],u:"1f619",a:"1.0"},{n:["smiling face with tear"],u:"1f972",a:"13.0"},{n:["yum","face savouring delicious food"],u:"1f60b",a:"0.6"},{n:["stuck out tongue","face with stuck-out tongue"],u:"1f61b",a:"1.0"},{n:["stuck out tongue winking eye","face with stuck-out tongue and winking eye"],u:"1f61c",a:"0.6"},{n:["zany face","grinning face with one large and one small eye"],u:"1f92a",a:"5.0"},{n:["stuck out tongue closed eyes","face with stuck-out tongue and tightly-closed eyes"],u:"1f61d",a:"0.6"},{n:["money-mouth face","money mouth face"],u:"1f911",a:"1.0"},{n:["hugging face"],u:"1f917",a:"1.0"},{n:["face with hand over mouth","smiling face with smiling eyes and hand covering mouth"],u:"1f92d",a:"5.0"},{n:["face with open eyes and hand over mouth"],u:"1fae2",a:"14.0"},{n:["face with peeking eye"],u:"1fae3",a:"14.0"},{n:["shushing face","face with finger covering closed lips"],u:"1f92b",a:"5.0"},{n:["thinking face"],u:"1f914",a:"1.0"},{n:["saluting face"],u:"1fae1",a:"14.0"},{n:["zipper-mouth face","zipper mouth face"],u:"1f910",a:"1.0"},{n:["face with raised eyebrow","face with one eyebrow raised"],u:"1f928",a:"5.0"},{n:["neutral face"],u:"1f610",a:"0.7"},{n:["expressionless","expressionless face"],u:"1f611",a:"1.0"},{n:["no mouth","face without mouth"],u:"1f636",a:"1.0"},{n:["dotted line face"],u:"1fae5",a:"14.0"},{n:["face in clouds"],u:"1f636-200d-1f32b-fe0f",a:"13.1"},{n:["smirk","smirking face"],u:"1f60f",a:"0.6"},{n:["unamused","unamused face"],u:"1f612",a:"0.6"},{n:["face with rolling eyes"],u:"1f644",a:"1.0"},{n:["grimacing","grimacing face"],u:"1f62c",a:"1.0"},{n:["face exhaling"],u:"1f62e-200d-1f4a8",a:"13.1"},{n:["lying face"],u:"1f925",a:"3.0"},{n:["relieved","relieved face"],u:"1f60c",a:"0.6"},{n:["pensive","pensive face"],u:"1f614",a:"0.6"},{n:["sleepy","sleepy face"],u:"1f62a",a:"0.6"},{n:["drooling face"],u:"1f924",a:"3.0"},{n:["sleeping","sleeping face"],u:"1f634",a:"1.0"},{n:["mask","face with medical mask"],u:"1f637",a:"0.6"},{n:["face with thermometer"],u:"1f912",a:"1.0"},{n:["face with head-bandage","face with head bandage"],u:"1f915",a:"1.0"},{n:["nauseated face"],u:"1f922",a:"3.0"},{n:["face vomiting","face with open mouth vomiting"],u:"1f92e",a:"5.0"},{n:["sneezing face"],u:"1f927",a:"3.0"},{n:["hot face","overheated face"],u:"1f975",a:"11.0"},{n:["cold face","freezing face"],u:"1f976",a:"11.0"},{n:["woozy face","face with uneven eyes and wavy mouth"],u:"1f974",a:"11.0"},{n:["dizzy face"],u:"1f635",a:"0.6"},{n:["face with spiral eyes"],u:"1f635-200d-1f4ab",a:"13.1"},{n:["exploding head","shocked face with exploding head"],u:"1f92f",a:"5.0"},{n:["face with cowboy hat"],u:"1f920",a:"3.0"},{n:["partying face","face with party horn and party hat"],u:"1f973",a:"11.0"},{n:["disguised face"],u:"1f978",a:"13.0"},{n:["sunglasses","smiling face with sunglasses"],u:"1f60e",a:"1.0"},{n:["nerd face"],u:"1f913",a:"1.0"},{n:["face with monocle"],u:"1f9d0",a:"5.0"},{n:["confused","confused face"],u:"1f615",a:"1.0"},{n:["face with diagonal mouth"],u:"1fae4",a:"14.0"},{n:["worried","worried face"],u:"1f61f",a:"1.0"},{n:["slightly frowning face"],u:"1f641",a:"1.0"},{n:["frowning face","white frowning face"],u:"2639-fe0f",a:"0.7"},{n:["open mouth","face with open mouth"],u:"1f62e",a:"1.0"},{n:["hushed","hushed face"],u:"1f62f",a:"1.0"},{n:["astonished","astonished face"],u:"1f632",a:"0.6"},{n:["flushed","flushed face"],u:"1f633",a:"0.6"},{n:["pleading face","face with pleading eyes"],u:"1f97a",a:"11.0"},{n:["face holding back tears"],u:"1f979",a:"14.0"},{n:["frowning","frowning face with open mouth"],u:"1f626",a:"1.0"},{n:["anguished","anguished face"],u:"1f627",a:"1.0"},{n:["fearful","fearful face"],u:"1f628",a:"0.6"},{n:["cold sweat","face with open mouth and cold sweat"],u:"1f630",a:"0.6"},{n:["disappointed relieved","disappointed but relieved face"],u:"1f625",a:"0.6"},{n:["cry","crying face"],u:"1f622",a:"0.6"},{n:["sob","loudly crying face"],u:"1f62d",a:"0.6"},{n:["scream","face screaming in fear"],u:"1f631",a:"0.6"},{n:["confounded","confounded face"],u:"1f616",a:"0.6"},{n:["persevere","persevering face"],u:"1f623",a:"0.6"},{n:["disappointed","disappointed face"],u:"1f61e",a:"0.6"},{n:["sweat","face with cold sweat"],u:"1f613",a:"0.6"},{n:["weary","weary face"],u:"1f629",a:"0.6"},{n:["tired face"],u:"1f62b",a:"0.6"},{n:["yawning face"],u:"1f971",a:"12.0"},{n:["triumph","face with look of triumph"],u:"1f624",a:"0.6"},{n:["rage","pouting face"],u:"1f621",a:"0.6"},{n:["angry","angry face"],u:"1f620",a:"0.6"},{n:["face with symbols on mouth","serious face with symbols covering mouth"],u:"1f92c",a:"5.0"},{n:["smiling imp","smiling face with horns"],u:"1f608",a:"1.0"},{n:["imp"],u:"1f47f",a:"0.6"},{n:["skull"],u:"1f480",a:"0.6"},{n:["skull and crossbones"],u:"2620-fe0f",a:"1.0"},{n:["poop","shit","hankey","pile of poo"],u:"1f4a9",a:"0.6"},{n:["clown face"],u:"1f921",a:"3.0"},{n:["japanese ogre"],u:"1f479",a:"0.6"},{n:["japanese goblin"],u:"1f47a",a:"0.6"},{n:["ghost"],u:"1f47b",a:"0.6"},{n:["alien","extraterrestrial alien"],u:"1f47d",a:"0.6"},{n:["alien monster","space invader"],u:"1f47e",a:"0.6"},{n:["robot face"],u:"1f916",a:"1.0"},{n:["smiley cat","smiling cat face with open mouth"],u:"1f63a",a:"0.6"},{n:["smile cat","grinning cat face with smiling eyes"],u:"1f638",a:"0.6"},{n:["joy cat","cat face with tears of joy"],u:"1f639",a:"0.6"},{n:["heart eyes cat","smiling cat face with heart-shaped eyes"],u:"1f63b",a:"0.6"},{n:["smirk cat","cat face with wry smile"],u:"1f63c",a:"0.6"},{n:["kissing cat","kissing cat face with closed eyes"],u:"1f63d",a:"0.6"},{n:["scream cat","weary cat face"],u:"1f640",a:"0.6"},{n:["crying cat face"],u:"1f63f",a:"0.6"},{n:["pouting cat","pouting cat face"],u:"1f63e",a:"0.6"},{n:["see no evil","see-no-evil monkey"],u:"1f648",a:"0.6"},{n:["hear no evil","hear-no-evil monkey"],u:"1f649",a:"0.6"},{n:["speak no evil","speak-no-evil monkey"],u:"1f64a",a:"0.6"},{n:["kiss","kiss mark"],u:"1f48b",a:"0.6"},{n:["love letter"],u:"1f48c",a:"0.6"},{n:["cupid","heart with arrow"],u:"1f498",a:"0.6"},{n:["gift heart","heart with ribbon"],u:"1f49d",a:"0.6"},{n:["sparkling heart"],u:"1f496",a:"0.6"},{n:["heartpulse","growing heart"],u:"1f497",a:"0.6"},{n:["heartbeat","beating heart"],u:"1f493",a:"0.6"},{n:["revolving hearts"],u:"1f49e",a:"0.6"},{n:["two hearts"],u:"1f495",a:"0.6"},{n:["heart decoration"],u:"1f49f",a:"0.6"},{n:["heart exclamation","heavy heart exclamation mark ornament"],u:"2763-fe0f",a:"1.0"},{n:["broken heart"],u:"1f494",a:"0.6"},{n:["heart on fire"],u:"2764-fe0f-200d-1f525",a:"13.1"},{n:["mending heart"],u:"2764-fe0f-200d-1fa79",a:"13.1"},{n:["heart","heavy black heart"],u:"2764-fe0f",a:"0.6"},{n:["orange heart"],u:"1f9e1",a:"5.0"},{n:["yellow heart"],u:"1f49b",a:"0.6"},{n:["green heart"],u:"1f49a",a:"0.6"},{n:["blue heart"],u:"1f499",a:"0.6"},{n:["purple heart"],u:"1f49c",a:"0.6"},{n:["brown heart"],u:"1f90e",a:"12.0"},{n:["black heart"],u:"1f5a4",a:"3.0"},{n:["white heart"],u:"1f90d",a:"12.0"},{n:["100","hundred points symbol"],u:"1f4af",a:"0.6"},{n:["anger","anger symbol"],u:"1f4a2",a:"0.6"},{n:["boom","collision","collision symbol"],u:"1f4a5",a:"0.6"},{n:["dizzy","dizzy symbol"],u:"1f4ab",a:"0.6"},{n:["sweat drops","splashing sweat symbol"],u:"1f4a6",a:"0.6"},{n:["dash","dash symbol"],u:"1f4a8",a:"0.6"},{n:["hole"],u:"1f573-fe0f",a:"0.7"},{n:["bomb"],u:"1f4a3",a:"0.6"},{n:["speech balloon"],u:"1f4ac",a:"0.6"},{n:["eye in speech bubble","eye-in-speech-bubble"],u:"1f441-fe0f-200d-1f5e8-fe0f",a:"2.0"},{n:["left speech bubble"],u:"1f5e8-fe0f",a:"2.0"},{n:["right anger bubble"],u:"1f5ef-fe0f",a:"0.7"},{n:["thought balloon"],u:"1f4ad",a:"1.0"},{n:["zzz","sleeping symbol"],u:"1f4a4",a:"0.6"},{n:["wave","waving hand sign"],u:"1f44b",v:["1f44b-1f3fb","1f44b-1f3fc","1f44b-1f3fd","1f44b-1f3fe","1f44b-1f3ff"],a:"0.6"},{n:["raised back of hand"],u:"1f91a",v:["1f91a-1f3fb","1f91a-1f3fc","1f91a-1f3fd","1f91a-1f3fe","1f91a-1f3ff"],a:"3.0"},{n:["hand with fingers splayed","raised hand with fingers splayed"],u:"1f590-fe0f",v:["1f590-1f3fb","1f590-1f3fc","1f590-1f3fd","1f590-1f3fe","1f590-1f3ff"],a:"0.7"},{n:["hand","raised hand"],u:"270b",v:["270b-1f3fb","270b-1f3fc","270b-1f3fd","270b-1f3fe","270b-1f3ff"],a:"0.6"},{n:["spock-hand","raised hand with part between middle and ring fingers"],u:"1f596",v:["1f596-1f3fb","1f596-1f3fc","1f596-1f3fd","1f596-1f3fe","1f596-1f3ff"],a:"1.0"},{n:["rightwards hand"],u:"1faf1",v:["1faf1-1f3fb","1faf1-1f3fc","1faf1-1f3fd","1faf1-1f3fe","1faf1-1f3ff"],a:"14.0"},{n:["leftwards hand"],u:"1faf2",v:["1faf2-1f3fb","1faf2-1f3fc","1faf2-1f3fd","1faf2-1f3fe","1faf2-1f3ff"],a:"14.0"},{n:["palm down hand"],u:"1faf3",v:["1faf3-1f3fb","1faf3-1f3fc","1faf3-1f3fd","1faf3-1f3fe","1faf3-1f3ff"],a:"14.0"},{n:["palm up hand"],u:"1faf4",v:["1faf4-1f3fb","1faf4-1f3fc","1faf4-1f3fd","1faf4-1f3fe","1faf4-1f3ff"],a:"14.0"},{n:["ok hand","ok hand sign"],u:"1f44c",v:["1f44c-1f3fb","1f44c-1f3fc","1f44c-1f3fd","1f44c-1f3fe","1f44c-1f3ff"],a:"0.6"},{n:["pinched fingers"],u:"1f90c",v:["1f90c-1f3fb","1f90c-1f3fc","1f90c-1f3fd","1f90c-1f3fe","1f90c-1f3ff"],a:"13.0"},{n:["pinching hand"],u:"1f90f",v:["1f90f-1f3fb","1f90f-1f3fc","1f90f-1f3fd","1f90f-1f3fe","1f90f-1f3ff"],a:"12.0"},{n:["v","victory hand"],u:"270c-fe0f",v:["270c-1f3fb","270c-1f3fc","270c-1f3fd","270c-1f3fe","270c-1f3ff"],a:"0.6"},{n:["crossed fingers","hand with index and middle fingers crossed"],u:"1f91e",v:["1f91e-1f3fb","1f91e-1f3fc","1f91e-1f3fd","1f91e-1f3fe","1f91e-1f3ff"],a:"3.0"},{n:["hand with index finger and thumb crossed"],u:"1faf0",v:["1faf0-1f3fb","1faf0-1f3fc","1faf0-1f3fd","1faf0-1f3fe","1faf0-1f3ff"],a:"14.0"},{n:["i love you hand sign"],u:"1f91f",v:["1f91f-1f3fb","1f91f-1f3fc","1f91f-1f3fd","1f91f-1f3fe","1f91f-1f3ff"],a:"5.0"},{n:["the horns","sign of the horns"],u:"1f918",v:["1f918-1f3fb","1f918-1f3fc","1f918-1f3fd","1f918-1f3fe","1f918-1f3ff"],a:"1.0"},{n:["call me hand"],u:"1f919",v:["1f919-1f3fb","1f919-1f3fc","1f919-1f3fd","1f919-1f3fe","1f919-1f3ff"],a:"3.0"},{n:["point left","white left pointing backhand index"],u:"1f448",v:["1f448-1f3fb","1f448-1f3fc","1f448-1f3fd","1f448-1f3fe","1f448-1f3ff"],a:"0.6"},{n:["point right","white right pointing backhand index"],u:"1f449",v:["1f449-1f3fb","1f449-1f3fc","1f449-1f3fd","1f449-1f3fe","1f449-1f3ff"],a:"0.6"},{n:["point up 2","white up pointing backhand index"],u:"1f446",v:["1f446-1f3fb","1f446-1f3fc","1f446-1f3fd","1f446-1f3fe","1f446-1f3ff"],a:"0.6"},{n:["middle finger","reversed hand with middle finger extended"],u:"1f595",v:["1f595-1f3fb","1f595-1f3fc","1f595-1f3fd","1f595-1f3fe","1f595-1f3ff"],a:"1.0"},{n:["point down","white down pointing backhand index"],u:"1f447",v:["1f447-1f3fb","1f447-1f3fc","1f447-1f3fd","1f447-1f3fe","1f447-1f3ff"],a:"0.6"},{n:["point up","white up pointing index"],u:"261d-fe0f",v:["261d-1f3fb","261d-1f3fc","261d-1f3fd","261d-1f3fe","261d-1f3ff"],a:"0.6"},{n:["index pointing at the viewer"],u:"1faf5",v:["1faf5-1f3fb","1faf5-1f3fc","1faf5-1f3fd","1faf5-1f3fe","1faf5-1f3ff"],a:"14.0"},{n:["+1","thumbsup","thumbs up sign"],u:"1f44d",v:["1f44d-1f3fb","1f44d-1f3fc","1f44d-1f3fd","1f44d-1f3fe","1f44d-1f3ff"],a:"0.6"},{n:["-1","thumbsdown","thumbs down sign"],u:"1f44e",v:["1f44e-1f3fb","1f44e-1f3fc","1f44e-1f3fd","1f44e-1f3fe","1f44e-1f3ff"],a:"0.6"},{n:["fist","raised fist"],u:"270a",v:["270a-1f3fb","270a-1f3fc","270a-1f3fd","270a-1f3fe","270a-1f3ff"],a:"0.6"},{n:["punch","facepunch","fisted hand sign"],u:"1f44a",v:["1f44a-1f3fb","1f44a-1f3fc","1f44a-1f3fd","1f44a-1f3fe","1f44a-1f3ff"],a:"0.6"},{n:["left-facing fist"],u:"1f91b",v:["1f91b-1f3fb","1f91b-1f3fc","1f91b-1f3fd","1f91b-1f3fe","1f91b-1f3ff"],a:"3.0"},{n:["right-facing fist"],u:"1f91c",v:["1f91c-1f3fb","1f91c-1f3fc","1f91c-1f3fd","1f91c-1f3fe","1f91c-1f3ff"],a:"3.0"},{n:["clap","clapping hands sign"],u:"1f44f",v:["1f44f-1f3fb","1f44f-1f3fc","1f44f-1f3fd","1f44f-1f3fe","1f44f-1f3ff"],a:"0.6"},{n:["raised hands","person raising both hands in celebration"],u:"1f64c",v:["1f64c-1f3fb","1f64c-1f3fc","1f64c-1f3fd","1f64c-1f3fe","1f64c-1f3ff"],a:"0.6"},{n:["heart hands"],u:"1faf6",v:["1faf6-1f3fb","1faf6-1f3fc","1faf6-1f3fd","1faf6-1f3fe","1faf6-1f3ff"],a:"14.0"},{n:["open hands","open hands sign"],u:"1f450",v:["1f450-1f3fb","1f450-1f3fc","1f450-1f3fd","1f450-1f3fe","1f450-1f3ff"],a:"0.6"},{n:["palms up together"],u:"1f932",v:["1f932-1f3fb","1f932-1f3fc","1f932-1f3fd","1f932-1f3fe","1f932-1f3ff"],a:"5.0"},{n:["handshake"],u:"1f91d",v:["1f91d-1f3fb","1f91d-1f3fc","1f91d-1f3fd","1f91d-1f3fe","1f91d-1f3ff","1faf1-1f3fb-200d-1faf2-1f3fc","1faf1-1f3fb-200d-1faf2-1f3fd","1faf1-1f3fb-200d-1faf2-1f3fe","1faf1-1f3fb-200d-1faf2-1f3ff","1faf1-1f3fc-200d-1faf2-1f3fb","1faf1-1f3fc-200d-1faf2-1f3fd","1faf1-1f3fc-200d-1faf2-1f3fe","1faf1-1f3fc-200d-1faf2-1f3ff","1faf1-1f3fd-200d-1faf2-1f3fb","1faf1-1f3fd-200d-1faf2-1f3fc","1faf1-1f3fd-200d-1faf2-1f3fe","1faf1-1f3fd-200d-1faf2-1f3ff","1faf1-1f3fe-200d-1faf2-1f3fb","1faf1-1f3fe-200d-1faf2-1f3fc","1faf1-1f3fe-200d-1faf2-1f3fd","1faf1-1f3fe-200d-1faf2-1f3ff","1faf1-1f3ff-200d-1faf2-1f3fb","1faf1-1f3ff-200d-1faf2-1f3fc","1faf1-1f3ff-200d-1faf2-1f3fd","1faf1-1f3ff-200d-1faf2-1f3fe"],a:"3.0"},{n:["pray","person with folded hands"],u:"1f64f",v:["1f64f-1f3fb","1f64f-1f3fc","1f64f-1f3fd","1f64f-1f3fe","1f64f-1f3ff"],a:"0.6"},{n:["writing hand"],u:"270d-fe0f",v:["270d-1f3fb","270d-1f3fc","270d-1f3fd","270d-1f3fe","270d-1f3ff"],a:"0.7"},{n:["nail care","nail polish"],u:"1f485",v:["1f485-1f3fb","1f485-1f3fc","1f485-1f3fd","1f485-1f3fe","1f485-1f3ff"],a:"0.6"},{n:["selfie"],u:"1f933",v:["1f933-1f3fb","1f933-1f3fc","1f933-1f3fd","1f933-1f3fe","1f933-1f3ff"],a:"3.0"},{n:["muscle","flexed biceps"],u:"1f4aa",v:["1f4aa-1f3fb","1f4aa-1f3fc","1f4aa-1f3fd","1f4aa-1f3fe","1f4aa-1f3ff"],a:"0.6"},{n:["mechanical arm"],u:"1f9be",a:"12.0"},{n:["mechanical leg"],u:"1f9bf",a:"12.0"},{n:["leg"],u:"1f9b5",v:["1f9b5-1f3fb","1f9b5-1f3fc","1f9b5-1f3fd","1f9b5-1f3fe","1f9b5-1f3ff"],a:"11.0"},{n:["foot"],u:"1f9b6",v:["1f9b6-1f3fb","1f9b6-1f3fc","1f9b6-1f3fd","1f9b6-1f3fe","1f9b6-1f3ff"],a:"11.0"},{n:["ear"],u:"1f442",v:["1f442-1f3fb","1f442-1f3fc","1f442-1f3fd","1f442-1f3fe","1f442-1f3ff"],a:"0.6"},{n:["ear with hearing aid"],u:"1f9bb",v:["1f9bb-1f3fb","1f9bb-1f3fc","1f9bb-1f3fd","1f9bb-1f3fe","1f9bb-1f3ff"],a:"12.0"},{n:["nose"],u:"1f443",v:["1f443-1f3fb","1f443-1f3fc","1f443-1f3fd","1f443-1f3fe","1f443-1f3ff"],a:"0.6"},{n:["brain"],u:"1f9e0",a:"5.0"},{n:["anatomical heart"],u:"1fac0",a:"13.0"},{n:["lungs"],u:"1fac1",a:"13.0"},{n:["tooth"],u:"1f9b7",a:"11.0"},{n:["bone"],u:"1f9b4",a:"11.0"},{n:["eyes"],u:"1f440",a:"0.6"},{n:["eye"],u:"1f441-fe0f",a:"0.7"},{n:["tongue"],u:"1f445",a:"0.6"},{n:["lips","mouth"],u:"1f444",a:"0.6"},{n:["biting lip"],u:"1fae6",a:"14.0"},{n:["baby"],u:"1f476",v:["1f476-1f3fb","1f476-1f3fc","1f476-1f3fd","1f476-1f3fe","1f476-1f3ff"],a:"0.6"},{n:["child"],u:"1f9d2",v:["1f9d2-1f3fb","1f9d2-1f3fc","1f9d2-1f3fd","1f9d2-1f3fe","1f9d2-1f3ff"],a:"5.0"},{n:["boy"],u:"1f466",v:["1f466-1f3fb","1f466-1f3fc","1f466-1f3fd","1f466-1f3fe","1f466-1f3ff"],a:"0.6"},{n:["girl"],u:"1f467",v:["1f467-1f3fb","1f467-1f3fc","1f467-1f3fd","1f467-1f3fe","1f467-1f3ff"],a:"0.6"},{n:["adult"],u:"1f9d1",v:["1f9d1-1f3fb","1f9d1-1f3fc","1f9d1-1f3fd","1f9d1-1f3fe","1f9d1-1f3ff"],a:"5.0"},{n:["person with blond hair"],u:"1f471",v:["1f471-1f3fb","1f471-1f3fc","1f471-1f3fd","1f471-1f3fe","1f471-1f3ff"],a:"0.6"},{n:["man"],u:"1f468",v:["1f468-1f3fb","1f468-1f3fc","1f468-1f3fd","1f468-1f3fe","1f468-1f3ff"],a:"0.6"},{n:["bearded person"],u:"1f9d4",v:["1f9d4-1f3fb","1f9d4-1f3fc","1f9d4-1f3fd","1f9d4-1f3fe","1f9d4-1f3ff"],a:"5.0"},{n:["man: beard","man with beard"],u:"1f9d4-200d-2642-fe0f",v:["1f9d4-1f3fb-200d-2642-fe0f","1f9d4-1f3fc-200d-2642-fe0f","1f9d4-1f3fd-200d-2642-fe0f","1f9d4-1f3fe-200d-2642-fe0f","1f9d4-1f3ff-200d-2642-fe0f"],a:"13.1"},{n:["woman: beard","woman with beard"],u:"1f9d4-200d-2640-fe0f",v:["1f9d4-1f3fb-200d-2640-fe0f","1f9d4-1f3fc-200d-2640-fe0f","1f9d4-1f3fd-200d-2640-fe0f","1f9d4-1f3fe-200d-2640-fe0f","1f9d4-1f3ff-200d-2640-fe0f"],a:"13.1"},{n:["man: red hair","red haired man"],u:"1f468-200d-1f9b0",v:["1f468-1f3fb-200d-1f9b0","1f468-1f3fc-200d-1f9b0","1f468-1f3fd-200d-1f9b0","1f468-1f3fe-200d-1f9b0","1f468-1f3ff-200d-1f9b0"],a:"11.0"},{n:["man: curly hair","curly haired man"],u:"1f468-200d-1f9b1",v:["1f468-1f3fb-200d-1f9b1","1f468-1f3fc-200d-1f9b1","1f468-1f3fd-200d-1f9b1","1f468-1f3fe-200d-1f9b1","1f468-1f3ff-200d-1f9b1"],a:"11.0"},{n:["man: white hair","white haired man"],u:"1f468-200d-1f9b3",v:["1f468-1f3fb-200d-1f9b3","1f468-1f3fc-200d-1f9b3","1f468-1f3fd-200d-1f9b3","1f468-1f3fe-200d-1f9b3","1f468-1f3ff-200d-1f9b3"],a:"11.0"},{n:["bald man","man: bald"],u:"1f468-200d-1f9b2",v:["1f468-1f3fb-200d-1f9b2","1f468-1f3fc-200d-1f9b2","1f468-1f3fd-200d-1f9b2","1f468-1f3fe-200d-1f9b2","1f468-1f3ff-200d-1f9b2"],a:"11.0"},{n:["woman"],u:"1f469",v:["1f469-1f3fb","1f469-1f3fc","1f469-1f3fd","1f469-1f3fe","1f469-1f3ff"],a:"0.6"},{n:["woman: red hair","red haired woman"],u:"1f469-200d-1f9b0",v:["1f469-1f3fb-200d-1f9b0","1f469-1f3fc-200d-1f9b0","1f469-1f3fd-200d-1f9b0","1f469-1f3fe-200d-1f9b0","1f469-1f3ff-200d-1f9b0"],a:"11.0"},{n:["person: red hair","red haired person"],u:"1f9d1-200d-1f9b0",v:["1f9d1-1f3fb-200d-1f9b0","1f9d1-1f3fc-200d-1f9b0","1f9d1-1f3fd-200d-1f9b0","1f9d1-1f3fe-200d-1f9b0","1f9d1-1f3ff-200d-1f9b0"],a:"12.1"},{n:["woman: curly hair","curly haired woman"],u:"1f469-200d-1f9b1",v:["1f469-1f3fb-200d-1f9b1","1f469-1f3fc-200d-1f9b1","1f469-1f3fd-200d-1f9b1","1f469-1f3fe-200d-1f9b1","1f469-1f3ff-200d-1f9b1"],a:"11.0"},{n:["person: curly hair","curly haired person"],u:"1f9d1-200d-1f9b1",v:["1f9d1-1f3fb-200d-1f9b1","1f9d1-1f3fc-200d-1f9b1","1f9d1-1f3fd-200d-1f9b1","1f9d1-1f3fe-200d-1f9b1","1f9d1-1f3ff-200d-1f9b1"],a:"12.1"},{n:["woman: white hair","white haired woman"],u:"1f469-200d-1f9b3",v:["1f469-1f3fb-200d-1f9b3","1f469-1f3fc-200d-1f9b3","1f469-1f3fd-200d-1f9b3","1f469-1f3fe-200d-1f9b3","1f469-1f3ff-200d-1f9b3"],a:"11.0"},{n:["person: white hair","white haired person"],u:"1f9d1-200d-1f9b3",v:["1f9d1-1f3fb-200d-1f9b3","1f9d1-1f3fc-200d-1f9b3","1f9d1-1f3fd-200d-1f9b3","1f9d1-1f3fe-200d-1f9b3","1f9d1-1f3ff-200d-1f9b3"],a:"12.1"},{n:["bald woman","woman: bald"],u:"1f469-200d-1f9b2",v:["1f469-1f3fb-200d-1f9b2","1f469-1f3fc-200d-1f9b2","1f469-1f3fd-200d-1f9b2","1f469-1f3fe-200d-1f9b2","1f469-1f3ff-200d-1f9b2"],a:"11.0"},{n:["bald person","person: bald"],u:"1f9d1-200d-1f9b2",v:["1f9d1-1f3fb-200d-1f9b2","1f9d1-1f3fc-200d-1f9b2","1f9d1-1f3fd-200d-1f9b2","1f9d1-1f3fe-200d-1f9b2","1f9d1-1f3ff-200d-1f9b2"],a:"12.1"},{n:["woman: blond hair","blond-haired-woman"],u:"1f471-200d-2640-fe0f",v:["1f471-1f3fb-200d-2640-fe0f","1f471-1f3fc-200d-2640-fe0f","1f471-1f3fd-200d-2640-fe0f","1f471-1f3fe-200d-2640-fe0f","1f471-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["man: blond hair","blond-haired-man"],u:"1f471-200d-2642-fe0f",v:["1f471-1f3fb-200d-2642-fe0f","1f471-1f3fc-200d-2642-fe0f","1f471-1f3fd-200d-2642-fe0f","1f471-1f3fe-200d-2642-fe0f","1f471-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["older adult"],u:"1f9d3",v:["1f9d3-1f3fb","1f9d3-1f3fc","1f9d3-1f3fd","1f9d3-1f3fe","1f9d3-1f3ff"],a:"5.0"},{n:["older man"],u:"1f474",v:["1f474-1f3fb","1f474-1f3fc","1f474-1f3fd","1f474-1f3fe","1f474-1f3ff"],a:"0.6"},{n:["older woman"],u:"1f475",v:["1f475-1f3fb","1f475-1f3fc","1f475-1f3fd","1f475-1f3fe","1f475-1f3ff"],a:"0.6"},{n:["person frowning"],u:"1f64d",v:["1f64d-1f3fb","1f64d-1f3fc","1f64d-1f3fd","1f64d-1f3fe","1f64d-1f3ff"],a:"0.6"},{n:["man frowning","man-frowning"],u:"1f64d-200d-2642-fe0f",v:["1f64d-1f3fb-200d-2642-fe0f","1f64d-1f3fc-200d-2642-fe0f","1f64d-1f3fd-200d-2642-fe0f","1f64d-1f3fe-200d-2642-fe0f","1f64d-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman frowning","woman-frowning"],u:"1f64d-200d-2640-fe0f",v:["1f64d-1f3fb-200d-2640-fe0f","1f64d-1f3fc-200d-2640-fe0f","1f64d-1f3fd-200d-2640-fe0f","1f64d-1f3fe-200d-2640-fe0f","1f64d-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["person with pouting face"],u:"1f64e",v:["1f64e-1f3fb","1f64e-1f3fc","1f64e-1f3fd","1f64e-1f3fe","1f64e-1f3ff"],a:"0.6"},{n:["man pouting","man-pouting"],u:"1f64e-200d-2642-fe0f",v:["1f64e-1f3fb-200d-2642-fe0f","1f64e-1f3fc-200d-2642-fe0f","1f64e-1f3fd-200d-2642-fe0f","1f64e-1f3fe-200d-2642-fe0f","1f64e-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman pouting","woman-pouting"],u:"1f64e-200d-2640-fe0f",v:["1f64e-1f3fb-200d-2640-fe0f","1f64e-1f3fc-200d-2640-fe0f","1f64e-1f3fd-200d-2640-fe0f","1f64e-1f3fe-200d-2640-fe0f","1f64e-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["no good","face with no good gesture"],u:"1f645",v:["1f645-1f3fb","1f645-1f3fc","1f645-1f3fd","1f645-1f3fe","1f645-1f3ff"],a:"0.6"},{n:["man gesturing no","man-gesturing-no"],u:"1f645-200d-2642-fe0f",v:["1f645-1f3fb-200d-2642-fe0f","1f645-1f3fc-200d-2642-fe0f","1f645-1f3fd-200d-2642-fe0f","1f645-1f3fe-200d-2642-fe0f","1f645-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman gesturing no","woman-gesturing-no"],u:"1f645-200d-2640-fe0f",v:["1f645-1f3fb-200d-2640-fe0f","1f645-1f3fc-200d-2640-fe0f","1f645-1f3fd-200d-2640-fe0f","1f645-1f3fe-200d-2640-fe0f","1f645-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["ok woman","face with ok gesture"],u:"1f646",v:["1f646-1f3fb","1f646-1f3fc","1f646-1f3fd","1f646-1f3fe","1f646-1f3ff"],a:"0.6"},{n:["man gesturing ok","man-gesturing-ok"],u:"1f646-200d-2642-fe0f",v:["1f646-1f3fb-200d-2642-fe0f","1f646-1f3fc-200d-2642-fe0f","1f646-1f3fd-200d-2642-fe0f","1f646-1f3fe-200d-2642-fe0f","1f646-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman gesturing ok","woman-gesturing-ok"],u:"1f646-200d-2640-fe0f",v:["1f646-1f3fb-200d-2640-fe0f","1f646-1f3fc-200d-2640-fe0f","1f646-1f3fd-200d-2640-fe0f","1f646-1f3fe-200d-2640-fe0f","1f646-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["information desk person"],u:"1f481",v:["1f481-1f3fb","1f481-1f3fc","1f481-1f3fd","1f481-1f3fe","1f481-1f3ff"],a:"0.6"},{n:["man tipping hand","man-tipping-hand"],u:"1f481-200d-2642-fe0f",v:["1f481-1f3fb-200d-2642-fe0f","1f481-1f3fc-200d-2642-fe0f","1f481-1f3fd-200d-2642-fe0f","1f481-1f3fe-200d-2642-fe0f","1f481-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman tipping hand","woman-tipping-hand"],u:"1f481-200d-2640-fe0f",v:["1f481-1f3fb-200d-2640-fe0f","1f481-1f3fc-200d-2640-fe0f","1f481-1f3fd-200d-2640-fe0f","1f481-1f3fe-200d-2640-fe0f","1f481-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["raising hand","happy person raising one hand"],u:"1f64b",v:["1f64b-1f3fb","1f64b-1f3fc","1f64b-1f3fd","1f64b-1f3fe","1f64b-1f3ff"],a:"0.6"},{n:["man raising hand","man-raising-hand"],u:"1f64b-200d-2642-fe0f",v:["1f64b-1f3fb-200d-2642-fe0f","1f64b-1f3fc-200d-2642-fe0f","1f64b-1f3fd-200d-2642-fe0f","1f64b-1f3fe-200d-2642-fe0f","1f64b-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman raising hand","woman-raising-hand"],u:"1f64b-200d-2640-fe0f",v:["1f64b-1f3fb-200d-2640-fe0f","1f64b-1f3fc-200d-2640-fe0f","1f64b-1f3fd-200d-2640-fe0f","1f64b-1f3fe-200d-2640-fe0f","1f64b-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["deaf person"],u:"1f9cf",v:["1f9cf-1f3fb","1f9cf-1f3fc","1f9cf-1f3fd","1f9cf-1f3fe","1f9cf-1f3ff"],a:"12.0"},{n:["deaf man"],u:"1f9cf-200d-2642-fe0f",v:["1f9cf-1f3fb-200d-2642-fe0f","1f9cf-1f3fc-200d-2642-fe0f","1f9cf-1f3fd-200d-2642-fe0f","1f9cf-1f3fe-200d-2642-fe0f","1f9cf-1f3ff-200d-2642-fe0f"],a:"12.0"},{n:["deaf woman"],u:"1f9cf-200d-2640-fe0f",v:["1f9cf-1f3fb-200d-2640-fe0f","1f9cf-1f3fc-200d-2640-fe0f","1f9cf-1f3fd-200d-2640-fe0f","1f9cf-1f3fe-200d-2640-fe0f","1f9cf-1f3ff-200d-2640-fe0f"],a:"12.0"},{n:["bow","person bowing deeply"],u:"1f647",v:["1f647-1f3fb","1f647-1f3fc","1f647-1f3fd","1f647-1f3fe","1f647-1f3ff"],a:"0.6"},{n:["man bowing","man-bowing"],u:"1f647-200d-2642-fe0f",v:["1f647-1f3fb-200d-2642-fe0f","1f647-1f3fc-200d-2642-fe0f","1f647-1f3fd-200d-2642-fe0f","1f647-1f3fe-200d-2642-fe0f","1f647-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman bowing","woman-bowing"],u:"1f647-200d-2640-fe0f",v:["1f647-1f3fb-200d-2640-fe0f","1f647-1f3fc-200d-2640-fe0f","1f647-1f3fd-200d-2640-fe0f","1f647-1f3fe-200d-2640-fe0f","1f647-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["face palm"],u:"1f926",v:["1f926-1f3fb","1f926-1f3fc","1f926-1f3fd","1f926-1f3fe","1f926-1f3ff"],a:"3.0"},{n:["man facepalming","man-facepalming"],u:"1f926-200d-2642-fe0f",v:["1f926-1f3fb-200d-2642-fe0f","1f926-1f3fc-200d-2642-fe0f","1f926-1f3fd-200d-2642-fe0f","1f926-1f3fe-200d-2642-fe0f","1f926-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman facepalming","woman-facepalming"],u:"1f926-200d-2640-fe0f",v:["1f926-1f3fb-200d-2640-fe0f","1f926-1f3fc-200d-2640-fe0f","1f926-1f3fd-200d-2640-fe0f","1f926-1f3fe-200d-2640-fe0f","1f926-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["shrug"],u:"1f937",v:["1f937-1f3fb","1f937-1f3fc","1f937-1f3fd","1f937-1f3fe","1f937-1f3ff"],a:"3.0"},{n:["man shrugging","man-shrugging"],u:"1f937-200d-2642-fe0f",v:["1f937-1f3fb-200d-2642-fe0f","1f937-1f3fc-200d-2642-fe0f","1f937-1f3fd-200d-2642-fe0f","1f937-1f3fe-200d-2642-fe0f","1f937-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman shrugging","woman-shrugging"],u:"1f937-200d-2640-fe0f",v:["1f937-1f3fb-200d-2640-fe0f","1f937-1f3fc-200d-2640-fe0f","1f937-1f3fd-200d-2640-fe0f","1f937-1f3fe-200d-2640-fe0f","1f937-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["health worker"],u:"1f9d1-200d-2695-fe0f",v:["1f9d1-1f3fb-200d-2695-fe0f","1f9d1-1f3fc-200d-2695-fe0f","1f9d1-1f3fd-200d-2695-fe0f","1f9d1-1f3fe-200d-2695-fe0f","1f9d1-1f3ff-200d-2695-fe0f"],a:"12.1"},{n:["male-doctor","man health worker"],u:"1f468-200d-2695-fe0f",v:["1f468-1f3fb-200d-2695-fe0f","1f468-1f3fc-200d-2695-fe0f","1f468-1f3fd-200d-2695-fe0f","1f468-1f3fe-200d-2695-fe0f","1f468-1f3ff-200d-2695-fe0f"],a:"4.0"},{n:["female-doctor","woman health worker"],u:"1f469-200d-2695-fe0f",v:["1f469-1f3fb-200d-2695-fe0f","1f469-1f3fc-200d-2695-fe0f","1f469-1f3fd-200d-2695-fe0f","1f469-1f3fe-200d-2695-fe0f","1f469-1f3ff-200d-2695-fe0f"],a:"4.0"},{n:["student"],u:"1f9d1-200d-1f393",v:["1f9d1-1f3fb-200d-1f393","1f9d1-1f3fc-200d-1f393","1f9d1-1f3fd-200d-1f393","1f9d1-1f3fe-200d-1f393","1f9d1-1f3ff-200d-1f393"],a:"12.1"},{n:["man student","male-student"],u:"1f468-200d-1f393",v:["1f468-1f3fb-200d-1f393","1f468-1f3fc-200d-1f393","1f468-1f3fd-200d-1f393","1f468-1f3fe-200d-1f393","1f468-1f3ff-200d-1f393"],a:"4.0"},{n:["woman student","female-student"],u:"1f469-200d-1f393",v:["1f469-1f3fb-200d-1f393","1f469-1f3fc-200d-1f393","1f469-1f3fd-200d-1f393","1f469-1f3fe-200d-1f393","1f469-1f3ff-200d-1f393"],a:"4.0"},{n:["teacher"],u:"1f9d1-200d-1f3eb",v:["1f9d1-1f3fb-200d-1f3eb","1f9d1-1f3fc-200d-1f3eb","1f9d1-1f3fd-200d-1f3eb","1f9d1-1f3fe-200d-1f3eb","1f9d1-1f3ff-200d-1f3eb"],a:"12.1"},{n:["man teacher","male-teacher"],u:"1f468-200d-1f3eb",v:["1f468-1f3fb-200d-1f3eb","1f468-1f3fc-200d-1f3eb","1f468-1f3fd-200d-1f3eb","1f468-1f3fe-200d-1f3eb","1f468-1f3ff-200d-1f3eb"],a:"4.0"},{n:["woman teacher","female-teacher"],u:"1f469-200d-1f3eb",v:["1f469-1f3fb-200d-1f3eb","1f469-1f3fc-200d-1f3eb","1f469-1f3fd-200d-1f3eb","1f469-1f3fe-200d-1f3eb","1f469-1f3ff-200d-1f3eb"],a:"4.0"},{n:["judge"],u:"1f9d1-200d-2696-fe0f",v:["1f9d1-1f3fb-200d-2696-fe0f","1f9d1-1f3fc-200d-2696-fe0f","1f9d1-1f3fd-200d-2696-fe0f","1f9d1-1f3fe-200d-2696-fe0f","1f9d1-1f3ff-200d-2696-fe0f"],a:"12.1"},{n:["man judge","male-judge"],u:"1f468-200d-2696-fe0f",v:["1f468-1f3fb-200d-2696-fe0f","1f468-1f3fc-200d-2696-fe0f","1f468-1f3fd-200d-2696-fe0f","1f468-1f3fe-200d-2696-fe0f","1f468-1f3ff-200d-2696-fe0f"],a:"4.0"},{n:["woman judge","female-judge"],u:"1f469-200d-2696-fe0f",v:["1f469-1f3fb-200d-2696-fe0f","1f469-1f3fc-200d-2696-fe0f","1f469-1f3fd-200d-2696-fe0f","1f469-1f3fe-200d-2696-fe0f","1f469-1f3ff-200d-2696-fe0f"],a:"4.0"},{n:["farmer"],u:"1f9d1-200d-1f33e",v:["1f9d1-1f3fb-200d-1f33e","1f9d1-1f3fc-200d-1f33e","1f9d1-1f3fd-200d-1f33e","1f9d1-1f3fe-200d-1f33e","1f9d1-1f3ff-200d-1f33e"],a:"12.1"},{n:["man farmer","male-farmer"],u:"1f468-200d-1f33e",v:["1f468-1f3fb-200d-1f33e","1f468-1f3fc-200d-1f33e","1f468-1f3fd-200d-1f33e","1f468-1f3fe-200d-1f33e","1f468-1f3ff-200d-1f33e"],a:"4.0"},{n:["woman farmer","female-farmer"],u:"1f469-200d-1f33e",v:["1f469-1f3fb-200d-1f33e","1f469-1f3fc-200d-1f33e","1f469-1f3fd-200d-1f33e","1f469-1f3fe-200d-1f33e","1f469-1f3ff-200d-1f33e"],a:"4.0"},{n:["cook"],u:"1f9d1-200d-1f373",v:["1f9d1-1f3fb-200d-1f373","1f9d1-1f3fc-200d-1f373","1f9d1-1f3fd-200d-1f373","1f9d1-1f3fe-200d-1f373","1f9d1-1f3ff-200d-1f373"],a:"12.1"},{n:["man cook","male-cook"],u:"1f468-200d-1f373",v:["1f468-1f3fb-200d-1f373","1f468-1f3fc-200d-1f373","1f468-1f3fd-200d-1f373","1f468-1f3fe-200d-1f373","1f468-1f3ff-200d-1f373"],a:"4.0"},{n:["woman cook","female-cook"],u:"1f469-200d-1f373",v:["1f469-1f3fb-200d-1f373","1f469-1f3fc-200d-1f373","1f469-1f3fd-200d-1f373","1f469-1f3fe-200d-1f373","1f469-1f3ff-200d-1f373"],a:"4.0"},{n:["mechanic"],u:"1f9d1-200d-1f527",v:["1f9d1-1f3fb-200d-1f527","1f9d1-1f3fc-200d-1f527","1f9d1-1f3fd-200d-1f527","1f9d1-1f3fe-200d-1f527","1f9d1-1f3ff-200d-1f527"],a:"12.1"},{n:["man mechanic","male-mechanic"],u:"1f468-200d-1f527",v:["1f468-1f3fb-200d-1f527","1f468-1f3fc-200d-1f527","1f468-1f3fd-200d-1f527","1f468-1f3fe-200d-1f527","1f468-1f3ff-200d-1f527"],a:"4.0"},{n:["woman mechanic","female-mechanic"],u:"1f469-200d-1f527",v:["1f469-1f3fb-200d-1f527","1f469-1f3fc-200d-1f527","1f469-1f3fd-200d-1f527","1f469-1f3fe-200d-1f527","1f469-1f3ff-200d-1f527"],a:"4.0"},{n:["factory worker"],u:"1f9d1-200d-1f3ed",v:["1f9d1-1f3fb-200d-1f3ed","1f9d1-1f3fc-200d-1f3ed","1f9d1-1f3fd-200d-1f3ed","1f9d1-1f3fe-200d-1f3ed","1f9d1-1f3ff-200d-1f3ed"],a:"12.1"},{n:["man factory worker","male-factory-worker"],u:"1f468-200d-1f3ed",v:["1f468-1f3fb-200d-1f3ed","1f468-1f3fc-200d-1f3ed","1f468-1f3fd-200d-1f3ed","1f468-1f3fe-200d-1f3ed","1f468-1f3ff-200d-1f3ed"],a:"4.0"},{n:["woman factory worker","female-factory-worker"],u:"1f469-200d-1f3ed",v:["1f469-1f3fb-200d-1f3ed","1f469-1f3fc-200d-1f3ed","1f469-1f3fd-200d-1f3ed","1f469-1f3fe-200d-1f3ed","1f469-1f3ff-200d-1f3ed"],a:"4.0"},{n:["office worker"],u:"1f9d1-200d-1f4bc",v:["1f9d1-1f3fb-200d-1f4bc","1f9d1-1f3fc-200d-1f4bc","1f9d1-1f3fd-200d-1f4bc","1f9d1-1f3fe-200d-1f4bc","1f9d1-1f3ff-200d-1f4bc"],a:"12.1"},{n:["man office worker","male-office-worker"],u:"1f468-200d-1f4bc",v:["1f468-1f3fb-200d-1f4bc","1f468-1f3fc-200d-1f4bc","1f468-1f3fd-200d-1f4bc","1f468-1f3fe-200d-1f4bc","1f468-1f3ff-200d-1f4bc"],a:"4.0"},{n:["woman office worker","female-office-worker"],u:"1f469-200d-1f4bc",v:["1f469-1f3fb-200d-1f4bc","1f469-1f3fc-200d-1f4bc","1f469-1f3fd-200d-1f4bc","1f469-1f3fe-200d-1f4bc","1f469-1f3ff-200d-1f4bc"],a:"4.0"},{n:["scientist"],u:"1f9d1-200d-1f52c",v:["1f9d1-1f3fb-200d-1f52c","1f9d1-1f3fc-200d-1f52c","1f9d1-1f3fd-200d-1f52c","1f9d1-1f3fe-200d-1f52c","1f9d1-1f3ff-200d-1f52c"],a:"12.1"},{n:["man scientist","male-scientist"],u:"1f468-200d-1f52c",v:["1f468-1f3fb-200d-1f52c","1f468-1f3fc-200d-1f52c","1f468-1f3fd-200d-1f52c","1f468-1f3fe-200d-1f52c","1f468-1f3ff-200d-1f52c"],a:"4.0"},{n:["woman scientist","female-scientist"],u:"1f469-200d-1f52c",v:["1f469-1f3fb-200d-1f52c","1f469-1f3fc-200d-1f52c","1f469-1f3fd-200d-1f52c","1f469-1f3fe-200d-1f52c","1f469-1f3ff-200d-1f52c"],a:"4.0"},{n:["technologist"],u:"1f9d1-200d-1f4bb",v:["1f9d1-1f3fb-200d-1f4bb","1f9d1-1f3fc-200d-1f4bb","1f9d1-1f3fd-200d-1f4bb","1f9d1-1f3fe-200d-1f4bb","1f9d1-1f3ff-200d-1f4bb"],a:"12.1"},{n:["man technologist","male-technologist"],u:"1f468-200d-1f4bb",v:["1f468-1f3fb-200d-1f4bb","1f468-1f3fc-200d-1f4bb","1f468-1f3fd-200d-1f4bb","1f468-1f3fe-200d-1f4bb","1f468-1f3ff-200d-1f4bb"],a:"4.0"},{n:["woman technologist","female-technologist"],u:"1f469-200d-1f4bb",v:["1f469-1f3fb-200d-1f4bb","1f469-1f3fc-200d-1f4bb","1f469-1f3fd-200d-1f4bb","1f469-1f3fe-200d-1f4bb","1f469-1f3ff-200d-1f4bb"],a:"4.0"},{n:["singer"],u:"1f9d1-200d-1f3a4",v:["1f9d1-1f3fb-200d-1f3a4","1f9d1-1f3fc-200d-1f3a4","1f9d1-1f3fd-200d-1f3a4","1f9d1-1f3fe-200d-1f3a4","1f9d1-1f3ff-200d-1f3a4"],a:"12.1"},{n:["man singer","male-singer"],u:"1f468-200d-1f3a4",v:["1f468-1f3fb-200d-1f3a4","1f468-1f3fc-200d-1f3a4","1f468-1f3fd-200d-1f3a4","1f468-1f3fe-200d-1f3a4","1f468-1f3ff-200d-1f3a4"],a:"4.0"},{n:["woman singer","female-singer"],u:"1f469-200d-1f3a4",v:["1f469-1f3fb-200d-1f3a4","1f469-1f3fc-200d-1f3a4","1f469-1f3fd-200d-1f3a4","1f469-1f3fe-200d-1f3a4","1f469-1f3ff-200d-1f3a4"],a:"4.0"},{n:["artist"],u:"1f9d1-200d-1f3a8",v:["1f9d1-1f3fb-200d-1f3a8","1f9d1-1f3fc-200d-1f3a8","1f9d1-1f3fd-200d-1f3a8","1f9d1-1f3fe-200d-1f3a8","1f9d1-1f3ff-200d-1f3a8"],a:"12.1"},{n:["man artist","male-artist"],u:"1f468-200d-1f3a8",v:["1f468-1f3fb-200d-1f3a8","1f468-1f3fc-200d-1f3a8","1f468-1f3fd-200d-1f3a8","1f468-1f3fe-200d-1f3a8","1f468-1f3ff-200d-1f3a8"],a:"4.0"},{n:["woman artist","female-artist"],u:"1f469-200d-1f3a8",v:["1f469-1f3fb-200d-1f3a8","1f469-1f3fc-200d-1f3a8","1f469-1f3fd-200d-1f3a8","1f469-1f3fe-200d-1f3a8","1f469-1f3ff-200d-1f3a8"],a:"4.0"},{n:["pilot"],u:"1f9d1-200d-2708-fe0f",v:["1f9d1-1f3fb-200d-2708-fe0f","1f9d1-1f3fc-200d-2708-fe0f","1f9d1-1f3fd-200d-2708-fe0f","1f9d1-1f3fe-200d-2708-fe0f","1f9d1-1f3ff-200d-2708-fe0f"],a:"12.1"},{n:["man pilot","male-pilot"],u:"1f468-200d-2708-fe0f",v:["1f468-1f3fb-200d-2708-fe0f","1f468-1f3fc-200d-2708-fe0f","1f468-1f3fd-200d-2708-fe0f","1f468-1f3fe-200d-2708-fe0f","1f468-1f3ff-200d-2708-fe0f"],a:"4.0"},{n:["woman pilot","female-pilot"],u:"1f469-200d-2708-fe0f",v:["1f469-1f3fb-200d-2708-fe0f","1f469-1f3fc-200d-2708-fe0f","1f469-1f3fd-200d-2708-fe0f","1f469-1f3fe-200d-2708-fe0f","1f469-1f3ff-200d-2708-fe0f"],a:"4.0"},{n:["astronaut"],u:"1f9d1-200d-1f680",v:["1f9d1-1f3fb-200d-1f680","1f9d1-1f3fc-200d-1f680","1f9d1-1f3fd-200d-1f680","1f9d1-1f3fe-200d-1f680","1f9d1-1f3ff-200d-1f680"],a:"12.1"},{n:["man astronaut","male-astronaut"],u:"1f468-200d-1f680",v:["1f468-1f3fb-200d-1f680","1f468-1f3fc-200d-1f680","1f468-1f3fd-200d-1f680","1f468-1f3fe-200d-1f680","1f468-1f3ff-200d-1f680"],a:"4.0"},{n:["woman astronaut","female-astronaut"],u:"1f469-200d-1f680",v:["1f469-1f3fb-200d-1f680","1f469-1f3fc-200d-1f680","1f469-1f3fd-200d-1f680","1f469-1f3fe-200d-1f680","1f469-1f3ff-200d-1f680"],a:"4.0"},{n:["firefighter"],u:"1f9d1-200d-1f692",v:["1f9d1-1f3fb-200d-1f692","1f9d1-1f3fc-200d-1f692","1f9d1-1f3fd-200d-1f692","1f9d1-1f3fe-200d-1f692","1f9d1-1f3ff-200d-1f692"],a:"12.1"},{n:["man firefighter","male-firefighter"],u:"1f468-200d-1f692",v:["1f468-1f3fb-200d-1f692","1f468-1f3fc-200d-1f692","1f468-1f3fd-200d-1f692","1f468-1f3fe-200d-1f692","1f468-1f3ff-200d-1f692"],a:"4.0"},{n:["woman firefighter","female-firefighter"],u:"1f469-200d-1f692",v:["1f469-1f3fb-200d-1f692","1f469-1f3fc-200d-1f692","1f469-1f3fd-200d-1f692","1f469-1f3fe-200d-1f692","1f469-1f3ff-200d-1f692"],a:"4.0"},{n:["cop","police officer"],u:"1f46e",v:["1f46e-1f3fb","1f46e-1f3fc","1f46e-1f3fd","1f46e-1f3fe","1f46e-1f3ff"],a:"0.6"},{n:["man police officer","male-police-officer"],u:"1f46e-200d-2642-fe0f",v:["1f46e-1f3fb-200d-2642-fe0f","1f46e-1f3fc-200d-2642-fe0f","1f46e-1f3fd-200d-2642-fe0f","1f46e-1f3fe-200d-2642-fe0f","1f46e-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman police officer","female-police-officer"],u:"1f46e-200d-2640-fe0f",v:["1f46e-1f3fb-200d-2640-fe0f","1f46e-1f3fc-200d-2640-fe0f","1f46e-1f3fd-200d-2640-fe0f","1f46e-1f3fe-200d-2640-fe0f","1f46e-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["detective","sleuth or spy"],u:"1f575-fe0f",v:["1f575-1f3fb","1f575-1f3fc","1f575-1f3fd","1f575-1f3fe","1f575-1f3ff"],a:"0.7"},{n:["man detective","male-detective"],u:"1f575-fe0f-200d-2642-fe0f",v:["1f575-1f3fb-200d-2642-fe0f","1f575-1f3fc-200d-2642-fe0f","1f575-1f3fd-200d-2642-fe0f","1f575-1f3fe-200d-2642-fe0f","1f575-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman detective","female-detective"],u:"1f575-fe0f-200d-2640-fe0f",v:["1f575-1f3fb-200d-2640-fe0f","1f575-1f3fc-200d-2640-fe0f","1f575-1f3fd-200d-2640-fe0f","1f575-1f3fe-200d-2640-fe0f","1f575-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["guardsman"],u:"1f482",v:["1f482-1f3fb","1f482-1f3fc","1f482-1f3fd","1f482-1f3fe","1f482-1f3ff"],a:"0.6"},{n:["man guard","male-guard"],u:"1f482-200d-2642-fe0f",v:["1f482-1f3fb-200d-2642-fe0f","1f482-1f3fc-200d-2642-fe0f","1f482-1f3fd-200d-2642-fe0f","1f482-1f3fe-200d-2642-fe0f","1f482-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman guard","female-guard"],u:"1f482-200d-2640-fe0f",v:["1f482-1f3fb-200d-2640-fe0f","1f482-1f3fc-200d-2640-fe0f","1f482-1f3fd-200d-2640-fe0f","1f482-1f3fe-200d-2640-fe0f","1f482-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["ninja"],u:"1f977",v:["1f977-1f3fb","1f977-1f3fc","1f977-1f3fd","1f977-1f3fe","1f977-1f3ff"],a:"13.0"},{n:["construction worker"],u:"1f477",v:["1f477-1f3fb","1f477-1f3fc","1f477-1f3fd","1f477-1f3fe","1f477-1f3ff"],a:"0.6"},{n:["man construction worker","male-construction-worker"],u:"1f477-200d-2642-fe0f",v:["1f477-1f3fb-200d-2642-fe0f","1f477-1f3fc-200d-2642-fe0f","1f477-1f3fd-200d-2642-fe0f","1f477-1f3fe-200d-2642-fe0f","1f477-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman construction worker","female-construction-worker"],u:"1f477-200d-2640-fe0f",v:["1f477-1f3fb-200d-2640-fe0f","1f477-1f3fc-200d-2640-fe0f","1f477-1f3fd-200d-2640-fe0f","1f477-1f3fe-200d-2640-fe0f","1f477-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["person with crown"],u:"1fac5",v:["1fac5-1f3fb","1fac5-1f3fc","1fac5-1f3fd","1fac5-1f3fe","1fac5-1f3ff"],a:"14.0"},{n:["prince"],u:"1f934",v:["1f934-1f3fb","1f934-1f3fc","1f934-1f3fd","1f934-1f3fe","1f934-1f3ff"],a:"3.0"},{n:["princess"],u:"1f478",v:["1f478-1f3fb","1f478-1f3fc","1f478-1f3fd","1f478-1f3fe","1f478-1f3ff"],a:"0.6"},{n:["man with turban"],u:"1f473",v:["1f473-1f3fb","1f473-1f3fc","1f473-1f3fd","1f473-1f3fe","1f473-1f3ff"],a:"0.6"},{n:["man wearing turban","man-wearing-turban"],u:"1f473-200d-2642-fe0f",v:["1f473-1f3fb-200d-2642-fe0f","1f473-1f3fc-200d-2642-fe0f","1f473-1f3fd-200d-2642-fe0f","1f473-1f3fe-200d-2642-fe0f","1f473-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman wearing turban","woman-wearing-turban"],u:"1f473-200d-2640-fe0f",v:["1f473-1f3fb-200d-2640-fe0f","1f473-1f3fc-200d-2640-fe0f","1f473-1f3fd-200d-2640-fe0f","1f473-1f3fe-200d-2640-fe0f","1f473-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["man with gua pi mao"],u:"1f472",v:["1f472-1f3fb","1f472-1f3fc","1f472-1f3fd","1f472-1f3fe","1f472-1f3ff"],a:"0.6"},{n:["person with headscarf"],u:"1f9d5",v:["1f9d5-1f3fb","1f9d5-1f3fc","1f9d5-1f3fd","1f9d5-1f3fe","1f9d5-1f3ff"],a:"5.0"},{n:["man in tuxedo","person in tuxedo"],u:"1f935",v:["1f935-1f3fb","1f935-1f3fc","1f935-1f3fd","1f935-1f3fe","1f935-1f3ff"],a:"3.0"},{n:["man in tuxedo"],u:"1f935-200d-2642-fe0f",v:["1f935-1f3fb-200d-2642-fe0f","1f935-1f3fc-200d-2642-fe0f","1f935-1f3fd-200d-2642-fe0f","1f935-1f3fe-200d-2642-fe0f","1f935-1f3ff-200d-2642-fe0f"],a:"13.0"},{n:["woman in tuxedo"],u:"1f935-200d-2640-fe0f",v:["1f935-1f3fb-200d-2640-fe0f","1f935-1f3fc-200d-2640-fe0f","1f935-1f3fd-200d-2640-fe0f","1f935-1f3fe-200d-2640-fe0f","1f935-1f3ff-200d-2640-fe0f"],a:"13.0"},{n:["bride with veil"],u:"1f470",v:["1f470-1f3fb","1f470-1f3fc","1f470-1f3fd","1f470-1f3fe","1f470-1f3ff"],a:"0.6"},{n:["man with veil"],u:"1f470-200d-2642-fe0f",v:["1f470-1f3fb-200d-2642-fe0f","1f470-1f3fc-200d-2642-fe0f","1f470-1f3fd-200d-2642-fe0f","1f470-1f3fe-200d-2642-fe0f","1f470-1f3ff-200d-2642-fe0f"],a:"13.0"},{n:["woman with veil"],u:"1f470-200d-2640-fe0f",v:["1f470-1f3fb-200d-2640-fe0f","1f470-1f3fc-200d-2640-fe0f","1f470-1f3fd-200d-2640-fe0f","1f470-1f3fe-200d-2640-fe0f","1f470-1f3ff-200d-2640-fe0f"],a:"13.0"},{n:["pregnant woman"],u:"1f930",v:["1f930-1f3fb","1f930-1f3fc","1f930-1f3fd","1f930-1f3fe","1f930-1f3ff"],a:"3.0"},{n:["pregnant man"],u:"1fac3",v:["1fac3-1f3fb","1fac3-1f3fc","1fac3-1f3fd","1fac3-1f3fe","1fac3-1f3ff"],a:"14.0"},{n:["pregnant person"],u:"1fac4",v:["1fac4-1f3fb","1fac4-1f3fc","1fac4-1f3fd","1fac4-1f3fe","1fac4-1f3ff"],a:"14.0"},{n:["breast-feeding"],u:"1f931",v:["1f931-1f3fb","1f931-1f3fc","1f931-1f3fd","1f931-1f3fe","1f931-1f3ff"],a:"5.0"},{n:["woman feeding baby"],u:"1f469-200d-1f37c",v:["1f469-1f3fb-200d-1f37c","1f469-1f3fc-200d-1f37c","1f469-1f3fd-200d-1f37c","1f469-1f3fe-200d-1f37c","1f469-1f3ff-200d-1f37c"],a:"13.0"},{n:["man feeding baby"],u:"1f468-200d-1f37c",v:["1f468-1f3fb-200d-1f37c","1f468-1f3fc-200d-1f37c","1f468-1f3fd-200d-1f37c","1f468-1f3fe-200d-1f37c","1f468-1f3ff-200d-1f37c"],a:"13.0"},{n:["person feeding baby"],u:"1f9d1-200d-1f37c",v:["1f9d1-1f3fb-200d-1f37c","1f9d1-1f3fc-200d-1f37c","1f9d1-1f3fd-200d-1f37c","1f9d1-1f3fe-200d-1f37c","1f9d1-1f3ff-200d-1f37c"],a:"13.0"},{n:["angel","baby angel"],u:"1f47c",v:["1f47c-1f3fb","1f47c-1f3fc","1f47c-1f3fd","1f47c-1f3fe","1f47c-1f3ff"],a:"0.6"},{n:["santa","father christmas"],u:"1f385",v:["1f385-1f3fb","1f385-1f3fc","1f385-1f3fd","1f385-1f3fe","1f385-1f3ff"],a:"0.6"},{n:["mrs claus","mother christmas"],u:"1f936",v:["1f936-1f3fb","1f936-1f3fc","1f936-1f3fd","1f936-1f3fe","1f936-1f3ff"],a:"3.0"},{n:["mx claus"],u:"1f9d1-200d-1f384",v:["1f9d1-1f3fb-200d-1f384","1f9d1-1f3fc-200d-1f384","1f9d1-1f3fd-200d-1f384","1f9d1-1f3fe-200d-1f384","1f9d1-1f3ff-200d-1f384"],a:"13.0"},{n:["superhero"],u:"1f9b8",v:["1f9b8-1f3fb","1f9b8-1f3fc","1f9b8-1f3fd","1f9b8-1f3fe","1f9b8-1f3ff"],a:"11.0"},{n:["man superhero","male superhero"],u:"1f9b8-200d-2642-fe0f",v:["1f9b8-1f3fb-200d-2642-fe0f","1f9b8-1f3fc-200d-2642-fe0f","1f9b8-1f3fd-200d-2642-fe0f","1f9b8-1f3fe-200d-2642-fe0f","1f9b8-1f3ff-200d-2642-fe0f"],a:"11.0"},{n:["woman superhero","female superhero"],u:"1f9b8-200d-2640-fe0f",v:["1f9b8-1f3fb-200d-2640-fe0f","1f9b8-1f3fc-200d-2640-fe0f","1f9b8-1f3fd-200d-2640-fe0f","1f9b8-1f3fe-200d-2640-fe0f","1f9b8-1f3ff-200d-2640-fe0f"],a:"11.0"},{n:["supervillain"],u:"1f9b9",v:["1f9b9-1f3fb","1f9b9-1f3fc","1f9b9-1f3fd","1f9b9-1f3fe","1f9b9-1f3ff"],a:"11.0"},{n:["man supervillain","male supervillain"],u:"1f9b9-200d-2642-fe0f",v:["1f9b9-1f3fb-200d-2642-fe0f","1f9b9-1f3fc-200d-2642-fe0f","1f9b9-1f3fd-200d-2642-fe0f","1f9b9-1f3fe-200d-2642-fe0f","1f9b9-1f3ff-200d-2642-fe0f"],a:"11.0"},{n:["woman supervillain","female supervillain"],u:"1f9b9-200d-2640-fe0f",v:["1f9b9-1f3fb-200d-2640-fe0f","1f9b9-1f3fc-200d-2640-fe0f","1f9b9-1f3fd-200d-2640-fe0f","1f9b9-1f3fe-200d-2640-fe0f","1f9b9-1f3ff-200d-2640-fe0f"],a:"11.0"},{n:["mage"],u:"1f9d9",v:["1f9d9-1f3fb","1f9d9-1f3fc","1f9d9-1f3fd","1f9d9-1f3fe","1f9d9-1f3ff"],a:"5.0"},{n:["man mage","male mage"],u:"1f9d9-200d-2642-fe0f",v:["1f9d9-1f3fb-200d-2642-fe0f","1f9d9-1f3fc-200d-2642-fe0f","1f9d9-1f3fd-200d-2642-fe0f","1f9d9-1f3fe-200d-2642-fe0f","1f9d9-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman mage","female mage"],u:"1f9d9-200d-2640-fe0f",v:["1f9d9-1f3fb-200d-2640-fe0f","1f9d9-1f3fc-200d-2640-fe0f","1f9d9-1f3fd-200d-2640-fe0f","1f9d9-1f3fe-200d-2640-fe0f","1f9d9-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["fairy"],u:"1f9da",v:["1f9da-1f3fb","1f9da-1f3fc","1f9da-1f3fd","1f9da-1f3fe","1f9da-1f3ff"],a:"5.0"},{n:["man fairy","male fairy"],u:"1f9da-200d-2642-fe0f",v:["1f9da-1f3fb-200d-2642-fe0f","1f9da-1f3fc-200d-2642-fe0f","1f9da-1f3fd-200d-2642-fe0f","1f9da-1f3fe-200d-2642-fe0f","1f9da-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman fairy","female fairy"],u:"1f9da-200d-2640-fe0f",v:["1f9da-1f3fb-200d-2640-fe0f","1f9da-1f3fc-200d-2640-fe0f","1f9da-1f3fd-200d-2640-fe0f","1f9da-1f3fe-200d-2640-fe0f","1f9da-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["vampire"],u:"1f9db",v:["1f9db-1f3fb","1f9db-1f3fc","1f9db-1f3fd","1f9db-1f3fe","1f9db-1f3ff"],a:"5.0"},{n:["man vampire","male vampire"],u:"1f9db-200d-2642-fe0f",v:["1f9db-1f3fb-200d-2642-fe0f","1f9db-1f3fc-200d-2642-fe0f","1f9db-1f3fd-200d-2642-fe0f","1f9db-1f3fe-200d-2642-fe0f","1f9db-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman vampire","female vampire"],u:"1f9db-200d-2640-fe0f",v:["1f9db-1f3fb-200d-2640-fe0f","1f9db-1f3fc-200d-2640-fe0f","1f9db-1f3fd-200d-2640-fe0f","1f9db-1f3fe-200d-2640-fe0f","1f9db-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["merperson"],u:"1f9dc",v:["1f9dc-1f3fb","1f9dc-1f3fc","1f9dc-1f3fd","1f9dc-1f3fe","1f9dc-1f3ff"],a:"5.0"},{n:["merman"],u:"1f9dc-200d-2642-fe0f",v:["1f9dc-1f3fb-200d-2642-fe0f","1f9dc-1f3fc-200d-2642-fe0f","1f9dc-1f3fd-200d-2642-fe0f","1f9dc-1f3fe-200d-2642-fe0f","1f9dc-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["mermaid"],u:"1f9dc-200d-2640-fe0f",v:["1f9dc-1f3fb-200d-2640-fe0f","1f9dc-1f3fc-200d-2640-fe0f","1f9dc-1f3fd-200d-2640-fe0f","1f9dc-1f3fe-200d-2640-fe0f","1f9dc-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["elf"],u:"1f9dd",v:["1f9dd-1f3fb","1f9dd-1f3fc","1f9dd-1f3fd","1f9dd-1f3fe","1f9dd-1f3ff"],a:"5.0"},{n:["man elf","male elf"],u:"1f9dd-200d-2642-fe0f",v:["1f9dd-1f3fb-200d-2642-fe0f","1f9dd-1f3fc-200d-2642-fe0f","1f9dd-1f3fd-200d-2642-fe0f","1f9dd-1f3fe-200d-2642-fe0f","1f9dd-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman elf","female elf"],u:"1f9dd-200d-2640-fe0f",v:["1f9dd-1f3fb-200d-2640-fe0f","1f9dd-1f3fc-200d-2640-fe0f","1f9dd-1f3fd-200d-2640-fe0f","1f9dd-1f3fe-200d-2640-fe0f","1f9dd-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["genie"],u:"1f9de",a:"5.0"},{n:["man genie","male genie"],u:"1f9de-200d-2642-fe0f",a:"5.0"},{n:["woman genie","female genie"],u:"1f9de-200d-2640-fe0f",a:"5.0"},{n:["zombie"],u:"1f9df",a:"5.0"},{n:["man zombie","male zombie"],u:"1f9df-200d-2642-fe0f",a:"5.0"},{n:["woman zombie","female zombie"],u:"1f9df-200d-2640-fe0f",a:"5.0"},{n:["troll"],u:"1f9cc",a:"14.0"},{n:["massage","face massage"],u:"1f486",v:["1f486-1f3fb","1f486-1f3fc","1f486-1f3fd","1f486-1f3fe","1f486-1f3ff"],a:"0.6"},{n:["man getting massage","man-getting-massage"],u:"1f486-200d-2642-fe0f",v:["1f486-1f3fb-200d-2642-fe0f","1f486-1f3fc-200d-2642-fe0f","1f486-1f3fd-200d-2642-fe0f","1f486-1f3fe-200d-2642-fe0f","1f486-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman getting massage","woman-getting-massage"],u:"1f486-200d-2640-fe0f",v:["1f486-1f3fb-200d-2640-fe0f","1f486-1f3fc-200d-2640-fe0f","1f486-1f3fd-200d-2640-fe0f","1f486-1f3fe-200d-2640-fe0f","1f486-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["haircut"],u:"1f487",v:["1f487-1f3fb","1f487-1f3fc","1f487-1f3fd","1f487-1f3fe","1f487-1f3ff"],a:"0.6"},{n:["man getting haircut","man-getting-haircut"],u:"1f487-200d-2642-fe0f",v:["1f487-1f3fb-200d-2642-fe0f","1f487-1f3fc-200d-2642-fe0f","1f487-1f3fd-200d-2642-fe0f","1f487-1f3fe-200d-2642-fe0f","1f487-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman getting haircut","woman-getting-haircut"],u:"1f487-200d-2640-fe0f",v:["1f487-1f3fb-200d-2640-fe0f","1f487-1f3fc-200d-2640-fe0f","1f487-1f3fd-200d-2640-fe0f","1f487-1f3fe-200d-2640-fe0f","1f487-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["walking","pedestrian"],u:"1f6b6",v:["1f6b6-1f3fb","1f6b6-1f3fc","1f6b6-1f3fd","1f6b6-1f3fe","1f6b6-1f3ff"],a:"0.6"},{n:["man walking","man-walking"],u:"1f6b6-200d-2642-fe0f",v:["1f6b6-1f3fb-200d-2642-fe0f","1f6b6-1f3fc-200d-2642-fe0f","1f6b6-1f3fd-200d-2642-fe0f","1f6b6-1f3fe-200d-2642-fe0f","1f6b6-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman walking","woman-walking"],u:"1f6b6-200d-2640-fe0f",v:["1f6b6-1f3fb-200d-2640-fe0f","1f6b6-1f3fc-200d-2640-fe0f","1f6b6-1f3fd-200d-2640-fe0f","1f6b6-1f3fe-200d-2640-fe0f","1f6b6-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["standing person"],u:"1f9cd",v:["1f9cd-1f3fb","1f9cd-1f3fc","1f9cd-1f3fd","1f9cd-1f3fe","1f9cd-1f3ff"],a:"12.0"},{n:["man standing"],u:"1f9cd-200d-2642-fe0f",v:["1f9cd-1f3fb-200d-2642-fe0f","1f9cd-1f3fc-200d-2642-fe0f","1f9cd-1f3fd-200d-2642-fe0f","1f9cd-1f3fe-200d-2642-fe0f","1f9cd-1f3ff-200d-2642-fe0f"],a:"12.0"},{n:["woman standing"],u:"1f9cd-200d-2640-fe0f",v:["1f9cd-1f3fb-200d-2640-fe0f","1f9cd-1f3fc-200d-2640-fe0f","1f9cd-1f3fd-200d-2640-fe0f","1f9cd-1f3fe-200d-2640-fe0f","1f9cd-1f3ff-200d-2640-fe0f"],a:"12.0"},{n:["kneeling person"],u:"1f9ce",v:["1f9ce-1f3fb","1f9ce-1f3fc","1f9ce-1f3fd","1f9ce-1f3fe","1f9ce-1f3ff"],a:"12.0"},{n:["man kneeling"],u:"1f9ce-200d-2642-fe0f",v:["1f9ce-1f3fb-200d-2642-fe0f","1f9ce-1f3fc-200d-2642-fe0f","1f9ce-1f3fd-200d-2642-fe0f","1f9ce-1f3fe-200d-2642-fe0f","1f9ce-1f3ff-200d-2642-fe0f"],a:"12.0"},{n:["woman kneeling"],u:"1f9ce-200d-2640-fe0f",v:["1f9ce-1f3fb-200d-2640-fe0f","1f9ce-1f3fc-200d-2640-fe0f","1f9ce-1f3fd-200d-2640-fe0f","1f9ce-1f3fe-200d-2640-fe0f","1f9ce-1f3ff-200d-2640-fe0f"],a:"12.0"},{n:["person with white cane","person with probing cane"],u:"1f9d1-200d-1f9af",v:["1f9d1-1f3fb-200d-1f9af","1f9d1-1f3fc-200d-1f9af","1f9d1-1f3fd-200d-1f9af","1f9d1-1f3fe-200d-1f9af","1f9d1-1f3ff-200d-1f9af"],a:"12.1"},{n:["man with white cane","man with probing cane"],u:"1f468-200d-1f9af",v:["1f468-1f3fb-200d-1f9af","1f468-1f3fc-200d-1f9af","1f468-1f3fd-200d-1f9af","1f468-1f3fe-200d-1f9af","1f468-1f3ff-200d-1f9af"],a:"12.0"},{n:["woman with white cane","woman with probing cane"],u:"1f469-200d-1f9af",v:["1f469-1f3fb-200d-1f9af","1f469-1f3fc-200d-1f9af","1f469-1f3fd-200d-1f9af","1f469-1f3fe-200d-1f9af","1f469-1f3ff-200d-1f9af"],a:"12.0"},{n:["person in motorized wheelchair"],u:"1f9d1-200d-1f9bc",v:["1f9d1-1f3fb-200d-1f9bc","1f9d1-1f3fc-200d-1f9bc","1f9d1-1f3fd-200d-1f9bc","1f9d1-1f3fe-200d-1f9bc","1f9d1-1f3ff-200d-1f9bc"],a:"12.1"},{n:["man in motorized wheelchair"],u:"1f468-200d-1f9bc",v:["1f468-1f3fb-200d-1f9bc","1f468-1f3fc-200d-1f9bc","1f468-1f3fd-200d-1f9bc","1f468-1f3fe-200d-1f9bc","1f468-1f3ff-200d-1f9bc"],a:"12.0"},{n:["woman in motorized wheelchair"],u:"1f469-200d-1f9bc",v:["1f469-1f3fb-200d-1f9bc","1f469-1f3fc-200d-1f9bc","1f469-1f3fd-200d-1f9bc","1f469-1f3fe-200d-1f9bc","1f469-1f3ff-200d-1f9bc"],a:"12.0"},{n:["person in manual wheelchair"],u:"1f9d1-200d-1f9bd",v:["1f9d1-1f3fb-200d-1f9bd","1f9d1-1f3fc-200d-1f9bd","1f9d1-1f3fd-200d-1f9bd","1f9d1-1f3fe-200d-1f9bd","1f9d1-1f3ff-200d-1f9bd"],a:"12.1"},{n:["man in manual wheelchair"],u:"1f468-200d-1f9bd",v:["1f468-1f3fb-200d-1f9bd","1f468-1f3fc-200d-1f9bd","1f468-1f3fd-200d-1f9bd","1f468-1f3fe-200d-1f9bd","1f468-1f3ff-200d-1f9bd"],a:"12.0"},{n:["woman in manual wheelchair"],u:"1f469-200d-1f9bd",v:["1f469-1f3fb-200d-1f9bd","1f469-1f3fc-200d-1f9bd","1f469-1f3fd-200d-1f9bd","1f469-1f3fe-200d-1f9bd","1f469-1f3ff-200d-1f9bd"],a:"12.0"},{n:["runner","running"],u:"1f3c3",v:["1f3c3-1f3fb","1f3c3-1f3fc","1f3c3-1f3fd","1f3c3-1f3fe","1f3c3-1f3ff"],a:"0.6"},{n:["man running","man-running"],u:"1f3c3-200d-2642-fe0f",v:["1f3c3-1f3fb-200d-2642-fe0f","1f3c3-1f3fc-200d-2642-fe0f","1f3c3-1f3fd-200d-2642-fe0f","1f3c3-1f3fe-200d-2642-fe0f","1f3c3-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman running","woman-running"],u:"1f3c3-200d-2640-fe0f",v:["1f3c3-1f3fb-200d-2640-fe0f","1f3c3-1f3fc-200d-2640-fe0f","1f3c3-1f3fd-200d-2640-fe0f","1f3c3-1f3fe-200d-2640-fe0f","1f3c3-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["dancer"],u:"1f483",v:["1f483-1f3fb","1f483-1f3fc","1f483-1f3fd","1f483-1f3fe","1f483-1f3ff"],a:"0.6"},{n:["man dancing"],u:"1f57a",v:["1f57a-1f3fb","1f57a-1f3fc","1f57a-1f3fd","1f57a-1f3fe","1f57a-1f3ff"],a:"3.0"},{n:["person in suit levitating","man in business suit levitating"],u:"1f574-fe0f",v:["1f574-1f3fb","1f574-1f3fc","1f574-1f3fd","1f574-1f3fe","1f574-1f3ff"],a:"0.7"},{n:["dancers","woman with bunny ears"],u:"1f46f",a:"0.6"},{n:["men with bunny ears","men-with-bunny-ears-partying","man-with-bunny-ears-partying"],u:"1f46f-200d-2642-fe0f",a:"4.0"},{n:["women with bunny ears","women-with-bunny-ears-partying","woman-with-bunny-ears-partying"],u:"1f46f-200d-2640-fe0f",a:"4.0"},{n:["person in steamy room"],u:"1f9d6",v:["1f9d6-1f3fb","1f9d6-1f3fc","1f9d6-1f3fd","1f9d6-1f3fe","1f9d6-1f3ff"],a:"5.0"},{n:["man in steamy room"],u:"1f9d6-200d-2642-fe0f",v:["1f9d6-1f3fb-200d-2642-fe0f","1f9d6-1f3fc-200d-2642-fe0f","1f9d6-1f3fd-200d-2642-fe0f","1f9d6-1f3fe-200d-2642-fe0f","1f9d6-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman in steamy room"],u:"1f9d6-200d-2640-fe0f",v:["1f9d6-1f3fb-200d-2640-fe0f","1f9d6-1f3fc-200d-2640-fe0f","1f9d6-1f3fd-200d-2640-fe0f","1f9d6-1f3fe-200d-2640-fe0f","1f9d6-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["person climbing"],u:"1f9d7",v:["1f9d7-1f3fb","1f9d7-1f3fc","1f9d7-1f3fd","1f9d7-1f3fe","1f9d7-1f3ff"],a:"5.0"},{n:["man climbing"],u:"1f9d7-200d-2642-fe0f",v:["1f9d7-1f3fb-200d-2642-fe0f","1f9d7-1f3fc-200d-2642-fe0f","1f9d7-1f3fd-200d-2642-fe0f","1f9d7-1f3fe-200d-2642-fe0f","1f9d7-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman climbing"],u:"1f9d7-200d-2640-fe0f",v:["1f9d7-1f3fb-200d-2640-fe0f","1f9d7-1f3fc-200d-2640-fe0f","1f9d7-1f3fd-200d-2640-fe0f","1f9d7-1f3fe-200d-2640-fe0f","1f9d7-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["fencer"],u:"1f93a",a:"3.0"},{n:["horse racing"],u:"1f3c7",v:["1f3c7-1f3fb","1f3c7-1f3fc","1f3c7-1f3fd","1f3c7-1f3fe","1f3c7-1f3ff"],a:"1.0"},{n:["skier"],u:"26f7-fe0f",a:"0.7"},{n:["snowboarder"],u:"1f3c2",v:["1f3c2-1f3fb","1f3c2-1f3fc","1f3c2-1f3fd","1f3c2-1f3fe","1f3c2-1f3ff"],a:"0.6"},{n:["golfer","person golfing"],u:"1f3cc-fe0f",v:["1f3cc-1f3fb","1f3cc-1f3fc","1f3cc-1f3fd","1f3cc-1f3fe","1f3cc-1f3ff"],a:"0.7"},{n:["man golfing","man-golfing"],u:"1f3cc-fe0f-200d-2642-fe0f",v:["1f3cc-1f3fb-200d-2642-fe0f","1f3cc-1f3fc-200d-2642-fe0f","1f3cc-1f3fd-200d-2642-fe0f","1f3cc-1f3fe-200d-2642-fe0f","1f3cc-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman golfing","woman-golfing"],u:"1f3cc-fe0f-200d-2640-fe0f",v:["1f3cc-1f3fb-200d-2640-fe0f","1f3cc-1f3fc-200d-2640-fe0f","1f3cc-1f3fd-200d-2640-fe0f","1f3cc-1f3fe-200d-2640-fe0f","1f3cc-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["surfer"],u:"1f3c4",v:["1f3c4-1f3fb","1f3c4-1f3fc","1f3c4-1f3fd","1f3c4-1f3fe","1f3c4-1f3ff"],a:"0.6"},{n:["man surfing","man-surfing"],u:"1f3c4-200d-2642-fe0f",v:["1f3c4-1f3fb-200d-2642-fe0f","1f3c4-1f3fc-200d-2642-fe0f","1f3c4-1f3fd-200d-2642-fe0f","1f3c4-1f3fe-200d-2642-fe0f","1f3c4-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman surfing","woman-surfing"],u:"1f3c4-200d-2640-fe0f",v:["1f3c4-1f3fb-200d-2640-fe0f","1f3c4-1f3fc-200d-2640-fe0f","1f3c4-1f3fd-200d-2640-fe0f","1f3c4-1f3fe-200d-2640-fe0f","1f3c4-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["rowboat"],u:"1f6a3",v:["1f6a3-1f3fb","1f6a3-1f3fc","1f6a3-1f3fd","1f6a3-1f3fe","1f6a3-1f3ff"],a:"1.0"},{n:["man rowing boat","man-rowing-boat"],u:"1f6a3-200d-2642-fe0f",v:["1f6a3-1f3fb-200d-2642-fe0f","1f6a3-1f3fc-200d-2642-fe0f","1f6a3-1f3fd-200d-2642-fe0f","1f6a3-1f3fe-200d-2642-fe0f","1f6a3-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman rowing boat","woman-rowing-boat"],u:"1f6a3-200d-2640-fe0f",v:["1f6a3-1f3fb-200d-2640-fe0f","1f6a3-1f3fc-200d-2640-fe0f","1f6a3-1f3fd-200d-2640-fe0f","1f6a3-1f3fe-200d-2640-fe0f","1f6a3-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["swimmer"],u:"1f3ca",v:["1f3ca-1f3fb","1f3ca-1f3fc","1f3ca-1f3fd","1f3ca-1f3fe","1f3ca-1f3ff"],a:"0.6"},{n:["man swimming","man-swimming"],u:"1f3ca-200d-2642-fe0f",v:["1f3ca-1f3fb-200d-2642-fe0f","1f3ca-1f3fc-200d-2642-fe0f","1f3ca-1f3fd-200d-2642-fe0f","1f3ca-1f3fe-200d-2642-fe0f","1f3ca-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman swimming","woman-swimming"],u:"1f3ca-200d-2640-fe0f",v:["1f3ca-1f3fb-200d-2640-fe0f","1f3ca-1f3fc-200d-2640-fe0f","1f3ca-1f3fd-200d-2640-fe0f","1f3ca-1f3fe-200d-2640-fe0f","1f3ca-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["person with ball","person bouncing ball"],u:"26f9-fe0f",v:["26f9-1f3fb","26f9-1f3fc","26f9-1f3fd","26f9-1f3fe","26f9-1f3ff"],a:"0.7"},{n:["man bouncing ball","man-bouncing-ball"],u:"26f9-fe0f-200d-2642-fe0f",v:["26f9-1f3fb-200d-2642-fe0f","26f9-1f3fc-200d-2642-fe0f","26f9-1f3fd-200d-2642-fe0f","26f9-1f3fe-200d-2642-fe0f","26f9-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman bouncing ball","woman-bouncing-ball"],u:"26f9-fe0f-200d-2640-fe0f",v:["26f9-1f3fb-200d-2640-fe0f","26f9-1f3fc-200d-2640-fe0f","26f9-1f3fd-200d-2640-fe0f","26f9-1f3fe-200d-2640-fe0f","26f9-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["weight lifter","person lifting weights"],u:"1f3cb-fe0f",v:["1f3cb-1f3fb","1f3cb-1f3fc","1f3cb-1f3fd","1f3cb-1f3fe","1f3cb-1f3ff"],a:"0.7"},{n:["man lifting weights","man-lifting-weights"],u:"1f3cb-fe0f-200d-2642-fe0f",v:["1f3cb-1f3fb-200d-2642-fe0f","1f3cb-1f3fc-200d-2642-fe0f","1f3cb-1f3fd-200d-2642-fe0f","1f3cb-1f3fe-200d-2642-fe0f","1f3cb-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman lifting weights","woman-lifting-weights"],u:"1f3cb-fe0f-200d-2640-fe0f",v:["1f3cb-1f3fb-200d-2640-fe0f","1f3cb-1f3fc-200d-2640-fe0f","1f3cb-1f3fd-200d-2640-fe0f","1f3cb-1f3fe-200d-2640-fe0f","1f3cb-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["bicyclist"],u:"1f6b4",v:["1f6b4-1f3fb","1f6b4-1f3fc","1f6b4-1f3fd","1f6b4-1f3fe","1f6b4-1f3ff"],a:"1.0"},{n:["man biking","man-biking"],u:"1f6b4-200d-2642-fe0f",v:["1f6b4-1f3fb-200d-2642-fe0f","1f6b4-1f3fc-200d-2642-fe0f","1f6b4-1f3fd-200d-2642-fe0f","1f6b4-1f3fe-200d-2642-fe0f","1f6b4-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman biking","woman-biking"],u:"1f6b4-200d-2640-fe0f",v:["1f6b4-1f3fb-200d-2640-fe0f","1f6b4-1f3fc-200d-2640-fe0f","1f6b4-1f3fd-200d-2640-fe0f","1f6b4-1f3fe-200d-2640-fe0f","1f6b4-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["mountain bicyclist"],u:"1f6b5",v:["1f6b5-1f3fb","1f6b5-1f3fc","1f6b5-1f3fd","1f6b5-1f3fe","1f6b5-1f3ff"],a:"1.0"},{n:["man mountain biking","man-mountain-biking"],u:"1f6b5-200d-2642-fe0f",v:["1f6b5-1f3fb-200d-2642-fe0f","1f6b5-1f3fc-200d-2642-fe0f","1f6b5-1f3fd-200d-2642-fe0f","1f6b5-1f3fe-200d-2642-fe0f","1f6b5-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman mountain biking","woman-mountain-biking"],u:"1f6b5-200d-2640-fe0f",v:["1f6b5-1f3fb-200d-2640-fe0f","1f6b5-1f3fc-200d-2640-fe0f","1f6b5-1f3fd-200d-2640-fe0f","1f6b5-1f3fe-200d-2640-fe0f","1f6b5-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["person doing cartwheel"],u:"1f938",v:["1f938-1f3fb","1f938-1f3fc","1f938-1f3fd","1f938-1f3fe","1f938-1f3ff"],a:"3.0"},{n:["man cartwheeling","man-cartwheeling"],u:"1f938-200d-2642-fe0f",v:["1f938-1f3fb-200d-2642-fe0f","1f938-1f3fc-200d-2642-fe0f","1f938-1f3fd-200d-2642-fe0f","1f938-1f3fe-200d-2642-fe0f","1f938-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman cartwheeling","woman-cartwheeling"],u:"1f938-200d-2640-fe0f",v:["1f938-1f3fb-200d-2640-fe0f","1f938-1f3fc-200d-2640-fe0f","1f938-1f3fd-200d-2640-fe0f","1f938-1f3fe-200d-2640-fe0f","1f938-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["wrestlers"],u:"1f93c",a:"3.0"},{n:["men wrestling","man-wrestling"],u:"1f93c-200d-2642-fe0f",a:"4.0"},{n:["women wrestling","woman-wrestling"],u:"1f93c-200d-2640-fe0f",a:"4.0"},{n:["water polo"],u:"1f93d",v:["1f93d-1f3fb","1f93d-1f3fc","1f93d-1f3fd","1f93d-1f3fe","1f93d-1f3ff"],a:"3.0"},{n:["man playing water polo","man-playing-water-polo"],u:"1f93d-200d-2642-fe0f",v:["1f93d-1f3fb-200d-2642-fe0f","1f93d-1f3fc-200d-2642-fe0f","1f93d-1f3fd-200d-2642-fe0f","1f93d-1f3fe-200d-2642-fe0f","1f93d-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman playing water polo","woman-playing-water-polo"],u:"1f93d-200d-2640-fe0f",v:["1f93d-1f3fb-200d-2640-fe0f","1f93d-1f3fc-200d-2640-fe0f","1f93d-1f3fd-200d-2640-fe0f","1f93d-1f3fe-200d-2640-fe0f","1f93d-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["handball"],u:"1f93e",v:["1f93e-1f3fb","1f93e-1f3fc","1f93e-1f3fd","1f93e-1f3fe","1f93e-1f3ff"],a:"3.0"},{n:["man playing handball","man-playing-handball"],u:"1f93e-200d-2642-fe0f",v:["1f93e-1f3fb-200d-2642-fe0f","1f93e-1f3fc-200d-2642-fe0f","1f93e-1f3fd-200d-2642-fe0f","1f93e-1f3fe-200d-2642-fe0f","1f93e-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman playing handball","woman-playing-handball"],u:"1f93e-200d-2640-fe0f",v:["1f93e-1f3fb-200d-2640-fe0f","1f93e-1f3fc-200d-2640-fe0f","1f93e-1f3fd-200d-2640-fe0f","1f93e-1f3fe-200d-2640-fe0f","1f93e-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["juggling"],u:"1f939",v:["1f939-1f3fb","1f939-1f3fc","1f939-1f3fd","1f939-1f3fe","1f939-1f3ff"],a:"3.0"},{n:["man juggling","man-juggling"],u:"1f939-200d-2642-fe0f",v:["1f939-1f3fb-200d-2642-fe0f","1f939-1f3fc-200d-2642-fe0f","1f939-1f3fd-200d-2642-fe0f","1f939-1f3fe-200d-2642-fe0f","1f939-1f3ff-200d-2642-fe0f"],a:"4.0"},{n:["woman juggling","woman-juggling"],u:"1f939-200d-2640-fe0f",v:["1f939-1f3fb-200d-2640-fe0f","1f939-1f3fc-200d-2640-fe0f","1f939-1f3fd-200d-2640-fe0f","1f939-1f3fe-200d-2640-fe0f","1f939-1f3ff-200d-2640-fe0f"],a:"4.0"},{n:["person in lotus position"],u:"1f9d8",v:["1f9d8-1f3fb","1f9d8-1f3fc","1f9d8-1f3fd","1f9d8-1f3fe","1f9d8-1f3ff"],a:"5.0"},{n:["man in lotus position"],u:"1f9d8-200d-2642-fe0f",v:["1f9d8-1f3fb-200d-2642-fe0f","1f9d8-1f3fc-200d-2642-fe0f","1f9d8-1f3fd-200d-2642-fe0f","1f9d8-1f3fe-200d-2642-fe0f","1f9d8-1f3ff-200d-2642-fe0f"],a:"5.0"},{n:["woman in lotus position"],u:"1f9d8-200d-2640-fe0f",v:["1f9d8-1f3fb-200d-2640-fe0f","1f9d8-1f3fc-200d-2640-fe0f","1f9d8-1f3fd-200d-2640-fe0f","1f9d8-1f3fe-200d-2640-fe0f","1f9d8-1f3ff-200d-2640-fe0f"],a:"5.0"},{n:["bath"],u:"1f6c0",v:["1f6c0-1f3fb","1f6c0-1f3fc","1f6c0-1f3fd","1f6c0-1f3fe","1f6c0-1f3ff"],a:"0.6"},{n:["sleeping accommodation"],u:"1f6cc",v:["1f6cc-1f3fb","1f6cc-1f3fc","1f6cc-1f3fd","1f6cc-1f3fe","1f6cc-1f3ff"],a:"1.0"},{n:["people holding hands"],u:"1f9d1-200d-1f91d-200d-1f9d1",v:["1f9d1-1f3fb-200d-1f91d-200d-1f9d1-1f3fb","1f9d1-1f3fb-200d-1f91d-200d-1f9d1-1f3fc","1f9d1-1f3fb-200d-1f91d-200d-1f9d1-1f3fd","1f9d1-1f3fb-200d-1f91d-200d-1f9d1-1f3fe","1f9d1-1f3fb-200d-1f91d-200d-1f9d1-1f3ff","1f9d1-1f3fc-200d-1f91d-200d-1f9d1-1f3fb","1f9d1-1f3fc-200d-1f91d-200d-1f9d1-1f3fc","1f9d1-1f3fc-200d-1f91d-200d-1f9d1-1f3fd","1f9d1-1f3fc-200d-1f91d-200d-1f9d1-1f3fe","1f9d1-1f3fc-200d-1f91d-200d-1f9d1-1f3ff","1f9d1-1f3fd-200d-1f91d-200d-1f9d1-1f3fb","1f9d1-1f3fd-200d-1f91d-200d-1f9d1-1f3fc","1f9d1-1f3fd-200d-1f91d-200d-1f9d1-1f3fd","1f9d1-1f3fd-200d-1f91d-200d-1f9d1-1f3fe","1f9d1-1f3fd-200d-1f91d-200d-1f9d1-1f3ff","1f9d1-1f3fe-200d-1f91d-200d-1f9d1-1f3fb","1f9d1-1f3fe-200d-1f91d-200d-1f9d1-1f3fc","1f9d1-1f3fe-200d-1f91d-200d-1f9d1-1f3fd","1f9d1-1f3fe-200d-1f91d-200d-1f9d1-1f3fe","1f9d1-1f3fe-200d-1f91d-200d-1f9d1-1f3ff","1f9d1-1f3ff-200d-1f91d-200d-1f9d1-1f3fb","1f9d1-1f3ff-200d-1f91d-200d-1f9d1-1f3fc","1f9d1-1f3ff-200d-1f91d-200d-1f9d1-1f3fd","1f9d1-1f3ff-200d-1f91d-200d-1f9d1-1f3fe","1f9d1-1f3ff-200d-1f91d-200d-1f9d1-1f3ff"],a:"12.0"},{n:["women holding hands","two women holding hands"],u:"1f46d",v:["1f46d-1f3fb","1f46d-1f3fc","1f46d-1f3fd","1f46d-1f3fe","1f46d-1f3ff","1f469-1f3fb-200d-1f91d-200d-1f469-1f3fc","1f469-1f3fb-200d-1f91d-200d-1f469-1f3fd","1f469-1f3fb-200d-1f91d-200d-1f469-1f3fe","1f469-1f3fb-200d-1f91d-200d-1f469-1f3ff","1f469-1f3fc-200d-1f91d-200d-1f469-1f3fb","1f469-1f3fc-200d-1f91d-200d-1f469-1f3fd","1f469-1f3fc-200d-1f91d-200d-1f469-1f3fe","1f469-1f3fc-200d-1f91d-200d-1f469-1f3ff","1f469-1f3fd-200d-1f91d-200d-1f469-1f3fb","1f469-1f3fd-200d-1f91d-200d-1f469-1f3fc","1f469-1f3fd-200d-1f91d-200d-1f469-1f3fe","1f469-1f3fd-200d-1f91d-200d-1f469-1f3ff","1f469-1f3fe-200d-1f91d-200d-1f469-1f3fb","1f469-1f3fe-200d-1f91d-200d-1f469-1f3fc","1f469-1f3fe-200d-1f91d-200d-1f469-1f3fd","1f469-1f3fe-200d-1f91d-200d-1f469-1f3ff","1f469-1f3ff-200d-1f91d-200d-1f469-1f3fb","1f469-1f3ff-200d-1f91d-200d-1f469-1f3fc","1f469-1f3ff-200d-1f91d-200d-1f469-1f3fd","1f469-1f3ff-200d-1f91d-200d-1f469-1f3fe"],a:"1.0"},{n:["couple","man and woman holding hands","woman and man holding hands"],u:"1f46b",v:["1f46b-1f3fb","1f46b-1f3fc","1f46b-1f3fd","1f46b-1f3fe","1f46b-1f3ff","1f469-1f3fb-200d-1f91d-200d-1f468-1f3fc","1f469-1f3fb-200d-1f91d-200d-1f468-1f3fd","1f469-1f3fb-200d-1f91d-200d-1f468-1f3fe","1f469-1f3fb-200d-1f91d-200d-1f468-1f3ff","1f469-1f3fc-200d-1f91d-200d-1f468-1f3fb","1f469-1f3fc-200d-1f91d-200d-1f468-1f3fd","1f469-1f3fc-200d-1f91d-200d-1f468-1f3fe","1f469-1f3fc-200d-1f91d-200d-1f468-1f3ff","1f469-1f3fd-200d-1f91d-200d-1f468-1f3fb","1f469-1f3fd-200d-1f91d-200d-1f468-1f3fc","1f469-1f3fd-200d-1f91d-200d-1f468-1f3fe","1f469-1f3fd-200d-1f91d-200d-1f468-1f3ff","1f469-1f3fe-200d-1f91d-200d-1f468-1f3fb","1f469-1f3fe-200d-1f91d-200d-1f468-1f3fc","1f469-1f3fe-200d-1f91d-200d-1f468-1f3fd","1f469-1f3fe-200d-1f91d-200d-1f468-1f3ff","1f469-1f3ff-200d-1f91d-200d-1f468-1f3fb","1f469-1f3ff-200d-1f91d-200d-1f468-1f3fc","1f469-1f3ff-200d-1f91d-200d-1f468-1f3fd","1f469-1f3ff-200d-1f91d-200d-1f468-1f3fe"],a:"0.6"},{n:["men holding hands","two men holding hands"],u:"1f46c",v:["1f46c-1f3fb","1f46c-1f3fc","1f46c-1f3fd","1f46c-1f3fe","1f46c-1f3ff","1f468-1f3fb-200d-1f91d-200d-1f468-1f3fc","1f468-1f3fb-200d-1f91d-200d-1f468-1f3fd","1f468-1f3fb-200d-1f91d-200d-1f468-1f3fe","1f468-1f3fb-200d-1f91d-200d-1f468-1f3ff","1f468-1f3fc-200d-1f91d-200d-1f468-1f3fb","1f468-1f3fc-200d-1f91d-200d-1f468-1f3fd","1f468-1f3fc-200d-1f91d-200d-1f468-1f3fe","1f468-1f3fc-200d-1f91d-200d-1f468-1f3ff","1f468-1f3fd-200d-1f91d-200d-1f468-1f3fb","1f468-1f3fd-200d-1f91d-200d-1f468-1f3fc","1f468-1f3fd-200d-1f91d-200d-1f468-1f3fe","1f468-1f3fd-200d-1f91d-200d-1f468-1f3ff","1f468-1f3fe-200d-1f91d-200d-1f468-1f3fb","1f468-1f3fe-200d-1f91d-200d-1f468-1f3fc","1f468-1f3fe-200d-1f91d-200d-1f468-1f3fd","1f468-1f3fe-200d-1f91d-200d-1f468-1f3ff","1f468-1f3ff-200d-1f91d-200d-1f468-1f3fb","1f468-1f3ff-200d-1f91d-200d-1f468-1f3fc","1f468-1f3ff-200d-1f91d-200d-1f468-1f3fd","1f468-1f3ff-200d-1f91d-200d-1f468-1f3fe"],a:"1.0"},{n:["kiss","couplekiss"],u:"1f48f",v:["1f48f-1f3fb","1f48f-1f3fc","1f48f-1f3fd","1f48f-1f3fe","1f48f-1f3ff","1f9d1-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fc","1f9d1-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fd","1f9d1-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fe","1f9d1-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3ff","1f9d1-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fb","1f9d1-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fd","1f9d1-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fe","1f9d1-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3ff","1f9d1-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fb","1f9d1-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fc","1f9d1-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fe","1f9d1-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3ff","1f9d1-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fb","1f9d1-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fc","1f9d1-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fd","1f9d1-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3ff","1f9d1-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fb","1f9d1-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fc","1f9d1-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fd","1f9d1-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f9d1-1f3fe"],a:"0.6"},{n:["woman-kiss-man","kiss: woman, man"],u:"1f469-200d-2764-fe0f-200d-1f48b-200d-1f468",v:["1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff"],a:"2.0"},{n:["man-kiss-man","kiss: man, man"],u:"1f468-200d-2764-fe0f-200d-1f48b-200d-1f468",v:["1f468-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f468-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f468-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f468-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f468-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f468-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f468-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f468-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f468-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f468-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f468-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f468-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f468-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f468-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f468-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f468-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f468-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f468-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f468-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f468-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff","1f468-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fb","1f468-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fc","1f468-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fd","1f468-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3fe","1f468-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f468-1f3ff"],a:"2.0"},{n:["woman-kiss-woman","kiss: woman, woman"],u:"1f469-200d-2764-fe0f-200d-1f48b-200d-1f469",v:["1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fb","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fc","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fd","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fe","1f469-1f3fb-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3ff","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fb","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fc","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fd","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fe","1f469-1f3fc-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3ff","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fb","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fc","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fd","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fe","1f469-1f3fd-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3ff","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fb","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fc","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fd","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fe","1f469-1f3fe-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3ff","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fb","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fc","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fd","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3fe","1f469-1f3ff-200d-2764-fe0f-200d-1f48b-200d-1f469-1f3ff"],a:"2.0"},{n:["couple with heart"],u:"1f491",v:["1f491-1f3fb","1f491-1f3fc","1f491-1f3fd","1f491-1f3fe","1f491-1f3ff","1f9d1-1f3fb-200d-2764-fe0f-200d-1f9d1-1f3fc","1f9d1-1f3fb-200d-2764-fe0f-200d-1f9d1-1f3fd","1f9d1-1f3fb-200d-2764-fe0f-200d-1f9d1-1f3fe","1f9d1-1f3fb-200d-2764-fe0f-200d-1f9d1-1f3ff","1f9d1-1f3fc-200d-2764-fe0f-200d-1f9d1-1f3fb","1f9d1-1f3fc-200d-2764-fe0f-200d-1f9d1-1f3fd","1f9d1-1f3fc-200d-2764-fe0f-200d-1f9d1-1f3fe","1f9d1-1f3fc-200d-2764-fe0f-200d-1f9d1-1f3ff","1f9d1-1f3fd-200d-2764-fe0f-200d-1f9d1-1f3fb","1f9d1-1f3fd-200d-2764-fe0f-200d-1f9d1-1f3fc","1f9d1-1f3fd-200d-2764-fe0f-200d-1f9d1-1f3fe","1f9d1-1f3fd-200d-2764-fe0f-200d-1f9d1-1f3ff","1f9d1-1f3fe-200d-2764-fe0f-200d-1f9d1-1f3fb","1f9d1-1f3fe-200d-2764-fe0f-200d-1f9d1-1f3fc","1f9d1-1f3fe-200d-2764-fe0f-200d-1f9d1-1f3fd","1f9d1-1f3fe-200d-2764-fe0f-200d-1f9d1-1f3ff","1f9d1-1f3ff-200d-2764-fe0f-200d-1f9d1-1f3fb","1f9d1-1f3ff-200d-2764-fe0f-200d-1f9d1-1f3fc","1f9d1-1f3ff-200d-2764-fe0f-200d-1f9d1-1f3fd","1f9d1-1f3ff-200d-2764-fe0f-200d-1f9d1-1f3fe"],a:"0.6"},{n:["woman-heart-man","couple with heart: woman, man"],u:"1f469-200d-2764-fe0f-200d-1f468",v:["1f469-1f3fb-200d-2764-fe0f-200d-1f468-1f3fb","1f469-1f3fb-200d-2764-fe0f-200d-1f468-1f3fc","1f469-1f3fb-200d-2764-fe0f-200d-1f468-1f3fd","1f469-1f3fb-200d-2764-fe0f-200d-1f468-1f3fe","1f469-1f3fb-200d-2764-fe0f-200d-1f468-1f3ff","1f469-1f3fc-200d-2764-fe0f-200d-1f468-1f3fb","1f469-1f3fc-200d-2764-fe0f-200d-1f468-1f3fc","1f469-1f3fc-200d-2764-fe0f-200d-1f468-1f3fd","1f469-1f3fc-200d-2764-fe0f-200d-1f468-1f3fe","1f469-1f3fc-200d-2764-fe0f-200d-1f468-1f3ff","1f469-1f3fd-200d-2764-fe0f-200d-1f468-1f3fb","1f469-1f3fd-200d-2764-fe0f-200d-1f468-1f3fc","1f469-1f3fd-200d-2764-fe0f-200d-1f468-1f3fd","1f469-1f3fd-200d-2764-fe0f-200d-1f468-1f3fe","1f469-1f3fd-200d-2764-fe0f-200d-1f468-1f3ff","1f469-1f3fe-200d-2764-fe0f-200d-1f468-1f3fb","1f469-1f3fe-200d-2764-fe0f-200d-1f468-1f3fc","1f469-1f3fe-200d-2764-fe0f-200d-1f468-1f3fd","1f469-1f3fe-200d-2764-fe0f-200d-1f468-1f3fe","1f469-1f3fe-200d-2764-fe0f-200d-1f468-1f3ff","1f469-1f3ff-200d-2764-fe0f-200d-1f468-1f3fb","1f469-1f3ff-200d-2764-fe0f-200d-1f468-1f3fc","1f469-1f3ff-200d-2764-fe0f-200d-1f468-1f3fd","1f469-1f3ff-200d-2764-fe0f-200d-1f468-1f3fe","1f469-1f3ff-200d-2764-fe0f-200d-1f468-1f3ff"],a:"2.0"},{n:["man-heart-man","couple with heart: man, man"],u:"1f468-200d-2764-fe0f-200d-1f468",v:["1f468-1f3fb-200d-2764-fe0f-200d-1f468-1f3fb","1f468-1f3fb-200d-2764-fe0f-200d-1f468-1f3fc","1f468-1f3fb-200d-2764-fe0f-200d-1f468-1f3fd","1f468-1f3fb-200d-2764-fe0f-200d-1f468-1f3fe","1f468-1f3fb-200d-2764-fe0f-200d-1f468-1f3ff","1f468-1f3fc-200d-2764-fe0f-200d-1f468-1f3fb","1f468-1f3fc-200d-2764-fe0f-200d-1f468-1f3fc","1f468-1f3fc-200d-2764-fe0f-200d-1f468-1f3fd","1f468-1f3fc-200d-2764-fe0f-200d-1f468-1f3fe","1f468-1f3fc-200d-2764-fe0f-200d-1f468-1f3ff","1f468-1f3fd-200d-2764-fe0f-200d-1f468-1f3fb","1f468-1f3fd-200d-2764-fe0f-200d-1f468-1f3fc","1f468-1f3fd-200d-2764-fe0f-200d-1f468-1f3fd","1f468-1f3fd-200d-2764-fe0f-200d-1f468-1f3fe","1f468-1f3fd-200d-2764-fe0f-200d-1f468-1f3ff","1f468-1f3fe-200d-2764-fe0f-200d-1f468-1f3fb","1f468-1f3fe-200d-2764-fe0f-200d-1f468-1f3fc","1f468-1f3fe-200d-2764-fe0f-200d-1f468-1f3fd","1f468-1f3fe-200d-2764-fe0f-200d-1f468-1f3fe","1f468-1f3fe-200d-2764-fe0f-200d-1f468-1f3ff","1f468-1f3ff-200d-2764-fe0f-200d-1f468-1f3fb","1f468-1f3ff-200d-2764-fe0f-200d-1f468-1f3fc","1f468-1f3ff-200d-2764-fe0f-200d-1f468-1f3fd","1f468-1f3ff-200d-2764-fe0f-200d-1f468-1f3fe","1f468-1f3ff-200d-2764-fe0f-200d-1f468-1f3ff"],a:"2.0"},{n:["woman-heart-woman","couple with heart: woman, woman"],u:"1f469-200d-2764-fe0f-200d-1f469",v:["1f469-1f3fb-200d-2764-fe0f-200d-1f469-1f3fb","1f469-1f3fb-200d-2764-fe0f-200d-1f469-1f3fc","1f469-1f3fb-200d-2764-fe0f-200d-1f469-1f3fd","1f469-1f3fb-200d-2764-fe0f-200d-1f469-1f3fe","1f469-1f3fb-200d-2764-fe0f-200d-1f469-1f3ff","1f469-1f3fc-200d-2764-fe0f-200d-1f469-1f3fb","1f469-1f3fc-200d-2764-fe0f-200d-1f469-1f3fc","1f469-1f3fc-200d-2764-fe0f-200d-1f469-1f3fd","1f469-1f3fc-200d-2764-fe0f-200d-1f469-1f3fe","1f469-1f3fc-200d-2764-fe0f-200d-1f469-1f3ff","1f469-1f3fd-200d-2764-fe0f-200d-1f469-1f3fb","1f469-1f3fd-200d-2764-fe0f-200d-1f469-1f3fc","1f469-1f3fd-200d-2764-fe0f-200d-1f469-1f3fd","1f469-1f3fd-200d-2764-fe0f-200d-1f469-1f3fe","1f469-1f3fd-200d-2764-fe0f-200d-1f469-1f3ff","1f469-1f3fe-200d-2764-fe0f-200d-1f469-1f3fb","1f469-1f3fe-200d-2764-fe0f-200d-1f469-1f3fc","1f469-1f3fe-200d-2764-fe0f-200d-1f469-1f3fd","1f469-1f3fe-200d-2764-fe0f-200d-1f469-1f3fe","1f469-1f3fe-200d-2764-fe0f-200d-1f469-1f3ff","1f469-1f3ff-200d-2764-fe0f-200d-1f469-1f3fb","1f469-1f3ff-200d-2764-fe0f-200d-1f469-1f3fc","1f469-1f3ff-200d-2764-fe0f-200d-1f469-1f3fd","1f469-1f3ff-200d-2764-fe0f-200d-1f469-1f3fe","1f469-1f3ff-200d-2764-fe0f-200d-1f469-1f3ff"],a:"2.0"},{n:["family"],u:"1f46a",a:"0.6"},{n:["man-woman-boy","family: man, woman, boy"],u:"1f468-200d-1f469-200d-1f466",a:"2.0"},{n:["man-woman-girl","family: man, woman, girl"],u:"1f468-200d-1f469-200d-1f467",a:"2.0"},{n:["man-woman-girl-boy","family: man, woman, girl, boy"],u:"1f468-200d-1f469-200d-1f467-200d-1f466",a:"2.0"},{n:["man-woman-boy-boy","family: man, woman, boy, boy"],u:"1f468-200d-1f469-200d-1f466-200d-1f466",a:"2.0"},{n:["man-woman-girl-girl","family: man, woman, girl, girl"],u:"1f468-200d-1f469-200d-1f467-200d-1f467",a:"2.0"},{n:["man-man-boy","family: man, man, boy"],u:"1f468-200d-1f468-200d-1f466",a:"2.0"},{n:["man-man-girl","family: man, man, girl"],u:"1f468-200d-1f468-200d-1f467",a:"2.0"},{n:["man-man-girl-boy","family: man, man, girl, boy"],u:"1f468-200d-1f468-200d-1f467-200d-1f466",a:"2.0"},{n:["man-man-boy-boy","family: man, man, boy, boy"],u:"1f468-200d-1f468-200d-1f466-200d-1f466",a:"2.0"},{n:["man-man-girl-girl","family: man, man, girl, girl"],u:"1f468-200d-1f468-200d-1f467-200d-1f467",a:"2.0"},{n:["woman-woman-boy","family: woman, woman, boy"],u:"1f469-200d-1f469-200d-1f466",a:"2.0"},{n:["woman-woman-girl","family: woman, woman, girl"],u:"1f469-200d-1f469-200d-1f467",a:"2.0"},{n:["woman-woman-girl-boy","family: woman, woman, girl, boy"],u:"1f469-200d-1f469-200d-1f467-200d-1f466",a:"2.0"},{n:["woman-woman-boy-boy","family: woman, woman, boy, boy"],u:"1f469-200d-1f469-200d-1f466-200d-1f466",a:"2.0"},{n:["woman-woman-girl-girl","family: woman, woman, girl, girl"],u:"1f469-200d-1f469-200d-1f467-200d-1f467",a:"2.0"},{n:["man-boy","family: man, boy"],u:"1f468-200d-1f466",a:"4.0"},{n:["man-boy-boy","family: man, boy, boy"],u:"1f468-200d-1f466-200d-1f466",a:"4.0"},{n:["man-girl","family: man, girl"],u:"1f468-200d-1f467",a:"4.0"},{n:["man-girl-boy","family: man, girl, boy"],u:"1f468-200d-1f467-200d-1f466",a:"4.0"},{n:["man-girl-girl","family: man, girl, girl"],u:"1f468-200d-1f467-200d-1f467",a:"4.0"},{n:["woman-boy","family: woman, boy"],u:"1f469-200d-1f466",a:"4.0"},{n:["woman-boy-boy","family: woman, boy, boy"],u:"1f469-200d-1f466-200d-1f466",a:"4.0"},{n:["woman-girl","family: woman, girl"],u:"1f469-200d-1f467",a:"4.0"},{n:["woman-girl-boy","family: woman, girl, boy"],u:"1f469-200d-1f467-200d-1f466",a:"4.0"},{n:["woman-girl-girl","family: woman, girl, girl"],u:"1f469-200d-1f467-200d-1f467",a:"4.0"},{n:["speaking head","speaking head in silhouette"],u:"1f5e3-fe0f",a:"0.7"},{n:["bust in silhouette"],u:"1f464",a:"0.6"},{n:["busts in silhouette"],u:"1f465",a:"1.0"},{n:["people hugging"],u:"1fac2",a:"13.0"},{n:["footprints"],u:"1f463",a:"0.6"}],animals_nature:[{n:["monkey face"],u:"1f435",a:"0.6"},{n:["monkey"],u:"1f412",a:"0.6"},{n:["gorilla"],u:"1f98d",a:"3.0"},{n:["orangutan"],u:"1f9a7",a:"12.0"},{n:["dog","dog face"],u:"1f436",a:"0.6"},{n:["dog","dog2"],u:"1f415",a:"0.7"},{n:["guide dog"],u:"1f9ae",a:"12.0"},{n:["service dog"],u:"1f415-200d-1f9ba",a:"12.0"},{n:["poodle"],u:"1f429",a:"0.6"},{n:["wolf","wolf face"],u:"1f43a",a:"0.6"},{n:["fox face"],u:"1f98a",a:"3.0"},{n:["raccoon"],u:"1f99d",a:"11.0"},{n:["cat","cat face"],u:"1f431",a:"0.6"},{n:["cat","cat2"],u:"1f408",a:"0.7"},{n:["black cat"],u:"1f408-200d-2b1b",a:"13.0"},{n:["lion face"],u:"1f981",a:"1.0"},{n:["tiger","tiger face"],u:"1f42f",a:"0.6"},{n:["tiger","tiger2"],u:"1f405",a:"1.0"},{n:["leopard"],u:"1f406",a:"1.0"},{n:["horse","horse face"],u:"1f434",a:"0.6"},{n:["horse","racehorse"],u:"1f40e",a:"0.6"},{n:["unicorn face"],u:"1f984",a:"1.0"},{n:["zebra face"],u:"1f993",a:"5.0"},{n:["deer"],u:"1f98c",a:"3.0"},{n:["bison"],u:"1f9ac",a:"13.0"},{n:["cow","cow face"],u:"1f42e",a:"0.6"},{n:["ox"],u:"1f402",a:"1.0"},{n:["water buffalo"],u:"1f403",a:"1.0"},{n:["cow","cow2"],u:"1f404",a:"1.0"},{n:["pig","pig face"],u:"1f437",a:"0.6"},{n:["pig","pig2"],u:"1f416",a:"1.0"},{n:["boar"],u:"1f417",a:"0.6"},{n:["pig nose"],u:"1f43d",a:"0.6"},{n:["ram"],u:"1f40f",a:"1.0"},{n:["sheep"],u:"1f411",a:"0.6"},{n:["goat"],u:"1f410",a:"1.0"},{n:["dromedary camel"],u:"1f42a",a:"1.0"},{n:["camel","bactrian camel"],u:"1f42b",a:"0.6"},{n:["llama"],u:"1f999",a:"11.0"},{n:["giraffe face"],u:"1f992",a:"5.0"},{n:["elephant"],u:"1f418",a:"0.6"},{n:["mammoth"],u:"1f9a3",a:"13.0"},{n:["rhinoceros"],u:"1f98f",a:"3.0"},{n:["hippopotamus"],u:"1f99b",a:"11.0"},{n:["mouse","mouse face"],u:"1f42d",a:"0.6"},{n:["mouse","mouse2"],u:"1f401",a:"1.0"},{n:["rat"],u:"1f400",a:"1.0"},{n:["hamster","hamster face"],u:"1f439",a:"0.6"},{n:["rabbit","rabbit face"],u:"1f430",a:"0.6"},{n:["rabbit","rabbit2"],u:"1f407",a:"1.0"},{n:["chipmunk"],u:"1f43f-fe0f",a:"0.7"},{n:["beaver"],u:"1f9ab",a:"13.0"},{n:["hedgehog"],u:"1f994",a:"5.0"},{n:["bat"],u:"1f987",a:"3.0"},{n:["bear","bear face"],u:"1f43b",a:"0.6"},{n:["polar bear"],u:"1f43b-200d-2744-fe0f",a:"13.0"},{n:["koala"],u:"1f428",a:"0.6"},{n:["panda face"],u:"1f43c",a:"0.6"},{n:["sloth"],u:"1f9a5",a:"12.0"},{n:["otter"],u:"1f9a6",a:"12.0"},{n:["skunk"],u:"1f9a8",a:"12.0"},{n:["kangaroo"],u:"1f998",a:"11.0"},{n:["badger"],u:"1f9a1",a:"11.0"},{n:["feet","paw prints"],u:"1f43e",a:"0.6"},{n:["turkey"],u:"1f983",a:"1.0"},{n:["chicken"],u:"1f414",a:"0.6"},{n:["rooster"],u:"1f413",a:"1.0"},{n:["hatching chick"],u:"1f423",a:"0.6"},{n:["baby chick"],u:"1f424",a:"0.6"},{n:["hatched chick","front-facing baby chick"],u:"1f425",a:"0.6"},{n:["bird"],u:"1f426",a:"0.6"},{n:["penguin"],u:"1f427",a:"0.6"},{n:["dove","dove of peace"],u:"1f54a-fe0f",a:"0.7"},{n:["eagle"],u:"1f985",a:"3.0"},{n:["duck"],u:"1f986",a:"3.0"},{n:["swan"],u:"1f9a2",a:"11.0"},{n:["owl"],u:"1f989",a:"3.0"},{n:["dodo"],u:"1f9a4",a:"13.0"},{n:["feather"],u:"1fab6",a:"13.0"},{n:["flamingo"],u:"1f9a9",a:"12.0"},{n:["peacock"],u:"1f99a",a:"11.0"},{n:["parrot"],u:"1f99c",a:"11.0"},{n:["frog","frog face"],u:"1f438",a:"0.6"},{n:["crocodile"],u:"1f40a",a:"1.0"},{n:["turtle"],u:"1f422",a:"0.6"},{n:["lizard"],u:"1f98e",a:"3.0"},{n:["snake"],u:"1f40d",a:"0.6"},{n:["dragon face"],u:"1f432",a:"0.6"},{n:["dragon"],u:"1f409",a:"1.0"},{n:["sauropod"],u:"1f995",a:"5.0"},{n:["t-rex"],u:"1f996",a:"5.0"},{n:["whale","spouting whale"],u:"1f433",a:"0.6"},{n:["whale","whale2"],u:"1f40b",a:"1.0"},{n:["dolphin","flipper"],u:"1f42c",a:"0.6"},{n:["seal"],u:"1f9ad",a:"13.0"},{n:["fish"],u:"1f41f",a:"0.6"},{n:["tropical fish"],u:"1f420",a:"0.6"},{n:["blowfish"],u:"1f421",a:"0.6"},{n:["shark"],u:"1f988",a:"3.0"},{n:["octopus"],u:"1f419",a:"0.6"},{n:["shell","spiral shell"],u:"1f41a",a:"0.6"},{n:["coral"],u:"1fab8",a:"14.0"},{n:["snail"],u:"1f40c",a:"0.6"},{n:["butterfly"],u:"1f98b",a:"3.0"},{n:["bug"],u:"1f41b",a:"0.6"},{n:["ant"],u:"1f41c",a:"0.6"},{n:["bee","honeybee"],u:"1f41d",a:"0.6"},{n:["beetle"],u:"1fab2",a:"13.0"},{n:["ladybug","lady beetle"],u:"1f41e",a:"0.6"},{n:["cricket"],u:"1f997",a:"5.0"},{n:["cockroach"],u:"1fab3",a:"13.0"},{n:["spider"],u:"1f577-fe0f",a:"0.7"},{n:["spider web"],u:"1f578-fe0f",a:"0.7"},{n:["scorpion"],u:"1f982",a:"1.0"},{n:["mosquito"],u:"1f99f",a:"11.0"},{n:["fly"],u:"1fab0",a:"13.0"},{n:["worm"],u:"1fab1",a:"13.0"},{n:["microbe"],u:"1f9a0",a:"11.0"},{n:["bouquet"],u:"1f490",a:"0.6"},{n:["cherry blossom"],u:"1f338",a:"0.6"},{n:["white flower"],u:"1f4ae",a:"0.6"},{n:["lotus"],u:"1fab7",a:"14.0"},{n:["rosette"],u:"1f3f5-fe0f",a:"0.7"},{n:["rose"],u:"1f339",a:"0.6"},{n:["wilted flower"],u:"1f940",a:"3.0"},{n:["hibiscus"],u:"1f33a",a:"0.6"},{n:["sunflower"],u:"1f33b",a:"0.6"},{n:["blossom"],u:"1f33c",a:"0.6"},{n:["tulip"],u:"1f337",a:"0.6"},{n:["seedling"],u:"1f331",a:"0.6"},{n:["potted plant"],u:"1fab4",a:"13.0"},{n:["evergreen tree"],u:"1f332",a:"1.0"},{n:["deciduous tree"],u:"1f333",a:"1.0"},{n:["palm tree"],u:"1f334",a:"0.6"},{n:["cactus"],u:"1f335",a:"0.6"},{n:["ear of rice"],u:"1f33e",a:"0.6"},{n:["herb"],u:"1f33f",a:"0.6"},{n:["shamrock"],u:"2618-fe0f",a:"1.0"},{n:["four leaf clover"],u:"1f340",a:"0.6"},{n:["maple leaf"],u:"1f341",a:"0.6"},{n:["fallen leaf"],u:"1f342",a:"0.6"},{n:["leaves","leaf fluttering in wind"],u:"1f343",a:"0.6"},{n:["empty nest"],u:"1fab9",a:"14.0"},{n:["nest with eggs"],u:"1faba",a:"14.0"}],food_drink:[{n:["grapes"],u:"1f347",a:"0.6"},{n:["melon"],u:"1f348",a:"0.6"},{n:["watermelon"],u:"1f349",a:"0.6"},{n:["tangerine"],u:"1f34a",a:"0.6"},{n:["lemon"],u:"1f34b",a:"1.0"},{n:["banana"],u:"1f34c",a:"0.6"},{n:["pineapple"],u:"1f34d",a:"0.6"},{n:["mango"],u:"1f96d",a:"11.0"},{n:["apple","red apple"],u:"1f34e",a:"0.6"},{n:["green apple"],u:"1f34f",a:"0.6"},{n:["pear"],u:"1f350",a:"1.0"},{n:["peach"],u:"1f351",a:"0.6"},{n:["cherries"],u:"1f352",a:"0.6"},{n:["strawberry"],u:"1f353",a:"0.6"},{n:["blueberries"],u:"1fad0",a:"13.0"},{n:["kiwifruit"],u:"1f95d",a:"3.0"},{n:["tomato"],u:"1f345",a:"0.6"},{n:["olive"],u:"1fad2",a:"13.0"},{n:["coconut"],u:"1f965",a:"5.0"},{n:["avocado"],u:"1f951",a:"3.0"},{n:["eggplant","aubergine"],u:"1f346",a:"0.6"},{n:["potato"],u:"1f954",a:"3.0"},{n:["carrot"],u:"1f955",a:"3.0"},{n:["corn","ear of maize"],u:"1f33d",a:"0.6"},{n:["hot pepper"],u:"1f336-fe0f",a:"0.7"},{n:["bell pepper"],u:"1fad1",a:"13.0"},{n:["cucumber"],u:"1f952",a:"3.0"},{n:["leafy green"],u:"1f96c",a:"11.0"},{n:["broccoli"],u:"1f966",a:"5.0"},{n:["garlic"],u:"1f9c4",a:"12.0"},{n:["onion"],u:"1f9c5",a:"12.0"},{n:["mushroom"],u:"1f344",a:"0.6"},{n:["peanuts"],u:"1f95c",a:"3.0"},{n:["beans"],u:"1fad8",a:"14.0"},{n:["chestnut"],u:"1f330",a:"0.6"},{n:["bread"],u:"1f35e",a:"0.6"},{n:["croissant"],u:"1f950",a:"3.0"},{n:["baguette bread"],u:"1f956",a:"3.0"},{n:["flatbread"],u:"1fad3",a:"13.0"},{n:["pretzel"],u:"1f968",a:"5.0"},{n:["bagel"],u:"1f96f",a:"11.0"},{n:["pancakes"],u:"1f95e",a:"3.0"},{n:["waffle"],u:"1f9c7",a:"12.0"},{n:["cheese wedge"],u:"1f9c0",a:"1.0"},{n:["meat on bone"],u:"1f356",a:"0.6"},{n:["poultry leg"],u:"1f357",a:"0.6"},{n:["cut of meat"],u:"1f969",a:"5.0"},{n:["bacon"],u:"1f953",a:"3.0"},{n:["hamburger"],u:"1f354",a:"0.6"},{n:["fries","french fries"],u:"1f35f",a:"0.6"},{n:["pizza","slice of pizza"],u:"1f355",a:"0.6"},{n:["hotdog","hot dog"],u:"1f32d",a:"1.0"},{n:["sandwich"],u:"1f96a",a:"5.0"},{n:["taco"],u:"1f32e",a:"1.0"},{n:["burrito"],u:"1f32f",a:"1.0"},{n:["tamale"],u:"1fad4",a:"13.0"},{n:["stuffed flatbread"],u:"1f959",a:"3.0"},{n:["falafel"],u:"1f9c6",a:"12.0"},{n:["egg"],u:"1f95a",a:"3.0"},{n:["cooking","fried egg"],u:"1f373",a:"0.6"},{n:["shallow pan of food"],u:"1f958",a:"3.0"},{n:["stew","pot of food"],u:"1f372",a:"0.6"},{n:["fondue"],u:"1fad5",a:"13.0"},{n:["bowl with spoon"],u:"1f963",a:"5.0"},{n:["green salad"],u:"1f957",a:"3.0"},{n:["popcorn"],u:"1f37f",a:"1.0"},{n:["butter"],u:"1f9c8",a:"12.0"},{n:["salt","salt shaker"],u:"1f9c2",a:"11.0"},{n:["canned food"],u:"1f96b",a:"5.0"},{n:["bento","bento box"],u:"1f371",a:"0.6"},{n:["rice cracker"],u:"1f358",a:"0.6"},{n:["rice ball"],u:"1f359",a:"0.6"},{n:["rice","cooked rice"],u:"1f35a",a:"0.6"},{n:["curry","curry and rice"],u:"1f35b",a:"0.6"},{n:["ramen","steaming bowl"],u:"1f35c",a:"0.6"},{n:["spaghetti"],u:"1f35d",a:"0.6"},{n:["sweet potato","roasted sweet potato"],u:"1f360",a:"0.6"},{n:["oden"],u:"1f362",a:"0.6"},{n:["sushi"],u:"1f363",a:"0.6"},{n:["fried shrimp"],u:"1f364",a:"0.6"},{n:["fish cake","fish cake with swirl design"],u:"1f365",a:"0.6"},{n:["moon cake"],u:"1f96e",a:"11.0"},{n:["dango"],u:"1f361",a:"0.6"},{n:["dumpling"],u:"1f95f",a:"5.0"},{n:["fortune cookie"],u:"1f960",a:"5.0"},{n:["takeout box"],u:"1f961",a:"5.0"},{n:["crab"],u:"1f980",a:"1.0"},{n:["lobster"],u:"1f99e",a:"11.0"},{n:["shrimp"],u:"1f990",a:"3.0"},{n:["squid"],u:"1f991",a:"3.0"},{n:["oyster"],u:"1f9aa",a:"12.0"},{n:["icecream","soft ice cream"],u:"1f366",a:"0.6"},{n:["shaved ice"],u:"1f367",a:"0.6"},{n:["ice cream"],u:"1f368",a:"0.6"},{n:["doughnut"],u:"1f369",a:"0.6"},{n:["cookie"],u:"1f36a",a:"0.6"},{n:["birthday","birthday cake"],u:"1f382",a:"0.6"},{n:["cake","shortcake"],u:"1f370",a:"0.6"},{n:["cupcake"],u:"1f9c1",a:"11.0"},{n:["pie"],u:"1f967",a:"5.0"},{n:["chocolate bar"],u:"1f36b",a:"0.6"},{n:["candy"],u:"1f36c",a:"0.6"},{n:["lollipop"],u:"1f36d",a:"0.6"},{n:["custard"],u:"1f36e",a:"0.6"},{n:["honey pot"],u:"1f36f",a:"0.6"},{n:["baby bottle"],u:"1f37c",a:"1.0"},{n:["glass of milk"],u:"1f95b",a:"3.0"},{n:["coffee","hot beverage"],u:"2615",a:"0.6"},{n:["teapot"],u:"1fad6",a:"13.0"},{n:["tea","teacup without handle"],u:"1f375",a:"0.6"},{n:["sake","sake bottle and cup"],u:"1f376",a:"0.6"},{n:["champagne","bottle with popping cork"],u:"1f37e",a:"1.0"},{n:["wine glass"],u:"1f377",a:"0.6"},{n:["cocktail","cocktail glass"],u:"1f378",a:"0.6"},{n:["tropical drink"],u:"1f379",a:"0.6"},{n:["beer","beer mug"],u:"1f37a",a:"0.6"},{n:["beers","clinking beer mugs"],u:"1f37b",a:"0.6"},{n:["clinking glasses"],u:"1f942",a:"3.0"},{n:["tumbler glass"],u:"1f943",a:"3.0"},{n:["pouring liquid"],u:"1fad7",a:"14.0"},{n:["cup with straw"],u:"1f964",a:"5.0"},{n:["bubble tea"],u:"1f9cb",a:"13.0"},{n:["beverage box"],u:"1f9c3",a:"12.0"},{n:["mate drink"],u:"1f9c9",a:"12.0"},{n:["ice cube"],u:"1f9ca",a:"12.0"},{n:["chopsticks"],u:"1f962",a:"5.0"},{n:["knife fork plate","fork and knife with plate"],u:"1f37d-fe0f",a:"0.7"},{n:["fork and knife"],u:"1f374",a:"0.6"},{n:["spoon"],u:"1f944",a:"3.0"},{n:["hocho","knife"],u:"1f52a",a:"0.6"},{n:["jar"],u:"1fad9",a:"14.0"},{n:["amphora"],u:"1f3fa",a:"1.0"}],travel_places:[{n:["earth africa","earth globe europe-africa"],u:"1f30d",a:"0.7"},{n:["earth americas","earth globe americas"],u:"1f30e",a:"0.7"},{n:["earth asia","earth globe asia-australia"],u:"1f30f",a:"0.6"},{n:["globe with meridians"],u:"1f310",a:"1.0"},{n:["world map"],u:"1f5fa-fe0f",a:"0.7"},{n:["japan","silhouette of japan"],u:"1f5fe",a:"0.6"},{n:["compass"],u:"1f9ed",a:"11.0"},{n:["snow-capped mountain","snow capped mountain"],u:"1f3d4-fe0f",a:"0.7"},{n:["mountain"],u:"26f0-fe0f",a:"0.7"},{n:["volcano"],u:"1f30b",a:"0.6"},{n:["mount fuji"],u:"1f5fb",a:"0.6"},{n:["camping"],u:"1f3d5-fe0f",a:"0.7"},{n:["beach with umbrella"],u:"1f3d6-fe0f",a:"0.7"},{n:["desert"],u:"1f3dc-fe0f",a:"0.7"},{n:["desert island"],u:"1f3dd-fe0f",a:"0.7"},{n:["national park"],u:"1f3de-fe0f",a:"0.7"},{n:["stadium"],u:"1f3df-fe0f",a:"0.7"},{n:["classical building"],u:"1f3db-fe0f",a:"0.7"},{n:["building construction"],u:"1f3d7-fe0f",a:"0.7"},{n:["brick","bricks"],u:"1f9f1",a:"11.0"},{n:["rock"],u:"1faa8",a:"13.0"},{n:["wood"],u:"1fab5",a:"13.0"},{n:["hut"],u:"1f6d6",a:"13.0"},{n:["houses","house buildings"],u:"1f3d8-fe0f",a:"0.7"},{n:["derelict house","derelict house building"],u:"1f3da-fe0f",a:"0.7"},{n:["house","house building"],u:"1f3e0",a:"0.6"},{n:["house with garden"],u:"1f3e1",a:"0.6"},{n:["office","office building"],u:"1f3e2",a:"0.6"},{n:["post office","japanese post office"],u:"1f3e3",a:"0.6"},{n:["european post office"],u:"1f3e4",a:"1.0"},{n:["hospital"],u:"1f3e5",a:"0.6"},{n:["bank"],u:"1f3e6",a:"0.6"},{n:["hotel"],u:"1f3e8",a:"0.6"},{n:["love hotel"],u:"1f3e9",a:"0.6"},{n:["convenience store"],u:"1f3ea",a:"0.6"},{n:["school"],u:"1f3eb",a:"0.6"},{n:["department store"],u:"1f3ec",a:"0.6"},{n:["factory"],u:"1f3ed",a:"0.6"},{n:["japanese castle"],u:"1f3ef",a:"0.6"},{n:["european castle"],u:"1f3f0",a:"0.6"},{n:["wedding"],u:"1f492",a:"0.6"},{n:["tokyo tower"],u:"1f5fc",a:"0.6"},{n:["statue of liberty"],u:"1f5fd",a:"0.6"},{n:["church"],u:"26ea",a:"0.6"},{n:["mosque"],u:"1f54c",a:"1.0"},{n:["hindu temple"],u:"1f6d5",a:"12.0"},{n:["synagogue"],u:"1f54d",a:"1.0"},{n:["shinto shrine"],u:"26e9-fe0f",a:"0.7"},{n:["kaaba"],u:"1f54b",a:"1.0"},{n:["fountain"],u:"26f2",a:"0.6"},{n:["tent"],u:"26fa",a:"0.6"},{n:["foggy"],u:"1f301",a:"0.6"},{n:["night with stars"],u:"1f303",a:"0.6"},{n:["cityscape"],u:"1f3d9-fe0f",a:"0.7"},{n:["sunrise over mountains"],u:"1f304",a:"0.6"},{n:["sunrise"],u:"1f305",a:"0.6"},{n:["city sunset","cityscape at dusk"],u:"1f306",a:"0.6"},{n:["city sunrise","sunset over buildings"],u:"1f307",a:"0.6"},{n:["bridge at night"],u:"1f309",a:"0.6"},{n:["hotsprings","hot springs"],u:"2668-fe0f",a:"0.6"},{n:["carousel horse"],u:"1f3a0",a:"0.6"},{n:["playground slide"],u:"1f6dd",a:"14.0"},{n:["ferris wheel"],u:"1f3a1",a:"0.6"},{n:["roller coaster"],u:"1f3a2",a:"0.6"},{n:["barber","barber pole"],u:"1f488",a:"0.6"},{n:["circus tent"],u:"1f3aa",a:"0.6"},{n:["steam locomotive"],u:"1f682",a:"1.0"},{n:["railway car"],u:"1f683",a:"0.6"},{n:["high-speed train","bullettrain side"],u:"1f684",a:"0.6"},{n:["bullettrain front","high-speed train with bullet nose"],u:"1f685",a:"0.6"},{n:["train","train2"],u:"1f686",a:"1.0"},{n:["metro"],u:"1f687",a:"0.6"},{n:["light rail"],u:"1f688",a:"1.0"},{n:["station"],u:"1f689",a:"0.6"},{n:["tram"],u:"1f68a",a:"1.0"},{n:["monorail"],u:"1f69d",a:"1.0"},{n:["mountain railway"],u:"1f69e",a:"1.0"},{n:["train","tram car"],u:"1f68b",a:"1.0"},{n:["bus"],u:"1f68c",a:"0.6"},{n:["oncoming bus"],u:"1f68d",a:"0.7"},{n:["trolleybus"],u:"1f68e",a:"1.0"},{n:["minibus"],u:"1f690",a:"1.0"},{n:["ambulance"],u:"1f691",a:"0.6"},{n:["fire engine"],u:"1f692",a:"0.6"},{n:["police car"],u:"1f693",a:"0.6"},{n:["oncoming police car"],u:"1f694",a:"0.7"},{n:["taxi"],u:"1f695",a:"0.6"},{n:["oncoming taxi"],u:"1f696",a:"1.0"},{n:["car","red car","automobile"],u:"1f697",a:"0.6"},{n:["oncoming automobile"],u:"1f698",a:"0.7"},{n:["blue car","recreational vehicle"],u:"1f699",a:"0.6"},{n:["pickup truck"],u:"1f6fb",a:"13.0"},{n:["truck","delivery truck"],u:"1f69a",a:"0.6"},{n:["articulated lorry"],u:"1f69b",a:"1.0"},{n:["tractor"],u:"1f69c",a:"1.0"},{n:["racing car"],u:"1f3ce-fe0f",a:"0.7"},{n:["motorcycle","racing motorcycle"],u:"1f3cd-fe0f",a:"0.7"},{n:["motor scooter"],u:"1f6f5",a:"3.0"},{n:["manual wheelchair"],u:"1f9bd",a:"12.0"},{n:["motorized wheelchair"],u:"1f9bc",a:"12.0"},{n:["auto rickshaw"],u:"1f6fa",a:"12.0"},{n:["bike","bicycle"],u:"1f6b2",a:"0.6"},{n:["scooter"],u:"1f6f4",a:"3.0"},{n:["skateboard"],u:"1f6f9",a:"11.0"},{n:["roller skate"],u:"1f6fc",a:"13.0"},{n:["busstop","bus stop"],u:"1f68f",a:"0.6"},{n:["motorway"],u:"1f6e3-fe0f",a:"0.7"},{n:["railway track"],u:"1f6e4-fe0f",a:"0.7"},{n:["oil drum"],u:"1f6e2-fe0f",a:"0.7"},{n:["fuelpump","fuel pump"],u:"26fd",a:"0.6"},{n:["wheel"],u:"1f6de",a:"14.0"},{n:["rotating light","police cars revolving light"],u:"1f6a8",a:"0.6"},{n:["traffic light","horizontal traffic light"],u:"1f6a5",a:"0.6"},{n:["vertical traffic light"],u:"1f6a6",a:"1.0"},{n:["octagonal sign"],u:"1f6d1",a:"3.0"},{n:["construction","construction sign"],u:"1f6a7",a:"0.6"},{n:["anchor"],u:"2693",a:"0.6"},{n:["ring buoy"],u:"1f6df",a:"14.0"},{n:["boat","sailboat"],u:"26f5",a:"0.6"},{n:["canoe"],u:"1f6f6",a:"3.0"},{n:["speedboat"],u:"1f6a4",a:"0.6"},{n:["passenger ship"],u:"1f6f3-fe0f",a:"0.7"},{n:["ferry"],u:"26f4-fe0f",a:"0.7"},{n:["motor boat"],u:"1f6e5-fe0f",a:"0.7"},{n:["ship"],u:"1f6a2",a:"0.6"},{n:["airplane"],u:"2708-fe0f",a:"0.6"},{n:["small airplane"],u:"1f6e9-fe0f",a:"0.7"},{n:["airplane departure"],u:"1f6eb",a:"1.0"},{n:["airplane arriving"],u:"1f6ec",a:"1.0"},{n:["parachute"],u:"1fa82",a:"12.0"},{n:["seat"],u:"1f4ba",a:"0.6"},{n:["helicopter"],u:"1f681",a:"1.0"},{n:["suspension railway"],u:"1f69f",a:"1.0"},{n:["mountain cableway"],u:"1f6a0",a:"1.0"},{n:["aerial tramway"],u:"1f6a1",a:"1.0"},{n:["satellite"],u:"1f6f0-fe0f",a:"0.7"},{n:["rocket"],u:"1f680",a:"0.6"},{n:["flying saucer"],u:"1f6f8",a:"5.0"},{n:["bellhop bell"],u:"1f6ce-fe0f",a:"0.7"},{n:["luggage"],u:"1f9f3",a:"11.0"},{n:["hourglass"],u:"231b",a:"0.6"},{n:["hourglass flowing sand","hourglass with flowing sand"],u:"23f3",a:"0.6"},{n:["watch"],u:"231a",a:"0.6"},{n:["alarm clock"],u:"23f0",a:"0.6"},{n:["stopwatch"],u:"23f1-fe0f",a:"1.0"},{n:["timer clock"],u:"23f2-fe0f",a:"1.0"},{n:["mantelpiece clock"],u:"1f570-fe0f",a:"0.7"},{n:["clock12","clock face twelve oclock"],u:"1f55b",a:"0.6"},{n:["clock1230","clock face twelve-thirty"],u:"1f567",a:"0.7"},{n:["clock1","clock face one oclock"],u:"1f550",a:"0.6"},{n:["clock130","clock face one-thirty"],u:"1f55c",a:"0.7"},{n:["clock2","clock face two oclock"],u:"1f551",a:"0.6"},{n:["clock230","clock face two-thirty"],u:"1f55d",a:"0.7"},{n:["clock3","clock face three oclock"],u:"1f552",a:"0.6"},{n:["clock330","clock face three-thirty"],u:"1f55e",a:"0.7"},{n:["clock4","clock face four oclock"],u:"1f553",a:"0.6"},{n:["clock430","clock face four-thirty"],u:"1f55f",a:"0.7"},{n:["clock5","clock face five oclock"],u:"1f554",a:"0.6"},{n:["clock530","clock face five-thirty"],u:"1f560",a:"0.7"},{n:["clock6","clock face six oclock"],u:"1f555",a:"0.6"},{n:["clock630","clock face six-thirty"],u:"1f561",a:"0.7"},{n:["clock7","clock face seven oclock"],u:"1f556",a:"0.6"},{n:["clock730","clock face seven-thirty"],u:"1f562",a:"0.7"},{n:["clock8","clock face eight oclock"],u:"1f557",a:"0.6"},{n:["clock830","clock face eight-thirty"],u:"1f563",a:"0.7"},{n:["clock9","clock face nine oclock"],u:"1f558",a:"0.6"},{n:["clock930","clock face nine-thirty"],u:"1f564",a:"0.7"},{n:["clock10","clock face ten oclock"],u:"1f559",a:"0.6"},{n:["clock1030","clock face ten-thirty"],u:"1f565",a:"0.7"},{n:["clock11","clock face eleven oclock"],u:"1f55a",a:"0.6"},{n:["clock1130","clock face eleven-thirty"],u:"1f566",a:"0.7"},{n:["new moon","new moon symbol"],u:"1f311",a:"0.6"},{n:["waxing crescent moon","waxing crescent moon symbol"],u:"1f312",a:"1.0"},{n:["first quarter moon","first quarter moon symbol"],u:"1f313",a:"0.6"},{n:["moon","waxing gibbous moon","waxing gibbous moon symbol"],u:"1f314",a:"0.6"},{n:["full moon","full moon symbol"],u:"1f315",a:"0.6"},{n:["waning gibbous moon","waning gibbous moon symbol"],u:"1f316",a:"1.0"},{n:["last quarter moon","last quarter moon symbol"],u:"1f317",a:"1.0"},{n:["waning crescent moon","waning crescent moon symbol"],u:"1f318",a:"1.0"},{n:["crescent moon"],u:"1f319",a:"0.6"},{n:["new moon with face"],u:"1f31a",a:"1.0"},{n:["first quarter moon with face"],u:"1f31b",a:"0.6"},{n:["last quarter moon with face"],u:"1f31c",a:"0.7"},{n:["thermometer"],u:"1f321-fe0f",a:"0.7"},{n:["sunny","black sun with rays"],u:"2600-fe0f",a:"0.6"},{n:["full moon with face"],u:"1f31d",a:"1.0"},{n:["sun with face"],u:"1f31e",a:"1.0"},{n:["ringed planet"],u:"1fa90",a:"12.0"},{n:["star","white medium star"],u:"2b50",a:"0.6"},{n:["star2","glowing star"],u:"1f31f",a:"0.6"},{n:["stars","shooting star"],u:"1f320",a:"0.6"},{n:["milky way"],u:"1f30c",a:"0.6"},{n:["cloud"],u:"2601-fe0f",a:"0.6"},{n:["partly sunny","sun behind cloud"],u:"26c5",a:"0.6"},{n:["thunder cloud and rain","cloud with lightning and rain"],u:"26c8-fe0f",a:"0.7"},{n:["mostly sunny","sun small cloud","sun behind small cloud"],u:"1f324-fe0f",a:"0.7"},{n:["barely sunny","sun behind cloud","sun behind large cloud"],u:"1f325-fe0f",a:"0.7"},{n:["partly sunny rain","sun behind rain cloud"],u:"1f326-fe0f",a:"0.7"},{n:["rain cloud","cloud with rain"],u:"1f327-fe0f",a:"0.7"},{n:["snow cloud","cloud with snow"],u:"1f328-fe0f",a:"0.7"},{n:["lightning","lightning cloud","cloud with lightning"],u:"1f329-fe0f",a:"0.7"},{n:["tornado","tornado cloud"],u:"1f32a-fe0f",a:"0.7"},{n:["fog"],u:"1f32b-fe0f",a:"0.7"},{n:["wind face","wind blowing face"],u:"1f32c-fe0f",a:"0.7"},{n:["cyclone"],u:"1f300",a:"0.6"},{n:["rainbow"],u:"1f308",a:"0.6"},{n:["closed umbrella"],u:"1f302",a:"0.6"},{n:["umbrella"],u:"2602-fe0f",a:"0.7"},{n:["umbrella with rain drops"],u:"2614",a:"0.6"},{n:["umbrella on ground"],u:"26f1-fe0f",a:"0.7"},{n:["zap","high voltage sign"],u:"26a1",a:"0.6"},{n:["snowflake"],u:"2744-fe0f",a:"0.6"},{n:["snowman"],u:"2603-fe0f",a:"0.7"},{n:["snowman without snow"],u:"26c4",a:"0.6"},{n:["comet"],u:"2604-fe0f",a:"1.0"},{n:["fire"],u:"1f525",a:"0.6"},{n:["droplet"],u:"1f4a7",a:"0.6"},{n:["ocean","water wave"],u:"1f30a",a:"0.6"}],activities:[{n:["jack-o-lantern","jack o lantern"],u:"1f383",a:"0.6"},{n:["christmas tree"],u:"1f384",a:"0.6"},{n:["fireworks"],u:"1f386",a:"0.6"},{n:["sparkler","firework sparkler"],u:"1f387",a:"0.6"},{n:["firecracker"],u:"1f9e8",a:"11.0"},{n:["sparkles"],u:"2728",a:"0.6"},{n:["balloon"],u:"1f388",a:"0.6"},{n:["tada","party popper"],u:"1f389",a:"0.6"},{n:["confetti ball"],u:"1f38a",a:"0.6"},{n:["tanabata tree"],u:"1f38b",a:"0.6"},{n:["bamboo","pine decoration"],u:"1f38d",a:"0.6"},{n:["dolls","japanese dolls"],u:"1f38e",a:"0.6"},{n:["flags","carp streamer"],u:"1f38f",a:"0.6"},{n:["wind chime"],u:"1f390",a:"0.6"},{n:["rice scene","moon viewing ceremony"],u:"1f391",a:"0.6"},{n:["red envelope","red gift envelope"],u:"1f9e7",a:"11.0"},{n:["ribbon"],u:"1f380",a:"0.6"},{n:["gift","wrapped present"],u:"1f381",a:"0.6"},{n:["reminder ribbon"],u:"1f397-fe0f",a:"0.7"},{n:["admission tickets"],u:"1f39f-fe0f",a:"0.7"},{n:["ticket"],u:"1f3ab",a:"0.6"},{n:["medal","military medal"],u:"1f396-fe0f",a:"0.7"},{n:["trophy"],u:"1f3c6",a:"0.6"},{n:["sports medal"],u:"1f3c5",a:"1.0"},{n:["first place medal"],u:"1f947",a:"3.0"},{n:["second place medal"],u:"1f948",a:"3.0"},{n:["third place medal"],u:"1f949",a:"3.0"},{n:["soccer","soccer ball"],u:"26bd",a:"0.6"},{n:["baseball"],u:"26be",a:"0.6"},{n:["softball"],u:"1f94e",a:"11.0"},{n:["basketball","basketball and hoop"],u:"1f3c0",a:"0.6"},{n:["volleyball"],u:"1f3d0",a:"1.0"},{n:["football","american football"],u:"1f3c8",a:"0.6"},{n:["rugby football"],u:"1f3c9",a:"1.0"},{n:["tennis","tennis racquet and ball"],u:"1f3be",a:"0.6"},{n:["flying disc"],u:"1f94f",a:"11.0"},{n:["bowling"],u:"1f3b3",a:"0.6"},{n:["cricket bat and ball"],u:"1f3cf",a:"1.0"},{n:["field hockey stick and ball"],u:"1f3d1",a:"1.0"},{n:["ice hockey stick and puck"],u:"1f3d2",a:"1.0"},{n:["lacrosse","lacrosse stick and ball"],u:"1f94d",a:"11.0"},{n:["table tennis paddle and ball"],u:"1f3d3",a:"1.0"},{n:["badminton racquet and shuttlecock"],u:"1f3f8",a:"1.0"},{n:["boxing glove"],u:"1f94a",a:"3.0"},{n:["martial arts uniform"],u:"1f94b",a:"3.0"},{n:["goal net"],u:"1f945",a:"3.0"},{n:["golf","flag in hole"],u:"26f3",a:"0.6"},{n:["ice skate"],u:"26f8-fe0f",a:"0.7"},{n:["fishing pole and fish"],u:"1f3a3",a:"0.6"},{n:["diving mask"],u:"1f93f",a:"12.0"},{n:["running shirt with sash"],u:"1f3bd",a:"0.6"},{n:["ski","ski and ski boot"],u:"1f3bf",a:"0.6"},{n:["sled"],u:"1f6f7",a:"5.0"},{n:["curling stone"],u:"1f94c",a:"5.0"},{n:["dart","direct hit"],u:"1f3af",a:"0.6"},{n:["yo-yo"],u:"1fa80",a:"12.0"},{n:["kite"],u:"1fa81",a:"12.0"},{n:["8ball","billiards"],u:"1f3b1",a:"0.6"},{n:["crystal ball"],u:"1f52e",a:"0.6"},{n:["magic wand"],u:"1fa84",a:"13.0"},{n:["nazar amulet"],u:"1f9ff",a:"11.0"},{n:["hamsa"],u:"1faac",a:"14.0"},{n:["video game"],u:"1f3ae",a:"0.6"},{n:["joystick"],u:"1f579-fe0f",a:"0.7"},{n:["slot machine"],u:"1f3b0",a:"0.6"},{n:["game die"],u:"1f3b2",a:"0.6"},{n:["jigsaw","jigsaw puzzle piece"],u:"1f9e9",a:"11.0"},{n:["teddy bear"],u:"1f9f8",a:"11.0"},{n:["pinata"],u:"1fa85",a:"13.0"},{n:["mirror ball"],u:"1faa9",a:"14.0"},{n:["nesting dolls"],u:"1fa86",a:"13.0"},{n:["spades","black spade suit"],u:"2660-fe0f",a:"0.6"},{n:["hearts","black heart suit"],u:"2665-fe0f",a:"0.6"},{n:["diamonds","black diamond suit"],u:"2666-fe0f",a:"0.6"},{n:["clubs","black club suit"],u:"2663-fe0f",a:"0.6"},{n:["chess pawn"],u:"265f-fe0f",a:"11.0"},{n:["black joker","playing card black joker"],u:"1f0cf",a:"0.6"},{n:["mahjong","mahjong tile red dragon"],u:"1f004",a:"0.6"},{n:["flower playing cards"],u:"1f3b4",a:"0.6"},{n:["performing arts"],u:"1f3ad",a:"0.6"},{n:["framed picture","frame with picture"],u:"1f5bc-fe0f",a:"0.7"},{n:["art","artist palette"],u:"1f3a8",a:"0.6"},{n:["thread","spool of thread"],u:"1f9f5",a:"11.0"},{n:["sewing needle"],u:"1faa1",a:"13.0"},{n:["yarn","ball of yarn"],u:"1f9f6",a:"11.0"},{n:["knot"],u:"1faa2",a:"13.0"}],objects:[{n:["eyeglasses"],u:"1f453",a:"0.6"},{n:["sunglasses","dark sunglasses"],u:"1f576-fe0f",a:"0.7"},{n:["goggles"],u:"1f97d",a:"11.0"},{n:["lab coat"],u:"1f97c",a:"11.0"},{n:["safety vest"],u:"1f9ba",a:"12.0"},{n:["necktie"],u:"1f454",a:"0.6"},{n:["shirt","tshirt","t-shirt"],u:"1f455",a:"0.6"},{n:["jeans"],u:"1f456",a:"0.6"},{n:["scarf"],u:"1f9e3",a:"5.0"},{n:["gloves"],u:"1f9e4",a:"5.0"},{n:["coat"],u:"1f9e5",a:"5.0"},{n:["socks"],u:"1f9e6",a:"5.0"},{n:["dress"],u:"1f457",a:"0.6"},{n:["kimono"],u:"1f458",a:"0.6"},{n:["sari"],u:"1f97b",a:"12.0"},{n:["one-piece swimsuit"],u:"1fa71",a:"12.0"},{n:["briefs"],u:"1fa72",a:"12.0"},{n:["shorts"],u:"1fa73",a:"12.0"},{n:["bikini"],u:"1f459",a:"0.6"},{n:["womans clothes"],u:"1f45a",a:"0.6"},{n:["purse"],u:"1f45b",a:"0.6"},{n:["handbag"],u:"1f45c",a:"0.6"},{n:["pouch"],u:"1f45d",a:"0.6"},{n:["shopping bags"],u:"1f6cd-fe0f",a:"0.7"},{n:["school satchel"],u:"1f392",a:"0.6"},{n:["thong sandal"],u:"1fa74",a:"13.0"},{n:["shoe","mans shoe"],u:"1f45e",a:"0.6"},{n:["athletic shoe"],u:"1f45f",a:"0.6"},{n:["hiking boot"],u:"1f97e",a:"11.0"},{n:["flat shoe","womans flat shoe"],u:"1f97f",a:"11.0"},{n:["high heel","high-heeled shoe"],u:"1f460",a:"0.6"},{n:["sandal","womans sandal"],u:"1f461",a:"0.6"},{n:["ballet shoes"],u:"1fa70",a:"12.0"},{n:["boot","womans boots"],u:"1f462",a:"0.6"},{n:["crown"],u:"1f451",a:"0.6"},{n:["womans hat"],u:"1f452",a:"0.6"},{n:["tophat","top hat"],u:"1f3a9",a:"0.6"},{n:["mortar board","graduation cap"],u:"1f393",a:"0.6"},{n:["billed cap"],u:"1f9e2",a:"5.0"},{n:["military helmet"],u:"1fa96",a:"13.0"},{n:["rescue worker’s helmet","helmet with white cross"],u:"26d1-fe0f",a:"0.7"},{n:["prayer beads"],u:"1f4ff",a:"1.0"},{n:["lipstick"],u:"1f484",a:"0.6"},{n:["ring"],u:"1f48d",a:"0.6"},{n:["gem","gem stone"],u:"1f48e",a:"0.6"},{n:["mute","speaker with cancellation stroke"],u:"1f507",a:"1.0"},{n:["speaker"],u:"1f508",a:"0.7"},{n:["sound","speaker with one sound wave"],u:"1f509",a:"1.0"},{n:["loud sound","speaker with three sound waves"],u:"1f50a",a:"0.6"},{n:["loudspeaker","public address loudspeaker"],u:"1f4e2",a:"0.6"},{n:["mega","cheering megaphone"],u:"1f4e3",a:"0.6"},{n:["postal horn"],u:"1f4ef",a:"1.0"},{n:["bell"],u:"1f514",a:"0.6"},{n:["no bell","bell with cancellation stroke"],u:"1f515",a:"1.0"},{n:["musical score"],u:"1f3bc",a:"0.6"},{n:["musical note"],u:"1f3b5",a:"0.6"},{n:["notes","multiple musical notes"],u:"1f3b6",a:"0.6"},{n:["studio microphone"],u:"1f399-fe0f",a:"0.7"},{n:["level slider"],u:"1f39a-fe0f",a:"0.7"},{n:["control knobs"],u:"1f39b-fe0f",a:"0.7"},{n:["microphone"],u:"1f3a4",a:"0.6"},{n:["headphone","headphones"],u:"1f3a7",a:"0.6"},{n:["radio"],u:"1f4fb",a:"0.6"},{n:["saxophone"],u:"1f3b7",a:"0.6"},{n:["accordion"],u:"1fa97",a:"13.0"},{n:["guitar"],u:"1f3b8",a:"0.6"},{n:["musical keyboard"],u:"1f3b9",a:"0.6"},{n:["trumpet"],u:"1f3ba",a:"0.6"},{n:["violin"],u:"1f3bb",a:"0.6"},{n:["banjo"],u:"1fa95",a:"12.0"},{n:["drum with drumsticks"],u:"1f941",a:"3.0"},{n:["long drum"],u:"1fa98",a:"13.0"},{n:["iphone","mobile phone"],u:"1f4f1",a:"0.6"},{n:["calling","mobile phone with rightwards arrow at left"],u:"1f4f2",a:"0.6"},{n:["phone","telephone","black telephone"],u:"260e-fe0f",a:"0.6"},{n:["telephone receiver"],u:"1f4de",a:"0.6"},{n:["pager"],u:"1f4df",a:"0.6"},{n:["fax","fax machine"],u:"1f4e0",a:"0.6"},{n:["battery"],u:"1f50b",a:"0.6"},{n:["low battery"],u:"1faab",a:"14.0"},{n:["electric plug"],u:"1f50c",a:"0.6"},{n:["computer","personal computer"],u:"1f4bb",a:"0.6"},{n:["desktop computer"],u:"1f5a5-fe0f",a:"0.7"},{n:["printer"],u:"1f5a8-fe0f",a:"0.7"},{n:["keyboard"],u:"2328-fe0f",a:"1.0"},{n:["computer mouse","three button mouse"],u:"1f5b1-fe0f",a:"0.7"},{n:["trackball"],u:"1f5b2-fe0f",a:"0.7"},{n:["minidisc"],u:"1f4bd",a:"0.6"},{n:["floppy disk"],u:"1f4be",a:"0.6"},{n:["cd","optical disc"],u:"1f4bf",a:"0.6"},{n:["dvd"],u:"1f4c0",a:"0.6"},{n:["abacus"],u:"1f9ee",a:"11.0"},{n:["movie camera"],u:"1f3a5",a:"0.6"},{n:["film frames"],u:"1f39e-fe0f",a:"0.7"},{n:["film projector"],u:"1f4fd-fe0f",a:"0.7"},{n:["clapper","clapper board"],u:"1f3ac",a:"0.6"},{n:["tv","television"],u:"1f4fa",a:"0.6"},{n:["camera"],u:"1f4f7",a:"0.6"},{n:["camera with flash"],u:"1f4f8",a:"1.0"},{n:["video camera"],u:"1f4f9",a:"0.6"},{n:["vhs","videocassette"],u:"1f4fc",a:"0.6"},{n:["mag","left-pointing magnifying glass"],u:"1f50d",a:"0.6"},{n:["mag right","right-pointing magnifying glass"],u:"1f50e",a:"0.6"},{n:["candle"],u:"1f56f-fe0f",a:"0.7"},{n:["bulb","electric light bulb"],u:"1f4a1",a:"0.6"},{n:["flashlight","electric torch"],u:"1f526",a:"0.6"},{n:["lantern","izakaya lantern"],u:"1f3ee",a:"0.6"},{n:["diya lamp"],u:"1fa94",a:"12.0"},{n:["notebook with decorative cover"],u:"1f4d4",a:"0.6"},{n:["closed book"],u:"1f4d5",a:"0.6"},{n:["book","open book"],u:"1f4d6",a:"0.6"},{n:["green book"],u:"1f4d7",a:"0.6"},{n:["blue book"],u:"1f4d8",a:"0.6"},{n:["orange book"],u:"1f4d9",a:"0.6"},{n:["books"],u:"1f4da",a:"0.6"},{n:["notebook"],u:"1f4d3",a:"0.6"},{n:["ledger"],u:"1f4d2",a:"0.6"},{n:["page with curl"],u:"1f4c3",a:"0.6"},{n:["scroll"],u:"1f4dc",a:"0.6"},{n:["page facing up"],u:"1f4c4",a:"0.6"},{n:["newspaper"],u:"1f4f0",a:"0.6"},{n:["rolled-up newspaper","rolled up newspaper"],u:"1f5de-fe0f",a:"0.7"},{n:["bookmark tabs"],u:"1f4d1",a:"0.6"},{n:["bookmark"],u:"1f516",a:"0.6"},{n:["label"],u:"1f3f7-fe0f",a:"0.7"},{n:["moneybag","money bag"],u:"1f4b0",a:"0.6"},{n:["coin"],u:"1fa99",a:"13.0"},{n:["yen","banknote with yen sign"],u:"1f4b4",a:"0.6"},{n:["dollar","banknote with dollar sign"],u:"1f4b5",a:"0.6"},{n:["euro","banknote with euro sign"],u:"1f4b6",a:"1.0"},{n:["pound","banknote with pound sign"],u:"1f4b7",a:"1.0"},{n:["money with wings"],u:"1f4b8",a:"0.6"},{n:["credit card"],u:"1f4b3",a:"0.6"},{n:["receipt"],u:"1f9fe",a:"11.0"},{n:["chart","chart with upwards trend and yen sign"],u:"1f4b9",a:"0.6"},{n:["email","envelope"],u:"2709-fe0f",a:"0.6"},{n:["e-mail","e-mail symbol"],u:"1f4e7",a:"0.6"},{n:["incoming envelope"],u:"1f4e8",a:"0.6"},{n:["envelope with arrow","envelope with downwards arrow above"],u:"1f4e9",a:"0.6"},{n:["outbox tray"],u:"1f4e4",a:"0.6"},{n:["inbox tray"],u:"1f4e5",a:"0.6"},{n:["package"],u:"1f4e6",a:"0.6"},{n:["mailbox","closed mailbox with raised flag"],u:"1f4eb",a:"0.6"},{n:["mailbox closed","closed mailbox with lowered flag"],u:"1f4ea",a:"0.6"},{n:["mailbox with mail","open mailbox with raised flag"],u:"1f4ec",a:"0.7"},{n:["mailbox with no mail","open mailbox with lowered flag"],u:"1f4ed",a:"0.7"},{n:["postbox"],u:"1f4ee",a:"0.6"},{n:["ballot box with ballot"],u:"1f5f3-fe0f",a:"0.7"},{n:["pencil","pencil2"],u:"270f-fe0f",a:"0.6"},{n:["black nib"],u:"2712-fe0f",a:"0.6"},{n:["fountain pen","lower left fountain pen"],u:"1f58b-fe0f",a:"0.7"},{n:["pen","lower left ballpoint pen"],u:"1f58a-fe0f",a:"0.7"},{n:["paintbrush","lower left paintbrush"],u:"1f58c-fe0f",a:"0.7"},{n:["crayon","lower left crayon"],u:"1f58d-fe0f",a:"0.7"},{n:["memo","pencil"],u:"1f4dd",a:"0.6"},{n:["briefcase"],u:"1f4bc",a:"0.6"},{n:["file folder"],u:"1f4c1",a:"0.6"},{n:["open file folder"],u:"1f4c2",a:"0.6"},{n:["card index dividers"],u:"1f5c2-fe0f",a:"0.7"},{n:["date","calendar"],u:"1f4c5",a:"0.6"},{n:["calendar","tear-off calendar"],u:"1f4c6",a:"0.6"},{n:["spiral notepad","spiral note pad"],u:"1f5d2-fe0f",a:"0.7"},{n:["spiral calendar","spiral calendar pad"],u:"1f5d3-fe0f",a:"0.7"},{n:["card index"],u:"1f4c7",a:"0.6"},{n:["chart with upwards trend"],u:"1f4c8",a:"0.6"},{n:["chart with downwards trend"],u:"1f4c9",a:"0.6"},{n:["bar chart"],u:"1f4ca",a:"0.6"},{n:["clipboard"],u:"1f4cb",a:"0.6"},{n:["pushpin"],u:"1f4cc",a:"0.6"},{n:["round pushpin"],u:"1f4cd",a:"0.6"},{n:["paperclip"],u:"1f4ce",a:"0.6"},{n:["linked paperclips"],u:"1f587-fe0f",a:"0.7"},{n:["straight ruler"],u:"1f4cf",a:"0.6"},{n:["triangular ruler"],u:"1f4d0",a:"0.6"},{n:["scissors","black scissors"],u:"2702-fe0f",a:"0.6"},{n:["card file box"],u:"1f5c3-fe0f",a:"0.7"},{n:["file cabinet"],u:"1f5c4-fe0f",a:"0.7"},{n:["wastebasket"],u:"1f5d1-fe0f",a:"0.7"},{n:["lock"],u:"1f512",a:"0.6"},{n:["unlock","open lock"],u:"1f513",a:"0.6"},{n:["lock with ink pen"],u:"1f50f",a:"0.6"},{n:["closed lock with key"],u:"1f510",a:"0.6"},{n:["key"],u:"1f511",a:"0.6"},{n:["old key"],u:"1f5dd-fe0f",a:"0.7"},{n:["hammer"],u:"1f528",a:"0.6"},{n:["axe"],u:"1fa93",a:"12.0"},{n:["pick"],u:"26cf-fe0f",a:"0.7"},{n:["hammer and pick"],u:"2692-fe0f",a:"1.0"},{n:["hammer and wrench"],u:"1f6e0-fe0f",a:"0.7"},{n:["dagger","dagger knife"],u:"1f5e1-fe0f",a:"0.7"},{n:["crossed swords"],u:"2694-fe0f",a:"1.0"},{n:["gun","pistol"],u:"1f52b",a:"0.6"},{n:["boomerang"],u:"1fa83",a:"13.0"},{n:["bow and arrow"],u:"1f3f9",a:"1.0"},{n:["shield"],u:"1f6e1-fe0f",a:"0.7"},{n:["carpentry saw"],u:"1fa9a",a:"13.0"},{n:["wrench"],u:"1f527",a:"0.6"},{n:["screwdriver"],u:"1fa9b",a:"13.0"},{n:["nut and bolt"],u:"1f529",a:"0.6"},{n:["gear"],u:"2699-fe0f",a:"1.0"},{n:["clamp","compression"],u:"1f5dc-fe0f",a:"0.7"},{n:["scales","balance scale"],u:"2696-fe0f",a:"1.0"},{n:["probing cane"],u:"1f9af",a:"12.0"},{n:["link","link symbol"],u:"1f517",a:"0.6"},{n:["chains"],u:"26d3-fe0f",a:"0.7"},{n:["hook"],u:"1fa9d",a:"13.0"},{n:["toolbox"],u:"1f9f0",a:"11.0"},{n:["magnet"],u:"1f9f2",a:"11.0"},{n:["ladder"],u:"1fa9c",a:"13.0"},{n:["alembic"],u:"2697-fe0f",a:"1.0"},{n:["test tube"],u:"1f9ea",a:"11.0"},{n:["petri dish"],u:"1f9eb",a:"11.0"},{n:["dna","dna double helix"],u:"1f9ec",a:"11.0"},{n:["microscope"],u:"1f52c",a:"1.0"},{n:["telescope"],u:"1f52d",a:"1.0"},{n:["satellite antenna"],u:"1f4e1",a:"0.6"},{n:["syringe"],u:"1f489",a:"0.6"},{n:["drop of blood"],u:"1fa78",a:"12.0"},{n:["pill"],u:"1f48a",a:"0.6"},{n:["adhesive bandage"],u:"1fa79",a:"12.0"},{n:["crutch"],u:"1fa7c",a:"14.0"},{n:["stethoscope"],u:"1fa7a",a:"12.0"},{n:["x-ray"],u:"1fa7b",a:"14.0"},{n:["door"],u:"1f6aa",a:"0.6"},{n:["elevator"],u:"1f6d7",a:"13.0"},{n:["mirror"],u:"1fa9e",a:"13.0"},{n:["window"],u:"1fa9f",a:"13.0"},{n:["bed"],u:"1f6cf-fe0f",a:"0.7"},{n:["couch and lamp"],u:"1f6cb-fe0f",a:"0.7"},{n:["chair"],u:"1fa91",a:"12.0"},{n:["toilet"],u:"1f6bd",a:"0.6"},{n:["plunger"],u:"1faa0",a:"13.0"},{n:["shower"],u:"1f6bf",a:"1.0"},{n:["bathtub"],u:"1f6c1",a:"1.0"},{n:["mouse trap"],u:"1faa4",a:"13.0"},{n:["razor"],u:"1fa92",a:"12.0"},{n:["lotion bottle"],u:"1f9f4",a:"11.0"},{n:["safety pin"],u:"1f9f7",a:"11.0"},{n:["broom"],u:"1f9f9",a:"11.0"},{n:["basket"],u:"1f9fa",a:"11.0"},{n:["roll of paper"],u:"1f9fb",a:"11.0"},{n:["bucket"],u:"1faa3",a:"13.0"},{n:["soap","bar of soap"],u:"1f9fc",a:"11.0"},{n:["bubbles"],u:"1fae7",a:"14.0"},{n:["toothbrush"],u:"1faa5",a:"13.0"},{n:["sponge"],u:"1f9fd",a:"11.0"},{n:["fire extinguisher"],u:"1f9ef",a:"11.0"},{n:["shopping trolley"],u:"1f6d2",a:"3.0"},{n:["smoking","smoking symbol"],u:"1f6ac",a:"0.6"},{n:["coffin"],u:"26b0-fe0f",a:"1.0"},{n:["headstone"],u:"1faa6",a:"13.0"},{n:["funeral urn"],u:"26b1-fe0f",a:"1.0"},{n:["moyai"],u:"1f5ff",a:"0.6"},{n:["placard"],u:"1faa7",a:"13.0"},{n:["identification card"],u:"1faaa",a:"14.0"}],symbols:[{n:["atm","automated teller machine"],u:"1f3e7",a:"0.6"},{n:["put litter in its place","put litter in its place symbol"],u:"1f6ae",a:"1.0"},{n:["potable water","potable water symbol"],u:"1f6b0",a:"1.0"},{n:["wheelchair","wheelchair symbol"],u:"267f",a:"0.6"},{n:["mens","mens symbol"],u:"1f6b9",a:"0.6"},{n:["womens","womens symbol"],u:"1f6ba",a:"0.6"},{n:["restroom"],u:"1f6bb",a:"0.6"},{n:["baby symbol"],u:"1f6bc",a:"0.6"},{n:["wc","water closet"],u:"1f6be",a:"0.6"},{n:["passport control"],u:"1f6c2",a:"1.0"},{n:["customs"],u:"1f6c3",a:"1.0"},{n:["baggage claim"],u:"1f6c4",a:"1.0"},{n:["left luggage"],u:"1f6c5",a:"1.0"},{n:["warning","warning sign"],u:"26a0-fe0f",a:"0.6"},{n:["children crossing"],u:"1f6b8",a:"1.0"},{n:["no entry"],u:"26d4",a:"0.6"},{n:["no entry sign"],u:"1f6ab",a:"0.6"},{n:["no bicycles"],u:"1f6b3",a:"1.0"},{n:["no smoking","no smoking symbol"],u:"1f6ad",a:"0.6"},{n:["do not litter","do not litter symbol"],u:"1f6af",a:"1.0"},{n:["non-potable water","non-potable water symbol"],u:"1f6b1",a:"1.0"},{n:["no pedestrians"],u:"1f6b7",a:"1.0"},{n:["no mobile phones"],u:"1f4f5",a:"1.0"},{n:["underage","no one under eighteen symbol"],u:"1f51e",a:"0.6"},{n:["radioactive","radioactive sign"],u:"2622-fe0f",a:"1.0"},{n:["biohazard","biohazard sign"],u:"2623-fe0f",a:"1.0"},{n:["arrow up","upwards black arrow"],u:"2b06-fe0f",a:"0.6"},{n:["north east arrow","arrow upper right"],u:"2197-fe0f",a:"0.6"},{n:["arrow right","black rightwards arrow"],u:"27a1-fe0f",a:"0.6"},{n:["south east arrow","arrow lower right"],u:"2198-fe0f",a:"0.6"},{n:["arrow down","downwards black arrow"],u:"2b07-fe0f",a:"0.6"},{n:["south west arrow","arrow lower left"],u:"2199-fe0f",a:"0.6"},{n:["arrow left","leftwards black arrow"],u:"2b05-fe0f",a:"0.6"},{n:["north west arrow","arrow upper left"],u:"2196-fe0f",a:"0.6"},{n:["up down arrow","arrow up down"],u:"2195-fe0f",a:"0.6"},{n:["left right arrow"],u:"2194-fe0f",a:"0.6"},{n:["leftwards arrow with hook"],u:"21a9-fe0f",a:"0.6"},{n:["arrow right hook","rightwards arrow with hook"],u:"21aa-fe0f",a:"0.6"},{n:["arrow heading up","arrow pointing rightwards then curving upwards"],u:"2934-fe0f",a:"0.6"},{n:["arrow heading down","arrow pointing rightwards then curving downwards"],u:"2935-fe0f",a:"0.6"},{n:["arrows clockwise","clockwise downwards and upwards open circle arrows"],u:"1f503",a:"0.6"},{n:["arrows counterclockwise","anticlockwise downwards and upwards open circle arrows"],u:"1f504",a:"1.0"},{n:["back","back with leftwards arrow above"],u:"1f519",a:"0.6"},{n:["end","end with leftwards arrow above"],u:"1f51a",a:"0.6"},{n:["on","on with exclamation mark with left right arrow above"],u:"1f51b",a:"0.6"},{n:["soon","soon with rightwards arrow above"],u:"1f51c",a:"0.6"},{n:["top","top with upwards arrow above"],u:"1f51d",a:"0.6"},{n:["place of worship"],u:"1f6d0",a:"1.0"},{n:["atom symbol"],u:"269b-fe0f",a:"1.0"},{n:["om","om symbol"],u:"1f549-fe0f",a:"0.7"},{n:["star of david"],u:"2721-fe0f",a:"0.7"},{n:["wheel of dharma"],u:"2638-fe0f",a:"0.7"},{n:["yin yang"],u:"262f-fe0f",a:"0.7"},{n:["latin cross"],u:"271d-fe0f",a:"0.7"},{n:["orthodox cross"],u:"2626-fe0f",a:"1.0"},{n:["star and crescent"],u:"262a-fe0f",a:"0.7"},{n:["peace symbol"],u:"262e-fe0f",a:"1.0"},{n:["menorah with nine branches"],u:"1f54e",a:"1.0"},{n:["six pointed star","six pointed star with middle dot"],u:"1f52f",a:"0.6"},{n:["aries"],u:"2648",a:"0.6"},{n:["taurus"],u:"2649",a:"0.6"},{n:["gemini"],u:"264a",a:"0.6"},{n:["cancer"],u:"264b",a:"0.6"},{n:["leo"],u:"264c",a:"0.6"},{n:["virgo"],u:"264d",a:"0.6"},{n:["libra"],u:"264e",a:"0.6"},{n:["scorpius"],u:"264f",a:"0.6"},{n:["sagittarius"],u:"2650",a:"0.6"},{n:["capricorn"],u:"2651",a:"0.6"},{n:["aquarius"],u:"2652",a:"0.6"},{n:["pisces"],u:"2653",a:"0.6"},{n:["ophiuchus"],u:"26ce",a:"0.6"},{n:["twisted rightwards arrows"],u:"1f500",a:"1.0"},{n:["repeat","clockwise rightwards and leftwards open circle arrows"],u:"1f501",a:"1.0"},{n:["repeat one","clockwise rightwards and leftwards open circle arrows with circled one overlay"],u:"1f502",a:"1.0"},{n:["arrow forward","black right-pointing triangle"],u:"25b6-fe0f",a:"0.6"},{n:["fast forward","black right-pointing double triangle"],u:"23e9",a:"0.6"},{n:["next track button","black right pointing double triangle with vertical bar"],u:"23ed-fe0f",a:"0.7"},{n:["play or pause button","black right pointing triangle with double vertical bar"],u:"23ef-fe0f",a:"1.0"},{n:["arrow backward","black left-pointing triangle"],u:"25c0-fe0f",a:"0.6"},{n:["rewind","black left-pointing double triangle"],u:"23ea",a:"0.6"},{n:["last track button","black left pointing double triangle with vertical bar"],u:"23ee-fe0f",a:"0.7"},{n:["arrow up small","up-pointing small red triangle"],u:"1f53c",a:"0.6"},{n:["arrow double up","black up-pointing double triangle"],u:"23eb",a:"0.6"},{n:["arrow down small","down-pointing small red triangle"],u:"1f53d",a:"0.6"},{n:["arrow double down","black down-pointing double triangle"],u:"23ec",a:"0.6"},{n:["pause button","double vertical bar"],u:"23f8-fe0f",a:"0.7"},{n:["stop button","black square for stop"],u:"23f9-fe0f",a:"0.7"},{n:["record button","black circle for record"],u:"23fa-fe0f",a:"0.7"},{n:["eject","eject button"],u:"23cf-fe0f",a:"1.0"},{n:["cinema"],u:"1f3a6",a:"0.6"},{n:["low brightness","low brightness symbol"],u:"1f505",a:"1.0"},{n:["high brightness","high brightness symbol"],u:"1f506",a:"1.0"},{n:["signal strength","antenna with bars"],u:"1f4f6",a:"0.6"},{n:["vibration mode"],u:"1f4f3",a:"0.6"},{n:["mobile phone off"],u:"1f4f4",a:"0.6"},{n:["female sign"],u:"2640-fe0f",a:"4.0"},{n:["male sign"],u:"2642-fe0f",a:"4.0"},{n:["transgender symbol"],u:"26a7-fe0f",a:"13.0"},{n:["heavy multiplication x"],u:"2716-fe0f",a:"0.6"},{n:["heavy plus sign"],u:"2795",a:"0.6"},{n:["heavy minus sign"],u:"2796",a:"0.6"},{n:["heavy division sign"],u:"2797",a:"0.6"},{n:["heavy equals sign"],u:"1f7f0",a:"14.0"},{n:["infinity"],u:"267e-fe0f",a:"11.0"},{n:["bangbang","double exclamation mark"],u:"203c-fe0f",a:"0.6"},{n:["interrobang","exclamation question mark"],u:"2049-fe0f",a:"0.6"},{n:["question","black question mark ornament"],u:"2753",a:"0.6"},{n:["grey question","white question mark ornament"],u:"2754",a:"0.6"},{n:["grey exclamation","white exclamation mark ornament"],u:"2755",a:"0.6"},{n:["exclamation","heavy exclamation mark","heavy exclamation mark symbol"],u:"2757",a:"0.6"},{n:["wavy dash"],u:"3030-fe0f",a:"0.6"},{n:["currency exchange"],u:"1f4b1",a:"0.6"},{n:["heavy dollar sign"],u:"1f4b2",a:"0.6"},{n:["medical symbol","staff of aesculapius"],u:"2695-fe0f",a:"4.0"},{n:["recycle","black universal recycling symbol"],u:"267b-fe0f",a:"0.6"},{n:["fleur-de-lis","fleur de lis"],u:"269c-fe0f",a:"1.0"},{n:["trident","trident emblem"],u:"1f531",a:"0.6"},{n:["name badge"],u:"1f4db",a:"0.6"},{n:["beginner","japanese symbol for beginner"],u:"1f530",a:"0.6"},{n:["o","heavy large circle"],u:"2b55",a:"0.6"},{n:["white check mark","white heavy check mark"],u:"2705",a:"0.6"},{n:["ballot box with check"],u:"2611-fe0f",a:"0.6"},{n:["heavy check mark"],u:"2714-fe0f",a:"0.6"},{n:["x","cross mark"],u:"274c",a:"0.6"},{n:["negative squared cross mark"],u:"274e",a:"0.6"},{n:["curly loop"],u:"27b0",a:"0.6"},{n:["loop","double curly loop"],u:"27bf",a:"1.0"},{n:["part alternation mark"],u:"303d-fe0f",a:"0.6"},{n:["eight spoked asterisk"],u:"2733-fe0f",a:"0.6"},{n:["eight pointed black star"],u:"2734-fe0f",a:"0.6"},{n:["sparkle"],u:"2747-fe0f",a:"0.6"},{n:["copyright","copyright sign"],u:"00a9-fe0f",a:"0.6"},{n:["registered","registered sign"],u:"00ae-fe0f",a:"0.6"},{n:["tm","trade mark sign"],u:"2122-fe0f",a:"0.6"},{n:["hash","hash key"],u:"0023-fe0f-20e3",a:"0.6"},{n:["keycap: *","keycap star"],u:"002a-fe0f-20e3",a:"2.0"},{n:["zero","keycap 0"],u:"0030-fe0f-20e3",a:"0.6"},{n:["one","keycap 1"],u:"0031-fe0f-20e3",a:"0.6"},{n:["two","keycap 2"],u:"0032-fe0f-20e3",a:"0.6"},{n:["three","keycap 3"],u:"0033-fe0f-20e3",a:"0.6"},{n:["four","keycap 4"],u:"0034-fe0f-20e3",a:"0.6"},{n:["five","keycap 5"],u:"0035-fe0f-20e3",a:"0.6"},{n:["six","keycap 6"],u:"0036-fe0f-20e3",a:"0.6"},{n:["seven","keycap 7"],u:"0037-fe0f-20e3",a:"0.6"},{n:["eight","keycap 8"],u:"0038-fe0f-20e3",a:"0.6"},{n:["nine","keycap 9"],u:"0039-fe0f-20e3",a:"0.6"},{n:["keycap ten"],u:"1f51f",a:"0.6"},{n:["capital abcd","input symbol for latin capital letters"],u:"1f520",a:"0.6"},{n:["abcd","input symbol for latin small letters"],u:"1f521",a:"0.6"},{n:["1234","input symbol for numbers"],u:"1f522",a:"0.6"},{n:["symbols","input symbol for symbols"],u:"1f523",a:"0.6"},{n:["abc","input symbol for latin letters"],u:"1f524",a:"0.6"},{n:["a","negative squared latin capital letter a"],u:"1f170-fe0f",a:"0.6"},{n:["ab","negative squared ab"],u:"1f18e",a:"0.6"},{n:["b","negative squared latin capital letter b"],u:"1f171-fe0f",a:"0.6"},{n:["cl","squared cl"],u:"1f191",a:"0.6"},{n:["cool","squared cool"],u:"1f192",a:"0.6"},{n:["free","squared free"],u:"1f193",a:"0.6"},{n:["information source"],u:"2139-fe0f",a:"0.6"},{n:["id","squared id"],u:"1f194",a:"0.6"},{n:["m","circled latin capital letter m"],u:"24c2-fe0f",a:"0.6"},{n:["new","squared new"],u:"1f195",a:"0.6"},{n:["ng","squared ng"],u:"1f196",a:"0.6"},{n:["o2","negative squared latin capital letter o"],u:"1f17e-fe0f",a:"0.6"},{n:["ok","squared ok"],u:"1f197",a:"0.6"},{n:["parking","negative squared latin capital letter p"],u:"1f17f-fe0f",a:"0.6"},{n:["sos","squared sos"],u:"1f198",a:"0.6"},{n:["up","squared up with exclamation mark"],u:"1f199",a:"0.6"},{n:["vs","squared vs"],u:"1f19a",a:"0.6"},{n:["koko","squared katakana koko"],u:"1f201",a:"0.6"},{n:["sa","squared katakana sa"],u:"1f202-fe0f",a:"0.6"},{n:["u6708","squared cjk unified ideograph-6708"],u:"1f237-fe0f",a:"0.6"},{n:["u6709","squared cjk unified ideograph-6709"],u:"1f236",a:"0.6"},{n:["u6307","squared cjk unified ideograph-6307"],u:"1f22f",a:"0.6"},{n:["ideograph advantage","circled ideograph advantage"],u:"1f250",a:"0.6"},{n:["u5272","squared cjk unified ideograph-5272"],u:"1f239",a:"0.6"},{n:["u7121","squared cjk unified ideograph-7121"],u:"1f21a",a:"0.6"},{n:["u7981","squared cjk unified ideograph-7981"],u:"1f232",a:"0.6"},{n:["accept","circled ideograph accept"],u:"1f251",a:"0.6"},{n:["u7533","squared cjk unified ideograph-7533"],u:"1f238",a:"0.6"},{n:["u5408","squared cjk unified ideograph-5408"],u:"1f234",a:"0.6"},{n:["u7a7a","squared cjk unified ideograph-7a7a"],u:"1f233",a:"0.6"},{n:["congratulations","circled ideograph congratulation"],u:"3297-fe0f",a:"0.6"},{n:["secret","circled ideograph secret"],u:"3299-fe0f",a:"0.6"},{n:["u55b6","squared cjk unified ideograph-55b6"],u:"1f23a",a:"0.6"},{n:["u6e80","squared cjk unified ideograph-6e80"],u:"1f235",a:"0.6"},{n:["red circle","large red circle"],u:"1f534",a:"0.6"},{n:["large orange circle"],u:"1f7e0",a:"12.0"},{n:["large yellow circle"],u:"1f7e1",a:"12.0"},{n:["large green circle"],u:"1f7e2",a:"12.0"},{n:["large blue circle"],u:"1f535",a:"0.6"},{n:["large purple circle"],u:"1f7e3",a:"12.0"},{n:["large brown circle"],u:"1f7e4",a:"12.0"},{n:["black circle","medium black circle"],u:"26ab",a:"0.6"},{n:["white circle","medium white circle"],u:"26aa",a:"0.6"},{n:["large red square"],u:"1f7e5",a:"12.0"},{n:["large orange square"],u:"1f7e7",a:"12.0"},{n:["large yellow square"],u:"1f7e8",a:"12.0"},{n:["large green square"],u:"1f7e9",a:"12.0"},{n:["large blue square"],u:"1f7e6",a:"12.0"},{n:["large purple square"],u:"1f7ea",a:"12.0"},{n:["large brown square"],u:"1f7eb",a:"12.0"},{n:["black large square"],u:"2b1b",a:"0.6"},{n:["white large square"],u:"2b1c",a:"0.6"},{n:["black medium square"],u:"25fc-fe0f",a:"0.6"},{n:["white medium square"],u:"25fb-fe0f",a:"0.6"},{n:["black medium small square"],u:"25fe",a:"0.6"},{n:["white medium small square"],u:"25fd",a:"0.6"},{n:["black small square"],u:"25aa-fe0f",a:"0.6"},{n:["white small square"],u:"25ab-fe0f",a:"0.6"},{n:["large orange diamond"],u:"1f536",a:"0.6"},{n:["large blue diamond"],u:"1f537",a:"0.6"},{n:["small orange diamond"],u:"1f538",a:"0.6"},{n:["small blue diamond"],u:"1f539",a:"0.6"},{n:["small red triangle","up-pointing red triangle"],u:"1f53a",a:"0.6"},{n:["small red triangle down","down-pointing red triangle"],u:"1f53b",a:"0.6"},{n:["diamond shape with a dot inside"],u:"1f4a0",a:"0.6"},{n:["radio button"],u:"1f518",a:"0.6"},{n:["white square button"],u:"1f533",a:"0.6"},{n:["black square button"],u:"1f532",a:"0.6"}],flags:[{n:["chequered flag","checkered flag"],u:"1f3c1",a:"0.6"},{n:["triangular flag on post"],u:"1f6a9",a:"0.6"},{n:["crossed flags"],u:"1f38c",a:"0.6"},{n:["waving black flag"],u:"1f3f4",a:"1.0"},{n:["white flag","waving white flag"],u:"1f3f3-fe0f",a:"0.7"},{n:["rainbow flag","rainbow-flag"],u:"1f3f3-fe0f-200d-1f308",a:"4.0"},{n:["transgender flag"],u:"1f3f3-fe0f-200d-26a7-fe0f",a:"13.0"},{n:["pirate flag"],u:"1f3f4-200d-2620-fe0f",a:"11.0"},{n:["flag-ac","ascension island flag"],u:"1f1e6-1f1e8",a:"2.0"},{n:["flag-ad","andorra flag"],u:"1f1e6-1f1e9",a:"2.0"},{n:["flag-ae","united arab emirates flag"],u:"1f1e6-1f1ea",a:"2.0"},{n:["flag-af","afghanistan flag"],u:"1f1e6-1f1eb",a:"2.0"},{n:["flag-ag","antigua & barbuda flag"],u:"1f1e6-1f1ec",a:"2.0"},{n:["flag-ai","anguilla flag"],u:"1f1e6-1f1ee",a:"2.0"},{n:["flag-al","albania flag"],u:"1f1e6-1f1f1",a:"2.0"},{n:["flag-am","armenia flag"],u:"1f1e6-1f1f2",a:"2.0"},{n:["flag-ao","angola flag"],u:"1f1e6-1f1f4",a:"2.0"},{n:["flag-aq","antarctica flag"],u:"1f1e6-1f1f6",a:"2.0"},{n:["flag-ar","argentina flag"],u:"1f1e6-1f1f7",a:"2.0"},{n:["flag-as","american samoa flag"],u:"1f1e6-1f1f8",a:"2.0"},{n:["flag-at","austria flag"],u:"1f1e6-1f1f9",a:"2.0"},{n:["flag-au","australia flag"],u:"1f1e6-1f1fa",a:"2.0"},{n:["flag-aw","aruba flag"],u:"1f1e6-1f1fc",a:"2.0"},{n:["flag-ax","\xe5land islands flag"],u:"1f1e6-1f1fd",a:"2.0"},{n:["flag-az","azerbaijan flag"],u:"1f1e6-1f1ff",a:"2.0"},{n:["flag-ba","bosnia & herzegovina flag"],u:"1f1e7-1f1e6",a:"2.0"},{n:["flag-bb","barbados flag"],u:"1f1e7-1f1e7",a:"2.0"},{n:["flag-bd","bangladesh flag"],u:"1f1e7-1f1e9",a:"2.0"},{n:["flag-be","belgium flag"],u:"1f1e7-1f1ea",a:"2.0"},{n:["flag-bf","burkina faso flag"],u:"1f1e7-1f1eb",a:"2.0"},{n:["flag-bg","bulgaria flag"],u:"1f1e7-1f1ec",a:"2.0"},{n:["flag-bh","bahrain flag"],u:"1f1e7-1f1ed",a:"2.0"},{n:["flag-bi","burundi flag"],u:"1f1e7-1f1ee",a:"2.0"},{n:["flag-bj","benin flag"],u:"1f1e7-1f1ef",a:"2.0"},{n:["flag-bl","st. barth\xe9lemy flag"],u:"1f1e7-1f1f1",a:"2.0"},{n:["flag-bm","bermuda flag"],u:"1f1e7-1f1f2",a:"2.0"},{n:["flag-bn","brunei flag"],u:"1f1e7-1f1f3",a:"2.0"},{n:["flag-bo","bolivia flag"],u:"1f1e7-1f1f4",a:"2.0"},{n:["flag-bq","caribbean netherlands flag"],u:"1f1e7-1f1f6",a:"2.0"},{n:["flag-br","brazil flag"],u:"1f1e7-1f1f7",a:"2.0"},{n:["flag-bs","bahamas flag"],u:"1f1e7-1f1f8",a:"2.0"},{n:["flag-bt","bhutan flag"],u:"1f1e7-1f1f9",a:"2.0"},{n:["flag-bv","bouvet island flag"],u:"1f1e7-1f1fb",a:"2.0"},{n:["flag-bw","botswana flag"],u:"1f1e7-1f1fc",a:"2.0"},{n:["flag-by","belarus flag"],u:"1f1e7-1f1fe",a:"2.0"},{n:["flag-bz","belize flag"],u:"1f1e7-1f1ff",a:"2.0"},{n:["flag-ca","canada flag"],u:"1f1e8-1f1e6",a:"2.0"},{n:["flag-cc","cocos (keeling) islands flag"],u:"1f1e8-1f1e8",a:"2.0"},{n:["flag-cd","congo - kinshasa flag"],u:"1f1e8-1f1e9",a:"2.0"},{n:["flag-cf","central african republic flag"],u:"1f1e8-1f1eb",a:"2.0"},{n:["flag-cg","congo - brazzaville flag"],u:"1f1e8-1f1ec",a:"2.0"},{n:["flag-ch","switzerland flag"],u:"1f1e8-1f1ed",a:"2.0"},{n:["flag-ci","c\xf4te d’ivoire flag"],u:"1f1e8-1f1ee",a:"2.0"},{n:["flag-ck","cook islands flag"],u:"1f1e8-1f1f0",a:"2.0"},{n:["flag-cl","chile flag"],u:"1f1e8-1f1f1",a:"2.0"},{n:["flag-cm","cameroon flag"],u:"1f1e8-1f1f2",a:"2.0"},{n:["cn","flag-cn","china flag"],u:"1f1e8-1f1f3",a:"0.6"},{n:["flag-co","colombia flag"],u:"1f1e8-1f1f4",a:"2.0"},{n:["flag-cp","clipperton island flag"],u:"1f1e8-1f1f5",a:"2.0"},{n:["flag-cr","costa rica flag"],u:"1f1e8-1f1f7",a:"2.0"},{n:["flag-cu","cuba flag"],u:"1f1e8-1f1fa",a:"2.0"},{n:["flag-cv","cape verde flag"],u:"1f1e8-1f1fb",a:"2.0"},{n:["flag-cw","cura\xe7ao flag"],u:"1f1e8-1f1fc",a:"2.0"},{n:["flag-cx","christmas island flag"],u:"1f1e8-1f1fd",a:"2.0"},{n:["flag-cy","cyprus flag"],u:"1f1e8-1f1fe",a:"2.0"},{n:["flag-cz","czechia flag"],u:"1f1e8-1f1ff",a:"2.0"},{n:["de","flag-de","germany flag"],u:"1f1e9-1f1ea",a:"0.6"},{n:["flag-dg","diego garcia flag"],u:"1f1e9-1f1ec",a:"2.0"},{n:["flag-dj","djibouti flag"],u:"1f1e9-1f1ef",a:"2.0"},{n:["flag-dk","denmark flag"],u:"1f1e9-1f1f0",a:"2.0"},{n:["flag-dm","dominica flag"],u:"1f1e9-1f1f2",a:"2.0"},{n:["flag-do","dominican republic flag"],u:"1f1e9-1f1f4",a:"2.0"},{n:["flag-dz","algeria flag"],u:"1f1e9-1f1ff",a:"2.0"},{n:["flag-ea","ceuta & melilla flag"],u:"1f1ea-1f1e6",a:"2.0"},{n:["flag-ec","ecuador flag"],u:"1f1ea-1f1e8",a:"2.0"},{n:["flag-ee","estonia flag"],u:"1f1ea-1f1ea",a:"2.0"},{n:["flag-eg","egypt flag"],u:"1f1ea-1f1ec",a:"2.0"},{n:["flag-eh","western sahara flag"],u:"1f1ea-1f1ed",a:"2.0"},{n:["flag-er","eritrea flag"],u:"1f1ea-1f1f7",a:"2.0"},{n:["es","flag-es","spain flag"],u:"1f1ea-1f1f8",a:"0.6"},{n:["flag-et","ethiopia flag"],u:"1f1ea-1f1f9",a:"2.0"},{n:["flag-eu","european union flag"],u:"1f1ea-1f1fa",a:"2.0"},{n:["flag-fi","finland flag"],u:"1f1eb-1f1ee",a:"2.0"},{n:["flag-fj","fiji flag"],u:"1f1eb-1f1ef",a:"2.0"},{n:["flag-fk","falkland islands flag"],u:"1f1eb-1f1f0",a:"2.0"},{n:["flag-fm","micronesia flag"],u:"1f1eb-1f1f2",a:"2.0"},{n:["flag-fo","faroe islands flag"],u:"1f1eb-1f1f4",a:"2.0"},{n:["fr","flag-fr","france flag"],u:"1f1eb-1f1f7",a:"0.6"},{n:["flag-ga","gabon flag"],u:"1f1ec-1f1e6",a:"2.0"},{n:["gb","uk","flag-gb","united kingdom flag"],u:"1f1ec-1f1e7",a:"0.6"},{n:["flag-gd","grenada flag"],u:"1f1ec-1f1e9",a:"2.0"},{n:["flag-ge","georgia flag"],u:"1f1ec-1f1ea",a:"2.0"},{n:["flag-gf","french guiana flag"],u:"1f1ec-1f1eb",a:"2.0"},{n:["flag-gg","guernsey flag"],u:"1f1ec-1f1ec",a:"2.0"},{n:["flag-gh","ghana flag"],u:"1f1ec-1f1ed",a:"2.0"},{n:["flag-gi","gibraltar flag"],u:"1f1ec-1f1ee",a:"2.0"},{n:["flag-gl","greenland flag"],u:"1f1ec-1f1f1",a:"2.0"},{n:["flag-gm","gambia flag"],u:"1f1ec-1f1f2",a:"2.0"},{n:["flag-gn","guinea flag"],u:"1f1ec-1f1f3",a:"2.0"},{n:["flag-gp","guadeloupe flag"],u:"1f1ec-1f1f5",a:"2.0"},{n:["flag-gq","equatorial guinea flag"],u:"1f1ec-1f1f6",a:"2.0"},{n:["flag-gr","greece flag"],u:"1f1ec-1f1f7",a:"2.0"},{n:["flag-gs","south georgia & south sandwich islands flag"],u:"1f1ec-1f1f8",a:"2.0"},{n:["flag-gt","guatemala flag"],u:"1f1ec-1f1f9",a:"2.0"},{n:["flag-gu","guam flag"],u:"1f1ec-1f1fa",a:"2.0"},{n:["flag-gw","guinea-bissau flag"],u:"1f1ec-1f1fc",a:"2.0"},{n:["flag-gy","guyana flag"],u:"1f1ec-1f1fe",a:"2.0"},{n:["flag-hk","hong kong sar china flag"],u:"1f1ed-1f1f0",a:"2.0"},{n:["flag-hm","heard & mcdonald islands flag"],u:"1f1ed-1f1f2",a:"2.0"},{n:["flag-hn","honduras flag"],u:"1f1ed-1f1f3",a:"2.0"},{n:["flag-hr","croatia flag"],u:"1f1ed-1f1f7",a:"2.0"},{n:["flag-ht","haiti flag"],u:"1f1ed-1f1f9",a:"2.0"},{n:["flag-hu","hungary flag"],u:"1f1ed-1f1fa",a:"2.0"},{n:["flag-ic","canary islands flag"],u:"1f1ee-1f1e8",a:"2.0"},{n:["flag-id","indonesia flag"],u:"1f1ee-1f1e9",a:"2.0"},{n:["flag-ie","ireland flag"],u:"1f1ee-1f1ea",a:"2.0"},{n:["flag-il","israel flag"],u:"1f1ee-1f1f1",a:"2.0"},{n:["flag-im","isle of man flag"],u:"1f1ee-1f1f2",a:"2.0"},{n:["flag-in","india flag"],u:"1f1ee-1f1f3",a:"2.0"},{n:["flag-io","british indian ocean territory flag"],u:"1f1ee-1f1f4",a:"2.0"},{n:["flag-iq","iraq flag"],u:"1f1ee-1f1f6",a:"2.0"},{n:["flag-ir","iran flag"],u:"1f1ee-1f1f7",a:"2.0"},{n:["flag-is","iceland flag"],u:"1f1ee-1f1f8",a:"2.0"},{n:["it","flag-it","italy flag"],u:"1f1ee-1f1f9",a:"0.6"},{n:["flag-je","jersey flag"],u:"1f1ef-1f1ea",a:"2.0"},{n:["flag-jm","jamaica flag"],u:"1f1ef-1f1f2",a:"2.0"},{n:["flag-jo","jordan flag"],u:"1f1ef-1f1f4",a:"2.0"},{n:["jp","flag-jp","japan flag"],u:"1f1ef-1f1f5",a:"0.6"},{n:["flag-ke","kenya flag"],u:"1f1f0-1f1ea",a:"2.0"},{n:["flag-kg","kyrgyzstan flag"],u:"1f1f0-1f1ec",a:"2.0"},{n:["flag-kh","cambodia flag"],u:"1f1f0-1f1ed",a:"2.0"},{n:["flag-ki","kiribati flag"],u:"1f1f0-1f1ee",a:"2.0"},{n:["flag-km","comoros flag"],u:"1f1f0-1f1f2",a:"2.0"},{n:["flag-kn","st. kitts & nevis flag"],u:"1f1f0-1f1f3",a:"2.0"},{n:["flag-kp","north korea flag"],u:"1f1f0-1f1f5",a:"2.0"},{n:["kr","flag-kr","south korea flag"],u:"1f1f0-1f1f7",a:"0.6"},{n:["flag-kw","kuwait flag"],u:"1f1f0-1f1fc",a:"2.0"},{n:["flag-ky","cayman islands flag"],u:"1f1f0-1f1fe",a:"2.0"},{n:["flag-kz","kazakhstan flag"],u:"1f1f0-1f1ff",a:"2.0"},{n:["flag-la","laos flag"],u:"1f1f1-1f1e6",a:"2.0"},{n:["flag-lb","lebanon flag"],u:"1f1f1-1f1e7",a:"2.0"},{n:["flag-lc","st. lucia flag"],u:"1f1f1-1f1e8",a:"2.0"},{n:["flag-li","liechtenstein flag"],u:"1f1f1-1f1ee",a:"2.0"},{n:["flag-lk","sri lanka flag"],u:"1f1f1-1f1f0",a:"2.0"},{n:["flag-lr","liberia flag"],u:"1f1f1-1f1f7",a:"2.0"},{n:["flag-ls","lesotho flag"],u:"1f1f1-1f1f8",a:"2.0"},{n:["flag-lt","lithuania flag"],u:"1f1f1-1f1f9",a:"2.0"},{n:["flag-lu","luxembourg flag"],u:"1f1f1-1f1fa",a:"2.0"},{n:["flag-lv","latvia flag"],u:"1f1f1-1f1fb",a:"2.0"},{n:["flag-ly","libya flag"],u:"1f1f1-1f1fe",a:"2.0"},{n:["flag-ma","morocco flag"],u:"1f1f2-1f1e6",a:"2.0"},{n:["flag-mc","monaco flag"],u:"1f1f2-1f1e8",a:"2.0"},{n:["flag-md","moldova flag"],u:"1f1f2-1f1e9",a:"2.0"},{n:["flag-me","montenegro flag"],u:"1f1f2-1f1ea",a:"2.0"},{n:["flag-mf","st. martin flag"],u:"1f1f2-1f1eb",a:"2.0"},{n:["flag-mg","madagascar flag"],u:"1f1f2-1f1ec",a:"2.0"},{n:["flag-mh","marshall islands flag"],u:"1f1f2-1f1ed",a:"2.0"},{n:["flag-mk","north macedonia flag"],u:"1f1f2-1f1f0",a:"2.0"},{n:["flag-ml","mali flag"],u:"1f1f2-1f1f1",a:"2.0"},{n:["flag-mm","myanmar (burma) flag"],u:"1f1f2-1f1f2",a:"2.0"},{n:["flag-mn","mongolia flag"],u:"1f1f2-1f1f3",a:"2.0"},{n:["flag-mo","macao sar china flag"],u:"1f1f2-1f1f4",a:"2.0"},{n:["flag-mp","northern mariana islands flag"],u:"1f1f2-1f1f5",a:"2.0"},{n:["flag-mq","martinique flag"],u:"1f1f2-1f1f6",a:"2.0"},{n:["flag-mr","mauritania flag"],u:"1f1f2-1f1f7",a:"2.0"},{n:["flag-ms","montserrat flag"],u:"1f1f2-1f1f8",a:"2.0"},{n:["flag-mt","malta flag"],u:"1f1f2-1f1f9",a:"2.0"},{n:["flag-mu","mauritius flag"],u:"1f1f2-1f1fa",a:"2.0"},{n:["flag-mv","maldives flag"],u:"1f1f2-1f1fb",a:"2.0"},{n:["flag-mw","malawi flag"],u:"1f1f2-1f1fc",a:"2.0"},{n:["flag-mx","mexico flag"],u:"1f1f2-1f1fd",a:"2.0"},{n:["flag-my","malaysia flag"],u:"1f1f2-1f1fe",a:"2.0"},{n:["flag-mz","mozambique flag"],u:"1f1f2-1f1ff",a:"2.0"},{n:["flag-na","namibia flag"],u:"1f1f3-1f1e6",a:"2.0"},{n:["flag-nc","new caledonia flag"],u:"1f1f3-1f1e8",a:"2.0"},{n:["flag-ne","niger flag"],u:"1f1f3-1f1ea",a:"2.0"},{n:["flag-nf","norfolk island flag"],u:"1f1f3-1f1eb",a:"2.0"},{n:["flag-ng","nigeria flag"],u:"1f1f3-1f1ec",a:"2.0"},{n:["flag-ni","nicaragua flag"],u:"1f1f3-1f1ee",a:"2.0"},{n:["flag-nl","netherlands flag"],u:"1f1f3-1f1f1",a:"2.0"},{n:["flag-no","norway flag"],u:"1f1f3-1f1f4",a:"2.0"},{n:["flag-np","nepal flag"],u:"1f1f3-1f1f5",a:"2.0"},{n:["flag-nr","nauru flag"],u:"1f1f3-1f1f7",a:"2.0"},{n:["flag-nu","niue flag"],u:"1f1f3-1f1fa",a:"2.0"},{n:["flag-nz","new zealand flag"],u:"1f1f3-1f1ff",a:"2.0"},{n:["flag-om","oman flag"],u:"1f1f4-1f1f2",a:"2.0"},{n:["flag-pa","panama flag"],u:"1f1f5-1f1e6",a:"2.0"},{n:["flag-pe","peru flag"],u:"1f1f5-1f1ea",a:"2.0"},{n:["flag-pf","french polynesia flag"],u:"1f1f5-1f1eb",a:"2.0"},{n:["flag-pg","papua new guinea flag"],u:"1f1f5-1f1ec",a:"2.0"},{n:["flag-ph","philippines flag"],u:"1f1f5-1f1ed",a:"2.0"},{n:["flag-pk","pakistan flag"],u:"1f1f5-1f1f0",a:"2.0"},{n:["flag-pl","poland flag"],u:"1f1f5-1f1f1",a:"2.0"},{n:["flag-pm","st. pierre & miquelon flag"],u:"1f1f5-1f1f2",a:"2.0"},{n:["flag-pn","pitcairn islands flag"],u:"1f1f5-1f1f3",a:"2.0"},{n:["flag-pr","puerto rico flag"],u:"1f1f5-1f1f7",a:"2.0"},{n:["flag-ps","palestinian territories flag"],u:"1f1f5-1f1f8",a:"2.0"},{n:["flag-pt","portugal flag"],u:"1f1f5-1f1f9",a:"2.0"},{n:["flag-pw","palau flag"],u:"1f1f5-1f1fc",a:"2.0"},{n:["flag-py","paraguay flag"],u:"1f1f5-1f1fe",a:"2.0"},{n:["flag-qa","qatar flag"],u:"1f1f6-1f1e6",a:"2.0"},{n:["flag-re","r\xe9union flag"],u:"1f1f7-1f1ea",a:"2.0"},{n:["flag-ro","romania flag"],u:"1f1f7-1f1f4",a:"2.0"},{n:["flag-rs","serbia flag"],u:"1f1f7-1f1f8",a:"2.0"},{n:["ru","flag-ru","russia flag"],u:"1f1f7-1f1fa",a:"0.6"},{n:["flag-rw","rwanda flag"],u:"1f1f7-1f1fc",a:"2.0"},{n:["flag-sa","saudi arabia flag"],u:"1f1f8-1f1e6",a:"2.0"},{n:["flag-sb","solomon islands flag"],u:"1f1f8-1f1e7",a:"2.0"},{n:["flag-sc","seychelles flag"],u:"1f1f8-1f1e8",a:"2.0"},{n:["flag-sd","sudan flag"],u:"1f1f8-1f1e9",a:"2.0"},{n:["flag-se","sweden flag"],u:"1f1f8-1f1ea",a:"2.0"},{n:["flag-sg","singapore flag"],u:"1f1f8-1f1ec",a:"2.0"},{n:["flag-sh","st. helena flag"],u:"1f1f8-1f1ed",a:"2.0"},{n:["flag-si","slovenia flag"],u:"1f1f8-1f1ee",a:"2.0"},{n:["flag-sj","svalbard & jan mayen flag"],u:"1f1f8-1f1ef",a:"2.0"},{n:["flag-sk","slovakia flag"],u:"1f1f8-1f1f0",a:"2.0"},{n:["flag-sl","sierra leone flag"],u:"1f1f8-1f1f1",a:"2.0"},{n:["flag-sm","san marino flag"],u:"1f1f8-1f1f2",a:"2.0"},{n:["flag-sn","senegal flag"],u:"1f1f8-1f1f3",a:"2.0"},{n:["flag-so","somalia flag"],u:"1f1f8-1f1f4",a:"2.0"},{n:["flag-sr","suriname flag"],u:"1f1f8-1f1f7",a:"2.0"},{n:["flag-ss","south sudan flag"],u:"1f1f8-1f1f8",a:"2.0"},{n:["flag-st","s\xe3o tom\xe9 & pr\xedncipe flag"],u:"1f1f8-1f1f9",a:"2.0"},{n:["flag-sv","el salvador flag"],u:"1f1f8-1f1fb",a:"2.0"},{n:["flag-sx","sint maarten flag"],u:"1f1f8-1f1fd",a:"2.0"},{n:["flag-sy","syria flag"],u:"1f1f8-1f1fe",a:"2.0"},{n:["flag-sz","eswatini flag"],u:"1f1f8-1f1ff",a:"2.0"},{n:["flag-ta","tristan da cunha flag"],u:"1f1f9-1f1e6",a:"2.0"},{n:["flag-tc","turks & caicos islands flag"],u:"1f1f9-1f1e8",a:"2.0"},{n:["flag-td","chad flag"],u:"1f1f9-1f1e9",a:"2.0"},{n:["flag-tf","french southern territories flag"],u:"1f1f9-1f1eb",a:"2.0"},{n:["flag-tg","togo flag"],u:"1f1f9-1f1ec",a:"2.0"},{n:["flag-th","thailand flag"],u:"1f1f9-1f1ed",a:"2.0"},{n:["flag-tj","tajikistan flag"],u:"1f1f9-1f1ef",a:"2.0"},{n:["flag-tk","tokelau flag"],u:"1f1f9-1f1f0",a:"2.0"},{n:["flag-tl","timor-leste flag"],u:"1f1f9-1f1f1",a:"2.0"},{n:["flag-tm","turkmenistan flag"],u:"1f1f9-1f1f2",a:"2.0"},{n:["flag-tn","tunisia flag"],u:"1f1f9-1f1f3",a:"2.0"},{n:["flag-to","tonga flag"],u:"1f1f9-1f1f4",a:"2.0"},{n:["flag-tr","turkey flag"],u:"1f1f9-1f1f7",a:"2.0"},{n:["flag-tt","trinidad & tobago flag"],u:"1f1f9-1f1f9",a:"2.0"},{n:["flag-tv","tuvalu flag"],u:"1f1f9-1f1fb",a:"2.0"},{n:["flag-tw","taiwan flag"],u:"1f1f9-1f1fc",a:"2.0"},{n:["flag-tz","tanzania flag"],u:"1f1f9-1f1ff",a:"2.0"},{n:["flag-ua","ukraine flag"],u:"1f1fa-1f1e6",a:"2.0"},{n:["flag-ug","uganda flag"],u:"1f1fa-1f1ec",a:"2.0"},{n:["flag-um","u.s. outlying islands flag"],u:"1f1fa-1f1f2",a:"2.0"},{n:["flag-un","united nations flag"],u:"1f1fa-1f1f3",a:"4.0"},{n:["us","flag-us","united states flag"],u:"1f1fa-1f1f8",a:"0.6"},{n:["flag-uy","uruguay flag"],u:"1f1fa-1f1fe",a:"2.0"},{n:["flag-uz","uzbekistan flag"],u:"1f1fa-1f1ff",a:"2.0"},{n:["flag-va","vatican city flag"],u:"1f1fb-1f1e6",a:"2.0"},{n:["flag-vc","st. vincent & grenadines flag"],u:"1f1fb-1f1e8",a:"2.0"},{n:["flag-ve","venezuela flag"],u:"1f1fb-1f1ea",a:"2.0"},{n:["flag-vg","british virgin islands flag"],u:"1f1fb-1f1ec",a:"2.0"},{n:["flag-vi","u.s. virgin islands flag"],u:"1f1fb-1f1ee",a:"2.0"},{n:["flag-vn","vietnam flag"],u:"1f1fb-1f1f3",a:"2.0"},{n:["flag-vu","vanuatu flag"],u:"1f1fb-1f1fa",a:"2.0"},{n:["flag-wf","wallis & futuna flag"],u:"1f1fc-1f1eb",a:"2.0"},{n:["flag-ws","samoa flag"],u:"1f1fc-1f1f8",a:"2.0"},{n:["flag-xk","kosovo flag"],u:"1f1fd-1f1f0",a:"2.0"},{n:["flag-ye","yemen flag"],u:"1f1fe-1f1ea",a:"2.0"},{n:["flag-yt","mayotte flag"],u:"1f1fe-1f1f9",a:"2.0"},{n:["flag-za","south africa flag"],u:"1f1ff-1f1e6",a:"2.0"},{n:["flag-zm","zambia flag"],u:"1f1ff-1f1f2",a:"2.0"},{n:["flag-zw","zimbabwe flag"],u:"1f1ff-1f1fc",a:"2.0"},{n:["england flag","flag-england"],u:"1f3f4-e0067-e0062-e0065-e006e-e0067-e007f",a:"5.0"},{n:["scotland flag","flag-scotland"],u:"1f3f4-e0067-e0062-e0073-e0063-e0074-e007f",a:"5.0"},{n:["wales flag","flag-wales"],u:"1f3f4-e0067-e0062-e0077-e006c-e0073-e007f",a:"5.0"}]},et=[s.NEUTRAL,s.LIGHT,s.MEDIUM_LIGHT,s.MEDIUM,s.MEDIUM_DARK,s.DARK],en=Object.entries(s).reduce(function(e,f){var t=f[0];return e[f[1]]=t,e},{}),ea=et.reduce(function(e,f){var t;return Object.assign(e,((t={})[f]=f,t))},{});!function(e){e.name="n",e.unified="u",e.variations="v",e.added_in="a",e.imgUrl="imgUrl"}(d||(d={}));var ei={};function er(e){es(e).flat().join("").toLowerCase().replace(/[^a-zA-Z\d]/g,"").split("").forEach(function(f){var t;ei[f]=null!=(t=ei[f])?t:{},ei[f][ec(e)]=e})}function es(e){var f;return null!=(f=e[d.name])?f:[]}function eo(e){return e?es(e)[0]:""}function eu(e){var f=e.split("-");return ea[f.splice(1,1)[0]]?f.join("-"):e}function ec(e,f){var t,n,a,i=e[d.unified];return f&&eh(e)&&null!=(t=e,a=(n=f)?el(t).find(function(e){return e.includes(n)}):ec(t))?a:i}function ed(e,f){return""+function(e){switch(e){case i.TWITTER:return"https://cdn.jsdelivr.net/npm/emoji-datasource-twitter/img/twitter/64/";case i.GOOGLE:return"https://cdn.jsdelivr.net/npm/emoji-datasource-google/img/google/64/";case i.FACEBOOK:return"https://cdn.jsdelivr.net/npm/emoji-datasource-facebook/img/facebook/64/";case i.APPLE:default:return"https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/"}}(f)+e+".png"}function el(e){var f;return null!=(f=e[d.variations])?f:[]}function eh(e){return el(e).length>0}function eg(e){return e?ep[e]?ep[e]:ep[eu(e)]:void 0}setTimeout(function(){ew.reduce(function(e,f){return er(f),e},ei)});var ew=Object.values(ef).flat(),ep={};setTimeout(function(){ew.reduce(function(e,f){return e[ec(f)]=f,eh(f)&&el(f).forEach(function(t){e[t]=f}),e},ep)});var eM=["2640-fe0f","2642-fe0f","2695-fe0f"],eL="Search",eb=" found. Use up and down arrow keys to navigate.",em="1 result"+eb,ey="%n results"+eb;function eC(e){void 0===e&&(e={});var f,t,n,i,r,s,c,l,h=ej(),g=Object.assign(h.previewConfig,null!=(c=e.previewConfig)?c:{}),w=Object.assign(h,e),p=(f=e.categories,t={suggestionMode:w.suggestedEmojisMode},void 0===f&&(f=[]),void 0===t&&(t={}),i={},t.suggestionMode===a.RECENT&&(i[o.SUGGESTED]=X),r=q(i),null!=(n=f)&&n.length?f.map(function(e){return"string"==typeof e?ee(e,i[e]):O({},ee(e.category,i[e.category]),e)}):r);w.hiddenEmojis.forEach(function(e){w.unicodeToHide.add(e)}),s=null!=(l=w.customEmojis)?l:[],ef[o.CUSTOM].length=0,s.forEach(function(e){var f,t,n=(f=e,(t={})[d.name]=f.names.map(function(e){return e.toLowerCase()}),t[d.unified]=f.id.toLowerCase(),t[d.added_in]="0",t[d.imgUrl]=f.imgUrl,t);ef[o.CUSTOM].push(n),!ep[n[d.unified]]&&(ew.push(n),ep[n[d.unified]]=n,er(n))});var M=w.searchDisabled?u.PREVIEW:w.skinTonePickerLocation;return O({},w,{categories:p,previewConfig:g,skinTonePickerLocation:M})}function ej(){return{autoFocusSearch:!0,categories:q(),className:"",customEmojis:[],defaultSkinTone:s.NEUTRAL,emojiStyle:i.APPLE,emojiVersion:null,getEmojiUrl:ed,height:450,lazyLoadEmojis:!1,previewConfig:O({},eI),searchDisabled:!1,searchPlaceHolder:eL,searchPlaceholder:eL,skinTonePickerLocation:u.SEARCH,skinTonesDisabled:!1,style:{},suggestedEmojisMode:a.FREQUENT,theme:r.LIGHT,unicodeToHide:new Set(eM),width:350,reactionsDefaultOpen:!1,reactions:H,open:!0,allowExpandReactions:!0,hiddenEmojis:[]}}var eI={defaultEmoji:"1f60a",defaultCaption:"What's your mood?",showPreview:!0},ev=["children"],ek=(0,L.createContext)(ej());function eN(e){var f,t,n,a,i,r=e.children,s=(f=Q(e,ev),a=(n=(0,L.useState)(function(){return eC(f)}))[0],i=n[1],(0,L.useEffect)(function(){!W(a,f)&&i(eC(f))},[null==(t=f.customEmojis)?void 0:t.length,f.open,f.emojiVersion,f.reactionsDefaultOpen,f.searchPlaceHolder,f.searchPlaceholder,f.defaultSkinTone,f.skinTonesDisabled,f.autoFocusSearch,f.emojiStyle,f.theme,f.suggestedEmojisMode,f.lazyLoadEmojis,f.className,f.height,f.width,f.searchDisabled,f.skinTonePickerLocation,f.allowExpandReactions]),a);return(0,L.createElement)(ek.Provider,{value:s},r)}function eS(){return(0,L.useContext)(ek)}var eD=b().createContext({});function eT(){return b().useContext(eD)}function eE(){}function ex(){return eS().allowExpandReactions}function ez(){return eS().skinTonesDisabled}function eA(){return eS().emojiStyle}function e_(){return eS().categories}function eO(){return eS().previewConfig}function eP(){return eS().searchDisabled}function eQ(){return eS().skinTonePickerLocation}function eY(){return eS().getEmojiUrl}function eR(e){return"number"==typeof e?e+"px":e}function eJ(e,f){void 0===f&&(f=0);var t=(0,L.useState)(e),n=t[0],a=t[1],i=(0,L.useRef)(null);return[n,function(e){return new Promise(function(t){var n;i.current&&clearTimeout(i.current),i.current=null==(n=window)?void 0:n.setTimeout(function(){a(e),t(e)},f)})}]}function eB(){var e=(0,L.useRef)({}),f=eS().emojiVersion;return(0,L.useMemo)(function(){var t=parseFloat(""+f);return!f||Number.isNaN(t)?e.current:ew.reduce(function(e,f){var n,a;return n=f,a=t,parseFloat(n[d.added_in])>a&&(e[ec(f)]=!0),e},e.current)},[f])}function eU(e){var f,t=e.children,n=eB(),a=eS().defaultSkinTone,i=eS().reactionsDefaultOpen,r=(0,L.useRef)(ei),s=(0,L.useRef)(!1),o=(0,L.useRef)(!1),u=(0,L.useRef)(n),c=eJ(Date.now(),200),d=eJ("",100),l=(0,L.useState)(!1),h=(0,L.useState)(a),g=(0,L.useState)(null),w=(0,L.useState)(new Set),p=(0,L.useState)(null),M=(0,L.useState)(i),b=(0,L.useState)(!1),m=b[0];return f=b[1],(0,L.useEffect)(function(){f(!0)},[f]),(0,L.createElement)(eG.Provider,{value:{activeCategoryState:g,activeSkinTone:h,disallowClickRef:s,disallowMouseRef:o,disallowedEmojisRef:u,emojiVariationPickerState:p,emojisThatFailedToLoadState:w,filterRef:r,isPastInitialLoad:m,searchTerm:d,skinToneFanOpenState:l,suggestedUpdateState:c,reactionsModeState:M}},t)}!function(e){e.REACTIONS="reactions",e.PICKER="picker"}(l||(l={}));var eG=(0,L.createContext)({activeCategoryState:[null,function(){}],activeSkinTone:[s.NEUTRAL,function(){}],disallowClickRef:{current:!1},disallowMouseRef:{current:!1},disallowedEmojisRef:{current:{}},emojiVariationPickerState:[null,function(){}],emojisThatFailedToLoadState:[new Set,function(){}],filterRef:{current:{}},isPastInitialLoad:!0,searchTerm:["",function(){return new Promise(function(){})}],skinToneFanOpenState:[!1,function(){}],suggestedUpdateState:[Date.now(),function(){}],reactionsModeState:[!1,function(){}]});function eZ(){return(0,L.useContext)(eG).filterRef}function eF(){return(0,L.useContext)(eG).disallowMouseRef}function eW(){return(0,L.useContext)(eG).reactionsModeState}function eH(){return(0,L.useContext)(eG).searchTerm}function eV(){return(0,L.useContext)(eG).activeSkinTone}function eX(){return(0,L.useContext)(eG).emojisThatFailedToLoadState}function eK(){return(0,L.useContext)(eG).emojiVariationPickerState}function eq(){return(0,L.useContext)(eG).skinToneFanOpenState}function e$(){var e=(0,L.useContext)(eG).suggestedUpdateState,f=e[0],t=e[1];return[f,function(){t(Date.now())}]}function e0(){return!!eH()[0]}function e1(e){e&&requestAnimationFrame(function(){e.focus()})}function e4(e){e&&e1(e.previousElementSibling)}function e2(e){e&&e1(e.nextElementSibling)}function e3(e){e&&e1(e.firstElementChild)}function e6(){return document.activeElement}function e9(e){var f=e.children,t=(0,L.useRef)(null),n=(0,L.useRef)(null),a=(0,L.useRef)(null),i=(0,L.useRef)(null),r=(0,L.useRef)(null),s=(0,L.useRef)(null),o=(0,L.useRef)(null),u=(0,L.useRef)(null);return(0,L.createElement)(e8.Provider,{value:{AnchoredEmojiRef:n,BodyRef:a,CategoryNavigationRef:s,PickerMainRef:t,SearchInputRef:i,SkinTonePickerRef:r,VariationPickerRef:o,ReactionsRef:u}},f)}var e8=(0,L.createContext)({AnchoredEmojiRef:(0,L.createRef)(),BodyRef:(0,L.createRef)(),CategoryNavigationRef:(0,L.createRef)(),PickerMainRef:(0,L.createRef)(),SearchInputRef:(0,L.createRef)(),SkinTonePickerRef:(0,L.createRef)(),VariationPickerRef:(0,L.createRef)(),ReactionsRef:(0,L.createRef)()});function e5(){return(0,L.useContext)(e8)}function e7(){return e5().PickerMainRef}function fe(){return e5().AnchoredEmojiRef}function ff(){var e=fe();return function(f){null===f&&null!==e.current&&e1(e.current),e.current=f}}function ft(){return e5().BodyRef}function fn(){return e5().SearchInputRef}function fa(){return e5().SkinTonePickerRef}function fi(){return e5().CategoryNavigationRef}function fr(e,f){void 0===f&&(f=0);var t=fU(e);t&&requestAnimationFrame(function(){t.scrollTop=f})}function fs(e){var f;if(!(!e||!((f=e)&&fG(f)<fB(f6(f)))||e.closest(R(n.variationPicker)))){var t,a,i,r=fZ(e),s=fG(e);t=r,a=-(fB(f6(e))-s),!(i=fU(t))||requestAnimationFrame(function(){i.scrollTop=i.scrollTop+a})}}function fo(e){var f=f4(e);e1(f),fs(f)}function fu(){var e=eK(),f=e[0],t=e[1],n=eq(),a=n[0],i=n[1];return(0,L.useCallback)(function(){f&&t(null),a&&i(!1)},[f,a,t,i])}function fc(){var e=eK()[0],f=eq()[0];return function(){return!!e||f}}function fd(){var e=eF();return function(){e.current=!1}}function fl(){var e=eF();return function(){return e.current}}function fh(){var e=fn();return(0,L.useCallback)(function(){e1(e.current)},[e])}function fg(){var e=fi();return(0,L.useCallback)(function(){e.current&&e3(e.current)},[e])}function fw(){var e=fp(),f=fn(),t=fh();return function(){f.current&&(f.current.value=""),e(""),t()}}function fp(){var e=eH()[1],f=e7();return function(t){requestAnimationFrame(function(){e(t?null==t?void 0:t.toLowerCase():t).then(function(){fr(f.current,0)})})}}function fM(e){return e&&"string"==typeof e?e.trim().toLowerCase():""}function fL(){var e=ff(),f=eK()[1];return function(t){var n=fY(t)[0];n&&(e(t),f(n))}}function fb(){return eQ()===u.SEARCH}function fm(){return eQ()===u.PREVIEW}function fy(){var e=fg(),f=e0(),t=ft();return(0,L.useCallback)(function(){return f?fo(t.current):e()},[t,e,f])}function fC(e){var f=e6();if(f)f.nextElementSibling||e(),e2(f)}function fj(){var e=e6();e&&e4(e)}function fI(){var e,f,t=(e=fn(),f=fp(),function(t){e.current?(e.current.value=""+e.current.value+t,f(fM(e.current.value))):f(fM(t))}),n=fh(),a=eP(),i=fu();return function(e){var f,r,s,o,u=e.key;if(r=(f=e).metaKey,s=f.ctrlKey,o=f.altKey,!r&&!s&&!o&&!a)u.match(/(^[a-zA-Z0-9]$){1}/)&&(e.preventDefault(),i(),n(),t(u))}}!function(e){e.ArrowDown="ArrowDown",e.ArrowUp="ArrowUp",e.ArrowLeft="ArrowLeft",e.ArrowRight="ArrowRight",e.Escape="Escape",e.Enter="Enter",e.Space=" "}(h||(h={}));var fv=new Set,fk=["width","height"];function fN(e){var f=e.children;return(0,L.createElement)(eU,null,(0,L.createElement)(fS,null,f))}function fS(e){var f,t,a,s,o,u,c,d,l,g,w,p,M,b,m,y,C,j,I,v,k,N,S,D,T,E,x,z,_,P,Y,R,J,B,U,G,Z,F,W,H,V,X,K,q,$,ee,ef,et,en,ea,ei,er,es,eo,eu=e.children,ed=eW()[0],eg=eS().theme,ew=e0(),ep=e7(),eM=eS().className,eL=(et=(ef=eS()).height,en=ef.width,ea=ef.style,O({height:eR(et),width:eR(en)},ea));a=e7(),s=fw(),o=(f=ft(),(0,L.useCallback)(function(e){requestAnimationFrame(function(){f.current&&(f.current.scrollTop=e)})},[f])),u=fn(),c=fh(),d=fc(),l=(t=eF(),function(){t.current=!0}),g=fu(),w=(0,L.useMemo)(function(){return function(e){var f=e.key;if(l(),f===h.Escape){if(e.preventDefault(),d()){g();return}s(),o(0),c()}}},[o,s,g,c,d,l]),(0,L.useEffect)(function(){var e=a.current;if(e)return e.addEventListener("keydown",w),function(){e.removeEventListener("keydown",w)}},[a,u,o,w]),M=(p=fa(),(0,L.useCallback)(function(){p.current&&e3(p.current)},[p])),b=e7(),m=ft(),y=fn(),C=eq()[1],j=fy(),I=fb(),v=(0,L.useMemo)(function(){return function(e){switch(e.key){case h.ArrowRight:if(!I)return;e.preventDefault(),C(!0),M();break;case h.ArrowDown:e.preventDefault(),j();break;case h.Enter:var f;e.preventDefault(),e1(f=f4(m.current)),null==f||f.click()}}},[M,j,C,m,I]),(0,L.useEffect)(function(){var e=y.current;if(e)return e.addEventListener("keydown",v),function(){e.removeEventListener("keydown",v)}},[b,y,v]),k=fa(),N=fh(),S=fn(),D=fy(),E=(T=eq())[0],x=T[1],z=fm(),_=fb(),P=fI(),Y=(0,L.useMemo)(function(){return function(e){var f=e.key;if(_)switch(f){case h.ArrowLeft:if(e.preventDefault(),!E)return N();fC(N);break;case h.ArrowRight:if(e.preventDefault(),!E)return N();fj();break;case h.ArrowDown:e.preventDefault(),E&&x(!1),D();break;default:P(e)}if(z)switch(f){case h.ArrowUp:if(e.preventDefault(),!E)return N();fC(N);break;case h.ArrowDown:if(e.preventDefault(),!E)return N();fj();break;default:P(e)}}},[E,N,x,D,P,z,_]),(0,L.useEffect)(function(){var e=k.current;if(e)return e.addEventListener("keydown",Y),function(){e.removeEventListener("keydown",Y)}},[k,S,E,Y]),R=fh(),J=fi(),B=ft(),U=fI(),G=(0,L.useMemo)(function(){return function(e){switch(e.key){case h.ArrowUp:e.preventDefault(),R();break;case h.ArrowRight:e.preventDefault(),e2(e6());break;case h.ArrowLeft:e.preventDefault(),e4(e6());break;case h.ArrowDown:e.preventDefault(),fo(B.current);break;default:U(e)}}},[B,R,U]),(0,L.useEffect)(function(){var e=J.current;if(e)return e.addEventListener("keydown",G),function(){e.removeEventListener("keydown",G)}},[J,B,G]),H=ft(),V=(Z=fh(),F=fg(),W=e0(),(0,L.useCallback)(function(){return W?Z():F()},[Z,W,F])),X=fL(),K=fc(),q=fu(),$=fI(),ee=(0,L.useMemo)(function(){return function(e){var f,t=e.key,n=fQ(e6());switch(t){case h.ArrowRight:e.preventDefault(),function(e){if(e){var f=function e(f){var t=f.nextElementSibling;return t?fK(t)?t:e(t):f4(f3(f))}(e);if(!f)return fo(f3(e));e1(f),fs(f)}}(n);break;case h.ArrowLeft:e.preventDefault(),function(e){if(e){var f=f1(e);if(!f)return void e1(f0(f2(e)));e1(f),fs(f)}}(n);break;case h.ArrowDown:if(e.preventDefault(),K()){q();break}!(f=n)||e1(function(e){if(!e)return null;var f=f9(e),t=f6(f),n=fx(f,e),a=fz(f,e),i=fE(f,e);if(!function(e,f){if(!e||!f)return!1;var t=f.getBoundingClientRect().height;return Math.round(f.getBoundingClientRect().top-e.getBoundingClientRect().top+t)<e.getBoundingClientRect().height}(f,e)){var r=f3(t);return r?f_(f$(r),0,i,n):null}return function(e,f,t,n){var a,i=(a=f+1)*t>e.length?[]:fA(e,a,t);return i[n]||i[i.length-1]||null}(f$(f),a,i,n)}(f));break;case h.ArrowUp:if(e.preventDefault(),K()){q();break}!function(e,f){if(e){var t=function(e){if(!e)return null;var f,t,n,a,i,r=f9(e),s=f6(r),o=fx(r,e),u=fz(r,e),c=fE(r,e);if(0===u){var d=f2(s);return d?f_(f$(d),-1,c,o):null}return f=f$(r),t=u,n=c,a=o,(i=fA(f,t-1,n))[a]||i[i.length-1]||null}(e);if(!t)return f();e1(t),fs(t)}}(n,V);break;case h.Space:e.preventDefault(),X(e.target);break;default:$(e)}}},[V,$,X,K,q]),(0,L.useEffect)(function(){var e=H.current;if(e)return e.addEventListener("keydown",ee),function(){e.removeEventListener("keydown",ee)}},[H,ee]),ei=ft(),er=eA(),es=eY(),(0,L.useEffect)(function(){if(er!==i.NATIVE){var e=ei.current;return null==e||e.addEventListener("focusin",f),function(){null==e||e.removeEventListener("focusin",f)}}function f(e){var f=fQ(e.target);if(f){var t=fY(f)[0];t&&eh(t)&&function(e,f,t){if(f&&t!==i.NATIVE){var n=ec(f);!fv.has(n)&&(el(f).forEach(function(f){var n;n=e(f,t),new Image().src=n}),fv.add(n))}}(es,t,er)}}},[ei,er,es]);var eb=eL||{},em=eb.width,ey=eb.height,eC=Q(eb,fk);return(0,L.createElement)("aside",{className:A(fT.main,fT.baseVariables,eg===r.DARK&&fT.darkTheme,eg===r.AUTO&&fT.autoThemeDark,((eo={})[n.searchActive]=ew,eo),ed&&fT.reactionsMenu,eM),ref:ep,style:O({},eC,!ed&&{height:ey,width:em})},eu)}var fD={"--epr-emoji-variation-picker-bg-color":"var(--epr-dark-emoji-variation-picker-bg-color)","--epr-hover-bg-color-reduced-opacity":"var(--epr-dark-hover-bg-color-reduced-opacity)","--epr-highlight-color":"var(--epr-dark-highlight-color)","--epr-text-color":"var(--epr-dark-text-color)","--epr-hover-bg-color":"var(--epr-dark-hover-bg-color)","--epr-focus-bg-color":"var(--epr-dark-focus-bg-color)","--epr-search-input-bg-color":"var(--epr-dark-search-input-bg-color)","--epr-category-label-bg-color":"var(--epr-dark-category-label-bg-color)","--epr-picker-border-color":"var(--epr-dark-picker-border-color)","--epr-bg-color":"var(--epr-dark-bg-color)","--epr-reactions-bg-color":"var(--epr-dark-reactions-bg-color)","--epr-search-input-bg-color-active":"var(--epr-dark-search-input-bg-color-active)","--epr-emoji-variation-indicator-color":"var(--epr-dark-emoji-variation-indicator-color)","--epr-category-icon-active-color":"var(--epr-dark-category-icon-active-color)","--epr-skin-tone-picker-menu-color":"var(--epr-dark-skin-tone-picker-menu-color)","--epr-skin-tone-outer-border-color":"var(--epr-dark-skin-tone-outer-border-color)","--epr-skin-tone-inner-border-color":"var(--epr-dark-skin-tone-inner-border-color)"},fT=J.create({main:{".":["epr-main",n.emojiPicker],position:"relative",display:"flex",flexDirection:"column",borderWidth:"1px",borderStyle:"solid",borderRadius:"var(--epr-picker-border-radius)",borderColor:"var(--epr-picker-border-color)",backgroundColor:"var(--epr-bg-color)",overflow:"hidden",transition:"all 0.3s ease-in-out, background-color 0.1s ease-in-out","*":{boxSizing:"border-box",fontFamily:"sans-serif"}},baseVariables:{"--":{"--epr-highlight-color":"#007aeb","--epr-hover-bg-color":"#e5f0fa","--epr-hover-bg-color-reduced-opacity":"#e5f0fa80","--epr-focus-bg-color":"#e0f0ff","--epr-text-color":"#858585","--epr-search-input-bg-color":"#f6f6f6","--epr-picker-border-color":"#e7e7e7","--epr-bg-color":"#fff","--epr-reactions-bg-color":"#ffffff90","--epr-category-icon-active-color":"#6aa8de","--epr-skin-tone-picker-menu-color":"#ffffff95","--epr-skin-tone-outer-border-color":"#555555","--epr-skin-tone-inner-border-color":"var(--epr-bg-color)","--epr-horizontal-padding":"10px","--epr-picker-border-radius":"8px","--epr-search-border-color":"var(--epr-highlight-color)","--epr-header-padding":"15px var(--epr-horizontal-padding)","--epr-active-skin-tone-indicator-border-color":"var(--epr-highlight-color)","--epr-active-skin-hover-color":"var(--epr-hover-bg-color)","--epr-search-input-bg-color-active":"var(--epr-search-input-bg-color)","--epr-search-input-padding":"0 30px","--epr-search-input-border-radius":"8px","--epr-search-input-height":"40px","--epr-search-input-text-color":"var(--epr-text-color)","--epr-search-input-placeholder-color":"var(--epr-text-color)","--epr-search-bar-inner-padding":"var(--epr-horizontal-padding)","--epr-category-navigation-button-size":"30px","--epr-emoji-variation-picker-height":"45px","--epr-emoji-variation-picker-bg-color":"var(--epr-bg-color)","--epr-preview-height":"70px","--epr-preview-text-size":"14px","--epr-preview-text-padding":"0 var(--epr-horizontal-padding)","--epr-preview-border-color":"var(--epr-picker-border-color)","--epr-preview-text-color":"var(--epr-text-color)","--epr-category-padding":"0 var(--epr-horizontal-padding)","--epr-category-label-bg-color":"#ffffffe6","--epr-category-label-text-color":"var(--epr-text-color)","--epr-category-label-padding":"0 var(--epr-horizontal-padding)","--epr-category-label-height":"40px","--epr-emoji-size":"30px","--epr-emoji-padding":"5px","--epr-emoji-fullsize":"calc(var(--epr-emoji-size) + var(--epr-emoji-padding) * 2)","--epr-emoji-hover-color":"var(--epr-hover-bg-color)","--epr-emoji-variation-indicator-color":"var(--epr-picker-border-color)","--epr-emoji-variation-indicator-color-hover":"var(--epr-text-color)","--epr-header-overlay-z-index":"3","--epr-emoji-variations-indictator-z-index":"1","--epr-category-label-z-index":"2","--epr-skin-variation-picker-z-index":"5","--epr-preview-z-index":"6","--epr-dark":"#000","--epr-dark-emoji-variation-picker-bg-color":"var(--epr-dark)","--epr-dark-highlight-color":"#c0c0c0","--epr-dark-text-color":"var(--epr-highlight-color)","--epr-dark-hover-bg-color":"#363636f6","--epr-dark-hover-bg-color-reduced-opacity":"#36363680","--epr-dark-focus-bg-color":"#474747","--epr-dark-search-input-bg-color":"#333333","--epr-dark-category-label-bg-color":"#222222e6","--epr-dark-picker-border-color":"#151617","--epr-dark-bg-color":"#222222","--epr-dark-reactions-bg-color":"#22222290","--epr-dark-search-input-bg-color-active":"var(--epr-dark)","--epr-dark-emoji-variation-indicator-color":"#444","--epr-dark-category-icon-active-color":"#3271b7","--epr-dark-skin-tone-picker-menu-color":"#22222295","--epr-dark-skin-tone-outer-border-color":"var(--epr-dark-picker-border-color)","--epr-dark-skin-tone-inner-border-color":"#FFFFFF"}},autoThemeDark:{".":n.autoTheme,"@media (prefers-color-scheme: dark)":{"--":fD}},darkTheme:{".":n.darkTheme,"--":fD},reactionsMenu:{".":"epr-reactions",height:"50px",display:"inline-flex",backgroundColor:"var(--epr-reactions-bg-color)",backdropFilter:"blur(8px)","--":{"--epr-picker-border-radius":"50px"}}});function fE(e,f){return e&&f?Math.floor(e.getBoundingClientRect().width/f.getBoundingClientRect().width):0}function fx(e,f){if(!e||!f)return 0;var t=f.getBoundingClientRect().width;return Math.floor((f.getBoundingClientRect().left-e.getBoundingClientRect().left)/t)}function fz(e,f){if(!e||!f)return 0;var t=f.getBoundingClientRect().height;return Math.round((f.getBoundingClientRect().top-e.getBoundingClientRect().top)/t)}function fA(e,f,t){if(-1===f){var n=Math.floor((e.length-1)/t),a=e.length-1;return e.slice(n*t,a+1)}return e.slice(f*t,(f+1)*t)}function f_(e,f,t,n){var a=fA(e,f,t);return a[n]||a[a.length-1]||null}var fO="button"+R(n.emoji),fP=[fO,R(n.visible),":not("+R(n.hidden)+")"].join("");function fQ(e){var f;return null!=(f=null==e?void 0:e.closest(fO))?f:null}function fY(e){var f=fV(e),t=fH(e);if(!f)return[];var n=eg(null!=t?t:f);return n?[n,t]:[]}function fR(e){var f;return null!=(f=null==e?void 0:e.clientHeight)?f:0}function fJ(e){if(!e)return 0;var f=fQ(e),t=f6(f),n=fB(t);return fF(f)+fF(t)+n}function fB(e){if(!e)return 0;var f,t,a=e.querySelector(R(n.categoryContent));return(null!=(f=null==e?void 0:e.clientHeight)?f:0)-(null!=(t=null==a?void 0:a.clientHeight)?t:0)}function fU(e){return e?e.matches(R(n.scrollBody))?e:e.querySelector(R(n.scrollBody)):null}function fG(e){var f,t;return e?fJ(e)-(null!=(f=null==(t=fZ(e))?void 0:t.scrollTop)?f:0):0}function fZ(e){var f;return e&&null!=(f=e.closest(R(n.scrollBody)))?f:null}function fF(e){var f;return null!=(f=null==e?void 0:e.offsetTop)?f:0}function fW(e){var f;return null!=(f=null==e?void 0:e.offsetLeft)?f:0}function fH(e){var f,t,n,a;return null!=(f=null!=(t=(null!=(a=null==(n=fQ(e))?void 0:n.dataset)?a:{}).unified)?t:null)?f:null}function fV(e){var f=fH(e);return f?eu(f):null}function fX(e){return e?{unified:fH(e),originalUnified:fV(e)}:{unified:null,originalUnified:null}}function fK(e){return e.classList.contains(n.visible)}function fq(e){return!e||e.classList.contains(n.hidden)}function f$(e){return e?Array.from(e.querySelectorAll(fP)):[]}function f0(e){if(!e)return null;var f=f$(e).slice(-1)[0];return f?fK(f)?f:f1(f):null}function f1(e){var f=e.previousElementSibling;return f?fK(f)?f:f1(f):f0(f2(e))}function f4(e){if(!e)return null;var f=f$(e);return function(e,f,t){if(void 0===t&&(t=0),!e||!f.length)return null;var a=e.getBoundingClientRect().top,i=e.getBoundingClientRect().bottom,r=a+function(e){for(var f=Array.from(e.querySelectorAll(R(n.label))),t=0;t<f.length;t++){var a=f[t].getBoundingClientRect().height;if(a>0)return a}return 40}(e);return f.find(function(e){var f=e.getBoundingClientRect().top,n=e.getBoundingClientRect().bottom,s=e.clientHeight*t,o=f+s,u=n-s;return!(o<r)&&(o>=a&&o<=i||u>=a&&u<=i)})||null}(e,f,.1)}function f2(e){var f=f6(e);if(!f)return null;var t=f.previousElementSibling;return t?fq(t)?f2(t):t:null}function f3(e){var f=f6(e);if(!f)return null;var t=f.nextElementSibling;return t?fq(t)?f3(t):t:null}function f6(e){return e?e.closest(R(n.category)):null}function f9(e){return e?e.closest(R(n.categoryContent)):null}function f8(e){return e.split("-").map(function(e){return String.fromCodePoint(parseInt(e,16))}).join("")}var f5="epr_suggested";function f7(e){try{if(!(null!=(f=window)&&f.localStorage))return[];var f,t,n,i=JSON.parse(null!=(t=null==(n=window)?void 0:n.localStorage.getItem(f5))?t:"[]");if(e===a.FREQUENT)return i.sort(function(e,f){return f.count-e.count});return i}catch(e){return[]}}function te(e){return void 0!==e.imgUrl}function tf(e,f){var t,n,a=(0,L.useRef)(),r=fL(),s=(0,L.useContext)(eG).disallowClickRef,o=eK()[1],u=fu(),c=eV()[0],d=(n=eT().current,(null!=(t=f===l.REACTIONS?n.onReactionClick:n.onEmojiClick)?t:n.onEmojiClick)||function(){}),h=e$()[1],g=eY(),w=eA(),p=(0,L.useCallback)(function(e){if(!s.current){u();var f,t=tt(e),n=t[0],a=t[1];if(n&&a){var r=(f=a.split("-")[1],(et.includes(f)?f:null)||c);h(),function(e,f){var t,n,a=f7(),i=ec(e,f),r=ec(e),s=a.find(function(e){return e.unified===i});t=s?[s].concat(a.filter(function(e){return e!==s})):[s={unified:i,original:r,count:0}].concat(a),s.count++,t.length=Math.min(t.length,14);try{null==(n=window)||n.localStorage.setItem(f5,JSON.stringify(t))}catch(e){}}(n,r),d(function(e,f,t,n){var a=es(e);if(te(e)){var r=ec(e);return{activeSkinTone:f,emoji:r,getImageUrl:function(){return e.imgUrl},imageUrl:e.imgUrl,isCustom:!0,names:a,unified:r,unifiedWithoutSkinTone:r}}var s=ec(e,f);return{activeSkinTone:f,emoji:f8(s),getImageUrl:function(e){return void 0===e&&(e=null!=t?t:i.APPLE),n(s,e)},imageUrl:n(s,null!=t?t:i.APPLE),isCustom:!1,names:a,unified:s,unifiedWithoutSkinTone:ec(e)}}(n,r,w,g),e)}}},[c,u,s,d,h,g,w]),M=(0,L.useCallback)(function(e){a.current&&clearTimeout(a.current);var f,t=tt(e)[0];t&&eh(t)&&(a.current=null==(f=window)?void 0:f.setTimeout(function(){s.current=!0,a.current=void 0,u(),r(e.target),o(t)},500))},[s,u,r,o]),b=(0,L.useCallback)(function(){a.current?(clearTimeout(a.current),a.current=void 0):s.current&&requestAnimationFrame(function(){s.current=!1})},[s]);(0,L.useEffect)(function(){if(e.current){var f=e.current;return f.addEventListener("click",p,{passive:!0}),f.addEventListener("mousedown",M,{passive:!0}),f.addEventListener("mouseup",b,{passive:!0}),function(){null==f||f.removeEventListener("click",p),null==f||f.removeEventListener("mousedown",M),null==f||f.removeEventListener("mouseup",b)}}},[e,p,M,b])}function tt(e){var f,t=null==e?void 0:e.target;return(null==t?void 0:t.matches(fO))||(null==t?void 0:null==(f=t.parentElement)?void 0:f.matches(fO))?fY(t):[]}function tn(e){return(0,L.createElement)("button",Object.assign({type:"button"},e,{className:A(ta.button,e.className)}),e.children)}var ta=J.create({button:{".":"epr-btn",cursor:"pointer",border:"0",background:"none",outline:"none"}});function ti(e){var f,t,a,i=e.emojiNames,r=e.unified,s=e.hidden,o=e.hiddenOnSearch,u=e.showVariations,c=e.hasVariations,d=e.children,l=e.className,h=e.noBackground;return(0,L.createElement)(tn,{className:A(tr.emoji,s&&U.hidden,o&&Z.hiddenOnSearch,((a={})[n.visible]=!s&&!o,a),!!(c&&(void 0===u||u))&&tr.hasVariations,void 0!==h&&h&&tr.noBackground,l),"data-unified":r,"aria-label":(f=i)[0].match("flag-")&&null!=(t=f[1])?t:f[0],"data-full-name":i},d)}var tr=J.create({emoji:{".":n.emoji,position:"relative",width:"var(--epr-emoji-fullsize)",height:"var(--epr-emoji-fullsize)",boxSizing:"border-box",display:"flex",alignItems:"center",justifyContent:"center",maxWidth:"var(--epr-emoji-fullsize)",maxHeight:"var(--epr-emoji-fullsize)",borderRadius:"8px",overflow:"hidden",transition:"background-color 0.2s",":hover":{backgroundColor:"var(--epr-emoji-hover-color)"},":focus":{backgroundColor:"var(--epr-focus-bg-color)"}},noBackground:{background:"none",":hover":{backgroundColor:"transparent",background:"none"},":focus":{backgroundColor:"transparent",background:"none"}},hasVariations:{".":n.emojiHasVariations,":after":{content:"",display:"block",width:"0",height:"0",right:"0px",bottom:"1px",position:"absolute",borderLeft:"4px solid transparent",borderRight:"4px solid transparent",transform:"rotate(135deg)",borderBottom:"4px solid var(--epr-emoji-variation-indicator-color)",zIndex:"var(--epr-emoji-variations-indictator-z-index)"},":hover:after":{borderBottom:"4px solid var(--epr-emoji-variation-indicator-color-hover)"}}}),ts=J.create({external:{".":n.external,fontSize:"0"},common:{alignSelf:"center",justifySelf:"center",display:"block"}});function to(e){var f=e.emojiName,t=e.style,n=e.lazyLoad,a=e.imgUrl,i=e.onError,r=e.className;return(0,L.createElement)("img",{src:a,alt:f,className:A(tu.emojiImag,ts.external,ts.common,r),loading:void 0!==n&&n?"lazy":"eager",onError:i,style:t})}var tu=J.create({emojiImag:{".":"epr-emoji-img",maxWidth:"var(--epr-emoji-fullsize)",maxHeight:"var(--epr-emoji-fullsize)",minWidth:"var(--epr-emoji-fullsize)",minHeight:"var(--epr-emoji-fullsize)",padding:"var(--epr-emoji-padding)"}});function tc(e){var f=e.unified,t=e.style,n=e.className;return(0,L.createElement)("span",{className:A(td.nativeEmoji,ts.common,ts.external,n),"data-unified":f,style:t},f8(f))}var td=J.create({nativeEmoji:{".":"epr-emoji-native",fontFamily:'"Segoe UI Emoji", "Segoe UI Symbol", "Segoe UI", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "EmojiOne Color", "Android Emoji"!important',position:"relative",lineHeight:"100%",fontSize:"var(--epr-emoji-size)",textAlign:"center",alignSelf:"center",justifySelf:"center",letterSpacing:"0",padding:"var(--epr-emoji-padding)"}});function tl(e){var f=e.emoji,t=e.unified,n=e.emojiStyle,a=e.size,r=e.lazyLoad,s=e.getEmojiUrl,o=e.className,u=eX()[1],c={};a&&(c.width=c.height=c.fontSize=a+"px");var d=f||eg(t);if(!d)return null;if(te(d))return(0,L.createElement)(to,{style:c,emojiName:t,emojiStyle:i.NATIVE,lazyLoad:r,imgUrl:d.imgUrl,onError:l,className:o});return(0,L.createElement)(L.Fragment,null,n===i.NATIVE?(0,L.createElement)(tc,{unified:t,style:c,className:o}):(0,L.createElement)(to,{style:c,emojiName:eo(d),emojiStyle:n,lazyLoad:r,imgUrl:(void 0===s?ed:s)(t,n),onError:l,className:o}));function l(){u(function(e){return new Set(e).add(t)})}}function th(e){var f=e.emoji,t=e.unified,n=e.hidden,a=e.hiddenOnSearch,i=e.emojiStyle,r=e.showVariations,s=e.size,o=e.lazyLoad,u=e.getEmojiUrl,c=e.className,d=e.noBackground,l=eh(f);return(0,L.createElement)(ti,{hasVariations:l,showVariations:void 0===r||r,hidden:n,hiddenOnSearch:a,emojiNames:es(f),unified:t,noBackground:void 0!==d&&d},(0,L.createElement)(tl,{unified:t,emoji:f,size:s,emojiStyle:i,lazyLoad:o,getEmojiUrl:u,className:c}))}function tg(){var e=eW()[1];return(0,L.createElement)(tn,{"aria-label":"Show all Emojis",title:"Show all Emojis",tabIndex:0,className:A(tw.plusSign),onClick:function(){return e(!1)}})}var tw=J.create(O({plusSign:{fontSize:"20px",padding:"17px",color:"var(--epr-text-color)",borderRadius:"50%",textAlign:"center",lineHeight:"100%",width:"20px",height:"20px",display:"flex",justifyContent:"center",alignItems:"center",transition:"background-color 0.2s ease-in-out",":after":{content:"",minWidth:"20px",minHeight:"20px",backgroundImage:"url(data:image/svg+xml;base64,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)",backgroundColor:"transparent",backgroundRepeat:"no-repeat",backgroundSize:"20px",backgroundPositionY:"0"},":hover":{color:"var(--epr-highlight-color)",backgroundColor:"var(--epr-hover-bg-color-reduced-opacity)",":after":{backgroundPositionY:"-20px"}},":focus":{color:"var(--epr-highlight-color)",backgroundColor:"var(--epr-hover-bg-color-reduced-opacity)",":after":{backgroundPositionY:"-40px"}}}},F("plusSign",{":after":{backgroundPositionY:"-40px"},":hover:after":{backgroundPositionY:"-60px"}})));function tp(){var e=eW()[0],f=e5().ReactionsRef,t=eS().reactions;tf(f,l.REACTIONS);var n=eA(),a=ex(),i=eY();return e?(0,L.createElement)("ul",{className:A(tM.list,!e&&U.hidden),ref:f},t.map(function(e){return(0,L.createElement)("li",{key:e},(0,L.createElement)(th,{emoji:eg(e),emojiStyle:n,unified:e,showVariations:!1,className:A(tM.emojiButton),noBackground:!0,getEmojiUrl:i}))}),a?(0,L.createElement)("li",null,(0,L.createElement)(tg,null)):null):null}var tM=J.create({list:{listStyle:"none",margin:"0",padding:"0 5px",display:"flex",justifyContent:"space-between",alignItems:"center",height:"100%"},emojiButton:{":hover":{transform:"scale(1.2)"},":focus":{transform:"scale(1.2)"},":active":{transform:"scale(1.1)"},transition:"transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.5)"}});function tL(e){var f=e.categoryConfig,t=e.children,n=e.hidden,a=e.hiddenOnSearch,i=$(f),r=f.name;return(0,L.createElement)("li",{className:A(tb.category,n&&U.hidden,a&&Z.hiddenOnSearch),"data-name":i,"aria-label":r},(0,L.createElement)("h2",{className:A(tb.label)},r),(0,L.createElement)("div",{className:A(tb.categoryContent)},t))}var tb=J.create({category:{".":n.category,":not(:has(.epr-visible))":{display:"none"}},categoryContent:{".":n.categoryContent,display:"grid",gridGap:"0",gridTemplateColumns:"repeat(auto-fill, var(--epr-emoji-fullsize))",justifyContent:"space-between",margin:"var(--epr-category-padding)",position:"relative"},label:{".":n.label,alignItems:"center",backdropFilter:"blur(3px)",backgroundColor:"var(--epr-category-label-bg-color)",color:"var(--epr-category-label-text-color)",display:"flex",fontSize:"16px",fontWeight:"bold",height:"var(--epr-category-label-height)",margin:"0",padding:"var(--epr-category-label-padding)",position:"sticky",textTransform:"capitalize",top:"0",width:"100%",zIndex:"var(--epr-category-label-z-index)"}}),tm=!1;function ty(e){var f,t,n,a=e.categoryConfig,i=e$()[0],r=(t=(f=(0,L.useState)(tm))[0],n=f[1],(0,L.useEffect)(function(){n(!0),tm=!0},[]),t||tm),s=eS().suggestedEmojisMode,o=eY(),u=(0,L.useMemo)(function(){var e;return null!=(e=f7(s))?e:[]},[i,s]),c=eA();return r?(0,L.createElement)(tL,{categoryConfig:a,hiddenOnSearch:!0,hidden:0===u.length},u.map(function(e){var f=eg(e.original);return f?(0,L.createElement)(th,{showVariations:!1,unified:e.unified,emojiStyle:c,emoji:f,key:e.unified,getEmojiUrl:o}):null})):null}function tC(){var e=e_(),f=(0,L.useRef)(0);return(0,L.createElement)("ul",{className:A(tI.emojiList)},e.map(function(e){var t=$(e);return t===o.SUGGESTED?(0,L.createElement)(ty,{key:t,categoryConfig:e}):(0,L.createElement)(L.Suspense,{key:t},(0,L.createElement)(tj,{category:t,categoryConfig:e,renderdCategoriesCountRef:f}))}))}function tj(e){var f,t,n,a,i,r,s,o=e.category,u=e.categoryConfig,c=e.renderdCategoriesCountRef,d=(f=eX()[0],t=eZ().current,n=eH()[0],a=function(e){var f,a,i,r;return f=e,a=t,i=n,!!a&&!!i&&!(null!=(r=a[i])&&r[f])},function(e){var t=ec(e),n=f.has(t),i=a(t);return{failedToLoad:n,filteredOut:i,hidden:n||i}}),l=eS().lazyLoadEmojis,h=eA(),g=(0,L.useContext)(eG).isPastInitialLoad,w=eV()[0],p=(i=eB(),r=eS().unicodeToHide,function(e){var f,t=eu(ec(e));return!!(i[t]||(f=t,r.has(f)))}),M=eY(),b=!ez(),m=!g&&c.current>0?[]:null!=(s=null==ef?void 0:ef[o])?s:[];m.length>0&&c.current++;var y=0,C=m.map(function(e){var f=ec(e,w),t=d(e),n=t.failedToLoad,a=t.filteredOut,i=t.hidden,r=p(e);return((i||r)&&y++,r)?null:(0,L.createElement)(th,{showVariations:b,key:f,emoji:e,unified:f,hidden:n,hiddenOnSearch:a,emojiStyle:h,lazyLoad:l,getEmojiUrl:M})});return(0,L.createElement)(tL,{categoryConfig:u,hidden:y===C.length},C)}var tI=J.create({emojiList:{".":n.emojiList,listStyle:"none",margin:"0",padding:"0"}});function tv(){var e,f,t,a,i,r,s,o,u=fe(),c=e5().VariationPickerRef,d=eK()[0],l=eA(),h=(e=c,f=fe(),t=ft(),a=g.Up,{getMenuDirection:function(){return a},getTop:function(){a=g.Up;var n=0;if(!e.current)return 0;var i=fR(e.current);if(f.current){var r,s=t.current,o=fQ(f.current),u=fR(o);n=fJ(o),(null!=(r=null==s?void 0:s.scrollTop)?r:0)>n-i&&(a=g.Down,n+=u+i)}return n-i}}),w=h.getTop,p=h.getMenuDirection,M=ff(),b=(i=c,r=fe(),function(){var e={};if(!i.current)return e;if(r.current){var f,t,n=fQ(r.current),a=(t=f6(f=fQ(n)),fW(f)+fW(t));if(!n)return e;e.left=a+(null==n?void 0:n.clientWidth)/2}return e}),m=eY(),y=fQ(u.current),C=!!(d&&y&&eh(d)&&y.classList.contains(n.emojiHasVariations));return(0,L.useEffect)(function(){C&&fo(c.current)},[c,C,u]),!C&&u.current?M(null):(s=w(),o=b()),(0,L.createElement)("div",{ref:c,className:A(tk.variationPicker,p()===g.Down&&tk.pointingUp,C&&tk.visible),style:{top:s}},C&&d?[ec(d)].concat(el(d)).slice(0,6).map(function(e){return(0,L.createElement)(th,{key:e,emoji:d,unified:e,emojiStyle:l,showVariations:!1,getEmojiUrl:m})}):null,(0,L.createElement)("div",{className:A(tk.pointer),style:o}))}!function(e){e[e.Up=0]="Up",e[e.Down=1]="Down"}(g||(g={}));var tk=J.create(O({variationPicker:{".":n.variationPicker,position:"absolute",right:"15px",left:"15px",padding:"5px",boxShadow:"0px 2px 5px rgba(0, 0, 0, 0.2)",borderRadius:"3px",display:"flex",alignItems:"center",justifyContent:"space-around",opacity:"0",visibility:"hidden",pointerEvents:"none",top:"-100%",border:"1px solid var(--epr-picker-border-color)",height:"var(--epr-emoji-variation-picker-height)",zIndex:"var(--epr-skin-variation-picker-z-index)",background:"var(--epr-emoji-variation-picker-bg-color)",transform:"scale(0.9)",transition:"transform 0.1s ease-out, opacity 0.2s ease-out"},visible:{opacity:"1",visibility:"visible",pointerEvents:"all",transform:"scale(1)"},pointingUp:{".":"pointing-up",transformOrigin:"center 0%",transform:"scale(0.9)"},".pointing-up":{pointer:{top:"0",transform:"rotate(180deg) translateY(100%) translateX(18px)"}},pointer:{".":"epr-emoji-pointer",content:"",position:"absolute",width:"25px",height:"15px",backgroundRepeat:"no-repeat",backgroundPosition:"0 0",backgroundSize:"50px 15px",top:"100%",transform:"translateX(-18px)",backgroundImage:"url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI2LjMuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI1MHB4IgoJIGhlaWdodD0iMTVweCIgdmlld0JveD0iMCAwIDUwIDE1IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MCAxNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnIGlkPSJMYXllcl8xIj4KPC9nPgo8ZyBpZD0iTGF5ZXJfMiI+Cgk8cGF0aCBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiNFOEU3RTciIHN0cm9rZS1taXRlcmxpbWl0PSIxMCIgZD0iTTEuODYtMC40M2w5LjgzLDExLjUzYzAuNTksMC42OSwxLjU2LDAuNjksMi4xNCwwbDkuODMtMTEuNTMiLz4KCTxwYXRoIGZpbGw9IiMwMTAyMDIiIHN0cm9rZT0iIzE1MTYxNyIgc3Ryb2tlLW1pdGVybGltaXQ9IjEwIiBkPSJNMjYuODYtMC40M2w5LjgzLDExLjUzYzAuNTksMC42OSwxLjU2LDAuNjksMi4xNCwwbDkuODMtMTEuNTMiLz4KPC9nPgo8L3N2Zz4=)"}},F("pointer",{backgroundPosition:"-25px 0"})));function tN(){var e,f,t,n,a=ft();return e=fu(),(0,L.useEffect)(function(){var f=a.current;if(f)return f.addEventListener("scroll",t,{passive:!0}),function(){null==f||f.removeEventListener("scroll",t)};function t(){e()}},[a,e]),tf(a,l.PICKER),f=ft(),t=fd(),n=fl(),(0,L.useEffect)(function(){var e=f.current;function a(){n()&&t()}return null==e||e.addEventListener("mousemove",a,{passive:!0}),function(){null==e||e.removeEventListener("mousemove",a)}},[f,t,n]),(0,L.createElement)("div",{className:A(tS.body,Z.hiddenOnReactions),ref:a},(0,L.createElement)(tv,null),(0,L.createElement)(tC,null))}var tS=J.create({body:{".":n.scrollBody,flex:"1",overflowY:"scroll",overflowX:"hidden",position:"relative"}});function tD(e){var f=e.children,t=e.className,n=e.style,a=e.direction,i=void 0===a?p.ROW:a;return(0,L.createElement)("div",{style:O({},void 0===n?{}:n),className:A(tT.flex,t,tT[i])},f)}!function(e){e.ROW="FlexRow",e.COLUMN="FlexColumn"}(p||(p={}));var tT=J.create(((w={flex:{display:"flex"}})[p.ROW]={flexDirection:"row"},w[p.COLUMN]={flexDirection:"column"},w));function tE(e){var f=e.className,t=e.style;return(0,L.createElement)("div",{style:O({flex:1},void 0===t?{}:t),className:A(f)})}function tx(e){var f=e.children,t=e.className,n=e.style;return(0,L.createElement)("div",{style:O({},n,{position:"absolute"}),className:t},f)}function tz(e){var f=e.children,t=e.className,n=e.style;return(0,L.createElement)("div",{style:O({},n,{position:"relative"}),className:t},f)}function tA(e){var f=e.isOpen,t=e.onClick,n=e.isActive,a=e.skinToneVariation,i=e.style;return(0,L.createElement)(tn,{style:i,onClick:t,className:A("epr-tone-"+a,t_.tone,!f&&t_.closedTone,n&&t_.active),"aria-pressed":n,"aria-label":"Skin tone "+en[a]})}var t_=J.create({closedTone:{opacity:"0",zIndex:"0"},active:{".":"epr-active",zIndex:"1",opacity:"1"},tone:{".":"epr-tone",width:"var(--epr-skin-tone-size)",display:"block",cursor:"pointer",borderRadius:"4px",height:"var(--epr-skin-tone-size)",position:"absolute",right:"0",transition:"transform 0.3s ease-in-out, opacity 0.35s ease-in-out",zIndex:"0",border:"1px solid var(--epr-skin-tone-outer-border-color)",boxShadow:"inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)",":hover":{boxShadow:"0 0 0 3px var(--epr-active-skin-hover-color), inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)"},":focus":{boxShadow:"0 0 0 3px var(--epr-focus-bg-color)"},"&.epr-tone-neutral":{backgroundColor:"#ffd225"},"&.epr-tone-1f3fb":{backgroundColor:"#ffdfbd"},"&.epr-tone-1f3fc":{backgroundColor:"#e9c197"},"&.epr-tone-1f3fd":{backgroundColor:"#c88e62"},"&.epr-tone-1f3fe":{backgroundColor:"#a86637"},"&.epr-tone-1f3ff":{backgroundColor:"#60463a"}}});function tO(){return(0,L.createElement)(tz,{style:{height:28}},(0,L.createElement)(tx,{style:{bottom:0,right:0}},(0,L.createElement)(tP,{direction:M.VERTICAL})))}function tP(e){var f=e.direction,t=void 0===f?M.HORIZONTAL:f,n=fa(),a=ez(),i=eq(),r=i[0],s=i[1],o=eV(),u=o[0],c=o[1],d=eT().current.onSkinToneChange||function(){},l=fu(),h=fh();if(a)return null;var g=28*et.length+"px",w=r?g:"28px",p=t===M.VERTICAL;return(0,L.createElement)(tz,{className:A(tQ.skinTones,p&&tQ.vertical,r&&tQ.open,p&&r&&tQ.verticalShadow),style:p?{flexBasis:w,height:w}:{flexBasis:w}},(0,L.createElement)("div",{className:A(tQ.select),ref:n},et.map(function(e,f){var t=e===u;return(0,L.createElement)(tA,{key:e,skinToneVariation:e,isOpen:r,style:{transform:A(p?"translateY(-"+28*!!r*f+"px)":"translateX(-"+28*!!r*f+"px)",r&&t&&"scale(1.3)")},isActive:t,onClick:function(){r?(c(e),d(e),h()):s(!0),l()}})})))}!function(e){e.VERTICAL="epr-vertical",e.HORIZONTAL="epr-horizontal"}(M||(M={}));var tQ=J.create({skinTones:{".":"epr-skin-tones","--":{"--epr-skin-tone-size":"15px"},display:"flex",alignItems:"center",justifyContent:"flex-end",transition:"all 0.3s ease-in-out",padding:"10px 0"},vertical:{padding:"9px",alignItems:"flex-end",flexDirection:"column",borderRadius:"6px",border:"1px solid var(--epr-bg-color)"},verticalShadow:{boxShadow:"0px 0 7px var(--epr-picker-border-color)"},open:{backdropFilter:"blur(5px)",background:"var(--epr-skin-tone-picker-menu-color)",".epr-active":{border:"1px solid var(--epr-active-skin-tone-indicator-border-color)"}},select:{".":"epr-skin-tone-select",position:"relative",width:"var(--epr-skin-tone-size)",height:"var(--epr-skin-tone-size)"}});function tY(){var e=eO(),f=fm();return e.showPreview?(0,L.createElement)(tD,{className:A(tJ.preview,Z.hiddenOnReactions)},(0,L.createElement)(tR,null),(0,L.createElement)(tE,null),f?(0,L.createElement)(tO,null):null):null}function tR(){var e,f,t,n,a,i=eO(),r=(0,L.useState)(null),s=r[0],o=r[1],u=eA(),c=eK()[0],d=eY();e=i.showPreview,f=ft(),t=fl(),n=fd(),(0,L.useEffect)(function(){if(e){var n=f.current;return null==n||n.addEventListener("keydown",r,{passive:!0}),null==n||n.addEventListener("mouseover",s,!0),null==n||n.addEventListener("focus",a,!0),null==n||n.addEventListener("mouseout",i,{passive:!0}),null==n||n.addEventListener("blur",i,!0),function(){null==n||n.removeEventListener("mouseover",s),null==n||n.removeEventListener("mouseout",i),null==n||n.removeEventListener("focus",a,!0),null==n||n.removeEventListener("blur",i,!0),null==n||n.removeEventListener("keydown",r)}}function a(e){var f=fQ(e.target);if(!f)return i();var t=fX(f),n=t.unified,a=t.originalUnified;if(!n||!a)return i();o({unified:n,originalUnified:a})}function i(e){if(e&&!fQ(e.relatedTarget))return o(null);o(null)}function r(e){"Escape"===e.key&&o(null)}function s(e){if(!t()){var f,a,i,r,s,u,c=fQ(e.target);if(c){if(function(e,f){if(!e||!f)return 0;var t=e.getBoundingClientRect(),n=f.getBoundingClientRect();return n.height-(t.y-n.y)}(c,n)<c.getBoundingClientRect().height){return f=c,a=o,s=(r=fX(f)).unified,u=r.originalUnified,void(s&&u&&(null==(i=document.activeElement)||null==i.blur||i.blur(),a({unified:s,originalUnified:u})))}e1(c)}}}},[f,e,o,t,n]);var l=eg(null!=(a=null==s?void 0:s.unified)?a:null==s?void 0:s.originalUnified),h=null!=l&&null!=s;return(0,L.createElement)(function(){var e=null!=c?c:eg(i.defaultEmoji);if(!e)return null;var f=c?eo(c):i.defaultCaption;return(0,L.createElement)(L.Fragment,null,(0,L.createElement)("div",null,h?(0,L.createElement)(tl,{unified:null==s?void 0:s.unified,emoji:l,emojiStyle:u,size:45,getEmojiUrl:d,className:A(tJ.emoji)}):e?(0,L.createElement)(tl,{unified:ec(e),emoji:e,emojiStyle:u,size:45,getEmojiUrl:d,className:A(tJ.emoji)}):null),(0,L.createElement)("div",{className:A(tJ.label)},h?eo(l):f))},null)}var tJ=J.create({preview:{alignItems:"center",borderTop:"1px solid var(--epr-preview-border-color)",height:"var(--epr-preview-height)",padding:"0 var(--epr-horizontal-padding)",position:"relative",zIndex:"var(--epr-preview-z-index)"},label:{color:"var(--epr-preview-text-color)",fontSize:"var(--epr-preview-text-size)",padding:"var(--epr-preview-text-padding)",textTransform:"capitalize"},emoji:{padding:"0"}});function tB(e){var f,t=e.isActiveCategory,a=e.category,i=e.allowNavigation,r=e.categoryConfig,s=e.onClick;return(0,L.createElement)(tn,{tabIndex:i?0:-1,className:A(tZ.catBtn,Z.categoryBtn,"epr-icn-"+a,((f={})[n.active]=t,f)),onClick:s,"aria-label":r.name,"aria-selected":t,role:"tab","aria-controls":"epr-category-nav-id"})}var tU={backgroundPositionY:"calc(var(--epr-category-navigation-button-size) * 3)"},tG={":not(.epr-search-active)":{catBtn:{":hover":tU,"&.epr-active":tU}}},tZ=J.create(O({catBtn:{".":"epr-cat-btn",display:"inline-block",transition:"opacity 0.2s ease-in-out",position:"relative",height:"var(--epr-category-navigation-button-size)",width:"var(--epr-category-navigation-button-size)",backgroundSize:"calc(var(--epr-category-navigation-button-size) * 10)",outline:"none",backgroundPosition:"0 0",backgroundImage:"url(data:image/svg+xml;base64,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)",":focus:before":{content:"",position:"absolute",top:"-2px",left:"-2px",right:"-2px",bottom:"-2px",border:"2px solid var(--epr-category-icon-active-color)",borderRadius:"50%"},"&.epr-icn-suggested":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -8)"},"&.epr-icn-custom":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -9)"},"&.epr-icn-activities":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -4)"},"&.epr-icn-animals_nature":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -1)"},"&.epr-icn-flags":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -7)"},"&.epr-icn-food_drink":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -2)"},"&.epr-icn-objects":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -5)"},"&.epr-icn-smileys_people":{backgroundPositionX:"0px"},"&.epr-icn-symbols":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -6)"},"&.epr-icn-travel_places":{backgroundPositionX:"calc(var(--epr-category-navigation-button-size) * -3)"}}},F("catBtn",{backgroundPositionY:"calc(var(--epr-category-navigation-button-size) * 2)"}),{".epr-dark-theme":O({},tG),".epr-auto-theme":O({},tG)}));function tF(){var e,f,t,a,i=(0,L.useState)(null),r=i[0],s=i[1],u=(e=ft(),f=e7(),function(t){if(e.current){var n,a=null==(n=e.current)?void 0:n.querySelector('[data-name="'+t+'"]');if(a){var i=a.offsetTop||0;fr(f.current,i)}}});t=ft(),(0,L.useEffect)(function(){var e=new Map,f=t.current,a=new IntersectionObserver(function(t){if(f){for(var n,a=function(e,f){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(t)return(t=t.call(e)).next.bind(t);if(Array.isArray(e)||(t=function(e,f){if(e){if("string"==typeof e)return Y(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Y(e,void 0)}}(e))){t&&(e=t);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(t);!(n=a()).done;){var i=n.value,r=function(e){var f;return null!=(f=null==e?void 0:e.getAttribute("data-name"))?f:null}(i.target);e.set(r,i.intersectionRatio)}var o=Array.from(e),u=o[o.length-1];if(1==u[1])return s(u[0]);for(var c=0;c<o.length;c++){var d=o[c],l=d[0];if(d[1]){s(l);break}}}},{threshold:[0,1]});null==f||f.querySelectorAll(R(n.category)).forEach(function(e){a.observe(e)})},[t,s]);var c=e0(),d=e_(),l=fi(),h=!!(a=eS().customEmojis)&&0===a.length;return(0,L.createElement)("div",{className:A(tW.nav),role:"tablist","aria-label":"Category navigation",id:"epr-category-nav-id",ref:l},d.map(function(e){var f=$(e),t=f===r;return e.category===o.CUSTOM&&h?null:(0,L.createElement)(tB,{key:f,category:f,isActiveCategory:t,allowNavigation:!c&&!t,categoryConfig:e,onClick:function(){s(f),u(f)}})}))}var tW=J.create({nav:{".":"epr-category-nav",display:"flex",flexDirection:"row",justifyContent:"space-around",padding:"var(--epr-header-padding)"},".epr-search-active":{nav:{opacity:"0.3",cursor:"default",pointerEvents:"none"}},".epr-main:has(input:not(:placeholder-shown))":{nav:{opacity:"0.3",cursor:"default",pointerEvents:"none"}}}),tH="data:image/svg+xml;base64,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";function tV(){var e=fw();return(0,L.createElement)(tn,{className:A(tX.btnClearSearch,Z.visibleOnSearchOnly),onClick:e,"aria-label":"Clear",title:"Clear"},(0,L.createElement)("div",{className:A(tX.icnClearnSearch)}))}var tX=J.create(O({btnClearSearch:{".":"epr-btn-clear-search",position:"absolute",right:"var(--epr-search-bar-inner-padding)",height:"30px",width:"30px",display:"flex",alignItems:"center",justifyContent:"center",top:"50%",transform:"translateY(-50%)",padding:"0",borderRadius:"50%",":hover":{background:"var(--epr-hover-bg-color)"},":focus":{background:"var(--epr-hover-bg-color)"}},icnClearnSearch:{".":"epr-icn-clear-search",backgroundColor:"transparent",backgroundRepeat:"no-repeat",backgroundSize:"20px",height:"20px",width:"20px",backgroundImage:"url("+tH+")",":hover":{backgroundPositionY:"-20px"},":focus":{backgroundPositionY:"-20px"}}},F("icnClearnSearch",{backgroundPositionY:"-40px"}),F("btnClearSearch",{":hover":{"> .epr-icn-clear-search":{backgroundPositionY:"-60px"}}}))),tK=R(n.emojiPicker)+" "+R(n.emojiList),tq=["button",R(n.emoji)].join(""),t$=R(n.category);function t0(e){var f=e.value;if(!f)return null;var t=[tq,'[data-full-name*="',fM(f),'"]'].join("");return(0,L.createElement)("style",null,"\n    "+tK+" "+tq+" {\n      display: none;\n    }\n\n\n    "+tK+" "+t+" {\n      display: flex;\n    }\n\n    "+tK+" "+t$+":not(:has("+t+")) {\n      display: none;\n    }\n  ")}function t1(){return(0,L.createElement)("div",{className:A(t4.icnSearch)})}var t4=J.create(O({icnSearch:{".":"epr-icn-search",content:"",position:"absolute",top:"50%",left:"var(--epr-search-bar-inner-padding)",transform:"translateY(-50%)",width:"20px",height:"20px",backgroundRepeat:"no-repeat",backgroundPosition:"0 0",backgroundSize:"20px",backgroundImage:"url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI2LjMuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjQwcHgiIHZpZXdCb3g9IjAgMCAyMCA0MCIgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgMjAgNDAiIHhtbDpzcGFjZT0icHJlc2VydmUiPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZmlsbD0iIzg2ODY4NiIgZD0iTTEyLDguODFjMCwyLjA4LTEuNjgsMy43Ni0zLjc2LDMuNzZjLTIuMDgsMC0zLjc2LTEuNjgtMy43Ni0zLjc2CgljMC0yLjA4LDEuNjgtMy43NiwzLjc2LTMuNzZDMTAuMzIsNS4wNSwxMiw2LjczLDEyLDguODF6IE0xMS4yMywxMi43MmMtMC44MywwLjY0LTEuODcsMS4wMS0yLjk5LDEuMDFjLTIuNzIsMC00LjkyLTIuMi00LjkyLTQuOTIKCWMwLTIuNzIsMi4yLTQuOTIsNC45Mi00LjkyYzIuNzIsMCw0LjkyLDIuMiw0LjkyLDQuOTJjMCwxLjEzLTAuMzgsMi4xNi0xLjAxLDIuOTlsMy45NCwzLjkzYzAuMjUsMC4yNSwwLjI1LDAuNjYsMCwwLjkyCgljLTAuMjUsMC4yNS0wLjY2LDAuMjUtMC45MiwwTDExLjIzLDEyLjcyeiIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZmlsbD0iI0MwQzBCRiIgZD0iTTEyLDI4LjgxYzAsMi4wOC0xLjY4LDMuNzYtMy43NiwzLjc2Yy0yLjA4LDAtMy43Ni0xLjY4LTMuNzYtMy43NgoJYzAtMi4wOCwxLjY4LTMuNzYsMy43Ni0zLjc2QzEwLjMyLDI1LjA1LDEyLDI2LjczLDEyLDI4LjgxeiBNMTEuMjMsMzIuNzJjLTAuODMsMC42NC0xLjg3LDEuMDEtMi45OSwxLjAxCgljLTIuNzIsMC00LjkyLTIuMi00LjkyLTQuOTJjMC0yLjcyLDIuMi00LjkyLDQuOTItNC45MmMyLjcyLDAsNC45MiwyLjIsNC45Miw0LjkyYzAsMS4xMy0wLjM4LDIuMTYtMS4wMSwyLjk5bDMuOTQsMy45MwoJYzAuMjUsMC4yNSwwLjI1LDAuNjYsMCwwLjkyYy0wLjI1LDAuMjUtMC42NiwwLjI1LTAuOTIsMEwxMS4yMywzMi43MnoiLz4KPC9zdmc+)"}},F("icnSearch",{backgroundPositionY:"-20px"})));function t2(){var e=eP(),f=fb();return e?null:(0,L.createElement)(tD,{className:A(t6.overlay)},(0,L.createElement)(t3,null),f?(0,L.createElement)(tP,null):null)}function t3(){var e,f,t,n,a,i,r,s,o,u,c,d,l,h,g=(0,L.useState)(0),w=g[0],p=g[1],M=fu(),b=fn(),m=null!=(r=[(s=eS()).searchPlaceHolder,s.searchPlaceholder].find(function(e){return e!==eL}))?r:eL,y=eS().autoFocusSearch,C=(o=fn(),u=eZ(),c=eZ(),d=function e(f){if("function"==typeof f)return e(f(c.current));c.current=f},l=fp(),h=eH()[0],e=u.current,f=h,{onChange:function(e){var f=u.current,t=e.toLowerCase();if(null!=f&&f[t]||t.length<=1)return l(t);var n=function(e,f){if(!f)return null;if(f[e])return f[e];var t=Object.keys(f).sort(function(e,f){return f.length-e.length}).find(function(f){return e.includes(f)});return t?f[t]:null}(t,f);if(!n)return l(t);d(function(e){var f;return Object.assign(e,((f={})[t]=function(e,f){var t={};for(var n in e){var a=e[n];(function(e,f){return es(e).some(function(e){return e.includes(f)})})(a,f)&&(t[n]=a)}return t}(n,t),f))}),l(t)},searchTerm:h,SearchInputRef:o,statusSearchResults:null!=e&&e[f]?(a=(n=(null==(t=Object.entries(null==e?void 0:e[f]))?void 0:t.length)||0)>0,i=n>1,a?i?ey.replace("%n",n.toString()):em:"No results found"):""}),j=C.statusSearchResults,I=C.searchTerm,v=C.onChange,k=null==b?void 0:b.current,N=null==k?void 0:k.value;return(0,L.createElement)(tz,{className:A(t6.searchContainer)},(0,L.createElement)(t0,{value:N}),(0,L.createElement)("input",{autoFocus:y,"aria-label":"Type to search for an emoji",onFocus:M,className:A(t6.search),type:"text","aria-controls":"epr-search-id",placeholder:m,onChange:function(e){p(w+1),setTimeout(function(){var f,t;v(null!=(f=null==e?void 0:null==(t=e.target)?void 0:t.value)?f:N)})},ref:b}),I?(0,L.createElement)("div",{role:"status",className:A("epr-status-search-results",t6.visuallyHidden),"aria-live":"polite",id:"epr-search-id","aria-atomic":"true"},j):null,(0,L.createElement)(t1,null),(0,L.createElement)(tV,null))}var t6=J.create(O({overlay:{padding:"var(--epr-header-padding)",zIndex:"var(--epr-header-overlay-z-index)"},searchContainer:{".":"epr-search-container",flex:"1",display:"block",minWidth:"0"},visuallyHidden:{clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",overflow:"hidden",position:"absolute",whiteSpace:"nowrap",width:"1px"},search:{outline:"none",transition:"all 0.2s ease-in-out",color:"var(--epr-search-input-text-color)",borderRadius:"var(--epr-search-input-border-radius)",padding:"var(--epr-search-input-padding)",height:"var(--epr-search-input-height)",backgroundColor:"var(--epr-search-input-bg-color)",border:"1px solid var(--epr-search-input-bg-color)",width:"100%",":focus":{backgroundColor:"var(--epr-search-input-bg-color-active)",border:"1px solid var(--epr-search-border-color)"},"::placeholder":{color:"var(--epr-search-input-placeholder-color)"}},btnClearSearch:{".":"epr-btn-clear-search",position:"absolute",right:"var(--epr-search-bar-inner-padding)",height:"30px",width:"30px",display:"flex",alignItems:"center",justifyContent:"center",top:"50%",transform:"translateY(-50%)",padding:"0",borderRadius:"50%",":hover":{background:"var(--epr-hover-bg-color)"},":focus":{background:"var(--epr-hover-bg-color)"}},icnClearnSearch:{".":"epr-icn-clear-search",backgroundColor:"transparent",backgroundRepeat:"no-repeat",backgroundSize:"20px",height:"20px",width:"20px",backgroundImage:"url("+tH+")",":hover":{backgroundPositionY:"-20px"},":focus":{backgroundPositionY:"-20px"}}},F("icnClearnSearch",{backgroundPositionY:"-40px"}),F("btnClearSearch",{":hover > .epr-icn-clear-search":{backgroundPositionY:"-60px"}})));function t9(){return(0,L.createElement)(tz,{className:A("epr-header",Z.hiddenOnReactions)},(0,L.createElement)(t2,null),(0,L.createElement)(tF,null))}function t8(){var e=eW()[0],f=ex(),t=(0,L.useState)(!e),n=t[0],a=t[1],i=eS().open;return((0,L.useEffect)(function(){(!e||f)&&(n||a(!0))},[n,f,e]),i)?(0,L.createElement)(fN,null,(0,L.createElement)(tp,null),(0,L.createElement)(t5,{renderAll:n})):null}function t5(e){return e.renderAll?(0,L.createElement)(L.Fragment,null,(0,L.createElement)(t9,null),(0,L.createElement)(tN,null),(0,L.createElement)(tY,null)):null}var t7=(0,L.memo)(function(e){return(0,L.createElement)(e9,null,(0,L.createElement)(G,null),(0,L.createElement)(eN,Object.assign({},e),(0,L.createElement)(t8,null)))},W),ne=function(e){function f(f){var t;return(t=e.call(this,f)||this).state={hasError:!1},t}f.prototype=Object.create(e.prototype),f.prototype.constructor=f,P(f,e),f.getDerivedStateFromError=function(){return{hasError:!0}};var t=f.prototype;return t.componentDidCatch=function(e,f){console.error("Emoji Picker React failed to render:",e,f)},t.render=function(){return this.state.hasError?null:this.props.children},f}(L.Component);let nf=function(e){var f,t,n=(f={onEmojiClick:e.onEmojiClick,onReactionClick:e.onReactionClick,onSkinToneChange:e.onSkinToneChange},t=b().useRef({onEmojiClick:f.onEmojiClick||eE,onReactionClick:f.onReactionClick||f.onEmojiClick,onSkinToneChange:f.onSkinToneChange||eE}),b().useEffect(function(){t.current.onEmojiClick=f.onEmojiClick||eE,t.current.onReactionClick=f.onReactionClick||f.onEmojiClick},[f.onEmojiClick,f.onReactionClick]),b().useEffect(function(){t.current.onSkinToneChange=f.onSkinToneChange||eE},[f.onSkinToneChange]),t);return(0,L.createElement)(ne,null,(0,L.createElement)(eD.Provider,{value:n},(0,L.createElement)(t7,Object.assign({},e))))}},57405:(e,f,t)=>{"use strict";let n,a;t.d(f,{io:()=>eE});var i,r={};t.r(r),t.d(r,{Decoder:()=>em,Encoder:()=>eL,PacketType:()=>i,protocol:()=>eM});var s=t(37101),o=t.t(s,2);let u=Object.create(null);u.open="0",u.close="1",u.ping="2",u.pong="3",u.message="4",u.upgrade="5",u.noop="6";let c=Object.create(null);Object.keys(u).forEach(e=>{c[u[e]]=e});let d={type:"error",data:"parser error"},l=({type:e,data:f},t,n)=>n(f instanceof ArrayBuffer||ArrayBuffer.isView(f)?t?f:"b"+h(f,!0).toString("base64"):u[e]+(f||"")),h=(e,f)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!f?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),g=(e,f)=>{if("string"!=typeof e)return{type:"message",data:w(e,f)};let t=e.charAt(0);return"b"===t?{type:"message",data:w(Buffer.from(e.substring(1),"base64"),f)}:c[t]?e.length>1?{type:c[t],data:e.substring(1)}:{type:c[t]}:d},w=(e,f)=>"arraybuffer"===f?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),p=(e,f)=>{let t=e.length,n=Array(t),a=0;e.forEach((e,i)=>{l(e,!1,e=>{n[i]=e,++a===t&&f(n.join("\x1e"))})})},M=(e,f)=>{let t=e.split("\x1e"),n=[];for(let e=0;e<t.length;e++){let a=g(t[e],f);if(n.push(a),"error"===a.type)break}return n};function L(e){return e.reduce((e,f)=>e+f.length,0)}function b(e,f){if(e[0].length===f)return e.shift();let t=new Uint8Array(f),n=0;for(let a=0;a<f;a++)t[a]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),t}function m(e){if(e)return function(e){for(var f in m.prototype)e[f]=m.prototype[f];return e}(e)}m.prototype.on=m.prototype.addEventListener=function(e,f){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(f),this},m.prototype.once=function(e,f){function t(){this.off(e,t),f.apply(this,arguments)}return t.fn=f,this.on(e,t),this},m.prototype.off=m.prototype.removeListener=m.prototype.removeAllListeners=m.prototype.removeEventListener=function(e,f){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var t,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var a=0;a<n.length;a++)if((t=n[a])===f||t.fn===f){n.splice(a,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},m.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var f=Array(arguments.length-1),t=this._callbacks["$"+e],n=1;n<arguments.length;n++)f[n-1]=arguments[n];if(t){t=t.slice(0);for(var n=0,a=t.length;n<a;++n)t[n].apply(this,f)}return this},m.prototype.emitReserved=m.prototype.emit,m.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},m.prototype.hasListeners=function(e){return!!this.listeners(e).length};let y=process.nextTick,C=global;class j{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let f=function(e){let f=e.split("; "),t=f[0].indexOf("=");if(-1===t)return;let n=f[0].substring(0,t).trim();if(!n.length)return;let a=f[0].substring(t+1).trim();34===a.charCodeAt(0)&&(a=a.slice(1,-1));let i={name:n,value:a};for(let e=1;e<f.length;e++){let t=f[e].split("=");if(2!==t.length)continue;let n=t[0].trim(),a=t[1].trim();switch(n){case"Expires":i.expires=new Date(a);break;case"Max-Age":let r=new Date;r.setUTCSeconds(r.getUTCSeconds()+parseInt(a,10)),i.expires=r}}return i}(e);f&&this._cookies.set(f.name,f)})}get cookies(){let e=Date.now();return this._cookies.forEach((f,t)=>{var n;(null===(n=f.expires)||void 0===n?void 0:n.getTime())<e&&this._cookies.delete(t)}),this._cookies.entries()}addCookies(e){let f=[];for(let[e,t]of this.cookies)f.push(`${e}=${t.value}`);f.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",f.join("; ")))}appendCookies(e){for(let[f,t]of this.cookies)e.append("cookie",`${f}=${t.value}`)}}function I(e,...f){return f.reduce((f,t)=>(e.hasOwnProperty(t)&&(f[t]=e[t]),f),{})}let v=C.setTimeout,k=C.clearTimeout;function N(e,f){f.useNativeTimers?(e.setTimeoutFn=v.bind(C),e.clearTimeoutFn=k.bind(C)):(e.setTimeoutFn=C.setTimeout.bind(C),e.clearTimeoutFn=C.clearTimeout.bind(C))}function S(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var D=t(99616);let T=D("engine.io-client:transport");class E extends Error{constructor(e,f,t){super(e),this.description=f,this.context=t,this.type="TransportError"}}class x extends m{constructor(e){super(),this.writable=!1,N(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,f,t){return super.emitReserved("error",new E(e,f,t)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):T("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let f=g(e,this.socket.binaryType);this.onPacket(f)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,f={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(f)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let f=function(e){let f="";for(let t in e)e.hasOwnProperty(t)&&(f.length&&(f+="&"),f+=encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return f}(e);return f.length?"?"+f:""}}let z=D("engine.io-client:polling");class A extends x{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let f=()=>{z("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(z("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){z("pre-pause polling complete"),--e||f()})),this.writable||(z("we are currently writing - waiting to pause"),e++,this.once("drain",function(){z("pre-pause writing complete"),--e||f()}))}else f()}_poll(){z("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){z("polling got data %s",e),M(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():z('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{z("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(z("transport open - closing"),e()):(z("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,p(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",f=this.query||{};return!1!==this.opts.timestampRequests&&(f[this.opts.timestampParam]=S()),this.supportsBinary||f.sid||(f.b64=1),this.createUri(e,f)}}let _=!1;try{_="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let O=_,P=D("engine.io-client:polling");function Q(){}class Y extends A{constructor(e){if(super(e),"undefined"!=typeof location){let f="https:"===location.protocol,t=location.port;t||(t=f?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||t!==e.port}}doWrite(e,f){let t=this.request({method:"POST",data:e});t.on("success",f),t.on("error",(e,f)=>{this.onError("xhr post error",e,f)})}doPoll(){P("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,f)=>{this.onError("xhr poll error",e,f)}),this.pollXhr=e}}class R extends m{constructor(e,f,t){super(),this.createRequest=e,N(this,t),this._opts=t,this._method=t.method||"GET",this._uri=f,this._data=void 0!==t.data?t.data:null,this._create()}_create(){var e;let f=I(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");f.xdomain=!!this._opts.xd;let t=this._xhr=this.createRequest(f);try{P("xhr open %s: %s",this._method,this._uri),t.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in t.setDisableHeaderCheck&&t.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&t.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{t.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{t.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(t),"withCredentials"in t&&(t.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(t.timeout=this._opts.requestTimeout),t.onreadystatechange=()=>{var e;3===t.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(t.getResponseHeader("set-cookie"))),4===t.readyState&&(200===t.status||1223===t.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof t.status?t.status:0)},0))},P("xhr data %s",this._data),t.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=R.requestsCount++,R.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=Q,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete R.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function J(){for(let e in R.requests)R.requests.hasOwnProperty(e)&&R.requests[e].abort()}R.requestsCount=0,R.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",J):"function"==typeof addEventListener&&addEventListener("onpagehide"in C?"pagehide":"unload",J,!1)),function(){let e=function(e){let f=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!f||O))return new XMLHttpRequest}catch(e){}if(!f)try{return new C[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let B=s||o;class U extends Y{request(e={}){var f;return Object.assign(e,{xd:this.xd,cookieJar:null===(f=this.socket)||void 0===f?void 0:f._cookieJar},this.opts),new R(e=>new B(e),this.uri(),e)}}t(83090),t(64203),t(36207);var G=t(72635);t(36495);let Z=D("engine.io-client:websocket"),F="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class W extends x{get name(){return"websocket"}doOpen(){let e=this.uri(),f=this.opts.protocols,t=F?{}:I(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(t.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,f,t)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let f=0;f<e.length;f++){let t=e[f],n=f===e.length-1;l(t,this.supportsBinary,e=>{try{this.doWrite(t,e)}catch(e){Z("websocket closed before onclose event")}n&&y(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",f=this.query||{};return this.opts.timestampRequests&&(f[this.opts.timestampParam]=S()),this.supportsBinary||(f.b64=1),this.createUri(e,f)}}C.WebSocket||C.MozWebSocket;class H extends W{createSocket(e,f,t){var n;if(null===(n=this.socket)||void 0===n?void 0:n._cookieJar)for(let[e,f]of(t.headers=t.headers||{},t.headers.cookie="string"==typeof t.headers.cookie?[t.headers.cookie]:t.headers.cookie||[],this.socket._cookieJar.cookies))t.headers.cookie.push(`${e}=${f.value}`);return new G(e,f,t)}doWrite(e,f){let t={};e.options&&(t.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof f?Buffer.byteLength(f):f.length)<this.opts.perMessageDeflate.threshold&&(t.compress=!1),this.ws.send(f,t)}}let V=D("engine.io-client:webtransport");class X extends x{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{V("transport closed gracefully"),this.onClose()}).catch(e=>{V("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let f=function(e,f){a||(a=new TextDecoder);let t=[],n=0,i=-1,r=!1;return new TransformStream({transform(s,o){for(t.push(s);;){if(0===n){if(1>L(t))break;let e=b(t,1);r=(128&e[0])==128,n=(i=127&e[0])<126?3:126===i?1:2}else if(1===n){if(2>L(t))break;let e=b(t,2);i=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),n=3}else if(2===n){if(8>L(t))break;let e=b(t,8),f=new DataView(e.buffer,e.byteOffset,e.length),a=f.getUint32(0);if(a>2097151){o.enqueue(d);break}i=0x100000000*a+f.getUint32(4),n=3}else{if(L(t)<i)break;let e=b(t,i);o.enqueue(g(r?e:a.decode(e),f)),n=0}if(0===i||i>e){o.enqueue(d);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),t=e.readable.pipeThrough(f).getReader(),i=new TransformStream({transform(e,f){!function(e,f){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return f(h(e.data,!1));l(e,!0,e=>{n||(n=new TextEncoder),f(n.encode(e))})}(e,t=>{let n;let a=t.length;if(a<126)new DataView((n=new Uint8Array(1)).buffer).setUint8(0,a);else if(a<65536){let e=new DataView((n=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,a)}else{let e=new DataView((n=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(a))}e.data&&"string"!=typeof e.data&&(n[0]|=128),f.enqueue(n),f.enqueue(t)})}});i.readable.pipeTo(e.writable),this._writer=i.writable.getWriter();let r=()=>{t.read().then(({done:e,value:f})=>{if(e){V("session is closed");return}V("received chunk: %o",f),this.onPacket(f),r()}).catch(e=>{V("an error occurred while reading: %s",e)})};r();let s={type:"open"};this.query.sid&&(s.data=`{"sid":"${this.query.sid}"}`),this._writer.write(s).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let f=0;f<e.length;f++){let t=e[f],n=f===e.length-1;this._writer.write(t).then(()=>{n&&y(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let K={websocket:H,webtransport:X,polling:U},q=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,$=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ee(e){if(e.length>8e3)throw"URI too long";let f=e,t=e.indexOf("["),n=e.indexOf("]");-1!=t&&-1!=n&&(e=e.substring(0,t)+e.substring(t,n).replace(/:/g,";")+e.substring(n,e.length));let a=q.exec(e||""),i={},r=14;for(;r--;)i[$[r]]=a[r]||"";return -1!=t&&-1!=n&&(i.source=f,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(e,f){let t=f.replace(/\/{2,9}/g,"/").split("/");return("/"==f.slice(0,1)||0===f.length)&&t.splice(0,1),"/"==f.slice(-1)&&t.splice(t.length-1,1),t}(0,i.path),i.queryKey=function(e,f){let t={};return f.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,f,n){f&&(t[f]=n)}),t}(0,i.query),i}let ef=D("engine.io-client:socket"),et="function"==typeof addEventListener&&"function"==typeof removeEventListener,en=[];et&&addEventListener("offline",()=>{ef("closing %d connection(s) because the network was lost",en.length),en.forEach(e=>e())},!1);class ea extends m{constructor(e,f){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(f=e,e=null),e){let t=ee(e);f.hostname=t.host,f.secure="https"===t.protocol||"wss"===t.protocol,f.port=t.port,t.query&&(f.query=t.query)}else f.host&&(f.hostname=ee(f.host).host);N(this,f),this.secure=null!=f.secure?f.secure:"undefined"!=typeof location&&"https:"===location.protocol,f.hostname&&!f.port&&(f.port=this.secure?"443":"80"),this.hostname=f.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=f.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},f.transports.forEach(e=>{let f=e.prototype.name;this.transports.push(f),this._transportsByName[f]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},f),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let f={},t=e.split("&");for(let e=0,n=t.length;e<n;e++){let n=t[e].split("=");f[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}return f}(this.opts.query)),et&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(ef("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},en.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new j),this._open()}createTransport(e){ef('creating transport "%s"',e);let f=Object.assign({},this.opts.query);f.EIO=4,f.transport=e,this.id&&(f.sid=this.id);let t=Object.assign({},this.opts,{query:f,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return ef("options: %j",t),new this._transportsByName[e](t)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&ea.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let f=this.createTransport(e);f.open(),this.setTransport(f)}setTransport(e){ef("setting transport %s",e.name),this.transport&&(ef("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){ef("socket open"),this.readyState="open",ea.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(ef('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let f=Error("server error");f.code=e.data,this._onError(f);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else ef('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();ef("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let f=0;f<this.writeBuffer.length;f++){let t=this.writeBuffer[f].data;if(t)e+="string"==typeof t?function(e){let f=0,t=0;for(let n=0,a=e.length;n<a;n++)(f=e.charCodeAt(n))<128?t+=1:f<2048?t+=2:f<55296||f>=57344?t+=3:(n++,t+=4);return t}(t):Math.ceil(1.33*(t.byteLength||t.size));if(f>0&&e>this._maxPayload)return ef("only send %d out of %d packets",f,this.writeBuffer.length),this.writeBuffer.slice(0,f);e+=2}return ef("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(ef("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,y(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,f,t){return this._sendPacket("message",e,f,t),this}send(e,f,t){return this._sendPacket("message",e,f,t),this}_sendPacket(e,f,t,n){if("function"==typeof f&&(n=f,f=void 0),"function"==typeof t&&(n=t,t=null),"closing"===this.readyState||"closed"===this.readyState)return;(t=t||{}).compress=!1!==t.compress;let a={type:e,data:f,options:t};this.emitReserved("packetCreate",a),this.writeBuffer.push(a),n&&this.once("flush",n),this.flush()}close(){let e=()=>{this._onClose("forced close"),ef("socket closing - telling transport to close"),this.transport.close()},f=()=>{this.off("upgrade",f),this.off("upgradeError",f),e()},t=()=>{this.once("upgrade",f),this.once("upgradeError",f)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?t():e()}):this.upgrading?t():e()),this}_onError(e){if(ef("socket error %j",e),ea.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return ef("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,f){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(ef('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),et&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=en.indexOf(this._offlineEventListener);-1!==e&&(ef("removing listener for the 'offline' event"),en.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,f),this.writeBuffer=[],this._prevBufferLen=0}}}ea.protocol=4;class ei extends ea{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){ef("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){ef('probing transport "%s"',e);let f=this.createTransport(e),t=!1;ea.priorWebsocketSuccess=!1;let n=()=>{t||(ef('probe transport "%s" opened',e),f.send([{type:"ping",data:"probe"}]),f.once("packet",n=>{if(!t){if("pong"===n.type&&"probe"===n.data)ef('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",f),f&&(ea.priorWebsocketSuccess="websocket"===f.name,ef('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{!t&&"closed"!==this.readyState&&(ef("changing transport and sending upgrade packet"),u(),this.setTransport(f),f.send([{type:"upgrade"}]),this.emitReserved("upgrade",f),f=null,this.upgrading=!1,this.flush())}));else{ef('probe transport "%s" failed',e);let t=Error("probe error");t.transport=f.name,this.emitReserved("upgradeError",t)}}}))};function a(){t||(t=!0,u(),f.close(),f=null)}let i=t=>{let n=Error("probe error: "+t);n.transport=f.name,a(),ef('probe transport "%s" failed because of error: %s',e,t),this.emitReserved("upgradeError",n)};function r(){i("transport closed")}function s(){i("socket closed")}function o(e){f&&e.name!==f.name&&(ef('"%s" works - aborting "%s"',e.name,f.name),a())}let u=()=>{f.removeListener("open",n),f.removeListener("error",i),f.removeListener("close",r),this.off("close",s),this.off("upgrading",o)};f.once("open",n),f.once("error",i),f.once("close",r),this.once("close",s),this.once("upgrading",o),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{t||f.open()},200):f.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let f=[];for(let t=0;t<e.length;t++)~this.transports.indexOf(e[t])&&f.push(e[t]);return f}}class er extends ei{constructor(e,f={}){let t="object"==typeof e?e:f;(!t.transports||t.transports&&"string"==typeof t.transports[0])&&(t.transports=(t.transports||["polling","websocket","webtransport"]).map(e=>K[e]).filter(e=>!!e)),super(e,t)}}er.protocol;var es=t(47905);let eo=es("socket.io-client:url"),eu="function"==typeof ArrayBuffer,ec=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,ed=Object.prototype.toString,el="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===ed.call(Blob),eh="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===ed.call(File);function eg(e){return eu&&(e instanceof ArrayBuffer||ec(e))||el&&e instanceof Blob||eh&&e instanceof File}let ew=t(25133)("socket.io-parser"),ep=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],eM=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(i||(i={}));class eL{constructor(e){this.replacer=e}encode(e){return(ew("encoding packet %j",e),(e.type===i.EVENT||e.type===i.ACK)&&function e(f,t){if(!f||"object"!=typeof f)return!1;if(Array.isArray(f)){for(let t=0,n=f.length;t<n;t++)if(e(f[t]))return!0;return!1}if(eg(f))return!0;if(f.toJSON&&"function"==typeof f.toJSON&&1==arguments.length)return e(f.toJSON(),!0);for(let t in f)if(Object.prototype.hasOwnProperty.call(f,t)&&e(f[t]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===i.EVENT?i.BINARY_EVENT:i.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let f=""+e.type;return(e.type===i.BINARY_EVENT||e.type===i.BINARY_ACK)&&(f+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(f+=e.nsp+","),null!=e.id&&(f+=e.id),null!=e.data&&(f+=JSON.stringify(e.data,this.replacer)),ew("encoded %j as %s",e,f),f}encodeAsBinary(e){let f=function(e){let f=[],t=e.data;return e.data=function e(f,t){if(!f)return f;if(eg(f)){let e={_placeholder:!0,num:t.length};return t.push(f),e}if(Array.isArray(f)){let n=Array(f.length);for(let a=0;a<f.length;a++)n[a]=e(f[a],t);return n}if("object"==typeof f&&!(f instanceof Date)){let n={};for(let a in f)Object.prototype.hasOwnProperty.call(f,a)&&(n[a]=e(f[a],t));return n}return f}(t,f),e.attachments=f.length,{packet:e,buffers:f}}(e),t=this.encodeAsString(f.packet),n=f.buffers;return n.unshift(t),n}}function eb(e){return"[object Object]"===Object.prototype.toString.call(e)}class em extends m{constructor(e){super(),this.reviver=e}add(e){let f;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let t=(f=this.decodeString(e)).type===i.BINARY_EVENT;t||f.type===i.BINARY_ACK?(f.type=t?i.EVENT:i.ACK,this.reconstructor=new ey(f),0===f.attachments&&super.emitReserved("decoded",f)):super.emitReserved("decoded",f)}else if(eg(e)||e.base64){if(this.reconstructor)(f=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",f));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let f=0,t={type:Number(e.charAt(0))};if(void 0===i[t.type])throw Error("unknown packet type "+t.type);if(t.type===i.BINARY_EVENT||t.type===i.BINARY_ACK){let n=f+1;for(;"-"!==e.charAt(++f)&&f!=e.length;);let a=e.substring(n,f);if(a!=Number(a)||"-"!==e.charAt(f))throw Error("Illegal attachments");t.attachments=Number(a)}if("/"===e.charAt(f+1)){let n=f+1;for(;++f&&","!==e.charAt(f)&&f!==e.length;);t.nsp=e.substring(n,f)}else t.nsp="/";let n=e.charAt(f+1);if(""!==n&&Number(n)==n){let n=f+1;for(;++f;){let t=e.charAt(f);if(null==t||Number(t)!=t){--f;break}if(f===e.length)break}t.id=Number(e.substring(n,f+1))}if(e.charAt(++f)){let n=this.tryParse(e.substr(f));if(em.isPayloadValid(t.type,n))t.data=n;else throw Error("invalid payload")}return ew("decoded %s as %j",e,t),t}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,f){switch(e){case i.CONNECT:return eb(f);case i.DISCONNECT:return void 0===f;case i.CONNECT_ERROR:return"string"==typeof f||eb(f);case i.EVENT:case i.BINARY_EVENT:return Array.isArray(f)&&("number"==typeof f[0]||"string"==typeof f[0]&&-1===ep.indexOf(f[0]));case i.ACK:case i.BINARY_ACK:return Array.isArray(f)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ey{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var f,t;let e=(f=this.reconPack,t=this.buffers,f.data=function e(f,t){if(!f)return f;if(f&&!0===f._placeholder){if("number"==typeof f.num&&f.num>=0&&f.num<t.length)return t[f.num];throw Error("illegal attachments")}if(Array.isArray(f))for(let n=0;n<f.length;n++)f[n]=e(f[n],t);else if("object"==typeof f)for(let n in f)Object.prototype.hasOwnProperty.call(f,n)&&(f[n]=e(f[n],t));return f}(f.data,t),delete f.attachments,f);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eC(e,f,t){return e.on(f,t),function(){e.off(f,t)}}let ej=es("socket.io-client:socket"),eI=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class ev extends m{constructor(e,f,t){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=f,t&&t.auth&&(this.auth=t.auth),this._opts=Object.assign({},t),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eC(e,"open",this.onopen.bind(this)),eC(e,"packet",this.onpacket.bind(this)),eC(e,"error",this.onerror.bind(this)),eC(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...f){var t,n,a;if(eI.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(f.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(f),this;let r={type:i.EVENT,data:f};if(r.options={},r.options.compress=!1!==this.flags.compress,"function"==typeof f[f.length-1]){let e=this.ids++;ej("emitting packet with ack id %d",e);let t=f.pop();this._registerAckCallback(e,t),r.id=e}let s=null===(n=null===(t=this.io.engine)||void 0===t?void 0:t.transport)||void 0===n?void 0:n.writable,o=this.connected&&!(null===(a=this.io.engine)||void 0===a?void 0:a._hasPingExpired());return this.flags.volatile&&!s?ej("discard packet as the transport is not currently writable"):o?(this.notifyOutgoingListeners(r),this.packet(r)):this.sendBuffer.push(r),this.flags={},this}_registerAckCallback(e,f){var t;let n=null!==(t=this.flags.timeout)&&void 0!==t?t:this._opts.ackTimeout;if(void 0===n){this.acks[e]=f;return}let a=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let f=0;f<this.sendBuffer.length;f++)this.sendBuffer[f].id===e&&(ej("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(f,1));ej("event with ack id %d has timed out after %d ms",e,n),f.call(this,Error("operation has timed out"))},n),i=(...e)=>{this.io.clearTimeoutFn(a),f.apply(this,e)};i.withError=!0,this.acks[e]=i}emitWithAck(e,...f){return new Promise((t,n)=>{let a=(e,f)=>e?n(e):t(f);a.withError=!0,f.push(a),this.emit(e,...f)})}_addToQueue(e){let f;"function"==typeof e[e.length-1]&&(f=e.pop());let t={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...n)=>{if(t===this._queue[0])return null!==e?t.tryCount>this._opts.retries&&(ej("packet [%d] is discarded after %d tries",t.id,t.tryCount),this._queue.shift(),f&&f(e)):(ej("packet [%d] was successfully sent",t.id),this._queue.shift(),f&&f(null,...n)),t.pending=!1,this._drainQueue()}),this._queue.push(t),this._drainQueue()}_drainQueue(e=!1){if(ej("draining queue"),!this.connected||0===this._queue.length)return;let f=this._queue[0];if(f.pending&&!e){ej("packet [%d] has already been sent and is waiting for an ack",f.id);return}f.pending=!0,f.tryCount++,ej("sending packet [%d] (try n\xb0%d)",f.id,f.tryCount),this.flags=f.flags,this.emit.apply(this,f.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){ej("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:i.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,f){ej("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,f),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(f=>String(f.id)===e)){let f=this.acks[e];delete this.acks[e],f.withError&&f.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case i.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case i.EVENT:case i.BINARY_EVENT:this.onevent(e);break;case i.ACK:case i.BINARY_ACK:this.onack(e);break;case i.DISCONNECT:this.ondisconnect();break;case i.CONNECT_ERROR:this.destroy();let f=Error(e.data.message);f.data=e.data.data,this.emitReserved("connect_error",f)}}onevent(e){let f=e.data||[];ej("emitting event %j",f),null!=e.id&&(ej("attaching ack callback to event"),f.push(this.ack(e.id))),this.connected?this.emitEvent(f):this.receiveBuffer.push(Object.freeze(f))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let f of this._anyListeners.slice())f.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let f=this,t=!1;return function(...n){t||(t=!0,ej("sending ack %j",n),f.packet({type:i.ACK,id:e,data:n}))}}onack(e){let f=this.acks[e.id];if("function"!=typeof f){ej("bad ack %s",e.id);return}delete this.acks[e.id],ej("calling ack %s with %j",e.id,e.data),f.withError&&e.data.unshift(null),f.apply(this,e.data)}onconnect(e,f){ej("socket connected with id %s",e),this.id=e,this.recovered=f&&this._pid===f,this._pid=f,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){ej("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(ej("performing disconnect (%s)",this.nsp),this.packet({type:i.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let f=this._anyListeners;for(let t=0;t<f.length;t++)if(e===f[t]){f.splice(t,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let f=this._anyOutgoingListeners;for(let t=0;t<f.length;t++)if(e===f[t]){f.splice(t,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let f of this._anyOutgoingListeners.slice())f.apply(this,e.data)}}function ek(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ek.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var f=Math.random(),t=Math.floor(f*this.jitter*e);e=(1&Math.floor(10*f))==0?e-t:e+t}return 0|Math.min(e,this.max)},ek.prototype.reset=function(){this.attempts=0},ek.prototype.setMin=function(e){this.ms=e},ek.prototype.setMax=function(e){this.max=e},ek.prototype.setJitter=function(e){this.jitter=e};let eN=es("socket.io-client:manager");class eS extends m{constructor(e,f){var t;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(f=e,e=void 0),(f=f||{}).path=f.path||"/socket.io",this.opts=f,N(this,f),this.reconnection(!1!==f.reconnection),this.reconnectionAttempts(f.reconnectionAttempts||1/0),this.reconnectionDelay(f.reconnectionDelay||1e3),this.reconnectionDelayMax(f.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(t=f.randomizationFactor)&&void 0!==t?t:.5),this.backoff=new ek({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==f.timeout?2e4:f.timeout),this._readyState="closed",this.uri=e;let n=f.parser||r;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=!1!==f.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var f;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(f=this.backoff)||void 0===f||f.setMin(e),this)}randomizationFactor(e){var f;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(f=this.backoff)||void 0===f||f.setJitter(e),this)}reconnectionDelayMax(e){var f;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(f=this.backoff)||void 0===f||f.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(eN("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;eN("opening %s",this.uri),this.engine=new er(this.uri,this.opts);let f=this.engine,t=this;this._readyState="opening",this.skipReconnect=!1;let n=eC(f,"open",function(){t.onopen(),e&&e()}),a=f=>{eN("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",f),e?e(f):this.maybeReconnectOnOpen()},i=eC(f,"error",a);if(!1!==this._timeout){let e=this._timeout;eN("connect attempt will timeout after %d",e);let t=this.setTimeoutFn(()=>{eN("connect attempt timed out after %d",e),n(),a(Error("timeout")),f.close()},e);this.opts.autoUnref&&t.unref(),this.subs.push(()=>{this.clearTimeoutFn(t)})}return this.subs.push(n),this.subs.push(i),this}connect(e){return this.open(e)}onopen(){eN("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eC(e,"ping",this.onping.bind(this)),eC(e,"data",this.ondata.bind(this)),eC(e,"error",this.onerror.bind(this)),eC(e,"close",this.onclose.bind(this)),eC(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){y(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){eN("error",e),this.emitReserved("error",e)}socket(e,f){let t=this.nsps[e];return t?this._autoConnect&&!t.active&&t.connect():(t=new ev(this,e,f),this.nsps[e]=t),t}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active){eN("socket %s is still active, skipping close",e);return}this._close()}_packet(e){eN("writing packet %j",e);let f=this.encoder.encode(e);for(let t=0;t<f.length;t++)this.engine.write(f[t],e.options)}cleanup(){eN("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){eN("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,f){var t;eN("closed due to %s",e),this.cleanup(),null===(t=this.engine)||void 0===t||t.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,f),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)eN("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let f=this.backoff.duration();eN("will wait %dms before reconnect attempt",f),this._reconnecting=!0;let t=this.setTimeoutFn(()=>{!e.skipReconnect&&(eN("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(f=>{f?(eN("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",f)):(eN("reconnect success"),e.onreconnect())}))},f);this.opts.autoUnref&&t.unref(),this.subs.push(()=>{this.clearTimeoutFn(t)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eD=es("socket.io-client"),eT={};function eE(e,f){let t;"object"==typeof e&&(f=e,e=void 0);let n=function(e,f="",t){let n=e;t=t||"undefined"!=typeof location&&location,null==e&&(e=t.protocol+"//"+t.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?t.protocol+e:t.host+e),/^(https?|wss?):\/\//.test(e)||(eo("protocol-less url %s",e),e=void 0!==t?t.protocol+"//"+e:"https://"+e),eo("parse %s",e),n=ee(e)),!n.port&&(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";let a=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+a+":"+n.port+f,n.href=n.protocol+"://"+a+(t&&t.port===n.port?"":":"+n.port),n}(e,(f=f||{}).path||"/socket.io"),a=n.source,i=n.id,r=n.path,s=eT[i]&&r in eT[i].nsps;return f.forceNew||f["force new connection"]||!1===f.multiplex||s?(eD("ignoring socket cache for %s",a),t=new eS(a,f)):(eT[i]||(eD("new io instance for %s",a),eT[i]=new eS(a,f)),t=eT[i]),n.query&&!f.query&&(f.query=n.queryKey),t.socket(n.path,f)}Object.assign(eE,{Manager:eS,Socket:ev,io:eE,connect:eE})},64203:(e,f,t)=>{"use strict";let{Writable:n}=t(27910),a=t(2666),{BINARY_TYPES:i,EMPTY_BUFFER:r,kStatusCode:s,kWebSocket:o}=t(91813),{concat:u,toArrayBuffer:c,unmask:d}=t(10257),{isValidStatusCode:l,isValidUTF8:h}=t(37293),g=Buffer[Symbol.species];class w extends n{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[o]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,f,t){if(8===this._opcode&&0==this._state)return t();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(t)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let f=this._buffers[0];return this._buffers[0]=new g(f.buffer,f.byteOffset+e,f.length-e),new g(f.buffer,f.byteOffset,e)}let f=Buffer.allocUnsafe(e);do{let t=this._buffers[0],n=f.length-e;e>=t.length?f.set(this._buffers.shift(),n):(f.set(new Uint8Array(t.buffer,t.byteOffset,e),n),this._buffers[0]=new g(t.buffer,t.byteOffset+e,t.length-e)),e-=t.length}while(e>0);return f}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let f=this.consume(2);if((48&f[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let t=(64&f[0])==64;if(t&&!this._extensions[a.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&f[0])==128,this._opcode=15&f[0],this._payloadLength=127&f[1],0===this._opcode){if(t){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=t}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(t){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&f[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let f=this.consume(8),t=f.readUInt32BE(0);if(t>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=0x100000000*t+f.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let f=r;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}f=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&d(f,this._mask)}if(this._opcode>7){this.controlMessage(f,e);return}if(this._compressed){this._state=5,this.decompress(f,e);return}f.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(f)),this.dataMessage(e)}decompress(e,f){this._extensions[a.extensionName].decompress(e,this._fin,(e,t)=>{if(e)return f(e);if(t.length){if(this._messageLength+=t.length,this._messageLength>this._maxPayload&&this._maxPayload>0){f(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(t)}this.dataMessage(f),0===this._state&&this.startLoop(f)})}dataMessage(e){if(!this._fin){this._state=0;return}let f=this._messageLength,t=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let n;n="nodebuffer"===this._binaryType?u(t,f):"arraybuffer"===this._binaryType?c(u(t,f)):t,this._allowSynchronousEvents?(this.emit("message",n,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",n,!0),this._state=0,this.startLoop(e)}))}else{let n=u(t,f);if(!this._skipUTF8Validation&&!h(n)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",n,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",n,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,f){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,r),this.end();else{let t=e.readUInt16BE(0);if(!l(t)){f(this.createError(RangeError,`invalid status code ${t}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let n=new g(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!h(n)){f(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",t,n),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(f)}))}createError(e,f,t,n,a){this._loop=!1,this._errored=!0;let i=new e(t?`Invalid WebSocket frame: ${f}`:f);return Error.captureStackTrace(i,this.createError),i.code=a,i[s]=n,i}}e.exports=w},67461:(e,f,t)=>{let n=t(83997),a=t(28354);f.init=function(e){e.inspectOpts={};let t=Object.keys(f.inspectOpts);for(let n=0;n<t.length;n++)e.inspectOpts[t[n]]=f.inspectOpts[t[n]]},f.log=function(...e){return process.stderr.write(a.formatWithOptions(f.inspectOpts,...e)+"\n")},f.formatArgs=function(t){let{namespace:n,useColors:a}=this;if(a){let f=this.color,a="\x1b[3"+(f<8?f:"8;5;"+f),i=`  ${a};1m${n} \u001B[0m`;t[0]=i+t[0].split("\n").join("\n"+i),t.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else t[0]=(f.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+t[0]},f.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},f.load=function(){return process.env.DEBUG},f.useColors=function(){return"colors"in f.inspectOpts?!!f.inspectOpts.colors:n.isatty(process.stderr.fd)},f.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),f.colors=[6,2,3,4,5,1];try{let e=t(39228);e&&(e.stderr||e).level>=2&&(f.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}f.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,f)=>{let t=f.substring(6).toLowerCase().replace(/_([a-z])/g,(e,f)=>f.toUpperCase()),n=process.env[f];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[t]=n,e},{}),e.exports=t(29940)(f);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},70946:(e,f,t)=>{"use strict";let{tokenChars:n}=t(37293);e.exports={parse:function(e){let f=new Set,t=-1,a=-1,i=0;for(;i<e.length;i++){let r=e.charCodeAt(i);if(-1===a&&1===n[r])-1===t&&(t=i);else if(0!==i&&(32===r||9===r))-1===a&&-1!==t&&(a=i);else if(44===r){if(-1===t)throw SyntaxError(`Unexpected character at index ${i}`);-1===a&&(a=i);let n=e.slice(t,a);if(f.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);f.add(n),t=a=-1}else throw SyntaxError(`Unexpected character at index ${i}`)}if(-1===t||-1!==a)throw SyntaxError("Unexpected end of input");let r=e.slice(t,i);if(f.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);return f.add(r),f}}},72635:(e,f,t)=>{"use strict";let n=t(94735),a=t(55591),i=t(81630),r=t(91645),s=t(34631),{randomBytes:o,createHash:u}=t(55511),{Duplex:c,Readable:d}=t(27910),{URL:l}=t(79551),h=t(2666),g=t(64203),w=t(36207),{BINARY_TYPES:p,EMPTY_BUFFER:M,GUID:L,kForOnEventAttribute:b,kListener:m,kStatusCode:y,kWebSocket:C,NOOP:j}=t(91813),{EventTarget:{addEventListener:I,removeEventListener:v}}=t(81548),{format:k,parse:N}=t(31637),{toBuffer:S}=t(10257),D=Symbol("kAborted"),T=[8,13],E=["CONNECTING","OPEN","CLOSING","CLOSED"],x=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class z extends n{constructor(e,f,t){super(),this._binaryType=p[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=M,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=z.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===f?f=[]:Array.isArray(f)||("object"==typeof f&&null!==f?(t=f,f=[]):f=[f]),function e(f,t,n,r){let s,c,d,g;let w={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:T[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...r,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(f._autoPong=w.autoPong,!T.includes(w.protocolVersion))throw RangeError(`Unsupported protocol version: ${w.protocolVersion} (supported versions: ${T.join(", ")})`);if(t instanceof l)s=t;else try{s=new l(t)}catch(e){throw SyntaxError(`Invalid URL: ${t}`)}"http:"===s.protocol?s.protocol="ws:":"https:"===s.protocol&&(s.protocol="wss:"),f._url=s.href;let p="wss:"===s.protocol,M="ws+unix:"===s.protocol;if("ws:"===s.protocol||p||M?M&&!s.pathname?c="The URL's pathname is empty":s.hash&&(c="The URL contains a fragment identifier"):c='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',c){let e=SyntaxError(c);if(0===f._redirects)throw e;A(f,e);return}let b=p?443:80,m=o(16).toString("base64"),y=p?a.request:i.request,C=new Set;if(w.createConnection=w.createConnection||(p?O:_),w.defaultPort=w.defaultPort||b,w.port=s.port||b,w.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,w.headers={...w.headers,"Sec-WebSocket-Version":w.protocolVersion,"Sec-WebSocket-Key":m,Connection:"Upgrade",Upgrade:"websocket"},w.path=s.pathname+s.search,w.timeout=w.handshakeTimeout,w.perMessageDeflate&&(d=new h(!0!==w.perMessageDeflate?w.perMessageDeflate:{},!1,w.maxPayload),w.headers["Sec-WebSocket-Extensions"]=k({[h.extensionName]:d.offer()})),n.length){for(let e of n){if("string"!=typeof e||!x.test(e)||C.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");C.add(e)}w.headers["Sec-WebSocket-Protocol"]=n.join(",")}if(w.origin&&(w.protocolVersion<13?w.headers["Sec-WebSocket-Origin"]=w.origin:w.headers.Origin=w.origin),(s.username||s.password)&&(w.auth=`${s.username}:${s.password}`),M){let e=w.path.split(":");w.socketPath=e[0],w.path=e[1]}if(w.followRedirects){if(0===f._redirects){f._originalIpc=M,f._originalSecure=p,f._originalHostOrSocketPath=M?w.socketPath:s.host;let e=r&&r.headers;if(r={...r,headers:{}},e)for(let[f,t]of Object.entries(e))r.headers[f.toLowerCase()]=t}else if(0===f.listenerCount("redirect")){let e=M?!!f._originalIpc&&w.socketPath===f._originalHostOrSocketPath:!f._originalIpc&&s.host===f._originalHostOrSocketPath;e&&(!f._originalSecure||p)||(delete w.headers.authorization,delete w.headers.cookie,e||delete w.headers.host,w.auth=void 0)}w.auth&&!r.headers.authorization&&(r.headers.authorization="Basic "+Buffer.from(w.auth).toString("base64")),g=f._req=y(w),f._redirects&&f.emit("redirect",f.url,g)}else g=f._req=y(w);w.timeout&&g.on("timeout",()=>{P(f,g,"Opening handshake has timed out")}),g.on("error",e=>{null===g||g[D]||(g=f._req=null,A(f,e))}),g.on("response",a=>{let i=a.headers.location,s=a.statusCode;if(i&&w.followRedirects&&s>=300&&s<400){let a;if(++f._redirects>w.maxRedirects){P(f,g,"Maximum redirects exceeded");return}g.abort();try{a=new l(i,t)}catch(e){A(f,SyntaxError(`Invalid URL: ${i}`));return}e(f,a,n,r)}else f.emit("unexpected-response",g,a)||P(f,g,`Unexpected server response: ${a.statusCode}`)}),g.on("upgrade",(e,t,n)=>{let a;if(f.emit("upgrade",e),f.readyState!==z.CONNECTING)return;g=f._req=null;let i=e.headers.upgrade;if(void 0===i||"websocket"!==i.toLowerCase()){P(f,t,"Invalid Upgrade header");return}let r=u("sha1").update(m+L).digest("base64");if(e.headers["sec-websocket-accept"]!==r){P(f,t,"Invalid Sec-WebSocket-Accept header");return}let s=e.headers["sec-websocket-protocol"];if(void 0!==s?C.size?C.has(s)||(a="Server sent an invalid subprotocol"):a="Server sent a subprotocol but none was requested":C.size&&(a="Server sent no subprotocol"),a){P(f,t,a);return}s&&(f._protocol=s);let o=e.headers["sec-websocket-extensions"];if(void 0!==o){let e;if(!d){P(f,t,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=N(o)}catch(e){P(f,t,"Invalid Sec-WebSocket-Extensions header");return}let n=Object.keys(e);if(1!==n.length||n[0]!==h.extensionName){P(f,t,"Server indicated an extension that was not requested");return}try{d.accept(e[h.extensionName])}catch(e){P(f,t,"Invalid Sec-WebSocket-Extensions header");return}f._extensions[h.extensionName]=d}f.setSocket(t,n,{allowSynchronousEvents:w.allowSynchronousEvents,generateMask:w.generateMask,maxPayload:w.maxPayload,skipUTF8Validation:w.skipUTF8Validation})}),w.finishRequest?w.finishRequest(g,f):g.end()}(this,e,f,t)):(this._autoPong=t.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){p.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,f,t){let n=new g({allowSynchronousEvents:t.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:t.maxPayload,skipUTF8Validation:t.skipUTF8Validation});this._sender=new w(e,this._extensions,t.generateMask),this._receiver=n,this._socket=e,n[C]=this,e[C]=this,n.on("conclude",Y),n.on("drain",R),n.on("error",J),n.on("message",U),n.on("ping",G),n.on("pong",Z),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),f.length>0&&e.unshift(f),e.on("close",W),e.on("data",H),e.on("end",V),e.on("error",X),this._readyState=z.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=z.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[h.extensionName]&&this._extensions[h.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=z.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,f){if(this.readyState!==z.CLOSED){if(this.readyState===z.CONNECTING){P(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===z.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=z.CLOSING,this._sender.close(e,f,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==z.CONNECTING&&this.readyState!==z.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,f,t){if(this.readyState===z.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(t=e,e=f=void 0):"function"==typeof f&&(t=f,f=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==z.OPEN){Q(this,e,t);return}void 0===f&&(f=!this._isServer),this._sender.ping(e||M,f,t)}pong(e,f,t){if(this.readyState===z.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(t=e,e=f=void 0):"function"==typeof f&&(t=f,f=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==z.OPEN){Q(this,e,t);return}void 0===f&&(f=!this._isServer),this._sender.pong(e||M,f,t)}resume(){this.readyState!==z.CONNECTING&&this.readyState!==z.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,f,t){if(this.readyState===z.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof f&&(t=f,f={}),"number"==typeof e&&(e=e.toString()),this.readyState!==z.OPEN){Q(this,e,t);return}let n={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...f};this._extensions[h.extensionName]||(n.compress=!1),this._sender.send(e||M,n,t)}terminate(){if(this.readyState!==z.CLOSED){if(this.readyState===z.CONNECTING){P(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=z.CLOSING,this._socket.destroy())}}}function A(e,f){e._readyState=z.CLOSING,e.emit("error",f),e.emitClose()}function _(e){return e.path=e.socketPath,r.connect(e)}function O(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=r.isIP(e.host)?"":e.host),s.connect(e)}function P(e,f,t){e._readyState=z.CLOSING;let n=Error(t);Error.captureStackTrace(n,P),f.setHeader?(f[D]=!0,f.abort(),f.socket&&!f.socket.destroyed&&f.socket.destroy(),process.nextTick(A,e,n)):(f.destroy(n),f.once("error",e.emit.bind(e,"error")),f.once("close",e.emitClose.bind(e)))}function Q(e,f,t){if(f){let t=S(f).length;e._socket?e._sender._bufferedBytes+=t:e._bufferedAmount+=t}if(t){let f=Error(`WebSocket is not open: readyState ${e.readyState} (${E[e.readyState]})`);process.nextTick(t,f)}}function Y(e,f){let t=this[C];t._closeFrameReceived=!0,t._closeMessage=f,t._closeCode=e,void 0!==t._socket[C]&&(t._socket.removeListener("data",H),process.nextTick(F,t._socket),1005===e?t.close():t.close(e,f))}function R(){let e=this[C];e.isPaused||e._socket.resume()}function J(e){let f=this[C];void 0!==f._socket[C]&&(f._socket.removeListener("data",H),process.nextTick(F,f._socket),f.close(e[y])),f.emit("error",e)}function B(){this[C].emitClose()}function U(e,f){this[C].emit("message",e,f)}function G(e){let f=this[C];f._autoPong&&f.pong(e,!this._isServer,j),f.emit("ping",e)}function Z(e){this[C].emit("pong",e)}function F(e){e.resume()}function W(){let e;let f=this[C];this.removeListener("close",W),this.removeListener("data",H),this.removeListener("end",V),f._readyState=z.CLOSING,this._readableState.endEmitted||f._closeFrameReceived||f._receiver._writableState.errorEmitted||null===(e=f._socket.read())||f._receiver.write(e),f._receiver.end(),this[C]=void 0,clearTimeout(f._closeTimer),f._receiver._writableState.finished||f._receiver._writableState.errorEmitted?f.emitClose():(f._receiver.on("error",B),f._receiver.on("finish",B))}function H(e){this[C]._receiver.write(e)||this.pause()}function V(){let e=this[C];e._readyState=z.CLOSING,e._receiver.end(),this.end()}function X(){let e=this[C];this.removeListener("error",X),this.on("error",j),e&&(e._readyState=z.CLOSING,this.destroy())}Object.defineProperty(z,"CONNECTING",{enumerable:!0,value:E.indexOf("CONNECTING")}),Object.defineProperty(z.prototype,"CONNECTING",{enumerable:!0,value:E.indexOf("CONNECTING")}),Object.defineProperty(z,"OPEN",{enumerable:!0,value:E.indexOf("OPEN")}),Object.defineProperty(z.prototype,"OPEN",{enumerable:!0,value:E.indexOf("OPEN")}),Object.defineProperty(z,"CLOSING",{enumerable:!0,value:E.indexOf("CLOSING")}),Object.defineProperty(z.prototype,"CLOSING",{enumerable:!0,value:E.indexOf("CLOSING")}),Object.defineProperty(z,"CLOSED",{enumerable:!0,value:E.indexOf("CLOSED")}),Object.defineProperty(z.prototype,"CLOSED",{enumerable:!0,value:E.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(z.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(z.prototype,`on${e}`,{enumerable:!0,get(){for(let f of this.listeners(e))if(f[b])return f[m];return null},set(f){for(let f of this.listeners(e))if(f[b]){this.removeListener(e,f);break}"function"==typeof f&&this.addEventListener(e,f,{[b]:!0})}})}),z.prototype.addEventListener=I,z.prototype.removeEventListener=v,e.exports=z},76420:(e,f,t)=>{f.formatArgs=function(f){if(f[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+f[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;f.splice(1,0,t,"color: inherit");let n=0,a=0;f[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(a=n))}),f.splice(a,0,t)},f.save=function(e){try{e?f.storage.setItem("debug",e):f.storage.removeItem("debug")}catch(e){}},f.load=function(){let e;try{e=f.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},f.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},f.storage=function(){try{return localStorage}catch(e){}}(),f.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),f.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],f.log=console.debug||console.log||(()=>{}),e.exports=t(43095)(f);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},78122:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},81548:(e,f,t)=>{"use strict";let{kForOnEventAttribute:n,kListener:a}=t(91813),i=Symbol("kCode"),r=Symbol("kData"),s=Symbol("kError"),o=Symbol("kMessage"),u=Symbol("kReason"),c=Symbol("kTarget"),d=Symbol("kType"),l=Symbol("kWasClean");class h{constructor(e){this[c]=null,this[d]=e}get target(){return this[c]}get type(){return this[d]}}Object.defineProperty(h.prototype,"target",{enumerable:!0}),Object.defineProperty(h.prototype,"type",{enumerable:!0});class g extends h{constructor(e,f={}){super(e),this[i]=void 0===f.code?0:f.code,this[u]=void 0===f.reason?"":f.reason,this[l]=void 0!==f.wasClean&&f.wasClean}get code(){return this[i]}get reason(){return this[u]}get wasClean(){return this[l]}}Object.defineProperty(g.prototype,"code",{enumerable:!0}),Object.defineProperty(g.prototype,"reason",{enumerable:!0}),Object.defineProperty(g.prototype,"wasClean",{enumerable:!0});class w extends h{constructor(e,f={}){super(e),this[s]=void 0===f.error?null:f.error,this[o]=void 0===f.message?"":f.message}get error(){return this[s]}get message(){return this[o]}}Object.defineProperty(w.prototype,"error",{enumerable:!0}),Object.defineProperty(w.prototype,"message",{enumerable:!0});class p extends h{constructor(e,f={}){super(e),this[r]=void 0===f.data?null:f.data}get data(){return this[r]}}function M(e,f,t){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,t):e.call(f,t)}Object.defineProperty(p.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:g,ErrorEvent:w,Event:h,EventTarget:{addEventListener(e,f,t={}){let i;for(let i of this.listeners(e))if(!t[n]&&i[a]===f&&!i[n])return;if("message"===e)i=function(e,t){let n=new p("message",{data:t?e:e.toString()});n[c]=this,M(f,this,n)};else if("close"===e)i=function(e,t){let n=new g("close",{code:e,reason:t.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});n[c]=this,M(f,this,n)};else if("error"===e)i=function(e){let t=new w("error",{error:e,message:e.message});t[c]=this,M(f,this,t)};else{if("open"!==e)return;i=function(){let e=new h("open");e[c]=this,M(f,this,e)}}i[n]=!!t[n],i[a]=f,t.once?this.once(e,i):this.on(e,i)},removeEventListener(e,f){for(let t of this.listeners(e))if(t[a]===f&&!t[n]){this.removeListener(e,t);break}}},MessageEvent:p}},83090:(e,f,t)=>{"use strict";let{Duplex:n}=t(27910);function a(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function r(e){this.removeListener("error",r),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,f){let t=!0,s=new n({...f,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(f,t){let n=!t&&s._readableState.objectMode?f.toString():f;s.push(n)||e.pause()}),e.once("error",function(e){s.destroyed||(t=!1,s.destroy(e))}),e.once("close",function(){s.destroyed||s.push(null)}),s._destroy=function(f,n){if(e.readyState===e.CLOSED){n(f),process.nextTick(a,s);return}let i=!1;e.once("error",function(e){i=!0,n(e)}),e.once("close",function(){i||n(f),process.nextTick(a,s)}),t&&e.terminate()},s._final=function(f){if(e.readyState===e.CONNECTING){e.once("open",function(){s._final(f)});return}null!==e._socket&&(e._socket._writableState.finished?(f(),s._readableState.endEmitted&&s.destroy()):(e._socket.once("finish",function(){f()}),e.close()))},s._read=function(){e.isPaused&&e.resume()},s._write=function(f,t,n){if(e.readyState===e.CONNECTING){e.once("open",function(){s._write(f,t,n)});return}e.send(f,n)},s.on("end",i),s.on("error",r),s}},88573:(e,f,t)=>{f.formatArgs=function(f){if(f[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+f[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;f.splice(1,0,t,"color: inherit");let n=0,a=0;f[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(a=n))}),f.splice(a,0,t)},f.save=function(e){try{e?f.storage.setItem("debug",e):f.storage.removeItem("debug")}catch(e){}},f.load=function(){let e;try{e=f.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},f.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},f.storage=function(){try{return localStorage}catch(e){}}(),f.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),f.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],f.log=console.debug||console.log||(()=>{}),e.exports=t(29940)(f);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},91813:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},99270:(e,f,t)=>{"use strict";t.d(f,{A:()=>n});let n=(0,t(62688).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99616:(e,f,t)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=t(76420):e.exports=t(3890)}};