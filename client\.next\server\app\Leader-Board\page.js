(()=>{var e={};e.id=7127,e.ids=[7127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12331:(e,t,s)=>{Promise.resolve().then(s.bind(s,73243))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20181:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(60687),a=s(92449);s(43210);let l=({count:e})=>(0,r.jsxs)("svg",{className:"h-10 w-10 sm:h-12 sm:w-12",viewBox:"0 0 1550 1808",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M574.764 165.821C692.4 74.0944 857.6 74.0944 975.236 165.821L1262.05 386.652C1354.36 459.21 1407.96 571.58 1407.96 690.458V1117.54C1407.96 1236.42 1354.36 1348.79 1262.05 1421.35L975.236 1642.18C857.6 1733.91 692.4 1733.91 574.764 1642.18L287.951 1421.35C195.639 1348.79 142.037 1236.42 142.037 1117.54V690.458C142.037 571.58 195.639 459.21 287.951 386.652L574.764 165.821Z",fill:"#FDFEF9"}),(0,r.jsx)("mask",{id:"mask0",maskUnits:"userSpaceOnUse",x:"71",y:"180",width:"1408",height:"1484",children:(0,r.jsx)("path",{d:"M574.315 248.124C692.703 157.412 857.297 157.412 975.685 248.124L1262.5 468.955C1354.81 541.513 1408.41 653.884 1408.41 772.762V1199.84C1408.41 1318.72 1354.81 1431.09 1262.5 1503.65L975.685 1724.48C857.297 1815.19 692.703 1815.19 574.315 1724.48L287.502 1503.65C195.19 1431.09 141.588 1318.72 141.588 1199.84V772.762C141.588 653.884 195.19 541.513 287.502 468.955L574.315 248.124Z",fill:"#CCCCCC"})}),(0,r.jsx)("g",{mask:"url(#mask0)",children:(0,r.jsx)("rect",{x:"48",y:"146",width:"1454",height:"821",fill:"#CCCCCC"})}),(0,r.jsx)("path",{d:"M658.6 1303.6C658.6 1257.73 713.743 1221.6 783 1221.6C852.257 1221.6 907.4 1257.73 907.4 1303.6C907.4 1328.13 861.847 1343.6 783 1343.6C704.153 1343.6 658.6 1328.13 658.6 1303.6Z",fill:"#CCCCCC"}),(0,r.jsx)("path",{d:"M771.553 390C809.071 390 837.553 418.482 837.553 456C837.553 475.694 829.359 495.486 814.097 508.145L776.947 539.162C772.316 543.05 766.79 545.197 761.062 545.197C755.335 545.197 749.809 543.05 745.178 539.162L707.303 508.09C691.856 495.367 683.553 475.418 683.553 455.5C683.553 417.982 712.035 390 749.553 390H771.553Z",fill:"white"}),(0,r.jsx)("path",{d:"M774.5 828C743.884 828 719 852.884 719 883.5C719 914.116 743.884 939 774.5 939C805.116 939 830 914.116 830 883.5C830 852.884 805.116 828 774.5 828Z",fill:"#CCCCCC"}),(0,r.jsx)("path",{d:"M1043.07 450.583L1028.87 437.844C1028.55 437.56 1028.07 437.593 1027.79 437.917L1002.56 467.325C1002.28 467.648 1002.31 468.13 1002.64 468.413L1016.84 481.152C1017.16 481.436 1017.64 481.403 1017.93 481.079L1043.16 451.671C1043.44 451.348 1043.41 450.866 1043.07 450.583Z",fill:"white"}),(0,r.jsx)("path",{d:"M1044.49 451.93C1044.25 451.709 1043.87 451.743 1043.66 452.012L1018.13 484.509C1017.92 484.778 1017.95 485.158 1018.22 485.371L1020.46 487.151C1020.73 487.365 1021.11 487.331 1021.32 487.062L1046.85 454.565C1047.06 454.296 1047.03 453.916 1046.76 453.702L1044.49 451.93Z",fill:"white"}),(0,r.jsx)("path",{d:"M1044.53 453.612L1018.39 486.109C1018.17 486.385 1018.2 486.794 1018.46 487.033L1023.27 491.522C1023.53 491.76 1023.95 491.73 1024.18 491.454L1050.32 458.957C1050.54 458.681 1050.51 458.272 1050.25 458.033L1045.44 453.544C1045.18 453.306 1044.77 453.336 1044.53 453.612Z",fill:"white"}),(0,r.jsx)("text",{x:"50%",y:"80%",textAnchor:"middle",fill:"#222",fontSize:"300",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:e})]});var i=s(30474);function n({badge:e}){return e?.badges?.length?(0,r.jsx)("div",{className:"flex gap-3 mt-2",children:e.badges.map((e,t)=>(0,r.jsx)(a.P.div,{className:"relative",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},children:"DailyStreak"===e.badgeType?(0,r.jsx)(l,{count:e.count??0}):(0,r.jsx)(i.default,{src:e.badgeSrc??"/placeholder.png",alt:e.badgeAlt??"Badge",width:48,height:48,className:"object-contain sm:w-12 sm:h-12 w-10 h-10"})},t))}):null}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54884:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),l=s(29523);s(28527);let i=(0,s(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var n=s(92449),d=s(90269),o=s(46303),x=s(30474),c=s(69587),m=s(20181);let p=["Today","Weekly","All time"];function u(){let[e,t]=(0,a.useState)("Today"),[s,u]=(0,a.useState)([]),[h,g]=(0,a.useState)(1),[f,b]=(0,a.useState)(1),[j,w]=(0,a.useState)(!1),[v,C]=(0,a.useState)(null),y=e=>e>=100&&e<=499?"/scholer.svg":e>=500&&e<=999?"/Mastermind.svg":e>=1e3?"/Achiever.svg":null,N=s.slice(0,3),_=s.slice(3),L=(e,t=96,s=!1)=>{let a=e.profileImage&&""!==e.profileImage.trim()?(0,r.jsx)(x.default,{src:e.profileImage,alt:`${e.firstName||""} ${e.lastName||""}`,width:t,height:t,className:"rounded-full object-cover"}):(0,r.jsx)("div",{style:{width:t,height:t},className:"flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-lg sm:text-xl md:text-2xl border-4 border-customOrange",children:(e.firstName?.charAt(0)||"")+(e.lastName?.charAt(0)||"")});return s?(0,r.jsx)(n.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:a}):a};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.default,{}),(0,r.jsx)("div",{className:"min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center",children:(0,r.jsxs)("div",{className:"w-full max-w-5xl space-y-6 sm:space-y-8 pt-8",children:[(0,r.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange",children:"Daily Quiz Leaderboard"}),(0,r.jsx)("div",{className:"flex justify-center gap-4 sm:gap-10 overflow-x-auto",children:p.map(s=>(0,r.jsx)(l.$,{variant:e===s?"default":"outline",className:`rounded-full px-4 sm:px-6 py-1 sm:py-2 text-sm sm:text-base font-semibold ${e===s?"bg-customOrange text-white":"border-orange-400 text-orange-400"} whitespace-nowrap`,"aria-label":`Select ${s} leaderboard`,onClick:()=>{t(s),b(1)},children:s},s))}),j&&(0,r.jsx)("p",{className:"text-center text-gray-500",children:"Loading..."}),v&&(0,r.jsx)("p",{className:"text-center text-red-500",children:v}),!j&&!v&&(0,r.jsx)("div",{className:"flex flex-col sm:flex-row justify-around items-center sm:items-end gap-4 sm:gap-6 mt-6 sm:mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg",children:N.map((e,t)=>(0,r.jsx)(n.P.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2*t},className:`flex flex-col items-center ${0===t?"order-2":1===t?"order-1":"order-3"}`,children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,r.jsxs)("div",{className:`relative rounded-full border-4 p-2 ${0===t?"shadow-2xl scale-110 border-customOrange":"border-orange-500"}`,children:[0===t&&(0,r.jsx)(i,{className:"absolute -top-6 sm:-top-8 left-1/2 -translate-x-1/2 text-customOrange w-6 sm:w-8 h-6 sm:h-8"}),L(e,64,0===t),(0,r.jsx)("div",{className:`absolute -bottom-4 sm:-bottom-5 left-1/2 -translate-x-1/2 rounded-full flex items-center justify-center font-bold ${0===t?"w-7 h-7 sm:w-9 sm:h-9 bg-orange-500 text-white shadow-lg border-4 border-orange-500":1===t?"w-6 h-6 sm:w-8 sm:h-8 bg-orange-500 text-white shadow border-4 border-orange-500":"w-5 h-5 sm:w-7 sm:h-7 bg-orange-500 text-white border-4 border-orange-500"}`,children:e.rank})]}),(0,r.jsxs)("p",{className:"mt-6 sm:mt-8 font-semibold text-base sm:text-lg text-center",children:[e.firstName," ",e.lastName]}),(0,r.jsx)("div",{className:"mt-2 w-full flex justify-center",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 sm:gap-3",children:[y(e.coinEarnings)&&(0,r.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,r.jsx)(x.default,{src:y(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,r.jsx)(m.A,{badge:e.badge})]})}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-2 sm:gap-5 mt-2",children:[(0,r.jsx)("div",{className:`px-3 sm:px-4 py-1 rounded-full border border-orange-300 text-orange-600 font-bold text-xs sm:text-sm ${0===t?"bg-green-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"}`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.lHQ,{})," ",e.score]})}),(0,r.jsxs)("div",{className:`px-3 sm:px-4 py-1 rounded-full border border-green-300 text-green-600 font-bold text-xs sm:text-sm flex items-center gap-1 ${0===t?"bg-green-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"}`,children:[(0,r.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,r.jsxs)("div",{className:`px-3 sm:px-4 py-1 rounded-full border border-blue-300 text-blue-600 font-bold text-xs sm:text-sm ${0===t?"bg-blue-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-blue-100"}`,children:["\uD83D\uDD25 ",e.streakCount]})]})]})},e.studentId))}),!j&&!v&&(0,r.jsx)("div",{className:"rounded-lg mt-6 sm:mt-10 bg-white space-y-3 sm:space-y-4",children:_.map(e=>(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between p-3 sm:p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 sm:gap-4 w-full sm:w-auto",children:[(0,r.jsx)("div",{className:"relative flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-orange-100 text-orange-500 font-bold text-sm sm:text-lg",children:e.rank}),L(e,48),(0,r.jsxs)("p",{className:"font-semibold text-base sm:text-lg text-black",children:[e.firstName," ",e.lastName]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3 sm:gap-5 mt-3 sm:mt-0 w-full sm:w-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[y(e.coinEarnings)&&(0,r.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,r.jsx)(x.default,{src:y(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,r.jsx)(m.A,{badge:e.badge})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap justify-center",children:[(0,r.jsx)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-orange-300 bg-orange-100 text-orange-600",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.lHQ,{className:"mr-1"})," ",e.score]})}),(0,r.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-green-300 bg-green-100 text-green-700 flex items-center gap-1",children:[(0,r.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,r.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-blue-300 bg-blue-100 text-blue-700",children:["\uD83D\uDD25 ",e.streakCount]})]})]})]},e.studentId))}),!j&&!v&&s.length>0&&(0,r.jsxs)("div",{className:"flex justify-center gap-4 mt-6 sm:mt-8",children:[(0,r.jsx)(l.$,{disabled:1===f,onClick:()=>b(e=>e-1),className:"px-4 sm:px-6 py-1 sm:py-2 rounded-full bg-customOrange text-white disabled:bg-gray-300 text-sm sm:text-base","aria-label":"Go to previous page",children:"Previous"}),(0,r.jsxs)("span",{className:"flex items-center text-sm sm:text-lg",children:["Page ",f," of ",h]}),(0,r.jsx)(l.$,{disabled:f===h,onClick:()=>b(e=>e+1),className:"px-4 sm:px-6 py-1 sm:py-2 rounded-full bg-customOrange text-white disabled:bg-gray-300 text-sm sm:text-base","aria-label":"Go to next page",children:"Next"})]})]})}),(0,r.jsx)(o.default,{})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64151:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>x,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let o={children:["",{children:["Leader-Board",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73243)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/Leader-Board/page",pathname:"/Leader-Board",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},72003:(e,t,s)=>{Promise.resolve().then(s.bind(s,54884))},73243:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\Leader-Board\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8721,2800],()=>s(64151));module.exports=r})();