/******/ (() => { // webpackBootstrap
/*!********************************************************!*\
  !*** ./modules/ClassWork/resources/views/js/create.js ***!
  \********************************************************/
$("#createclassWork_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandlercircular(form, classWorksRoute.store, 'post', '#createclassWork_form', '#saveClassWorks', '#newClassWorkEntry', '#classWork_table');
    return false;
  }
});
/******/ })()
;