"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6046],{19946:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),c=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:c,className:l="",children:s,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...u,width:o,height:o,stroke:n,strokeWidth:c?24*Number(a)/Number(o):a,className:i("lucide",l),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),s=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:u,...s}=n;return(0,r.createElement)(l,{ref:a,iconNode:t,className:i("lucide-".concat(o(c(e))),"lucide-".concat(e),u),...s})});return n.displayName=c(e),n}},31114:(e,t,n)=>{n.d(t,{A:()=>F});var r,o=n(39249),a=n(12115),c="right-scroll-bar-position",i="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var l="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,s=new WeakMap;function d(e){return e}var f=function(e){void 0===e&&(e={});var t,n,r,a,c=(t=null,void 0===n&&(n=d),r=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,a);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){a=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},c=function(){return Promise.resolve().then(o)};c(),r={push:function(e){t.push(e),c()},filter:function(e){return t=t.filter(e),r}}}});return c.options=(0,o.Cl)({async:!0,ssr:!1},e),c}(),h=function(){},p=a.forwardRef(function(e,t){var n,r,c,i,d=a.useRef(null),p=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],m=p[1],g=e.forwardProps,y=e.children,w=e.className,b=e.removeScrollBar,E=e.enabled,C=e.shards,k=e.sideCar,S=e.noIsolation,A=e.inert,M=e.allowPinchZoom,N=e.as,R=e.gapMode,O=(0,o.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=(n=[d,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(c=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return c.value},set current(value){var e=c.value;e!==value&&(c.value=value,c.callback(value,e))}}}})[0]).callback=r,i=c.facade,l(function(){var e=s.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}s.set(i,n)},[n]),i),L=(0,o.Cl)((0,o.Cl)({},O),v);return a.createElement(a.Fragment,null,E&&a.createElement(k,{sideCar:f,removeScrollBar:b,shards:C,noIsolation:S,inert:A,setCallbacks:m,allowPinchZoom:!!M,lockRef:d,gapMode:R}),g?a.cloneElement(a.Children.only(y),(0,o.Cl)((0,o.Cl)({},L),{ref:x})):a.createElement(void 0===N?"div":N,(0,o.Cl)({},L,{className:w,ref:x}),y))});p.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},p.classNames={fullWidth:i,zeroRight:c};var v=function(e){var t=e.sideCar,n=(0,o.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,o.Cl)({},n))};v.isSideCarExport=!0;var m=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,c;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),c=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(c)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=m();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=y(),S="data-scroll-locked",A=function(e,t,n,r){var o=e.left,a=e.top,u=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(i," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},N=function(){a.useEffect(function(){return document.body.setAttribute(S,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},R=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var c=a.useMemo(function(){return C(o)},[o]);return a.createElement(k,{styles:A(c,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var x=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",x,x),window.removeEventListener("test",x,x)}catch(e){O=!1}var L=!!O&&{passive:!1},T=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},P=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=j(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?T(t,"overflowY"):T(t,"overflowX")},j=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var a,c=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=c*r,u=n.target,l=t.contains(u),s=!1,d=i>0,f=0,h=0;do{var p=j(e,u),v=p[0],m=p[1]-p[2]-c*v;(v||m)&&W(e,u)&&(f+=m,h+=v),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&i>f)?s=!0:!d&&(o&&1>Math.abs(h)||!o&&-i>h)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},X=function(e){return[e.deltaX,e.deltaY]},I=function(e){return e&&"current"in e?e.current:e},Y=0,Z=[];let D=(f.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),c=a.useState(Y++)[0],i=a.useState(y)[0],u=a.useRef(e);a.useEffect(function(){u.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(c));var t=(0,o.fX)([e.lockRef.current],(e.shards||[]).map(I),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(c))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=B(e),c=n.current,i="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(i)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=P(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=P(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(i||l)&&(r.current=o),!o)return!0;var h=r.current||o;return _(h,t,e,"h"===h?i:l,!0)},[]),s=a.useCallback(function(e){if(Z.length&&Z[Z.length-1]===i){var n="deltaY"in e?X(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(I).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=a.useCallback(function(e){n.current=B(e),r.current=void 0},[]),h=a.useCallback(function(t){d(t.type,X(t),t.target,l(t,e.lockRef.current))},[]),p=a.useCallback(function(t){d(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return Z.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",s,L),document.addEventListener("touchmove",s,L),document.addEventListener("touchstart",f,L),function(){Z=Z.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,L),document.removeEventListener("touchmove",s,L),document.removeEventListener("touchstart",f,L)}},[]);var v=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(c," {pointer-events: none;}\n  .allow-interactivity-").concat(c," {pointer-events: all;}\n")}):null,v?a.createElement(R,{gapMode:e.gapMode}):null)}),v);var z=a.forwardRef(function(e,t){return a.createElement(p,(0,o.Cl)({},e,{ref:t,sideCar:D}))});z.classNames=p.classNames;let F=z},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,c={},i=0,u=function(e){return e&&(e.host||u(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});c[n]||(c[n]=new WeakMap);var s=c[n],d=[],f=new Set,h=new Set(l),p=function(e){!(!e||f.has(e))&&(f.add(e),p(e.parentNode))};l.forEach(p);var v=function(e){!(!e||h.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),c=null!==t&&"false"!==t,i=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,i),s.set(e,u),d.push(e),1===i&&c&&a.set(e,!0),1===u&&e.setAttribute(n,"true"),c||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),i++,function(){d.forEach(function(e){var t=o.get(e)-1,c=s.get(e)-1;o.set(e,t),s.set(e,c),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),c||e.removeAttribute(n)}),--i||(o=new WeakMap,o=new WeakMap,a=new WeakMap,c={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),l(o,a,n,"aria-hidden")):function(){return null}}},39249:(e,t,n)=>{n.d(t,{C6:()=>o,Cl:()=>a,Tt:()=>c,fX:()=>i});var r=function(e,t){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function c(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;function i(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError}}]);