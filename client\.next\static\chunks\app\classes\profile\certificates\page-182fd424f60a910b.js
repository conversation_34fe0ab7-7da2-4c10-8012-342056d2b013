(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9236],{4418:(e,t,a)=>{"use strict";a.d(t,{CertificateForm:()=>w});var r=a(95155),s=a(12115),i=a(62177),n=a(90221),o=a(55594),c=a(56671),l=a(75937),d=a(62523),u=a(30285),f=a(47262),p=a(55077),m=a(34540),h=a(94314),g=a(45436),x=a(35695),v=a(62525),b=a(54165);let j=o.z.object({title:o.z.string().min(2,"Certificate title is required"),file:o.z.custom(e=>e instanceof FileList&&e.length>0,{message:"Certificate file is required"})}),y=o.z.object({noCertificates:o.z.boolean().optional(),certificates:o.z.array(j).optional()}),C=JSON.parse(localStorage.getItem("user")||"{}");function w(){var e;let[t,a]=(0,s.useState)(!1),[o,j]=(0,s.useState)(!1),w=(0,m.wA)(),k=(0,x.useRouter)(),N=(0,i.mN)({resolver:(0,n.u)(y),defaultValues:{noCertificates:!1,certificates:[{title:"",file:void 0}]}}),{fields:S,append:z,remove:I}=(0,i.jz)({control:N.control,name:"certificates"}),R=async e=>{if(e.noCertificates){_();return}if(!e.certificates||0===e.certificates.length){c.oR.error("Please add at least one certificate record");return}let t=new FormData;t.append("noCertificates","false"),t.append("certificates",JSON.stringify(e.certificates)),e.certificates.forEach(e=>{e.file instanceof FileList&&t.append("files",e.file[0])});try{await p.S.post("/classes-profile/certificates",t,{headers:{"Content-Type":"multipart/form-data"}}),await w((0,g.V)(C.id)),c.oR.success("Certificates uploaded successfully"),w((0,h.ac)(h._3.CERTIFICATES)),k.push("/classes/profile/tution-class")}catch(e){c.oR.error("Something went wrong"),console.log(e)}},E=(0,m.d4)(e=>e.class.classData);s.useEffect(()=>{if(E&&!o){var e;(null===(e=E.certificates)||void 0===e?void 0:e.some(e=>!1===e.isCertificate))&&(a(!0),N.setValue("noCertificates",!0),N.setValue("certificates",[]),c.oR.info("You have selected 'I don't have any certificates'. You cannot add certificate data unless you uncheck this option.")),j(!0)}},[E,N,o]);let _=async()=>{let e=new FormData;e.append("noCertificates","true");try{await p.S.post("/classes-profile/certificates",e,{headers:{"Content-Type":"multipart/form-data"}}),await w((0,g.V)(C.id)),c.oR.success("No certificates status saved"),w((0,h.ac)(h._3.CERTIFICATES)),k.push("/classes/profile/tution-class")}catch(e){c.oR.error("Something went wrong"),console.log(e)}},F=async()=>{let e=new FormData;e.append("noCertificates","false");try{await p.S.post("/classes-profile/certificates",e,{headers:{"Content-Type":"multipart/form-data"}}),await w((0,g.V)(C.id)),c.oR.success("You can now add your certificate details")}catch(e){c.oR.error("Something went wrong"),console.log(e)}},T=async(e,t)=>{try{await p.S.delete("/classes-profile/certificate/".concat(e),{data:{classId:t}}),c.oR.success("Certificate deleted successfully"),await w((0,g.V)(t)),N.reset({noCertificates:!1,certificates:[{title:"",file:void 0}]})}catch(e){c.oR.error("Failed to delete certificate"),console.log(e)}};return(0,r.jsx)(l.lV,{...N,children:(0,r.jsxs)("form",{onSubmit:N.handleSubmit(R),className:"space-y-6",children:[(0,r.jsx)(l.zB,{control:N.control,name:"noCertificates",render:e=>{let{field:t}=e;return(0,r.jsxs)(l.eI,{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.MJ,{children:(0,r.jsx)(f.S,{checked:t.value,onCheckedChange:e=>{t.onChange(e),a(!!e),e?_():F()}})}),(0,r.jsx)(l.lR,{className:"font-medium",children:"I dont have any certificates"})]})}}),(null==E?void 0:null===(e=E.certificates)||void 0===e?void 0:e.length)>0&&!t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Previous Certificates"}),E.certificates.map((e,t)=>(0,r.jsx)("div",{className:"rounded-2xl border bg-muted/20 p-4 shadow-sm space-y-1",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"font-medium",children:e.title}),e.certificateUrl&&(0,r.jsx)("a",{href:"".concat("http://localhost:4005/","uploads/classes/").concat(E.id,"/certificates/").concat(e.certificateUrl),target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 underline",children:"View Uploaded Certificate"})]}),(0,r.jsxs)(b.lG,{children:[(0,r.jsx)(b.zM,{asChild:!0,children:(0,r.jsx)(u.$,{variant:"ghost",size:"icon",className:"text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50",children:(0,r.jsx)(v.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(b.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(b.c7,{children:[(0,r.jsx)(b.L3,{children:"Delete Certificate"}),(0,r.jsx)(b.rr,{children:"Are you sure you want to delete this certificate? This action cannot be undone."})]}),(0,r.jsxs)(b.Es,{className:"gap-2",children:[(0,r.jsx)(u.$,{variant:"outline",onClick:()=>document.querySelector('button[data-state="open"]').click(),children:"Cancel"}),(0,r.jsx)(u.$,{variant:"destructive",onClick:()=>{T(e.id,E.id),document.querySelector('button[data-state="open"]').click()},children:"Delete"})]})]})]})]})},t))]}),!t&&S.map((e,t)=>(0,r.jsxs)("div",{className:"space-y-4 rounded-2xl border bg-muted/30 p-4 shadow-sm",children:[(0,r.jsx)(l.zB,{control:N.control,name:"certificates.".concat(t,".title"),render:e=>{let{field:t}=e;return(0,r.jsxs)(l.eI,{children:[(0,r.jsx)(l.lR,{children:"Certificate Title"}),(0,r.jsx)(l.MJ,{children:(0,r.jsx)(d.p,{placeholder:"e.g. Teaching Excellence",...t})}),(0,r.jsx)(l.C5,{})]})}}),(0,r.jsx)(l.zB,{control:N.control,name:"certificates.".concat(t,".file"),render:e=>{let{field:t}=e;return(0,r.jsxs)(l.eI,{children:[(0,r.jsx)(l.lR,{children:"Upload Certificate"}),(0,r.jsx)(l.MJ,{children:(0,r.jsx)(d.p,{type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:e=>{let a=e.target.files;if(a&&a.length>0){if(!["application/pdf","image/jpeg","image/jpg","image/png"].includes(a[0].type)){c.oR.error("Only PDF and image files (.pdf, .jpg, .jpeg, .png) are allowed"),e.target.value="";return}t.onChange(a)}}})}),(0,r.jsx)(l.C5,{})]})}}),S.length>1&&(0,r.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>I(t),children:"Remove"})]},e.id)),!t&&(0,r.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>z({title:"",file:void 0}),className:"flex items-center gap-2",children:"Add New Certificate"}),(0,r.jsx)(u.$,{type:"submit",children:"Save Certificates"})]})})}},5196:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},22346:(e,t,a)=>{"use strict";a.d(t,{Separator:()=>n});var r=a(95155);a(12115);var s=a(14050),i=a(59434);function n(e){let{className:t,orientation:a="horizontal",decorative:n=!0,...o}=e;return(0,r.jsx)(s.b,{"data-slot":"separator-root",decorative:n,orientation:a,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},22807:(e,t,a)=>{Promise.resolve().then(a.bind(a,4418)),Promise.resolve().then(a.bind(a,22346))},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>c,r:()=>o});var r=a(95155);a(12115);var s=a(66634),i=a(74466),n=a(59434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:a,size:i,asChild:c=!1,...l}=e,d=c?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:i,className:t})),...l})}},45436:(e,t,a)=>{"use strict";a.d(t,{V:()=>s});var r=a(55077);let s=(0,a(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:a}=t;try{return(await r.S.get("/classes/details/".concat(e))).data}catch(e){var s;return a((null===(s=e.response)||void 0===s?void 0:s.data)||"Fetch failed")}})},47262:(e,t,a)=>{"use strict";a.d(t,{S:()=>o});var r=a(95155);a(12115);var s=a(14885),i=a(5196),n=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(i.A,{className:"size-3.5"})})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>p,L3:()=>m,c7:()=>f,lG:()=>o,rr:()=>h,zM:()=>c});var r=a(95155);a(12115);var s=a(4033),i=a(54416),n=a(59434);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,...o}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},55077:(e,t,a)=>{"use strict";a.d(t,{S:()=>n});var r=a(23464),s=a(56671);let i=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",i);let n=r.A.create({baseURL:i,headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":i;let a=localStorage.getItem("studentToken");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(s.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,a)=>{"use strict";a.d(t,{MB:()=>o,ZO:()=>n,cn:()=>i,xh:()=>c});var r=a(52596),s=a(39688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}let n=()=>localStorage.getItem("studentToken"),o=()=>{localStorage.removeItem("studentToken")},c=()=>!!n()},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var r=a(95155);a(12115);var s=a(59434);function i(e){let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>d,MJ:()=>x,Rr:()=>v,zB:()=>f,eI:()=>h,lR:()=>g,C5:()=>b});var r=a(95155),s=a(12115),i=a(66634),n=a(62177),o=a(59434),c=a(24265);function l(e){let{className:t,...a}=e;return(0,r.jsx)(c.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let d=n.Op,u=s.createContext({}),f=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(n.xI,{...t})})},p=()=>{let e=s.useContext(u),t=s.useContext(m),{getFieldState:a}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),i=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...i}},m=s.createContext({});function h(e){let{className:t,...a}=e,i=s.useId();return(0,r.jsx)(m.Provider,{value:{id:i},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...a})})}function g(e){let{className:t,...a}=e,{error:s,formItemId:i}=p();return(0,r.jsx)(l,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:i,...a})}function x(e){let{...t}=e,{error:a,formItemId:s,formDescriptionId:n,formMessageId:o}=p();return(0,r.jsx)(i.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!a,...t})}function v(e){let{className:t,...a}=e,{formDescriptionId:s}=p();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function b(e){var t;let{className:a,...s}=e,{error:i,formMessageId:n}=p(),c=i?String(null!==(t=null==i?void 0:i.message)&&void 0!==t?t:""):s.children;return c?(0,r.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",a),...s,children:c}):null}},94314:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>c,_3:()=>s,ac:()=>n});var r=a(51990),s=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let i=(0,r.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let a=t.payload;e.completedForms[a]||(e.completedForms[a]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:n,setCurrentStep:o}=i.actions,c=i.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,6046,4632,1342,3e3,8441,1684,7358],()=>t(22807)),_N_E=e.O()}]);