"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1739],{71739:(e,t,n)=>{n.d(t,{i3:()=>eO,UC:()=>eP,ZL:()=>eR,Kq:()=>eC,bL:()=>eT,l9:()=>eN});var r,o=n(12115),i=n.t(o,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return o.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}(...e),e)}var s=n(95155);function d(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,d=o.useMemo(()=>a,Object.values(a));return(0,s.jsx)(u.Provider,{value:d,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var c=n(47650),f=n(66634),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,f.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function v(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var m="dismissableLayer.update",h=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:y,onDismiss:w,...b}=e,E=o.useContext(h),[C,T]=o.useState(null),N=null!==(i=null==C?void 0:C.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,R]=o.useState({}),P=u(t,e=>T(e)),O=Array.from(E.layers),[L]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),j=O.indexOf(L),S=C?O.indexOf(C):-1,A=E.layersWithOutsidePointerEventsDisabled.size>0,k=S>=j,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=v(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!k||n||(null==c||c(e),null==y||y(e),e.defaultPrevented||null==w||w())},N),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=v(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==f||f(e),null==y||y(e),e.defaultPrevented||null==w||w())},N);return!function(e,t=globalThis?.document){let n=v(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S===E.layers.size-1&&(null==d||d(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},N),o.useEffect(()=>{if(C)return a&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),g(),()=>{a&&1===E.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[C,N,a,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),g())},[C,E]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(m,e),()=>document.removeEventListener(m,e)},[]),(0,s.jsx)(p.div,{...b,ref:P,style:{pointerEvents:A?k?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,M.onFocusCapture),onBlurCapture:l(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,D.onPointerDownCapture)})});function g(){let e=new CustomEvent(m);document.dispatchEvent(e)}function x(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&c.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(h),r=o.useRef(null),i=u(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var w=globalThis?.document?o.useLayoutEffect:()=>{},b=i[" useId ".trim().toString()]||(()=>void 0),E=0,C=n(84945),T=n(22475),N=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,s.jsx)(p.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});N.displayName="Arrow";var R="Popper",[P,O]=d(R),[L,j]=P(R),S=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,s.jsx)(L,{scope:t,anchor:r,onAnchorChange:i,children:n})};S.displayName=R;var A="PopperAnchor",k=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=j(A,n),a=o.useRef(null),d=u(t,a);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,s.jsx)(p.div,{...i,ref:d})});k.displayName=A;var D="PopperContent",[M,_]=P(D),I=o.forwardRef((e,t)=>{var n,r,i,l,a,d,c,f;let{__scopePopper:m,side:h="bottom",sideOffset:y=0,align:g="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:E=!0,collisionBoundary:N=[],collisionPadding:R=0,sticky:P="partial",hideWhenDetached:O=!1,updatePositionStrategy:L="optimized",onPlaced:S,...A}=e,k=j(D,m),[_,I]=o.useState(null),W=u(t,e=>I(e)),[U,B]=o.useState(null),$=function(e){let[t,n]=o.useState(void 0);return w(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(U),Y=null!==(c=null==$?void 0:$.width)&&void 0!==c?c:0,X=null!==(f=null==$?void 0:$.height)&&void 0!==f?f:0,V="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},q=Array.isArray(N)?N:[N],K=q.length>0,Z={padding:V,boundary:q.filter(z),altBoundary:K},{refs:G,floatingStyles:J,placement:Q,isPositioned:ee,middlewareData:et}=(0,C.we)({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,T.ll)(...t,{animationFrame:"always"===L})},elements:{reference:k.anchor},middleware:[(0,C.cY)({mainAxis:y+X,alignmentAxis:x}),E&&(0,C.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===P?(0,C.ER)():void 0,...Z}),E&&(0,C.UU)({...Z}),(0,C.Ej)({...Z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),U&&(0,C.UE)({element:U,padding:b}),F({arrowWidth:Y,arrowHeight:X}),O&&(0,C.jD)({strategy:"referenceHidden",...Z})]}),[en,er]=H(Q),eo=v(S);w(()=>{ee&&(null==eo||eo())},[ee,eo]);let ei=null===(n=et.arrow)||void 0===n?void 0:n.x,el=null===(r=et.arrow)||void 0===r?void 0:r.y,ea=(null===(i=et.arrow)||void 0===i?void 0:i.centerOffset)!==0,[eu,es]=o.useState();return w(()=>{_&&es(window.getComputedStyle(_).zIndex)},[_]),(0,s.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...J,transform:ee?J.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null===(l=et.transformOrigin)||void 0===l?void 0:l.x,null===(a=et.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(d=et.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(M,{scope:m,placedSide:en,onArrowChange:B,arrowX:ei,arrowY:el,shouldHideArrow:ea,children:(0,s.jsx)(p.div,{"data-side":en,"data-align":er,...A,ref:W,style:{...A.style,animation:ee?void 0:"none"}})})})});I.displayName=D;var W="PopperArrow",U={top:"bottom",right:"left",bottom:"top",left:"right"},B=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=_(W,n),i=U[o.placedSide];return(0,s.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(N,{...r,ref:t,style:{...r.style,display:"block"}})})});function z(e){return null!==e}B.displayName=W;var F=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,d=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=H(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(i=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+c/2,y=(null!==(l=null===(o=s.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,g="",x="";return"bottom"===p?(g=d?m:"".concat(h,"px"),x="".concat(-f,"px")):"top"===p?(g=d?m:"".concat(h,"px"),x="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),x=d?m:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),x=d?m:"".concat(y,"px")),{data:{x:g,y:x}}}});function H(e){let[t,n="center"]=e.split("-");return[t,n]}var $=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);w(()=>u(!0),[]);let d=i||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return d?c.createPortal((0,s.jsx)(p.div,{...l,ref:t}),d):null});$.displayName="Portal";var Y=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=X(l.current);u.current="mounted"===s?e:"none"},[s]),w(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=X(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),w(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=X(l.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=X(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=u(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function X(e){return(null==e?void 0:e.animationName)||"none"}Y.displayName="Presence";var V=i[" useInsertionEffect ".trim().toString()]||w,q=(Symbol("RADIX:SYNC_STATE"),Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"})),K=o.forwardRef((e,t)=>(0,s.jsx)(p.span,{...e,ref:t,style:{...q,...e.style}}));K.displayName="VisuallyHidden";var[Z,G]=d("Tooltip",[O]),J=O(),Q="TooltipProvider",ee="tooltip.open",[et,en]=Z(Q),er=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:l}=e,a=o.useRef(!0),u=o.useRef(!1),d=o.useRef(0);return o.useEffect(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,s.jsx)(et,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(d.current),a.current=!1},[]),onClose:o.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>a.current=!0,r)},[r]),isPointerInTransitRef:u,onPointerInTransitChange:o.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};er.displayName=Q;var eo="Tooltip",[ei,el]=Z(eo),ea=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,d=en(eo,e.__scopeTooltip),c=J(t),[f,p]=o.useState(null),v=function(e){let[t,n]=o.useState(b());return w(()=>{n(e=>e??String(E++))},[void 0]),e||(t?`radix-${t}`:"")}(),m=o.useRef(0),h=null!=a?a:d.disableHoverableContent,y=null!=u?u:d.delayDuration,g=o.useRef(!1),[x,C]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return V(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}({prop:r,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(ee))):d.onClose(),null==l||l(e)},caller:eo}),T=o.useMemo(()=>x?g.current?"delayed-open":"instant-open":"closed",[x]),N=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,C(!0)},[C]),R=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,C(!1)},[C]),P=o.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,C(!0),m.current=0},y)},[y,C]);return o.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,s.jsx)(S,{...c,children:(0,s.jsx)(ei,{scope:t,contentId:v,open:x,stateAttribute:T,trigger:f,onTriggerChange:p,onTriggerEnter:o.useCallback(()=>{d.isOpenDelayedRef.current?P():N()},[d.isOpenDelayedRef,P,N]),onTriggerLeave:o.useCallback(()=>{h?R():(window.clearTimeout(m.current),m.current=0)},[R,h]),onOpen:N,onClose:R,disableHoverableContent:h,children:n})})};ea.displayName=eo;var eu="TooltipTrigger",es=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=el(eu,n),a=en(eu,n),d=J(n),c=u(t,o.useRef(null),i.onTriggerChange),f=o.useRef(!1),v=o.useRef(!1),m=o.useCallback(()=>f.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,s.jsx)(k,{asChild:!0,...d,children:(0,s.jsx)(p.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...r,ref:c,onPointerMove:l(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||a.isPointerInTransitRef.current||(i.onTriggerEnter(),v.current=!0)}),onPointerLeave:l(e.onPointerLeave,()=>{i.onTriggerLeave(),v.current=!1}),onPointerDown:l(e.onPointerDown,()=>{i.open&&i.onClose(),f.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:l(e.onFocus,()=>{f.current||i.onOpen()}),onBlur:l(e.onBlur,i.onClose),onClick:l(e.onClick,i.onClose)})})});es.displayName=eu;var ed="TooltipPortal",[ec,ef]=Z(ed,{forceMount:void 0}),ep=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=el(ed,t);return(0,s.jsx)(ec,{scope:t,forceMount:n,children:(0,s.jsx)(Y,{present:n||i.open,children:(0,s.jsx)($,{asChild:!0,container:o,children:r})})})};ep.displayName=ed;var ev="TooltipContent",em=o.forwardRef((e,t)=>{let n=ef(ev,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=el(ev,e.__scopeTooltip);return(0,s.jsx)(Y,{present:r||l.open,children:l.disableHoverableContent?(0,s.jsx)(ew,{side:o,...i,ref:t}):(0,s.jsx)(eh,{side:o,...i,ref:t})})}),eh=o.forwardRef((e,t)=>{let n=el(ev,e.__scopeTooltip),r=en(ev,e.__scopeTooltip),i=o.useRef(null),l=u(t,i),[a,d]=o.useState(null),{trigger:c,onClose:f}=n,p=i.current,{onPointerInTransitChange:v}=r,m=o.useCallback(()=>{d(null),v(!1)},[v]),h=o.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),v(!0)},[v]);return o.useEffect(()=>()=>m(),[m]),o.useEffect(()=>{if(c&&p){let e=e=>h(e,p),t=e=>h(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,h,m]),o.useEffect(()=>{if(a){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,d=a.x,c=a.y;s>r!=c>r&&n<(d-u)*(r-s)/(c-s)+u&&(o=!o)}return o}(n,a);r?m():o&&(m(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,a,f,m]),(0,s.jsx)(ew,{...e,ref:l})}),[ey,eg]=Z(eo,{isInside:!1}),ex=(0,f.Dc)("TooltipContent"),ew=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:a,...u}=e,d=el(ev,n),c=J(n),{onClose:f}=d;return o.useEffect(()=>(document.addEventListener(ee,f),()=>document.removeEventListener(ee,f)),[f]),o.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,s.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,s.jsxs)(I,{"data-state":d.stateAttribute,...c,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,s.jsx)(ex,{children:r}),(0,s.jsx)(ey,{scope:n,isInside:!0,children:(0,s.jsx)(K,{id:d.contentId,role:"tooltip",children:i||r})})]})})});em.displayName=ev;var eb="TooltipArrow",eE=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=J(n);return eg(eb,n).isInside?null:(0,s.jsx)(B,{...o,...r,ref:t})});eE.displayName=eb;var eC=er,eT=ea,eN=es,eR=ep,eP=em,eO=eE}}]);