/******/ (() => { // webpackBootstrap
/*!**********************************************************!*\
  !*** ./modules/Testimonial/resources/views/js/create.js ***!
  \**********************************************************/
$("#createtestimonial_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createtestimonialRoute.store, 'post', '#createtestimonial_form', '#savetestimonial', '#newTestimonialEntry', '#testimonial_table');
    return false;
  }
});
/******/ })()
;