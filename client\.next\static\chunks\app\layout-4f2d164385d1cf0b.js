(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{30347:()=>{},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},35919:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,B:()=>l});var a=r(51990),o=r(45436);let n=(0,a.Z0)({name:"class",initialState:{classData:null,loading:!1,error:null},reducers:{setClassData(e,t){e.classData=t.payload}},extraReducers:e=>{e.addCase(o.V.pending,e=>{e.loading=!0,e.error=null}).addCase(o.V.fulfilled,(e,t)=>{e.loading=!1,e.classData=t.payload}).addCase(o.V.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setClassData:l}=n.actions,s=n.reducer},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return n}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},a=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function o(e){return["async","defer","noModule"].includes(e)}function n(e,t){for(let[n,l]of Object.entries(t)){if(!t.hasOwnProperty(n)||a.includes(n)||void 0===l)continue;let s=r[n]||n.toLowerCase();"SCRIPT"===e.tagName&&o(s)?e[s]=!!l:e.setAttribute(s,String(l)),(!1===l||"SCRIPT"===e.tagName&&o(s)&&(!l||"false"===l))&&(e.setAttribute(s,""),e.removeAttribute(s))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42890:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.t.bind(r,51992,23)),Promise.resolve().then(r.bind(r,65249)),Promise.resolve().then(r.bind(r,93709)),Promise.resolve().then(r.bind(r,47377))},45436:(e,t,r)=>{"use strict";r.d(t,{V:()=>o});var a=r(55077);let o=(0,r(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:r}=t;try{return(await a.S.get("/classes/details/".concat(e))).data}catch(e){var o;return r((null===(o=e.response)||void 0===o?void 0:o.data)||"Fetch failed")}})},47377:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>c});var a=r(95155),o=r(34540),n=r(51990),l=r(92560),s=r(94314),i=r(35919),d=r(93457);let u=(0,n.U1)({reducer:{user:l.Ay,formProgress:s.Ay,class:i.A,studentProfile:d.Ay}});function c(e){let{children:t}=e;return(0,a.jsx)(o.Kq,{store:u,children:t})}},51992:e=>{e.exports={style:{fontFamily:"'Balsamiq Sans', 'Balsamiq Sans Fallback'",fontStyle:"normal"},className:"__className_6c5adf"}},55077:(e,t,r)=>{"use strict";r.d(t,{S:()=>l});var a=r(23464),o=r(56671);let n=r(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let l=a.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});l.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":n;let r=localStorage.getItem("studentToken");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(o.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},56762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,N:()=>n});var a=r(55077),o=r(51990);let n=(0,o.zD)("studentProfile/fetchStudentProfile",async(e,t)=>{let{rejectWithValue:r}=t;try{let e=localStorage.getItem("studentToken");if(!e)return r("No authentication token found");let t=await a.S.get("/student-profile/all-data",{headers:{Authorization:"Bearer ".concat(e)}});if(t.data&&"object"==typeof t.data){if(void 0!==t.data.success&&void 0!==t.data.data)return t.data.data;return t.data}return null}catch(e){var o,n,l;if((null===(o=e.response)||void 0===o?void 0:o.status)===404)return null;return r((null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message)||"Failed to fetch student data")}}),l=(0,o.zD)("studentProfile/updateStudentProfile",async(e,t)=>{let{rejectWithValue:r}=t;try{let t=localStorage.getItem("studentToken");if(!t)return r("No authentication token found");let o=await (0,a.S)({method:"put",url:"/student-profile/combined",data:e,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}});if(o.data&&"object"==typeof o.data){if(void 0!==o.data.success&&void 0!==o.data.data)return o.data.data;return o.data}return null}catch(e){var o,n;return r((null===(n=e.response)||void 0===n?void 0:null===(o=n.data)||void 0===o?void 0:o.message)||"Failed to update student profile")}})},65249:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>m});var a=r(95155),o=r(12115),n="(prefers-color-scheme: dark)",l=o.createContext(void 0),s={setTheme:e=>{},themes:[]},i=()=>{var e;return null!=(e=o.useContext(l))?e:s},d=null,u=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},c=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},f=e=>(e||(e=window.matchMedia(n)),e.matches?"dark":"light"),p=r(56671);let m=e=>{let{...t}=e,{theme:r="system"}=i();return(0,a.jsx)(p.l$,{theme:r,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return v},handleClientScriptLoad:function(){return h},initScriptLoader:function(){return y}});let a=r(88229),o=r(6966),n=r(95155),l=a._(r(47650)),s=o._(r(12115)),i=r(82830),d=r(42714),u=r(92374),c=new Map,f=new Set,p=e=>{if(l.default.preinit){e.forEach(e=>{l.default.preinit(e,{as:"style"})});return}{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},m=e=>{let{src:t,id:r,onLoad:a=()=>{},onReady:o=null,dangerouslySetInnerHTML:n,children:l="",strategy:s="afterInteractive",onError:i,stylesheets:u}=e,m=r||t;if(m&&f.has(m))return;if(c.has(t)){f.add(m),c.get(t).then(a,i);return}let h=()=>{o&&o(),f.add(m)},y=document.createElement("script"),g=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),a&&a.call(this,t),h()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){i&&i(e)});n?(y.innerHTML=n.__html||"",h()):l?(y.textContent="string"==typeof l?l:Array.isArray(l)?l.join(""):"",h()):t&&(y.src=t,c.set(t,g)),(0,d.setAttributesFromProps)(y,e),"worker"===s&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",s),u&&p(u),document.body.appendChild(y)};function h(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>m(e))}):m(e)}function y(e){e.forEach(h),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function g(e){let{id:t,src:r="",onLoad:a=()=>{},onReady:o=null,strategy:d="afterInteractive",onError:c,stylesheets:p,...h}=e,{updateScripts:y,scripts:g,getIsSsr:v,appDir:S,nonce:_}=(0,s.useContext)(i.HeadManagerContext),b=(0,s.useRef)(!1);(0,s.useEffect)(()=>{let e=t||r;b.current||(o&&e&&f.has(e)&&o(),b.current=!0)},[o,t,r]);let P=(0,s.useRef)(!1);if((0,s.useEffect)(()=>{if(!P.current){if("afterInteractive"===d)m(e);else if("lazyOnload"===d)"complete"===document.readyState?(0,u.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>m(e))});P.current=!0}},[e,d]),("beforeInteractive"===d||"worker"===d)&&(y?(g[d]=(g[d]||[]).concat([{id:t,src:r,onLoad:a,onReady:o,onError:c,...h}]),y(g)):v&&v()?f.add(t||r):v&&!v()&&m(e)),S){if(p&&p.forEach(e=>{l.default.preinit(e,{as:"style"})}),"beforeInteractive"===d)return r?(l.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin}),(0,n.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...h,id:t}])+")"}})):(h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),(0,n.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...h,id:t}])+")"}}));"afterInteractive"===d&&r&&l.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let v=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return a},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},a="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92560:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,gV:()=>s,lM:()=>i});var a=r(51990);let o=localStorage.getItem("user"),n={user:o?JSON.parse(o):null,isAuthenticated:!!o},l=(0,a.Z0)({name:"user",initialState:n,reducers:{setUser:(e,t)=>{e.user=t.payload.user,e.isAuthenticated=!0,localStorage.setItem("user",JSON.stringify(t.payload.user))},clearUser:e=>{e.user=null,e.isAuthenticated=!1,localStorage.removeItem("user")}}}),{setUser:s,clearUser:i}=l.actions,d=l.reducer},93457:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,Ig:()=>i,XY:()=>s});var a=r(51990),o=r(56762);let n=(0,a.Z0)({name:"studentProfile",initialState:{profileData:null,loading:!1,error:null},reducers:{setStudentProfileData(e,t){var r,a;if(e.profileData=t.payload,null===(a=t.payload)||void 0===a?void 0:null===(r=a.profile)||void 0===r?void 0:r.photo)try{localStorage.setItem("student_profile_photo",t.payload.profile.photo)}catch(e){console.error("Failed to persist photo to localStorage:",e)}},updateProfilePhoto(e,t){var r;if(null===(r=e.profileData)||void 0===r?void 0:r.profile){e.profileData.profile.photo=t.payload;try{t.payload?localStorage.setItem("student_profile_photo",t.payload):localStorage.removeItem("student_profile_photo")}catch(e){console.error("Failed to persist photo to localStorage:",e)}}},clearStudentProfileData(e){e.profileData=null,e.loading=!1,e.error=null;try{localStorage.removeItem("student_profile_photo")}catch(e){console.error("Failed to clear photo from localStorage:",e)}}},extraReducers:e=>{e.addCase(o.N.pending,e=>{e.loading=!0,e.error=null}).addCase(o.N.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(o.N.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}).addCase(o.A.pending,e=>{e.loading=!0,e.error=null}).addCase(o.A.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(o.A.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setStudentProfileData:l,updateProfilePhoto:s,clearStudentProfileData:i}=n.actions,d=n.reducer},93709:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(35695),o=r(12115);let n=e=>{window.gtag("config","G-N06ZRQXN1Y",{page_path:e})};function l(){let e=(0,a.usePathname)(),t=(0,a.useSearchParams)();return(0,o.useEffect)(()=>{n(e+t.toString())},[e,t]),null}},94314:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,_3:()=>o,ac:()=>l});var a=r(51990),o=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let n=(0,a.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let r=t.payload;e.completedForms[r]||(e.completedForms[r]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:l,setCurrentStep:s}=n.actions,i=n.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,3005,7040,4540,1990,8441,1684,7358],()=>t(42890)),_N_E=e.O()}]);