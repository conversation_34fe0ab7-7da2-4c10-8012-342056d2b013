(()=>{var e={};e.id=8694,e.ids=[8694],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20001:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var a=r(60687),t=r(43210),n=r(44493),l=r(29523),i=r(89667),d=r(96834),c=r(12837),o=r(41312),x=r(93508);let m=(0,r(62688).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var p=r(81620),u=r(70615),h=r(25334),f=r(40228),j=r(52581),g=r(28527),y=r(79663),N=r(34179),v=r(46303),b=r(90269);function w(){let[e,s]=(0,t.useState)(null),[r,w]=(0,t.useState)(!0),[k,R]=(0,t.useState)({startDate:"",endDate:""}),E=[{accessorKey:"referredUserName",header:"Name",cell:({row:e})=>(0,a.jsx)("span",{className:"font-medium",children:e.original.referredUserName})},{accessorKey:"referredUserEmail",header:"Email",cell:({row:e})=>(0,a.jsx)("span",{className:"text-gray-700",children:e.original.referredUserEmail})},{accessorKey:"referredUserType",header:"Type",cell:({row:e})=>(0,a.jsx)(d.E,{variant:"STUDENT"===e.original.referredUserType?"default":"secondary",children:e.original.referredUserType})},{accessorKey:"earnings",header:()=>(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"Earnings Status"]}),cell:({row:e})=>{let{earnings:s}=e.original;return s&&s.length>0?(0,a.jsx)("div",{className:"space-y-1",children:s.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium",children:["₹",e.amount]}),(0,a.jsxs)("span",{className:"text-gray-500 ml-1",children:["(","REGISTRATION"===e.earningType?"Registration":"U-whiz",")"]})]}),(0,a.jsx)(d.E,{variant:"PAID"===e.paymentStatus?"default":"secondary",className:"PAID"===e.paymentStatus?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800",children:"PAID"===e.paymentStatus?"Paid":"Pending"})]},e.id))}):(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"No earnings yet"})}},{accessorKey:"createdAt",header:"Date",cell:({row:e})=>(0,a.jsx)("span",{className:"text-gray-700",children:(0,y.GP)(new Date(e.original.createdAt),"MMM dd, yyyy HH:mm")})}],P=async()=>{try{let e=await g.S.post("/referral/generate-link");e.data.success&&(s(s=>s?{...s,referralCode:e.data.data.referralCode,links:e.data.data.links}:null),j.oR.success("Referral links generated successfully!"))}catch(e){console.error("Error generating referral link:",e),j.oR.error("Failed to generate referral links")}},S=(e,s)=>{navigator.clipboard.writeText(e),j.oR.success(`${s} link copied to clipboard!`)};return((0,t.useCallback)(async()=>{try{let e=new URLSearchParams;k.startDate&&e.append("startDate",k.startDate),k.endDate&&e.append("endDate",k.endDate);let r=await g.S.get(`/referral/history?${e.toString()}`);r.data.success&&s(e=>e?{...e,history:r.data.data.history}:null)}catch(e){console.error("Error fetching history:",e),j.oR.error("Failed to load referral history")}},[k.startDate,k.endDate]),r)?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.default,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-7xl",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Referral Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track your referrals and earn rewards"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Referrals"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:e?.totalReferrals||0})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Students Referred"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e?.studentsReferred||0})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Classes Referred"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e?.classesReferred||0})})]})]}),e?.earnings&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Earnings"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["₹",e.earnings.totalEarnings]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Registration + U-whiz"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Registration Earnings"}),(0,a.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:["₹",e.earnings.registrationEarnings]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"₹10 per student"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"U-whiz Earnings"}),(0,a.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["₹",e.earnings.uwhizEarnings]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"₹25 per application"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Payment Status"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsxs)("span",{className:"text-green-600 font-semibold",children:["Paid: ₹",e.earnings.paidEarnings]})}),(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsxs)("span",{className:"text-orange-600 font-semibold",children:["Pending: ₹",e.earnings.unpaidEarnings]})})]})})]})]}),(0,a.jsxs)(n.Zp,{className:"mb-8",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),"My Referral Links"]}),(0,a.jsx)(n.BT,{children:"Share these links to refer new students and classes"})]}),(0,a.jsx)(n.Wu,{className:"space-y-4",children:e?.links?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Student Registration Link"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.p,{value:e.links.studentLink,readOnly:!0,className:"flex-1"}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>S(e.links.studentLink,"Student"),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.links.studentLink,"_blank"),children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Classes Registration Link"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.p,{value:e.links.classLink,readOnly:!0,className:"flex-1"}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>S(e.links.classLink,"Classes"),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.links.classLink,"_blank"),children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"No referral links generated yet"}),(0,a.jsx)(l.$,{onClick:P,className:"bg-orange-500 hover:bg-orange-600",children:"Generate Referral Links"})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Referral History"]}),(0,a.jsx)(n.BT,{children:"Track all your successful referrals"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"flex gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Start Date"}),(0,a.jsx)(i.p,{type:"date",value:k.startDate,onChange:e=>R(s=>({...s,startDate:e.target.value}))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"End Date"}),(0,a.jsx)(i.p,{type:"date",value:k.endDate,onChange:e=>R(s=>({...s,endDate:e.target.value}))})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)(l.$,{variant:"outline",onClick:()=>R({startDate:"",endDate:""}),children:"Clear Filter"})})]}),(0,a.jsx)(N.E,{columns:E,data:e?.history||[],fetchData:()=>Promise.resolve(),totalItems:e?.history.length||0,isLoading:r,hidePagination:!0})]})]})]}),(0,a.jsx)(v.default,{})]})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36205:(e,s,r)=>{Promise.resolve().then(r.bind(r,77210))},37129:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=r(65239),t=r(48088),n=r(88170),l=r.n(n),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["classes",{children:["referral-dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,77210)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/classes/referral-dashboard/page",pathname:"/classes/referral-dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},42123:(e,s,r)=>{"use strict";r.d(s,{b:()=>o});var a=r(43210);r(51215);var t=r(11329),n=r(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,t.TL)(`Primitive.${s}`),l=a.forwardRef((e,a)=>{let{asChild:t,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(t?r:s,{...l,ref:a})});return l.displayName=`Primitive.${s}`,{...e,[s]:l}},{}),i="horizontal",d=["horizontal","vertical"],c=a.forwardRef((e,s)=>{var r;let{decorative:a,orientation:t=i,...c}=e,o=(r=t,d.includes(r))?t:i;return(0,n.jsx)(l.div,{"data-orientation":o,...a?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var o=c},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77210:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\referral-dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83469:(e,s,r)=>{Promise.resolve().then(r.bind(r,20001))},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[4447,8721,9191,9663,7490,2800,7200,7736],()=>r(37129));module.exports=a})();