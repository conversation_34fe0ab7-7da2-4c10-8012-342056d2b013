import { Router } from 'express';
import {
  createCategoryController,
  getAllCategoriesController,
  getCategoryByIdController,
  updateCategoryController,
  deleteCategoryController,
  createDetailController,
  getDetailsByCategoryController,
  getDetailByIdController,
  updateDetailController,
  deleteDetailController,
  createSubDetailController,
  getSubDetailsByDetailController,
  getSubDetailByIdController,
  updateSubDetailController,
  deleteSubDetailController,
  createValueController,
  getValuesBySubDetailController,
  getValueByIdController,
  updateValueController,
  deleteValueController,

} from '../controllers/constantController';
import {
  createCategorySchema,
  updateCategorySchema,
  createDetailSchema,
  updateDetailSchema,
  createSubDetailSchema,
  updateSubDetailSchema,
  createValueSchema,
  updateValueSchema
} from '../requests/constantRequest';
import validateRequest from '@/middlewares/validateRequest';
import { authMiddleware } from '@/middlewares/adminAuth';

const adminConstantRouter = Router();

// Apply auth middleware to all routes
adminConstantRouter.use(authMiddleware);

// ConstantCategory Routes
adminConstantRouter.post('/categories', validateRequest(createCategorySchema), createCategoryController);
adminConstantRouter.get('/categories', getAllCategoriesController); // Use admin controller with proper response format
adminConstantRouter.get('/categories/:id', getCategoryByIdController);
adminConstantRouter.put('/categories/:id', validateRequest(updateCategorySchema), updateCategoryController);
adminConstantRouter.delete('/categories/:id', deleteCategoryController);

// ConstantDetail Routes
adminConstantRouter.post('/details', validateRequest(createDetailSchema), createDetailController);
adminConstantRouter.get('/details/category/:categoryId', getDetailsByCategoryController);
adminConstantRouter.get('/details/:id', getDetailByIdController);
adminConstantRouter.put('/details/:id', validateRequest(updateDetailSchema), updateDetailController);
adminConstantRouter.delete('/details/:id', deleteDetailController);

// ConstantSubDetail Routes
adminConstantRouter.post('/sub-details', validateRequest(createSubDetailSchema), createSubDetailController);
adminConstantRouter.get('/sub-details/detail/:detailId', getSubDetailsByDetailController);
adminConstantRouter.get('/sub-details/:id', getSubDetailByIdController);
adminConstantRouter.put('/sub-details/:id', validateRequest(updateSubDetailSchema), updateSubDetailController);
adminConstantRouter.delete('/sub-details/:id', deleteSubDetailController);

// ConstantSubDetailValue Routes
adminConstantRouter.post('/values', validateRequest(createValueSchema), createValueController);
adminConstantRouter.get('/values/sub-detail/:subDetailId', getValuesBySubDetailController);
adminConstantRouter.get('/values/:id', getValueByIdController);
adminConstantRouter.put('/values/:id', validateRequest(updateValueSchema), updateValueController);
adminConstantRouter.delete('/values/:id', deleteValueController);

export default adminConstantRouter;
