(()=>{var e={};e.id=6915,e.ids=[6915],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},21165:()=>{},21820:e=>{"use strict";e.exports=require("os")},26334:(e,t,r)=>{Promise.resolve().then(r.bind(r,97727))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42123:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(43210);r(51215);var s=r(11329),l=r(60687),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?r:t,{...n,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),o="horizontal",i=["horizontal","vertical"],d=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:s=o,...d}=e,c=(r=s,i.includes(r))?s:o;return(0,l.jsx)(n.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var c=d},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>c});var a=r(60687);r(43210);var s=r(4780);function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},46739:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(65239),s=r(48088),l=r(88170),n=r.n(l),o=r(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let d={children:["",{children:["classes",{children:["blogs",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90129)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/classes/blogs/add/page",pathname:"/classes/blogs/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},49587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let a=r(14985)._(r(64963));function s(e,t){var r;let s={};"function"==typeof e&&(s.loader=e);let l={...s,...t};return(0,a.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let a=r(81208);function s(e){let{reason:t,children:r}=e;throw Object.defineProperty(new a.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(43210);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var l=r(60687),n=Symbol("radix.slottable");function o(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===n}var i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...l}=e;if(a.isValidElement(r)){var n;let e,o;let i=(n=r,(o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,t){let r={...t};for(let a in t){let s=e[a],l=t[a];/^on[A-Z]/.test(a)?s&&l?r[a]=(...e)=>{l(...e),s(...e)}:s&&(r[a]=s):"style"===a?r[a]={...s,...l}:"className"===a&&(r[a]=[s,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==a.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}(t,i):i),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...n}=e,i=a.Children.toArray(s),d=i.find(o);if(d){let e=d.props.children,s=i.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...n,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,l.jsx)(t,{...n,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?r:t,{...n,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),d=a.forwardRef((e,t)=>(0,l.jsx)(i.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return o}});let a=r(60687),s=r(51215),l=r(29294),n=r(19587);function o(e){let{moduleIds:t}=e,r=l.workAsyncStorage.getStore();if(void 0===r)return null;let o=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;o.push(...t)}}return 0===o.length?null:(0,a.jsx)(a.Fragment,{children:o.map(e=>{let t=r.assetPrefix+"/_next/"+(0,n.encodeURIPath)(e);return e.endsWith(".css")?(0,a.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,s.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let a=r(60687),s=r(43210),l=r(56780),n=r(64777);function o(e){return{default:e&&"default"in e?e.default:e}}let i={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},d=function(e){let t={...i,...e},r=(0,s.lazy)(()=>t.loader().then(o)),d=t.loading;function c(e){let o=d?(0,a.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,i=!t.ssr||!!t.loading,c=i?s.Suspense:s.Fragment,u=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.PreloadChunks,{moduleIds:t.modules}),(0,a.jsx)(r,{...e})]}):(0,a.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(r,{...e})});return(0,a.jsx)(c,{...i?{fallback:o}:{},children:u})}return c.displayName="LoadableComponent",c}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80942:(e,t,r)=>{"use strict";r.d(t,{lV:()=>c,MJ:()=>x,Rr:()=>h,zB:()=>p,eI:()=>f,lR:()=>b,C5:()=>v});var a=r(60687),s=r(43210),l=r(11329),n=r(27605),o=r(4780),i=r(61170);function d({className:e,...t}){return(0,a.jsx)(i.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let c=n.Op,u=s.createContext({}),p=({...e})=>(0,a.jsx)(u.Provider,{value:{name:e.name},children:(0,a.jsx)(n.xI,{...e})}),m=()=>{let e=s.useContext(u),t=s.useContext(g),{getFieldState:r}=(0,n.xW)(),a=(0,n.lN)({name:e.name}),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...l}},g=s.createContext({});function f({className:e,...t}){let r=s.useId();return(0,a.jsx)(g.Provider,{value:{id:r},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function b({className:e,...t}){let{error:r,formItemId:s}=m();return(0,a.jsx)(d,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:s,...t})}function x({...e}){let{error:t,formItemId:r,formDescriptionId:s,formMessageId:n}=m();return(0,a.jsx)(l.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${s} ${n}`:`${s}`,"aria-invalid":!!t,...e})}function h({className:e,...t}){let{formDescriptionId:r}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:r,className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function v({className:e,...t}){let{error:r,formMessageId:s}=m(),l=r?String(r?.message??""):t.children;return l?(0,a.jsx)("p",{"data-slot":"form-message",id:s,className:(0,o.cn)("text-destructive text-sm",e),...t,children:l}):null}},81630:e=>{"use strict";e.exports=require("http")},82150:(e,t,r)=>{"use strict";r.d(t,{$5:()=>o,BU:()=>s,c5:()=>i,cc:()=>d,dZ:()=>n,sq:()=>l});var a=r(28527);let s=async(e=1,t=10)=>{try{return(await a.S.get("/blogs/approved",{params:{page:e,limit:t}})).data}catch(e){throw Error(e.response?.data?.message||`Failed to fetch approved blogs: ${e.message}`)}},l=async(e=1,t=10,r)=>{try{return(await a.S.get("/blogs/my-blogs",{params:{page:e,limit:t,status:r}})).data}catch(e){throw Error(e.response?.data?.message||`Failed to fetch your blogs: ${e.message}`)}},n=async e=>{try{return(await a.S.get(`/blogs/${e}`)).data.data}catch(e){throw Error(e.response?.data?.message||`Failed to fetch blog: ${e.message}`)}},o=async e=>{try{let t=new FormData;return t.append("blogTitle",e.blogTitle),t.append("blogDescription",e.blogDescription),e.blogImage&&t.append("blogImage",e.blogImage),(await a.S.post("/blogs",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){throw Error(e.response?.data?.message||`Failed to create blog: ${e.message}`)}},i=async(e,t)=>{try{let r=new FormData;return t.blogTitle&&r.append("blogTitle",t.blogTitle),t.blogDescription&&r.append("blogDescription",t.blogDescription),t.blogImage&&r.append("blogImage",t.blogImage),t.status&&r.append("status",t.status),(await a.S.put(`/blogs/${e}`,r,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){throw Error(e.response?.data?.message||`Failed to update blog: ${e.message}`)}},d=async e=>{try{await a.S.delete(`/blogs/${e}`)}catch(e){throw Error(e.response?.data?.message||`Failed to delete blog: ${e.message}`)}}},83997:e=>{"use strict";e.exports=require("tty")},90129:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(37413),s=r(61120),l=r(97727);function n(){return(0,a.jsx)(s.Suspense,{children:(0,a.jsx)(l.default,{})})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>d});var a=r(60687),s=r(43210),l=r(24224),n=r(4780);let o=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=s.forwardRef(({className:e,variant:t,...r},s)=>(0,a.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(o({variant:t}),e),...r}));i.displayName="Alert",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},94790:(e,t,r)=>{Promise.resolve().then(r.bind(r,97453))},97453:(e,t,r)=>{"use strict";r.d(t,{default:()=>N});var a=r(60687),s=r(43210),l=r(30474),n=r(54864),o=r(16189),i=r(49587),d=r.n(i),c=r(52581),u=r(29523),p=r(44493),m=r(89667),g=r(93613),f=r(41862),b=r(82150),x=r(27605),h=r(63442),v=r(45880),y=r(91821),j=r(80942);r(21165);let w=d()(async()=>{},{loadableGenerated:{modules:["app\\classes\\blogs\\add\\AddBlogPageContent.tsx -> react-quill-new"]},ssr:!1}),P=v.z.object({blogTitle:v.z.string().min(3,"Blog title must be at least 3 characters"),blogDescription:v.z.string().min(10,"Blog description must be at least 10 characters"),blogImage:v.z.custom(e=>e instanceof File||!!e,{message:"Blog image is required"})}),_=({message:e})=>e?(0,a.jsxs)(y.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)(y.TN,{className:"text-red-500",children:e})]}):null,N=()=>{let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!1),[d,g]=(0,s.useState)(""),{user:v}=(0,n.d4)(e=>e.user),y=(0,o.useRouter)(),N=(0,o.useSearchParams)().get("id"),C=!!N,R=(0,x.mN)({resolver:(0,h.u)(P),defaultValues:{blogTitle:"",blogDescription:"",blogImage:void 0},mode:"onChange"}),S=(0,s.useCallback)(async e=>{try{i(!0);let r=await (0,b.dZ)(e);if(R.setValue("blogTitle",r.blogTitle),R.setValue("blogDescription",r.blogDescription),r.blogImage){let e=r.blogImage.replace(/^\/+/,""),a=`http://localhost:4005/${e}`;t(a),R.setValue("blogImage",new File([],"existing-image.jpg"))}}catch(e){c.oR.error(e.message||"Failed to fetch blog"),y.push("/classes/blogs")}finally{i(!1)}},[y,R]);(0,s.useEffect)(()=>{if(!v){y.push("/");return}C&&N&&S(N)},[v,y,C,N,S]);let I=e=>{if(e.target.files&&e.target.files[0]){let r=e.target.files[0];if(!["image/jpeg","image/jpg","image/png"].includes(r.type)){c.oR.error("Only image files (.jpg, .jpeg, .png) are allowed"),e.target.value="";return}R.setValue("blogImage",r);let a=new FileReader;a.onload=()=>{t(a.result)},a.readAsDataURL(r)}},T=async e=>{g("");try{C&&N?(await (0,b.c5)(N,{blogTitle:e.blogTitle,blogDescription:e.blogDescription,blogImage:e.blogImage}),c.oR.success("Blog updated successfully")):(await (0,b.$5)({blogTitle:e.blogTitle,blogDescription:e.blogDescription,blogImage:e.blogImage}),c.oR.success("Blog created successfully")),y.push("/classes/blogs")}catch(e){g(e.message||"Failed to save blog")}};return r?(0,a.jsx)("div",{className:"container mx-auto py-6 px-4 flex justify-center items-center h-64",children:(0,a.jsx)(f.A,{className:"h-8 w-8 animate-spin text-primary"})}):(0,a.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,a.jsxs)(p.Zp,{className:"max-w-3xl mx-auto",children:[(0,a.jsx)(p.aR,{children:(0,a.jsx)(p.ZB,{children:C?"Edit Blog":"Create New Blog"})}),(0,a.jsxs)(p.Wu,{children:[d&&(0,a.jsx)(_,{message:d}),(0,a.jsx)(j.lV,{...R,children:(0,a.jsxs)("form",{onSubmit:R.handleSubmit(T),className:"space-y-6",children:[(0,a.jsx)(j.zB,{control:R.control,name:"blogTitle",render:({field:e})=>(0,a.jsxs)(j.eI,{children:[(0,a.jsx)(j.lR,{children:"Title"}),(0,a.jsx)(j.MJ,{children:(0,a.jsx)(m.p,{placeholder:"Enter blog title",className:"w-full",...e})}),(0,a.jsx)(j.C5,{})]})}),(0,a.jsx)(j.zB,{control:R.control,name:"blogImage",render:({})=>(0,a.jsxs)(j.eI,{children:[(0,a.jsx)(j.lR,{children:"Image"}),(0,a.jsx)(j.MJ,{children:(0,a.jsx)(m.p,{id:"image",type:"file",accept:".jpg,.jpeg,.png",onChange:I,className:"w-full"})}),e&&(0,a.jsx)("div",{className:"mt-4 flex justify-center items-center h-60 w-full border rounded-md p-2 overflow-hidden",children:(0,a.jsx)(l.default,{src:e,alt:"Blog Preview",width:500,height:500,className:"object-contain rounded-md",style:{maxHeight:"100%",maxWidth:"100%",display:"block",margin:"auto"}})}),(0,a.jsx)(j.C5,{})]})}),(0,a.jsx)(j.zB,{control:R.control,name:"blogDescription",render:({field:e})=>(0,a.jsxs)(j.eI,{children:[(0,a.jsx)(j.lR,{children:"Description"}),(0,a.jsx)(j.MJ,{children:(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(w,{theme:"snow",value:e.value,onChange:e.onChange,className:"h-full",modules:{toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{align:[]}],["link","image"],["clean"]]}})})}),(0,a.jsx)(j.C5,{})]})}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 mt-20",children:[(0,a.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>y.push("/classes/blogs"),children:"Cancel"}),(0,a.jsx)(u.$,{type:"submit",disabled:R.formState.isSubmitting,children:R.formState.isSubmitting?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4 animate-spin"}),C?"Updating...":"Creating..."]}):(0,a.jsx)(a.Fragment,{children:C?"Update":"Create"})})]})]})})]})]})})}},97727:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\blogs\\\\add\\\\AddBlogPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,8721,2105,9191,2800,7200],()=>r(46739));module.exports=a})();