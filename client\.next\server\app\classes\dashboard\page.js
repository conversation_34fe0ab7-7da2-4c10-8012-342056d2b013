(()=>{var e={};e.id=2498,e.ids=[2498],e.modules={1733:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),p={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);t.d(r,p);let u={children:["",{children:["classes",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74196)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/classes/dashboard/page",pathname:"/classes/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42123:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var s=t(43210);t(51215);var a=t(11329),i=t(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.TL)(`Primitive.${r}`),o=s.forwardRef((e,s)=>{let{asChild:a,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?t:r,{...o,ref:s})});return o.displayName=`Primitive.${r}`,{...e,[r]:o}},{}),n="horizontal",p=["horizontal","vertical"],u=s.forwardRef((e,r)=>{var t;let{decorative:s,orientation:a=n,...u}=e,d=(t=a,p.includes(t))?a:n;return(0,i.jsx)(o.div,{"data-orientation":d,...s?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:r})});u.displayName="Separator";var d=u},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74196:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(37413);function a(){return(0,s.jsx)("div",{children:"Dashboard"})}t(61120)},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,8721,9191,2800,7200],()=>t(1733));module.exports=s})();