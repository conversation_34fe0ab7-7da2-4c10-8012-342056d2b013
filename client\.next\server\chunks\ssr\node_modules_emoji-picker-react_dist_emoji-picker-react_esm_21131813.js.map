{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "emoji-picker-react.esm.js", "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/classNames.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/Stylesheet/stylesheet.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/config/compareConfig.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Reactions/DEFAULT_REACTIONS.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/types/exposedTypes.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/config/categoryConfig.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/config/cdnUrls.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/data/skinToneVariations.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/dataUtils/DataTypes.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/dataUtils/alphaNumericEmojiIndex.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/dataUtils/emojiSelectors.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/config/config.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/context/PickerConfigContext.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/config/mutableConfig.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/config/useConfig.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useDebouncedState.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useHideEmojisByUniocode.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useDisallowedEmojis.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useInitialLoad.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/context/PickerContext.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useIsSearchMode.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/focusElement.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/getActiveElement.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/context/ElementRefContext.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/scrollTo.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/keyboardNavigation.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useCloseAllOpenToggles.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useDisallowMouseMove.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useFocus.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useFilter.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useSetVariationPicker.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useShouldShowSkinTonePicker.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useKeyboardNavigation.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/preloadEmoji.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useOnFocus.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/main/PickerMain.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/elementPositionInRow.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/selectors.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/dataUtils/parseNativeEmoji.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/dataUtils/suggested.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/typeRefinements/typeRefinements.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useMouseDownHandlers.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/atoms/Button.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/ClickableEmojiButton.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/emojiStyles.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/EmojiImg.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/NativeEmoji.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/ViewOnlyEmoji.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/Emoji.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Reactions/BtnPlus.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Reactions/Reactions.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useOnScroll.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useIsEmojiHidden.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/body/EmojiCategory.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useIsEverMounted.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/body/Suggested.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/body/EmojiList.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/body/EmojiVariationPicker.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/body/Body.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/detectEmojyPartiallyBelowFold.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useEmojiPreviewEvents.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Layout/Flex.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Layout/Space.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Layout/Absolute.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/Layout/Relative.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/SkinTonePicker/BtnSkinToneVariation.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/SkinTonePicker/SkinTonePicker.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/footer/Preview.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/DomUtils/categoryNameFromDom.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useActiveCategoryScrollDetection.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useScrollCategoryIntoView.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/hooks/useShouldHideCustomEmojis.ts", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/navigation/CategoryButton.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/navigation/CategoryNavigation.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/Search/BtnClearSearch.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/Search/CssSearch.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/Search/IcnSearch.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/Search/Search.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/header/Header.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/EmojiPickerReact.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/ErrorBoundary.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/components/emoji/ExportedEmoji.tsx", "file://G%3A/UEST/uest_app/uest-app/client/node_modules/emoji-picker-react/src/index.tsx"], "sourcesContent": ["export enum ClassNames {\n  hiddenOnSearch = 'epr-hidden-on-search',\n  searchActive = 'epr-search-active',\n  hidden = 'epr-hidden',\n  visible = 'epr-visible',\n  active = 'epr-active',\n  emoji = 'epr-emoji',\n  category = 'epr-emoji-category',\n  label = 'epr-emoji-category-label',\n  categoryContent = 'epr-emoji-category-content',\n  emojiHasVariations = 'epr-emoji-has-variations',\n  scrollBody = 'epr-body',\n  emojiList = 'epr-emoji-list',\n  external = '__EmojiPicker__',\n  emojiPicker = 'EmojiPickerReact',\n  open = 'epr-open',\n  vertical = 'epr-vertical',\n  horizontal = 'epr-horizontal',\n  variationPicker = 'epr-emoji-variation-picker',\n  darkTheme = 'epr-dark-theme',\n  autoTheme = 'epr-auto-theme'\n}\n\nexport function asSelectors(...classNames: ClassNames[]): string {\n  return classNames.map(c => `.${c}`).join('');\n}\n", "import { Styles, createSheet } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../DomUtils/classNames';\n\nexport const stylesheet = createSheet('epr', null);\n\nconst hidden = {\n  display: 'none',\n  opacity: '0',\n  pointerEvents: 'none',\n  visibility: 'hidden',\n  overflow: 'hidden'\n};\n\nexport const commonStyles = stylesheet.create({\n  hidden: {\n    '.': ClassNames.hidden,\n    ...hidden\n  }\n});\n\nexport const PickerStyleTag = React.memo(function PickerStyleTag() {\n  return (\n    <style\n      suppressHydrationWarning\n      dangerouslySetInnerHTML={{ __html: stylesheet.getStyle() }}\n    />\n  );\n});\n\nexport const commonInteractionStyles = stylesheet.create({\n  '.epr-main': {\n    ':has(input:not(:placeholder-shown))': {\n      categoryBtn: {\n        ':hover': {\n          opacity: '1',\n          backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n        }\n      },\n      hiddenOnSearch: {\n        '.': ClassNames.hiddenOnSearch,\n        ...hidden\n      }\n    },\n    ':has(input(:placeholder-shown))': {\n      visibleOnSearchOnly: hidden\n    }\n  },\n  hiddenOnReactions: {\n    transition: 'all 0.5s ease-in-out'\n  },\n  '.epr-reactions': {\n    hiddenOnReactions: {\n      height: '0px',\n      width: '0px',\n      opacity: '0',\n      pointerEvents: 'none',\n      overflow: 'hidden'\n    }\n  },\n  '.EmojiPickerReact:not(.epr-search-active)': {\n    categoryBtn: {\n      ':hover': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      },\n      '&.epr-active': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      }\n    },\n    visibleOnSearchOnly: {\n      '.': 'epr-visible-on-search-only',\n      ...hidden\n    }\n  }\n});\n\nexport function darkMode(key: string, value: Styles) {\n  return {\n    '.epr-dark-theme': {\n      [key]: value\n    },\n    '.epr-auto-theme': {\n      [key]: {\n        '@media (prefers-color-scheme: dark)': value\n      }\n    }\n  };\n}\n", "import { PickerConfig } from './config';\n\n// eslint-disable-next-line complexity\nexport function compareConfig(prev: PickerConfig, next: PickerConfig) {\n  const prevCustomEmojis = prev.customEmojis ?? [];\n  const nextCustomEmojis = next.customEmojis ?? [];\n  return (\n    prev.open === next.open &&\n    prev.emojiVersion === next.emojiVersion &&\n    prev.reactionsDefaultOpen === next.reactionsDefaultOpen &&\n    prev.searchPlaceHolder === next.searchPlaceHolder &&\n    prev.searchPlaceholder === next.searchPlaceholder &&\n    prev.defaultSkinTone === next.defaultSkinTone &&\n    prev.skinTonesDisabled === next.skinTonesDisabled &&\n    prev.autoFocusSearch === next.autoFocusSearch &&\n    prev.emojiStyle === next.emojiStyle &&\n    prev.theme === next.theme &&\n    prev.suggestedEmojisMode === next.suggestedEmojisMode &&\n    prev.lazyLoadEmojis === next.lazyLoadEmojis &&\n    prev.className === next.className &&\n    prev.height === next.height &&\n    prev.width === next.width &&\n    prev.style === next.style &&\n    prev.searchDisabled === next.searchDisabled &&\n    prev.skinTonePickerLocation === next.skinTonePickerLocation &&\n    prevCustomEmojis.length === nextCustomEmojis.length\n  );\n}", "export const DEFAULT_REACTIONS = [\n  '1f44d', // 👍\n  '2764-fe0f', // ❤️\n  '1f603', // 😃\n  '1f622', // 😢\n  '1f64f', // 🙏\n  '1f44e', // 👎\n  '1f621' // 😡\n];\n", "export type EmojiClickData = {\n  activeSkinTone: SkinTones;\n  unified: string;\n  unifiedWithoutSkinTone: string;\n  emoji: string;\n  names: string[];\n  imageUrl: string;\n  getImageUrl: (emojiStyle?: EmojiStyle) => string;\n  isCustom: boolean;\n};\n\nexport enum SuggestionMode {\n  RECENT = 'recent',\n  FREQUENT = 'frequent'\n}\n\nexport enum EmojiStyle {\n  NATIVE = 'native',\n  APPLE = 'apple',\n  TWITTER = 'twitter',\n  GOOGLE = 'google',\n  FACEBOOK = 'facebook'\n}\n\nexport enum Theme {\n  DARK = 'dark',\n  LIGHT = 'light',\n  AUTO = 'auto'\n}\n\nexport enum SkinTones {\n  NEUTRAL = 'neutral',\n  LIGHT = '1f3fb',\n  MEDIUM_LIGHT = '1f3fc',\n  MEDIUM = '1f3fd',\n  MEDIUM_DARK = '1f3fe',\n  DARK = '1f3ff'\n}\n\nexport enum Categories {\n  SUGGESTED = 'suggested',\n  CUSTOM = 'custom',\n  SMILEYS_PEOPLE = 'smileys_people',\n  ANIMALS_NATURE = 'animals_nature',\n  FOOD_DRINK = 'food_drink',\n  TRAVEL_PLACES = 'travel_places',\n  ACTIVITIES = 'activities',\n  OBJECTS = 'objects',\n  SYMBOLS = 'symbols',\n  FLAGS = 'flags'\n}\n\nexport enum SkinTonePickerLocation {\n  SEARCH = 'SEARCH',\n  PREVIEW = 'PREVIEW'\n}\n", "import { Categories, SuggestionMode } from '../types/exposedTypes';\n\nexport { Categories };\n\nconst categoriesOrdered: Categories[] = [\n  Categories.SUGGESTED,\n  Categories.CUSTOM,\n  Categories.SMILEYS_PEOPLE,\n  Categories.ANIMALS_NATURE,\n  Categories.FOOD_DRINK,\n  Categories.TRAVEL_PLACES,\n  Categories.ACTIVITIES,\n  Categories.OBJECTS,\n  Categories.SYMBOLS,\n  Categories.FLAGS\n];\n\nexport const SuggestedRecent: CategoryConfig = {\n  name: 'Recently Used',\n  category: Categories.SUGGESTED\n};\n\nexport type CustomCategoryConfig = {\n  category: Categories.CUSTOM;\n  name: string;\n};\n\nconst configByCategory: Record<Categories, CategoryConfig> = {\n  [Categories.SUGGESTED]: {\n    category: Categories.SUGGESTED,\n    name: 'Frequently Used'\n  },\n  [Categories.CUSTOM]: {\n    category: Categories.CUSTOM,\n    name: 'Custom Emojis'\n  },\n  [Categories.SMILEYS_PEOPLE]: {\n    category: Categories.SMILEYS_PEOPLE,\n    name: 'Smileys & People'\n  },\n  [Categories.ANIMALS_NATURE]: {\n    category: Categories.ANIMALS_NATURE,\n    name: 'Animals & Nature'\n  },\n  [Categories.FOOD_DRINK]: {\n    category: Categories.FOOD_DRINK,\n    name: 'Food & Drink'\n  },\n  [Categories.TRAVEL_PLACES]: {\n    category: Categories.TRAVEL_PLACES,\n    name: 'Travel & Places'\n  },\n  [Categories.ACTIVITIES]: {\n    category: Categories.ACTIVITIES,\n    name: 'Activities'\n  },\n  [Categories.OBJECTS]: {\n    category: Categories.OBJECTS,\n    name: 'Objects'\n  },\n  [Categories.SYMBOLS]: {\n    category: Categories.SYMBOLS,\n    name: 'Symbols'\n  },\n  [Categories.FLAGS]: {\n    category: Categories.FLAGS,\n    name: 'Flags'\n  }\n};\n\nexport function baseCategoriesConfig(\n  modifiers?: Record<Categories, CategoryConfig>\n): CategoriesConfig {\n  return categoriesOrdered.map(category => {\n    return {\n      ...configByCategory[category],\n      ...(modifiers && modifiers[category] && modifiers[category])\n    };\n  });\n}\n\nexport function categoryFromCategoryConfig(category: CategoryConfig) {\n  return category.category;\n}\n\nexport function categoryNameFromCategoryConfig(category: CategoryConfig) {\n  return category.name;\n}\n\nexport type CategoriesConfig = CategoryConfig[];\n\nexport type CategoryConfig = {\n  category: Categories;\n  name: string;\n};\n\nexport type UserCategoryConfig = Array<Categories | CategoryConfig>;\n\nexport function mergeCategoriesConfig(\n  userCategoriesConfig: UserCategoryConfig = [],\n  modifiers: CategoryConfigModifiers = {}\n): CategoriesConfig {\n  const extra = {} as Record<Categories, CategoryConfig>;\n\n  if (modifiers.suggestionMode === SuggestionMode.RECENT) {\n    extra[Categories.SUGGESTED] = SuggestedRecent;\n  }\n\n  const base = baseCategoriesConfig(extra);\n  if (!userCategoriesConfig?.length) {\n    return base;\n  }\n\n  return userCategoriesConfig.map(category => {\n    if (typeof category === 'string') {\n      return getBaseConfigByCategory(category, extra[category]);\n    }\n\n    return {\n      ...getBaseConfigByCategory(category.category, extra[category.category]),\n      ...category\n    };\n  });\n}\n\nfunction getBaseConfigByCategory(\n  category: Categories,\n  modifier: CategoryConfig = {} as CategoryConfig\n) {\n  return Object.assign(configByCategory[category], modifier);\n}\n\ntype CategoryConfigModifiers = {\n  suggestionMode?: SuggestionMode;\n};\n", "import { EmojiStyle } from '../types/exposedTypes';\n\nconst CDN_URL_APPLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/';\nconst CDN_URL_FACEBOOK =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-facebook/img/facebook/64/';\nconst CDN_URL_TWITTER =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-twitter/img/twitter/64/';\nconst CDN_URL_GOOGLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-google/img/google/64/';\n\nexport function cdnUrl(emojiStyle: EmojiStyle): string {\n  switch (emojiStyle) {\n    case EmojiStyle.TWITTER:\n      return CDN_URL_TWITTER;\n    case EmojiStyle.GOOGLE:\n      return CDN_URL_GOOGLE;\n    case EmojiStyle.FACEBOOK:\n      return CDN_URL_FACEBOOK;\n    case EmojiStyle.APPLE:\n    default:\n      return CDN_URL_APPLE;\n  }\n}\n", "import { SkinTones } from '../types/exposedTypes';\n\nconst skinToneVariations = [\n  SkinTones.NEUTRAL,\n  SkinTones.LIGHT,\n  SkinTones.MEDIUM_LIGHT,\n  SkinTones.MEDIUM,\n  SkinTones.MEDIUM_DARK,\n  SkinTones.DARK\n];\n\nexport const skinTonesNamed = Object.entries(SkinTones).reduce(\n  (acc, [key, value]) => {\n    acc[value] = key;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\nexport const skinTonesMapped: Record<\n  string,\n  string\n> = skinToneVariations.reduce(\n  (mapped, skinTone) =>\n    Object.assign(mapped, {\n      [skinTone]: skinTone\n    }),\n  {}\n);\n\nexport default skinToneVariations;\n", "import emojis from '../data/emojis';\n\nexport enum EmojiProperties {\n  name = 'n',\n  unified = 'u',\n  variations = 'v',\n  added_in = 'a',\n  imgUrl = 'imgUrl'\n}\n\nexport interface DataEmoji extends WithName {\n  [EmojiProperties.unified]: string;\n  [EmojiProperties.variations]?: string[];\n  [EmojiProperties.added_in]: string;\n  [EmojiProperties.imgUrl]?: string;\n}\n\nexport type DataEmojis = DataEmoji[];\n\nexport type DataGroups = keyof typeof emojis;\n\nexport type WithName = {\n  [EmojiProperties.name]: string[];\n};\n", "import { DataEmoji } from './DataTypes';\nimport { allEmojis, emojiNames, emojiUnified } from './emojiSelectors';\n\nexport const alphaNumericEmojiIndex: BaseIndex = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((searchIndex, emoji) => {\n    indexEmoji(emoji);\n    return searchIndex;\n  }, alphaNumericEmojiIndex as BaseIndex);\n});\n\ntype BaseIndex = Record<string, Record<string, DataEmoji>>;\n\nexport function indexEmoji(emoji: DataEmoji): void {\n  const joinedNameString = emojiNames(emoji)\n    .flat()\n    .join('')\n    .toLowerCase()\n    .replace(/[^a-zA-Z\\d]/g, '')\n    .split('');\n\n  joinedNameString.forEach(char => {\n    alphaNumericEmojiIndex[char] = alphaNumericEmojiIndex[char] ?? {};\n\n    alphaNumericEmojiIndex[char][emojiUnified(emoji)] = emoji;\n  });\n}\n", "import { Categories } from '../config/categoryConfig';\nimport { cdnUrl } from '../config/cdnUrls';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport emojis from '../data/emojis';\nimport skinToneVariations, {\n  skinTonesMapped\n} from '../data/skinToneVariations';\nimport { EmojiStyle, SkinTones } from '../types/exposedTypes';\n\nimport { DataEmoji, DataEmojis, EmojiProperties, WithName } from './DataTypes';\nimport { indexEmoji } from './alphaNumericEmojiIndex';\n\nexport function emojiNames(emoji: WithName): string[] {\n  return emoji[EmojiProperties.name] ?? [];\n}\n\nexport function addedIn(emoji: DataEmoji): number {\n  return parseFloat(emoji[EmojiProperties.added_in]);\n}\n\nexport function emojiName(emoji?: WithName): string {\n  if (!emoji) {\n    return '';\n  }\n\n  return emojiNames(emoji)[0];\n}\n\nexport function unifiedWithoutSkinTone(unified: string): string {\n  const splat = unified.split('-');\n  const [skinTone] = splat.splice(1, 1);\n\n  if (skinTonesMapped[skinTone]) {\n    return splat.join('-');\n  }\n\n  return unified;\n}\n\nexport function emojiUnified(emoji: DataEmoji, skinTone?: string): string {\n  const unified = emoji[EmojiProperties.unified];\n\n  if (!skinTone || !emojiHasVariations(emoji)) {\n    return unified;\n  }\n\n  return emojiVariationUnified(emoji, skinTone) ?? unified;\n}\n\nexport function emojisByCategory(category: Categories): DataEmojis {\n  // @ts-ignore\n  return emojis?.[category] ?? [];\n}\n\n// WARNING: DO NOT USE DIRECTLY\nexport function emojiUrlByUnified(\n  unified: string,\n  emojiStyle: EmojiStyle\n): string {\n  return `${cdnUrl(emojiStyle)}${unified}.png`;\n}\n\nexport function emojiVariations(emoji: DataEmoji): string[] {\n  return emoji[EmojiProperties.variations] ?? [];\n}\n\nexport function emojiHasVariations(emoji: DataEmoji): boolean {\n  return emojiVariations(emoji).length > 0;\n}\n\nexport function emojiVariationUnified(\n  emoji: DataEmoji,\n  skinTone?: string\n): string | undefined {\n  return skinTone\n    ? emojiVariations(emoji).find(variation => variation.includes(skinTone))\n    : emojiUnified(emoji);\n}\n\nexport function emojiByUnified(unified?: string): DataEmoji | undefined {\n  if (!unified) {\n    return;\n  }\n\n  if (allEmojisByUnified[unified]) {\n    return allEmojisByUnified[unified];\n  }\n\n  const withoutSkinTone = unifiedWithoutSkinTone(unified);\n  return allEmojisByUnified[withoutSkinTone];\n}\n\nexport const allEmojis: DataEmojis = Object.values(emojis).flat();\n\nexport function setCustomEmojis(customEmojis: CustomEmoji[]): void {\n  emojis[Categories.CUSTOM].length = 0;\n\n  customEmojis.forEach(emoji => {\n    const emojiData = customToRegularEmoji(emoji);\n\n    emojis[Categories.CUSTOM].push(emojiData as never);\n\n    if (allEmojisByUnified[emojiData[EmojiProperties.unified]]) {\n      return;\n    }\n\n    allEmojis.push(emojiData);\n    allEmojisByUnified[emojiData[EmojiProperties.unified]] = emojiData;\n    indexEmoji(emojiData);\n  });\n}\n\nfunction customToRegularEmoji(emoji: CustomEmoji): DataEmoji {\n  return {\n    [EmojiProperties.name]: emoji.names.map(name => name.toLowerCase()),\n    [EmojiProperties.unified]: emoji.id.toLowerCase(),\n    [EmojiProperties.added_in]: '0',\n    [EmojiProperties.imgUrl]: emoji.imgUrl\n  };\n}\n\nconst allEmojisByUnified: {\n  [unified: string]: DataEmoji;\n} = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((allEmojis, Emoji) => {\n    allEmojis[emojiUnified(Emoji)] = Emoji;\n\n    if (emojiHasVariations(Emoji)) {\n      emojiVariations(Emoji).forEach(variation => {\n        allEmojis[variation] = Emoji;\n      });\n    }\n\n    return allEmojis;\n  }, allEmojisByUnified);\n});\n\nexport function activeVariationFromUnified(unified: string): SkinTones | null {\n  const [, suspectedSkinTone] = unified.split('-') as [string, SkinTones];\n  return skinToneVariations.includes(suspectedSkinTone)\n    ? suspectedSkinTone\n    : null;\n}\n", "import * as React from 'react';\n\nimport { DEFAULT_REACTIONS } from '../components/Reactions/DEFAULT_REACTIONS';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  setCustomEmojis,\n  emojiUrlByUnified\n} from '../dataUtils/emojiSelectors';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport {\n  CategoriesConfig,\n  baseCategoriesConfig,\n  mergeCategoriesConfig\n} from './categoryConfig';\nimport { CustomEmoji } from './customEmojiConfig';\n\nconst KNOWN_FAILING_EMOJIS = ['2640-fe0f', '2642-fe0f', '2695-fe0f'];\n\nexport const DEFAULT_SEARCH_PLACEHOLDER = 'Search';\nexport const SEARCH_RESULTS_NO_RESULTS_FOUND = 'No results found';\nexport const SEARCH_RESULTS_SUFFIX =\n  ' found. Use up and down arrow keys to navigate.';\nexport const SEARCH_RESULTS_ONE_RESULT_FOUND =\n  '1 result' + SEARCH_RESULTS_SUFFIX;\nexport const SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND =\n  '%n results' + SEARCH_RESULTS_SUFFIX;\n\nexport function mergeConfig(\n  userConfig: PickerConfig = {}\n): PickerConfigInternal {\n  const base = basePickerConfig();\n\n  const previewConfig = Object.assign(\n    base.previewConfig,\n    userConfig.previewConfig ?? {}\n  );\n  const config = Object.assign(base, userConfig);\n\n  const categories = mergeCategoriesConfig(userConfig.categories, {\n    suggestionMode: config.suggestedEmojisMode\n  });\n\n  config.hiddenEmojis.forEach(emoji => {\n    config.unicodeToHide.add(emoji);\n  });\n\n  setCustomEmojis(config.customEmojis ?? []);\n\n  const skinTonePickerLocation = config.searchDisabled\n    ? SkinTonePickerLocation.PREVIEW\n    : config.skinTonePickerLocation;\n\n  return {\n    ...config,\n    categories,\n    previewConfig,\n    skinTonePickerLocation\n  };\n}\n\nexport function basePickerConfig(): PickerConfigInternal {\n  return {\n    autoFocusSearch: true,\n    categories: baseCategoriesConfig(),\n    className: '',\n    customEmojis: [],\n    defaultSkinTone: SkinTones.NEUTRAL,\n    emojiStyle: EmojiStyle.APPLE,\n    emojiVersion: null,\n    getEmojiUrl: emojiUrlByUnified,\n    height: 450,\n    lazyLoadEmojis: false,\n    previewConfig: {\n      ...basePreviewConfig\n    },\n    searchDisabled: false,\n    searchPlaceHolder: DEFAULT_SEARCH_PLACEHOLDER,\n    searchPlaceholder: DEFAULT_SEARCH_PLACEHOLDER,\n    skinTonePickerLocation: SkinTonePickerLocation.SEARCH,\n    skinTonesDisabled: false,\n    style: {},\n    suggestedEmojisMode: SuggestionMode.FREQUENT,\n    theme: Theme.LIGHT,\n    unicodeToHide: new Set<string>(KNOWN_FAILING_EMOJIS),\n    width: 350,\n    reactionsDefaultOpen: false,\n    reactions: DEFAULT_REACTIONS,\n    open: true,\n    allowExpandReactions: true,\n    hiddenEmojis: []\n  };\n}\n\nexport type PickerConfigInternal = {\n  emojiVersion: string | null;\n  searchPlaceHolder: string;\n  searchPlaceholder: string;\n  defaultSkinTone: SkinTones;\n  skinTonesDisabled: boolean;\n  autoFocusSearch: boolean;\n  emojiStyle: EmojiStyle;\n  categories: CategoriesConfig;\n  theme: Theme;\n  suggestedEmojisMode: SuggestionMode;\n  lazyLoadEmojis: boolean;\n  previewConfig: PreviewConfig;\n  className: string;\n  height: PickerDimensions;\n  width: PickerDimensions;\n  style: React.CSSProperties;\n  getEmojiUrl: GetEmojiUrl;\n  searchDisabled: boolean;\n  skinTonePickerLocation: SkinTonePickerLocation;\n  unicodeToHide: Set<string>;\n  customEmojis: CustomEmoji[];\n  reactionsDefaultOpen: boolean;\n  reactions: string[];\n  open: boolean;\n  allowExpandReactions: boolean;\n  hiddenEmojis: string[];\n};\n\nexport type PreviewConfig = {\n  defaultEmoji: string;\n  defaultCaption: string;\n  showPreview: boolean;\n};\n\nconst basePreviewConfig: PreviewConfig = {\n  defaultEmoji: '1f60a',\n  defaultCaption: \"What's your mood?\",\n  showPreview: true\n};\n\ntype ConfigExternal = {\n  previewConfig: Partial<PreviewConfig>;\n  onEmojiClick: MouseDownEvent;\n  onReactionClick: MouseDownEvent;\n  onSkinToneChange: OnSkinToneChange;\n} & Omit<PickerConfigInternal, 'previewConfig' | 'unicodeToHide'>;\n\nexport type PickerConfig = Partial<ConfigExternal>;\n\nexport type PickerDimensions = string | number;\n\nexport type MouseDownEvent = (emoji: EmojiClickData, event: MouseEvent) => void;\nexport type OnSkinToneChange = (emoji: SkinTones) => void;\n", "import * as React from 'react';\n\nimport { compareConfig } from '../../config/compareConfig';\nimport {\n  basePickerConfig,\n  mergeConfig,\n  PickerConfig,\n  PickerConfigInternal\n} from '../../config/config';\n\ntype Props = PickerConfig &\n  Readonly<{\n    children: React.ReactNode;\n  }>;\n\nconst ConfigContext = React.createContext<PickerConfigInternal>(\n  basePickerConfig()\n);\n\nexport function PickerConfigProvider({ children, ...config }: Props) {\n  const mergedConfig = useSetConfig(config);\n\n  return (\n    <ConfigContext.Provider value={mergedConfig}>\n      {children}\n    </ConfigContext.Provider>\n  );\n}\n\nexport function useSetConfig(config: PickerConfig) {\n  const [mergedConfig, setMergedConfig] = React.useState(() =>\n    mergeConfig(config)\n  );\n\n  React.useEffect(() => {\n    if (compareConfig(mergedConfig, config)) {\n      return;\n    }\n    setMergedConfig(mergeConfig(config));\n    // not gonna...\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    config.customEmojis?.length,\n    config.open,\n    config.emojiVersion,\n    config.reactionsDefaultOpen,\n    config.searchPlaceHolder,\n    config.searchPlaceholder,\n    config.defaultSkinTone,\n    config.skinTonesDisabled,\n    config.autoFocusSearch,\n    config.emojiStyle,\n    config.theme,\n    config.suggestedEmojisMode,\n    config.lazyLoadEmojis,\n    config.className,\n    config.height,\n    config.width,\n    config.searchDisabled,\n    config.skinTonePickerLocation,\n    config.allowExpandReactions\n  ]);\n\n  return mergedConfig;\n}\n\nexport function usePickerConfig() {\n  return React.useContext(ConfigContext);\n}\n", "import React from 'react';\n\nimport {MouseDownEvent, OnSkinToneChange} from './config';\n\nexport type MutableConfig = {\n  onEmojiClick?: MouseDownEvent;\n  onReactionClick?: MouseDownEvent;\n  onSkinToneChange?: OnSkinToneChange;\n};\n\nexport const MutableConfigContext = React.createContext<\n  React.MutableRefObject<MutableConfig>\n>({} as React.MutableRefObject<MutableConfig>);\n\nexport function useMutableConfig(): React.MutableRefObject<MutableConfig> {\n  const mutableConfig = React.useContext(MutableConfigContext);\n  return mutableConfig;\n}\n\nexport function useDefineMutableConfig(\n  config: MutableConfig\n): React.MutableRefObject<MutableConfig> {\n  const MutableConfigRef = React.useRef<MutableConfig>({\n    onEmojiClick: config.onEmojiClick || emptyFunc,\n    onReactionClick: config.onReactionClick || config.onEmojiClick,\n    onSkinToneChange: config.onSkinToneChange || emptyFunc\n  });\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onEmojiClick = config.onEmojiClick || emptyFunc;\n    MutableConfigRef.current.onReactionClick =\n      config.onReactionClick || config.onEmojiClick;\n  }, [config.onEmojiClick, config.onReactionClick]);\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onSkinToneChange = config.onSkinToneChange || emptyFunc;\n  }, [config.onSkinToneChange])\n\n  return MutableConfigRef;\n}\n\nfunction emptyFunc() {}\n", "import * as React from 'react';\n\nimport { usePickerConfig } from '../components/context/PickerConfigContext';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport { CategoriesConfig } from './categoryConfig';\nimport {\n  DEFAULT_SEARCH_PLACEHOLDER,\n  SEARCH_RESULTS_NO_RESULTS_FOUND,\n  SEARCH_RESULTS_ONE_RESULT_FOUND,\n  SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND,\n  PickerDimensions,\n  PreviewConfig\n} from './config';\nimport { CustomEmoji } from './customEmojiConfig';\nimport { useMutableConfig } from './mutableConfig';\n\nexport enum MOUSE_EVENT_SOURCE {\n  REACTIONS = 'reactions',\n  PICKER = 'picker'\n}\n\nexport function useSearchPlaceHolderConfig(): string {\n  const { searchPlaceHolder, searchPlaceholder } = usePickerConfig();\n  return (\n    [searchPlaceHolder, searchPlaceholder].find(\n      p => p !== DEFAULT_SEARCH_PLACEHOLDER\n    ) ?? DEFAULT_SEARCH_PLACEHOLDER\n  );\n}\n\nexport function useDefaultSkinToneConfig(): SkinTones {\n  const { defaultSkinTone } = usePickerConfig();\n  return defaultSkinTone;\n}\n\nexport function useAllowExpandReactions(): boolean {\n  const { allowExpandReactions } = usePickerConfig();\n  return allowExpandReactions;\n}\n\nexport function useSkinTonesDisabledConfig(): boolean {\n  const { skinTonesDisabled } = usePickerConfig();\n  return skinTonesDisabled;\n}\n\nexport function useEmojiStyleConfig(): EmojiStyle {\n  const { emojiStyle } = usePickerConfig();\n  return emojiStyle;\n}\n\nexport function useAutoFocusSearchConfig(): boolean {\n  const { autoFocusSearch } = usePickerConfig();\n  return autoFocusSearch;\n}\n\nexport function useCategoriesConfig(): CategoriesConfig {\n  const { categories } = usePickerConfig();\n  return categories;\n}\n\nexport function useCustomEmojisConfig(): CustomEmoji[] {\n  const { customEmojis } = usePickerConfig();\n  return customEmojis;\n}\n\nexport function useOpenConfig(): boolean {\n  const { open } = usePickerConfig();\n  return open;\n}\n\nexport function useOnEmojiClickConfig(\n  mouseEventSource: MOUSE_EVENT_SOURCE\n): (emoji: EmojiClickData, event: MouseEvent) => void {\n  const { current } = useMutableConfig();\n\n  const handler =\n    (mouseEventSource === MOUSE_EVENT_SOURCE.REACTIONS\n      ? current.onReactionClick\n      : current.onEmojiClick) ?? current.onEmojiClick;\n\n  return handler || (() => {});\n}\n\nexport function useOnSkinToneChangeConfig(\n): (skinTone: SkinTones) => void {\n  const { current } = useMutableConfig();\n\n  return current.onSkinToneChange || (() => {});\n}\n\nexport function usePreviewConfig(): PreviewConfig {\n  const { previewConfig } = usePickerConfig();\n  return previewConfig;\n}\n\nexport function useThemeConfig(): Theme {\n  const { theme } = usePickerConfig();\n\n  return theme;\n}\n\nexport function useSuggestedEmojisModeConfig(): SuggestionMode {\n  const { suggestedEmojisMode } = usePickerConfig();\n  return suggestedEmojisMode;\n}\n\nexport function useLazyLoadEmojisConfig(): boolean {\n  const { lazyLoadEmojis } = usePickerConfig();\n  return lazyLoadEmojis;\n}\n\nexport function useClassNameConfig(): string {\n  const { className } = usePickerConfig();\n  return className;\n}\n\nexport function useStyleConfig(): React.CSSProperties {\n  const { height, width, style } = usePickerConfig();\n  return { height: getDimension(height), width: getDimension(width), ...style };\n}\n\nexport function useReactionsOpenConfig(): boolean {\n  const { reactionsDefaultOpen } = usePickerConfig();\n  return reactionsDefaultOpen;\n}\n\nexport function useEmojiVersionConfig(): string | null {\n  const { emojiVersion } = usePickerConfig();\n  return emojiVersion;\n}\n\nexport function useSearchDisabledConfig(): boolean {\n  const { searchDisabled } = usePickerConfig();\n  return searchDisabled;\n}\n\nexport function useSkinTonePickerLocationConfig(): SkinTonePickerLocation {\n  const { skinTonePickerLocation } = usePickerConfig();\n  return skinTonePickerLocation;\n}\n\nexport function useUnicodeToHide() {\n  const { unicodeToHide } = usePickerConfig();\n  return unicodeToHide;\n}\n\nexport function useReactionsConfig(): string[] {\n  const { reactions } = usePickerConfig();\n  return reactions;\n}\n\nexport function useGetEmojiUrlConfig(): (\n  unified: string,\n  style: EmojiStyle\n) => string {\n  const { getEmojiUrl } = usePickerConfig();\n  return getEmojiUrl;\n}\n\nfunction getDimension(dimensionConfig: PickerDimensions): PickerDimensions {\n  return typeof dimensionConfig === 'number'\n    ? `${dimensionConfig}px`\n    : dimensionConfig;\n}\n\nexport function useSearchResultsConfig(searchResultsCount: number): string {\n  const hasResults = searchResultsCount > 0;\n  const isPlural = searchResultsCount > 1;\n\n  if (hasResults) {\n    return isPlural\n      ? SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND.replace(\n          '%n',\n          searchResultsCount.toString()\n        )\n      : SEARCH_RESULTS_ONE_RESULT_FOUND;\n  }\n\n  return SEARCH_RESULTS_NO_RESULTS_FOUND;\n}\n", "import { useRef, useState } from 'react';\n\nexport function useDebouncedState<T>(\n  initialValue: T,\n  delay: number = 0\n): [T, (value: T) => Promise<T>] {\n  const [state, setState] = useState<T>(initialValue);\n  const timer = useRef<number | null>(null);\n\n  function debouncedSetState(value: T) {\n    return new Promise<T>(resolve => {\n      if (timer.current) {\n        clearTimeout(timer.current);\n      }\n\n      timer.current = window?.setTimeout(() => {\n        setState(value);\n        resolve(value);\n      }, delay);\n    });\n  }\n\n  return [state, debouncedSetState];\n}\n", "import { useUnicodeToHide } from \"../config/useConfig\";\n\nexport function useIsUnicodeHidden() {\n    const unicodeToHide = useUnicodeToHide();\n    return (emojiUnified: string) => unicodeToHide.has(emojiUnified);\n  }\n", "import { useRef, useMemo } from 'react';\n\nimport { useEmojiVersionConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  addedIn,\n  allEmojis,\n  emojiUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { useIsUnicodeHidden } from './useHideEmojisByUniocode';\n\nexport function useDisallowedEmojis() {\n  const DisallowedEmojisRef = useRef<Record<string, boolean>>({});\n  const emojiVersionConfig = useEmojiVersionConfig();\n\n  return useMemo(() => {\n    const emojiVersion = parseFloat(`${emojiVersionConfig}`);\n\n    if (!emojiVersionConfig || Number.isNaN(emojiVersion)) {\n      return DisallowedEmojisRef.current;\n    }\n\n    return allEmojis.reduce((disallowedEmojis, emoji) => {\n      if (addedInNewerVersion(emoji, emojiVersion)) {\n        disallowedEmojis[emojiUnified(emoji)] = true;\n      }\n\n      return disallowedEmojis;\n    }, DisallowedEmojisRef.current);\n  }, [emojiVersionConfig]);\n}\n\nexport function useIsEmojiDisallowed() {\n  const disallowedEmojis = useDisallowedEmojis();\n  const isUnicodeHidden = useIsUnicodeHidden();\n\n  return function isEmojiDisallowed(emoji: DataEmoji) {\n    const unified = unifiedWithoutSkinTone(emojiUnified(emoji));\n\n    return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));\n  };\n}\n\nfunction addedInNewerVersion(\n  emoji: DataEmoji,\n  supportedLevel: number\n): boolean {\n  return addedIn(emoji) > supportedLevel;\n}\n", "import { useEffect } from 'react';\nimport * as React from 'react';\n\nexport function useMarkInitialLoad(\n  dispatch: React.Dispatch<React.SetStateAction<boolean>>\n) {\n  useEffect(() => {\n    dispatch(true);\n  }, [dispatch]);\n}\n", "import * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  useDefaultSkinToneConfig,\n  useReactionsOpenConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { alphaNumericEmojiIndex } from '../../dataUtils/alphaNumericEmojiIndex';\nimport { useDebouncedState } from '../../hooks/useDebouncedState';\nimport { useDisallowedEmojis } from '../../hooks/useDisallowedEmojis';\nimport { FilterDict } from '../../hooks/useFilter';\nimport { useMarkInitialLoad } from '../../hooks/useInitialLoad';\nimport { SkinTones } from '../../types/exposedTypes';\n\nexport function PickerContextProvider({ children }: Props) {\n  const disallowedEmojis = useDisallowedEmojis();\n  const defaultSkinTone = useDefaultSkinToneConfig();\n  const reactionsDefaultOpen = useReactionsOpenConfig();\n\n  // Initialize the filter with the inititial dictionary\n  const filterRef = React.useRef<FilterState>(alphaNumericEmojiIndex);\n  const disallowClickRef = React.useRef<boolean>(false);\n  const disallowMouseRef = React.useRef<boolean>(false);\n  const disallowedEmojisRef = React.useRef<Record<string, boolean>>(\n    disallowedEmojis\n  );\n\n  const suggestedUpdateState = useDebouncedState(Date.now(), 200);\n  const searchTerm = useDebouncedState('', 100);\n  const skinToneFanOpenState = useState<boolean>(false);\n  const activeSkinTone = useState<SkinTones>(defaultSkinTone);\n  const activeCategoryState = useState<ActiveCategoryState>(null);\n  const emojisThatFailedToLoadState = useState<Set<string>>(new Set());\n  const emojiVariationPickerState = useState<DataEmoji | null>(null);\n  const reactionsModeState = useState(reactionsDefaultOpen);\n  const [isPastInitialLoad, setIsPastInitialLoad] = useState(false);\n\n  useMarkInitialLoad(setIsPastInitialLoad);\n\n  return (\n    <PickerContext.Provider\n      value={{\n        activeCategoryState,\n        activeSkinTone,\n        disallowClickRef,\n        disallowMouseRef,\n        disallowedEmojisRef,\n        emojiVariationPickerState,\n        emojisThatFailedToLoadState,\n        filterRef,\n        isPastInitialLoad,\n        searchTerm,\n        skinToneFanOpenState,\n        suggestedUpdateState,\n        reactionsModeState\n      }}\n    >\n      {children}\n    </PickerContext.Provider>\n  );\n}\n\ntype ReactState<T> = [T, React.Dispatch<React.SetStateAction<T>>];\n\nconst PickerContext = React.createContext<{\n  searchTerm: [string, (term: string) => Promise<string>];\n  suggestedUpdateState: [number, (term: number) => void];\n  activeCategoryState: ReactState<ActiveCategoryState>;\n  activeSkinTone: ReactState<SkinTones>;\n  emojisThatFailedToLoadState: ReactState<Set<string>>;\n  isPastInitialLoad: boolean;\n  emojiVariationPickerState: ReactState<DataEmoji | null>;\n  skinToneFanOpenState: ReactState<boolean>;\n  filterRef: React.MutableRefObject<FilterState>;\n  disallowClickRef: React.MutableRefObject<boolean>;\n  disallowMouseRef: React.MutableRefObject<boolean>;\n  disallowedEmojisRef: React.MutableRefObject<Record<string, boolean>>;\n  reactionsModeState: ReactState<boolean>;\n}>({\n  activeCategoryState: [null, () => {}],\n  activeSkinTone: [SkinTones.NEUTRAL, () => {}],\n  disallowClickRef: { current: false },\n  disallowMouseRef: { current: false },\n  disallowedEmojisRef: { current: {} },\n  emojiVariationPickerState: [null, () => {}],\n  emojisThatFailedToLoadState: [new Set(), () => {}],\n  filterRef: { current: {} },\n  isPastInitialLoad: true,\n  searchTerm: ['', () => new Promise<string>(() => undefined)],\n  skinToneFanOpenState: [false, () => {}],\n  suggestedUpdateState: [Date.now(), () => {}],\n  reactionsModeState: [false, () => {}]\n});\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport function useFilterRef() {\n  const { filterRef } = React.useContext(PickerContext);\n  return filterRef;\n}\n\nexport function useDisallowClickRef() {\n  const { disallowClickRef } = React.useContext(PickerContext);\n  return disallowClickRef;\n}\n\nexport function useDisallowMouseRef() {\n  const { disallowMouseRef } = React.useContext(PickerContext);\n  return disallowMouseRef;\n}\n\nexport function useReactionsModeState() {\n  const { reactionsModeState } = React.useContext(PickerContext);\n  return reactionsModeState;\n}\n\nexport function useSearchTermState() {\n  const { searchTerm } = React.useContext(PickerContext);\n  return searchTerm;\n}\n\nexport function useActiveSkinToneState(): [\n  SkinTones,\n  (skinTone: SkinTones) => void\n] {\n  const { activeSkinTone } = React.useContext(PickerContext);\n  return activeSkinTone;\n}\n\nexport function useEmojisThatFailedToLoadState() {\n  const { emojisThatFailedToLoadState } = React.useContext(PickerContext);\n  return emojisThatFailedToLoadState;\n}\n\nexport function useIsPastInitialLoad(): boolean {\n  const { isPastInitialLoad } = React.useContext(PickerContext);\n  return isPastInitialLoad;\n}\n\nexport function useEmojiVariationPickerState() {\n  const { emojiVariationPickerState } = React.useContext(PickerContext);\n  return emojiVariationPickerState;\n}\n\nexport function useSkinToneFanOpenState() {\n  const { skinToneFanOpenState } = React.useContext(PickerContext);\n  return skinToneFanOpenState;\n}\n\nexport function useDisallowedEmojisRef() {\n  const { disallowedEmojisRef } = React.useContext(PickerContext);\n  return disallowedEmojisRef;\n}\n\nexport function useUpdateSuggested(): [number, () => void] {\n  const { suggestedUpdateState } = React.useContext(PickerContext);\n\n  const [suggestedUpdated, setsuggestedUpdate] = suggestedUpdateState;\n  return [\n    suggestedUpdated,\n    function updateSuggested() {\n      setsuggestedUpdate(Date.now());\n    }\n  ];\n}\n\nexport type FilterState = Record<string, FilterDict>;\n\ntype ActiveCategoryState = null | string;\n", "import { useSearchTermState } from '../components/context/PickerContext';\n\nexport default function useIsSearchMode(): boolean {\n  const [searchTerm] = useSearchTermState();\n\n  return !!searchTerm;\n}\n", "import { NullableElement } from './selectors';\n\nexport function focusElement(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    element.focus();\n  });\n}\n\nexport function focusPrevElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const prev = element.previousElementSibling as HTMLElement;\n\n  focusElement(prev);\n}\n\nexport function focusNextElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const next = element.nextElementSibling as HTMLElement;\n\n  focusElement(next);\n}\n\nexport function focusFirstElementChild(element: NullableElement) {\n  if (!element) return;\n\n  const first = element.firstElementChild as HTMLElement;\n\n  focusElement(first);\n}\n", "import { NullableElement } from './selectors';\n\nexport function getActiveElement() {\n  return document.activeElement as NullableElement;\n}\n", "import * as React from 'react';\n\nimport { focusElement } from '../../DomUtils/focusElement';\nimport { NullableElement } from '../../DomUtils/selectors';\n\nexport function ElementRefContextProvider({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const PickerMainRef = React.useRef<HTMLElement>(null);\n  const AnchoredEmojiRef = React.useRef<HTMLElement>(null);\n  const BodyRef = React.useRef<HTMLDivElement>(null);\n  const SearchInputRef = React.useRef<HTMLInputElement>(null);\n  const SkinTonePickerRef = React.useRef<HTMLDivElement>(null);\n  const CategoryNavigationRef = React.useRef<HTMLDivElement>(null);\n  const VariationPickerRef = React.useRef<HTMLDivElement>(null);\n  const ReactionsRef = React.useRef<HTMLUListElement>(null);\n\n  return (\n    <ElementRefContext.Provider\n      value={{\n        AnchoredEmojiRef,\n        BodyRef,\n        CategoryNavigationRef,\n        PickerMainRef,\n        SearchInputRef,\n        SkinTonePickerRef,\n        VariationPickerRef,\n        ReactionsRef\n      }}\n    >\n      {children}\n    </ElementRefContext.Provider>\n  );\n}\n\nexport type ElementRef<\n  E extends HTMLElement = HTMLElement\n> = React.MutableRefObject<E | null>;\n\ntype ElementRefs = {\n  PickerMainRef: ElementRef;\n  AnchoredEmojiRef: ElementRef;\n  SkinTonePickerRef: ElementRef<HTMLDivElement>;\n  SearchInputRef: ElementRef<HTMLInputElement>;\n  BodyRef: ElementRef<HTMLDivElement>;\n  CategoryNavigationRef: ElementRef<HTMLDivElement>;\n  VariationPickerRef: ElementRef<HTMLDivElement>;\n  ReactionsRef: ElementRef<HTMLUListElement>;\n};\n\nconst ElementRefContext = React.createContext<ElementRefs>({\n  AnchoredEmojiRef: React.createRef(),\n  BodyRef: React.createRef(),\n  CategoryNavigationRef: React.createRef(),\n  PickerMainRef: React.createRef(),\n  SearchInputRef: React.createRef(),\n  SkinTonePickerRef: React.createRef(),\n  VariationPickerRef: React.createRef(),\n  ReactionsRef: React.createRef()\n});\n\nfunction useElementRef() {\n  return React.useContext(ElementRefContext);\n}\n\nexport function usePickerMainRef() {\n  return useElementRef()['PickerMainRef'];\n}\n\nexport function useAnchoredEmojiRef() {\n  return useElementRef()['AnchoredEmojiRef'];\n}\n\nexport function useSetAnchoredEmojiRef(): (target: NullableElement) => void {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return (target: NullableElement) => {\n    if (target === null && AnchoredEmojiRef.current !== null) {\n      focusElement(AnchoredEmojiRef.current);\n    }\n\n    AnchoredEmojiRef.current = target;\n  };\n}\n\nexport function useBodyRef() {\n  return useElementRef()['BodyRef'];\n}\n\nexport function useReactionsRef() {\n  return useElementRef()['ReactionsRef'];\n}\n\nexport function useSearchInputRef() {\n  return useElementRef()['SearchInputRef'];\n}\n\nexport function useSkinTonePickerRef() {\n  return useElementRef()['SkinTonePickerRef'];\n}\n\nexport function useCategoryNavigationRef() {\n  return useElementRef()['CategoryNavigationRef'];\n}\n\nexport function useVariationPickerRef() {\n  return useElementRef()['VariationPickerRef'];\n}\n", "import { useCallback } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport {\n  categoryLabelHeight,\n  closestCategory,\n  closestScrollBody,\n  emojiDistanceFromScrollTop,\n  isEmojiBehindLabel,\n  NullableElement,\n  queryScrollBody\n} from './selectors';\n\nexport function scrollTo(root: NullableElement, top: number = 0) {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = top;\n  });\n}\n\nexport function scrollBy(root: NullableElement, by: number): void {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = $eprBody.scrollTop + by;\n  });\n}\n\nexport function useScrollTo() {\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    (top: number) => {\n      requestAnimationFrame(() => {\n        if (BodyRef.current) {\n          BodyRef.current.scrollTop = top;\n        }\n      });\n    },\n    [BodyRef]\n  );\n}\n\nexport function scrollEmojiAboveLabel(emoji: NullableElement) {\n  if (!emoji || !isEmojiBehindLabel(emoji)) {\n    return;\n  }\n\n  if (emoji.closest(asSelectors(ClassNames.variationPicker))) {\n    return;\n  }\n\n  const scrollBody = closestScrollBody(emoji);\n  const by = emojiDistanceFromScrollTop(emoji);\n  scrollBy(scrollBody, -(categoryLabelHeight(closestCategory(emoji)) - by));\n}\n", "import {\n  elementCountInRow,\n  elementIndexInRow,\n  getElementInNextRow,\n  getElementInPrevRow,\n  getElementInRow,\n  hasNextRow,\n  rowNumber\n} from './elementPositionInRow';\nimport { focusElement } from './focusElement';\nimport { scrollEmojiAboveLabel } from './scrollTo';\nimport {\n  allVisibleEmojis,\n  closestCategory,\n  firstVisibleEmoji,\n  lastVisibleEmoji,\n  nextCategory,\n  nextVisibleEmoji,\n  NullableElement,\n  prevCategory,\n  prevVisibleEmoji,\n  closestCategoryContent\n} from './selectors';\n\nexport function focusFirstVisibleEmoji(parent: NullableElement) {\n  const emoji = firstVisibleEmoji(parent);\n  focusElement(emoji);\n  scrollEmojiAboveLabel(emoji);\n}\n\nexport function focusAndClickFirstVisibleEmoji(parent: NullableElement) {\n  const firstEmoji = firstVisibleEmoji(parent);\n\n  focusElement(firstEmoji);\n  firstEmoji?.click();\n}\n\nexport function focusLastVisibleEmoji(parent: NullableElement) {\n  focusElement(lastVisibleEmoji(parent));\n}\n\nexport function focusNextVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = nextVisibleEmoji(element);\n\n  if (!next) {\n    return focusFirstVisibleEmoji(nextCategory(element));\n  }\n\n  focusElement(next);\n  scrollEmojiAboveLabel(next);\n}\n\nexport function focusPrevVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const prev = prevVisibleEmoji(element);\n\n  if (!prev) {\n    return focusLastVisibleEmoji(prevCategory(element));\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowUp(\n  element: NullableElement,\n  exitUp: () => void\n) {\n  if (!element) {\n    return;\n  }\n\n  const prev = visibleEmojiOneRowUp(element);\n\n  if (!prev) {\n    return exitUp();\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowDown(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = visibleEmojiOneRowDown(element);\n\n  return focusElement(next);\n}\n\nfunction visibleEmojiOneRowUp(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n\n  if (row === 0) {\n    const prevVisibleCategory = prevCategory(category);\n\n    if (!prevVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(prevVisibleCategory),\n      -1, // last row\n      countInRow,\n      indexInRow\n    );\n  }\n\n  return getElementInPrevRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n}\n\nfunction visibleEmojiOneRowDown(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n  if (!hasNextRow(categoryContent, element)) {\n    const nextVisibleCategory = nextCategory(category);\n\n    if (!nextVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(nextVisibleCategory),\n      0,\n      countInRow,\n      indexInRow\n    );\n  }\n\n  const itemInNextRow = getElementInNextRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n\n  return itemInNextRow;\n}\n", "import { useCallback } from 'react';\n\nimport {\n  useEmojiVariationPickerState,\n  useSkinToneFanOpenState\n} from '../components/context/PickerContext';\n\nexport function useCloseAllOpenToggles() {\n  const [variationPicker, setVariationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen, setSkinToneFanOpen] = useSkinToneFanOpenState();\n\n  const closeAllOpenToggles = useCallback(() => {\n    if (variationPicker) {\n      setVariationPicker(null);\n    }\n\n    if (skinToneFanOpen) {\n      setSkinToneFanOpen(false);\n    }\n  }, [\n    variationPicker,\n    skinToneFanOpen,\n    setVariationPicker,\n    setSkinToneFanOpen\n  ]);\n\n  return closeAllOpenToggles;\n}\n\nexport function useHasOpenToggles() {\n  const [variationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen] = useSkinToneFanOpenState();\n\n  return function hasOpenToggles() {\n    return !!variationPicker || skinToneFanOpen;\n  };\n}\n", "import { useEffect } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useDisallowMouseRef } from '../components/context/PickerContext';\n\nexport function useDisallowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function disallowMouseMove() {\n    DisallowMouseRef.current = true;\n  };\n}\n\nexport function useAllowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function allowMouseMove() {\n    DisallowMouseRef.current = false;\n  };\n}\n\nexport function useIsMouseDisallowed() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function isMouseDisallowed() {\n    return DisallowMouseRef.current;\n  };\n}\n\nexport function useOnMouseMove() {\n  const BodyRef = useBodyRef();\n  const allowMouseMove = useAllowMouseMove();\n  const isMouseDisallowed = useIsMouseDisallowed();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    bodyRef?.addEventListener('mousemove', onMouseMove, {\n      passive: true\n    });\n\n    function onMouseMove() {\n      if (isMouseDisallowed()) {\n        allowMouseMove();\n      }\n    }\n    return () => {\n      bodyRef?.removeEventListener('mousemove', onMouseMove);\n    };\n  }, [BodyRef, allowMouseMove, isMouseDisallowed]);\n}\n", "import { useCallback } from 'react';\n\nimport { focusElement, focusFirstElementChild } from '../DomUtils/focusElement';\nimport {\n  useCategoryNavigationRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\n\nexport function useFocusSearchInput() {\n  const SearchInputRef = useSearchInputRef();\n\n  return useCallback(() => {\n    focusElement(SearchInputRef.current);\n  }, [SearchInputRef]);\n}\n\nexport function useFocusSkinTonePicker() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n\n  return useCallback(() => {\n    if (!SkinTonePickerRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(SkinTonePickerRef.current);\n  }, [SkinTonePickerRef]);\n}\n\nexport function useFocusCategoryNavigation() {\n  const CategoryNavigationRef = useCategoryNavigationRef();\n\n  return useCallback(() => {\n    if (!CategoryNavigationRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(CategoryNavigationRef.current);\n  }, [CategoryNavigationRef]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport {\n  usePickerMainRef,\n  useSearchInputRef\n} from '../components/context/ElementRefContext';\nimport {\n  FilterState,\n  useFilterRef,\n  useSearchTermState\n} from '../components/context/PickerContext';\nimport { useSearchResultsConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiNames } from '../dataUtils/emojiSelectors';\n\nimport { useFocusSearchInput } from './useFocus';\n\nfunction useSetFilterRef() {\n  const filterRef = useFilterRef();\n\n  return function setFilter(\n    setter: FilterState | ((current: FilterState) => FilterState)\n  ): void {\n    if (typeof setter === 'function') {\n      return setFilter(setter(filterRef.current));\n    }\n\n    filterRef.current = setter;\n  };\n}\n\nexport function useClearSearch() {\n  const applySearch = useApplySearch();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n\n  return function clearSearch() {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = '';\n    }\n\n    applySearch('');\n    focusSearchInput();\n  };\n}\n\nexport function useAppendSearch() {\n  const SearchInputRef = useSearchInputRef();\n  const applySearch = useApplySearch();\n\n  return function appendSearch(str: string) {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = `${SearchInputRef.current.value}${str}`;\n      applySearch(getNormalizedSearchTerm(SearchInputRef.current.value));\n    } else {\n      applySearch(getNormalizedSearchTerm(str));\n    }\n  };\n}\n\nexport function useFilter() {\n  const SearchInputRef = useSearchInputRef();\n  const filterRef = useFilterRef();\n  const setFilterRef = useSetFilterRef();\n  const applySearch = useApplySearch();\n\n  const [searchTerm] = useSearchTermState();\n  const statusSearchResults = getStatusSearchResults(\n    filterRef.current,\n    searchTerm\n  );\n\n  return {\n    onChange,\n    searchTerm,\n    SearchInputRef,\n    statusSearchResults\n  };\n\n  function onChange(inputValue: string) {\n    const filter = filterRef.current;\n\n    const nextValue = inputValue.toLowerCase();\n\n    if (filter?.[nextValue] || nextValue.length <= 1) {\n      return applySearch(nextValue);\n    }\n\n    const longestMatch = findLongestMatch(nextValue, filter);\n\n    if (!longestMatch) {\n      // Can we even get here?\n      // If so, we need to search among all emojis\n      return applySearch(nextValue);\n    }\n\n    setFilterRef(current =>\n      Object.assign(current, {\n        [nextValue]: filterEmojiObjectByKeyword(longestMatch, nextValue)\n      })\n    );\n    applySearch(nextValue);\n  }\n}\n\nfunction useApplySearch() {\n  const [, setSearchTerm] = useSearchTermState();\n  const PickerMainRef = usePickerMainRef();\n\n  return function applySearch(searchTerm: string) {\n    requestAnimationFrame(() => {\n      setSearchTerm(searchTerm ? searchTerm?.toLowerCase() : searchTerm).then(\n        () => {\n          scrollTo(PickerMainRef.current, 0);\n        }\n      );\n    });\n  };\n}\n\nfunction filterEmojiObjectByKeyword(\n  emojis: FilterDict,\n  keyword: string\n): FilterDict {\n  const filtered: FilterDict = {};\n\n  for (const unified in emojis) {\n    const emoji = emojis[unified];\n\n    if (hasMatch(emoji, keyword)) {\n      filtered[unified] = emoji;\n    }\n  }\n\n  return filtered;\n}\n\nfunction hasMatch(emoji: DataEmoji, keyword: string): boolean {\n  return emojiNames(emoji).some(name => name.includes(keyword));\n}\n\nexport function useIsEmojiFiltered(): (unified: string) => boolean {\n  const { current: filter } = useFilterRef();\n  const [searchTerm] = useSearchTermState();\n\n  return unified => isEmojiFilteredBySearchTerm(unified, filter, searchTerm);\n}\n\nfunction isEmojiFilteredBySearchTerm(\n  unified: string,\n  filter: FilterState,\n  searchTerm: string\n): boolean {\n  if (!filter || !searchTerm) {\n    return false;\n  }\n\n  return !filter[searchTerm]?.[unified];\n}\n\nexport type FilterDict = Record<string, DataEmoji>;\n\nfunction findLongestMatch(\n  keyword: string,\n  dict: Record<string, FilterDict> | null\n): FilterDict | null {\n  if (!dict) {\n    return null;\n  }\n\n  if (dict[keyword]) {\n    return dict[keyword];\n  }\n\n  const longestMatchingKey = Object.keys(dict)\n    .sort((a, b) => b.length - a.length)\n    .find(key => keyword.includes(key));\n\n  if (longestMatchingKey) {\n    return dict[longestMatchingKey];\n  }\n\n  return null;\n}\n\nexport function getNormalizedSearchTerm(str: string): string {\n  if (!str || typeof str !== 'string') {\n    return '';\n  }\n\n  return str.trim().toLowerCase();\n}\n\nfunction getStatusSearchResults(\n  filterState: FilterState,\n  searchTerm: string\n): string {\n  if (!filterState?.[searchTerm]) return '';\n\n  const searchResultsCount =\n    Object.entries(filterState?.[searchTerm])?.length || 0;\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useSearchResultsConfig(searchResultsCount);\n}\n", "import { emojiFromElement, NullableElement } from '../DomUtils/selectors';\nimport { useSetAnchoredEmojiRef } from '../components/context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../components/context/PickerContext';\n\nexport default function useSetVariationPicker() {\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n\n  return function setVariationPicker(element: NullableElement) {\n    const [emoji] = emojiFromElement(element);\n\n    if (emoji) {\n      setAnchoredEmojiRef(element);\n      setEmojiVariationPicker(emoji);\n    }\n  };\n}\n", "import { useSkinTonePickerLocationConfig } from '../config/useConfig';\nimport { SkinTonePickerLocation } from '../types/exposedTypes';\n\nexport function useShouldShowSkinTonePicker() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return function shouldShowSkinTonePicker(location: SkinTonePickerLocation) {\n    return skinTonePickerLocationConfig === location;\n  };\n}\n\nexport function useIsSkinToneInSearch() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.SEARCH;\n}\n\nexport function useIsSkinToneInPreview() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.PREVIEW;\n}\n", "import { useCallback, useEffect, useMemo } from 'react';\n\nimport { hasNextElementSibling } from '../DomUtils/elementPositionInRow';\nimport {\n  focusNextElementSibling,\n  focusPrevElementSibling\n} from '../DomUtils/focusElement';\nimport { getActiveElement } from '../DomUtils/getActiveElement';\nimport {\n  focusAndClickFirstVisibleEmoji,\n  focusFirstVisibleEmoji,\n  focusNextVisibleEmoji,\n  focusPrevVisibleEmoji,\n  focusVisibleEmojiOneRowDown,\n  focusVisibleEmojiOneRowUp\n} from '../DomUtils/keyboardNavigation';\nimport { useScrollTo } from '../DomUtils/scrollTo';\nimport { buttonFromTarget } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  useCategoryNavigationRef,\n  usePickerMainRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\nimport { useSkinToneFanOpenState } from '../components/context/PickerContext';\nimport { useSearchDisabledConfig } from '../config/useConfig';\n\nimport {\n  useCloseAllOpenToggles,\n  useHasOpenToggles\n} from './useCloseAllOpenToggles';\nimport { useDisallowMouseMove } from './useDisallowMouseMove';\nimport { useAppendSearch, useClearSearch } from './useFilter';\nimport {\n  useFocusCategoryNavigation,\n  useFocusSearchInput,\n  useFocusSkinTonePicker\n} from './useFocus';\nimport useIsSearchMode from './useIsSearchMode';\nimport useSetVariationPicker from './useSetVariationPicker';\nimport {\n  useIsSkinToneInPreview,\n  useIsSkinToneInSearch\n} from './useShouldShowSkinTonePicker';\n\nenum KeyboardEvents {\n  ArrowDown = 'ArrowDown',\n  ArrowUp = 'ArrowUp',\n  ArrowLeft = 'ArrowLeft',\n  ArrowRight = 'ArrowRight',\n  Escape = 'Escape',\n  Enter = 'Enter',\n  Space = ' '\n}\n\nexport function useKeyboardNavigation() {\n  usePickerMainKeyboardEvents();\n  useSearchInputKeyboardEvents();\n  useSkinTonePickerKeyboardEvents();\n  useCategoryNavigationKeyboardEvents();\n  useBodyKeyboardEvents();\n}\n\nfunction usePickerMainKeyboardEvents() {\n  const PickerMainRef = usePickerMainRef();\n  const clearSearch = useClearSearch();\n  const scrollTo = useScrollTo();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n  const hasOpenToggles = useHasOpenToggles();\n  const disallowMouseMove = useDisallowMouseMove();\n\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        disallowMouseMove();\n        switch (key) {\n          // eslint-disable-next-line no-fallthrough\n          case KeyboardEvents.Escape:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              return;\n            }\n            clearSearch();\n            scrollTo(0);\n            focusSearchInput();\n            break;\n        }\n      },\n    [\n      scrollTo,\n      clearSearch,\n      closeAllOpenToggles,\n      focusSearchInput,\n      hasOpenToggles,\n      disallowMouseMove\n    ]\n  );\n\n  useEffect(() => {\n    const current = PickerMainRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, scrollTo, onKeyDown]);\n}\n\nfunction useSearchInputKeyboardEvents() {\n  const focusSkinTonePicker = useFocusSkinTonePicker();\n  const PickerMainRef = usePickerMainRef();\n  const BodyRef = useBodyRef();\n  const SearchInputRef = useSearchInputRef();\n  const [, setSkinToneFanOpenState] = useSkinToneFanOpenState();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            if (!isSkinToneInSearch) {\n              return;\n            }\n            event.preventDefault();\n            setSkinToneFanOpenState(true);\n            focusSkinTonePicker();\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            goDownFromSearchInput();\n            break;\n          case KeyboardEvents.Enter:\n            event.preventDefault();\n            focusAndClickFirstVisibleEmoji(BodyRef.current);\n            break;\n        }\n      },\n    [\n      focusSkinTonePicker,\n      goDownFromSearchInput,\n      setSkinToneFanOpenState,\n      BodyRef,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SearchInputRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, onKeyDown]);\n}\n\nfunction useSkinTonePickerKeyboardEvents() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const focusSearchInput = useFocusSearchInput();\n  const SearchInputRef = useSearchInputRef();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        if (isSkinToneInSearch) {\n          switch (key) {\n            case KeyboardEvents.ArrowLeft:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowRight:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (isOpen) {\n                setIsOpen(false);\n              }\n              goDownFromSearchInput();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n\n        if (isSkinToneInPreview) {\n          switch (key) {\n            case KeyboardEvents.ArrowUp:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n      },\n    [\n      isOpen,\n      focusSearchInput,\n      setIsOpen,\n      goDownFromSearchInput,\n      onType,\n      isSkinToneInPreview,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SkinTonePickerRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [SkinTonePickerRef, SearchInputRef, isOpen, onKeyDown]);\n}\n\nfunction useCategoryNavigationKeyboardEvents() {\n  const focusSearchInput = useFocusSearchInput();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const BodyRef = useBodyRef();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            focusSearchInput();\n            break;\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            focusFirstVisibleEmoji(BodyRef.current);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [BodyRef, focusSearchInput, onType]\n  );\n\n  useEffect(() => {\n    const current = CategoryNavigationRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [CategoryNavigationRef, BodyRef, onKeyDown]);\n}\n\nfunction useBodyKeyboardEvents() {\n  const BodyRef = useBodyRef();\n  const goUpFromBody = useGoUpFromBody();\n  const setVariationPicker = useSetVariationPicker();\n  const hasOpenToggles = useHasOpenToggles();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        const activeElement = buttonFromTarget(getActiveElement());\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowDown(activeElement);\n            break;\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowUp(activeElement, goUpFromBody);\n            break;\n          case KeyboardEvents.Space:\n            event.preventDefault();\n            setVariationPicker(event.target as HTMLElement);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [\n      goUpFromBody,\n      onType,\n      setVariationPicker,\n      hasOpenToggles,\n      closeAllOpenToggles\n    ]\n  );\n\n  useEffect(() => {\n    const current = BodyRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [BodyRef, onKeyDown]);\n}\n\nfunction useGoDownFromSearchInput() {\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    function goDownFromSearchInput() {\n      if (isSearchMode) {\n        return focusFirstVisibleEmoji(BodyRef.current);\n      }\n      return focusCategoryNavigation();\n    },\n    [BodyRef, focusCategoryNavigation, isSearchMode]\n  );\n}\n\nfunction useGoUpFromBody() {\n  const focusSearchInput = useFocusSearchInput();\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n\n  return useCallback(\n    function goUpFromEmoji() {\n      if (isSearchMode) {\n        return focusSearchInput();\n      }\n      return focusCategoryNavigation();\n    },\n    [focusSearchInput, isSearchMode, focusCategoryNavigation]\n  );\n}\n\nfunction focusNextSkinTone(exitLeft: () => void) {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  if (!hasNextElementSibling(currentSkinTone)) {\n    exitLeft();\n  }\n\n  focusNextElementSibling(currentSkinTone);\n}\n\nfunction focusPrevSkinTone() {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  focusPrevElementSibling(currentSkinTone);\n}\n\nfunction useOnType() {\n  const appendSearch = useAppendSearch();\n  const focusSearchInput = useFocusSearchInput();\n  const searchDisabled = useSearchDisabledConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  return function onType(event: KeyboardEvent) {\n    const { key } = event;\n\n    if (hasModifier(event) || searchDisabled) {\n      return;\n    }\n\n    if (key.match(/(^[a-zA-Z0-9]$){1}/)) {\n      event.preventDefault();\n      closeAllOpenToggles();\n      focusSearchInput();\n      appendSearch(key);\n    }\n  };\n}\n\nfunction hasModifier(event: KeyboardEvent): boolean {\n  const { metaKey, ctrlKey, altKey } = event;\n\n  return metaKey || ctrlKey || altKey;\n}\n", "import { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified, emojiVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nexport function preloadEmoji(\n  getEmojiUrl: GetEmojiUrl,\n  emoji: undefined | DataEmoji,\n  emojiStyle: EmojiStyle\n): void {\n  if (!emoji) {\n    return;\n  }\n\n  if (emojiStyle === EmojiStyle.NATIVE) {\n    return;\n  }\n\n  const unified = emojiUnified(emoji);\n\n  if (preloadedEmojs.has(unified)) {\n    return;\n  }\n\n  emojiVariations(emoji).forEach((variation) => {\n    const emojiUrl = getEmojiUrl(variation, emojiStyle);\n    preloadImage(emojiUrl);\n  });\n\n  preloadedEmojs.add(unified);\n}\n\nexport const preloadedEmojs: Set<string> = new Set();\n\nfunction preloadImage(url: string): void {\n  const image = new Image();\n  image.src = url;\n}\n", "import { useEffect } from 'react';\n\nimport { buttonFromTarget, emojiFromElement } from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useEmojiStyleConfig, useGetEmojiUrlConfig } from '../config/useConfig';\nimport { emojiHasVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nimport { preloadEmoji } from './preloadEmoji';\n\nexport function useOnFocus() {\n  const BodyRef = useBodyRef();\n  const emojiStyle = useEmojiStyleConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEffect(() => {\n    if (emojiStyle === EmojiStyle.NATIVE) {\n      return;\n    }\n\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('focusin', onFocus);\n\n    return () => {\n      bodyRef?.removeEventListener('focusin', onFocus);\n    };\n\n    function onFocus(event: FocusEvent) {\n      const button = buttonFromTarget(event.target as HTMLElement);\n\n      if (!button) {\n        return;\n      }\n\n      const [emoji] = emojiFromElement(button);\n\n      if (!emoji) {\n        return;\n      }\n\n      if (emojiHasVariations(emoji)) {\n        preloadEmoji(getEmojiUrl, emoji, emojiStyle);\n      }\n    }\n  }, [BodyRef, emojiStyle, getEmojiUrl]);\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useClassNameConfig,\n  useStyleConfig,\n  useThemeConfig\n} from '../../config/useConfig';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';\nimport { useOnFocus } from '../../hooks/useOnFocus';\nimport { Theme } from '../../types/exposedTypes';\nimport { usePickerMainRef } from '../context/ElementRefContext';\nimport {\n  PickerContextProvider,\n  useReactionsModeState\n} from '../context/PickerContext';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport const DEFAULT_LABEL_HEIGHT = 40;\n\nexport default function PickerMain({ children }: Props) {\n  return (\n    <PickerContextProvider>\n      <PickerRootElement>{children}</PickerRootElement>\n    </PickerContextProvider>\n  );\n}\n\ntype RootProps = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n  children: React.ReactNode;\n}>;\n\nfunction PickerRootElement({ children }: RootProps) {\n  const [reactionsMode] = useReactionsModeState();\n  const theme = useThemeConfig();\n  const searchModeActive = useIsSearchMode();\n  const PickerMainRef = usePickerMainRef();\n  const className = useClassNameConfig();\n  const style = useStyleConfig();\n\n  useKeyboardNavigation();\n  useOnFocus();\n\n  const { width, height, ...styleProps } = style || {};\n\n  return (\n    <aside\n      className={cx(\n        styles.main,\n        styles.baseVariables,\n        theme === Theme.DARK && styles.darkTheme,\n        theme === Theme.AUTO && styles.autoThemeDark,\n        {\n          [ClassNames.searchActive]: searchModeActive\n        },\n        reactionsMode && styles.reactionsMenu,\n        className\n      )}\n      ref={PickerMainRef}\n      style={{\n        ...styleProps,\n        ...(!reactionsMode && { height, width })\n      }}\n    >\n      {children}\n    </aside>\n  );\n}\n\nconst DarkTheme = {\n  '--epr-emoji-variation-picker-bg-color':\n    'var(--epr-dark-emoji-variation-picker-bg-color)',\n  '--epr-hover-bg-color-reduced-opacity':\n    'var(--epr-dark-hover-bg-color-reduced-opacity)',\n  '--epr-highlight-color': 'var(--epr-dark-highlight-color)',\n  '--epr-text-color': 'var(--epr-dark-text-color)',\n  '--epr-hover-bg-color': 'var(--epr-dark-hover-bg-color)',\n  '--epr-focus-bg-color': 'var(--epr-dark-focus-bg-color)',\n  '--epr-search-input-bg-color': 'var(--epr-dark-search-input-bg-color)',\n  '--epr-category-label-bg-color': 'var(--epr-dark-category-label-bg-color)',\n  '--epr-picker-border-color': 'var(--epr-dark-picker-border-color)',\n  '--epr-bg-color': 'var(--epr-dark-bg-color)',\n  '--epr-reactions-bg-color': 'var(--epr-dark-reactions-bg-color)',\n  '--epr-search-input-bg-color-active':\n    'var(--epr-dark-search-input-bg-color-active)',\n  '--epr-emoji-variation-indicator-color':\n    'var(--epr-dark-emoji-variation-indicator-color)',\n  '--epr-category-icon-active-color':\n    'var(--epr-dark-category-icon-active-color)',\n  '--epr-skin-tone-picker-menu-color':\n    'var(--epr-dark-skin-tone-picker-menu-color)',\n  '--epr-skin-tone-outer-border-color': 'var(--epr-dark-skin-tone-outer-border-color)',\n  '--epr-skin-tone-inner-border-color': 'var(--epr-dark-skin-tone-inner-border-color)'\n};\n\nconst styles = stylesheet.create({\n  main: {\n    '.': ['epr-main', ClassNames.emojiPicker],\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    borderWidth: '1px',\n    borderStyle: 'solid',\n    borderRadius: 'var(--epr-picker-border-radius)',\n    borderColor: 'var(--epr-picker-border-color)',\n    backgroundColor: 'var(--epr-bg-color)',\n    overflow: 'hidden',\n    transition: 'all 0.3s ease-in-out, background-color 0.1s ease-in-out',\n    '*': {\n      boxSizing: 'border-box',\n      fontFamily: 'sans-serif'\n    }\n  },\n  baseVariables: {\n    '--': {\n      '--epr-highlight-color': '#007aeb',\n      '--epr-hover-bg-color': '#e5f0fa',\n      '--epr-hover-bg-color-reduced-opacity': '#e5f0fa80',\n      '--epr-focus-bg-color': '#e0f0ff',\n      '--epr-text-color': '#858585',\n      '--epr-search-input-bg-color': '#f6f6f6',\n      '--epr-picker-border-color': '#e7e7e7',\n      '--epr-bg-color': '#fff',\n      '--epr-reactions-bg-color': '#ffffff90',\n      '--epr-category-icon-active-color': '#6aa8de',\n      '--epr-skin-tone-picker-menu-color': '#ffffff95',\n      '--epr-skin-tone-outer-border-color': '#555555',\n      '--epr-skin-tone-inner-border-color': 'var(--epr-bg-color)',\n\n      '--epr-horizontal-padding': '10px',\n\n      '--epr-picker-border-radius': '8px',\n\n      /* Header */\n      '--epr-search-border-color': 'var(--epr-highlight-color)',\n      '--epr-header-padding': '15px var(--epr-horizontal-padding)',\n\n      /* Skin Tone Picker */\n      '--epr-active-skin-tone-indicator-border-color':\n        'var(--epr-highlight-color)',\n      '--epr-active-skin-hover-color': 'var(--epr-hover-bg-color)',\n\n      /* Search */\n      '--epr-search-input-bg-color-active': 'var(--epr-search-input-bg-color)',\n      '--epr-search-input-padding': '0 30px',\n      '--epr-search-input-border-radius': '8px',\n      '--epr-search-input-height': '40px',\n      '--epr-search-input-text-color': 'var(--epr-text-color)',\n      '--epr-search-input-placeholder-color': 'var(--epr-text-color)',\n      '--epr-search-bar-inner-padding': 'var(--epr-horizontal-padding)',\n\n      /*  Category Navigation */\n      '--epr-category-navigation-button-size': '30px',\n\n      /* Variation Picker */\n      '--epr-emoji-variation-picker-height': '45px',\n      '--epr-emoji-variation-picker-bg-color': 'var(--epr-bg-color)',\n\n      /*  Preview */\n      '--epr-preview-height': '70px',\n      '--epr-preview-text-size': '14px',\n      '--epr-preview-text-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-preview-border-color': 'var(--epr-picker-border-color)',\n      '--epr-preview-text-color': 'var(--epr-text-color)',\n\n      /* Category */\n      '--epr-category-padding': '0 var(--epr-horizontal-padding)',\n\n      /*  Category Label */\n      '--epr-category-label-bg-color': '#ffffffe6',\n      '--epr-category-label-text-color': 'var(--epr-text-color)',\n      '--epr-category-label-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-category-label-height': `${DEFAULT_LABEL_HEIGHT}px`,\n\n      /*  Emoji */\n      '--epr-emoji-size': '30px',\n      '--epr-emoji-padding': '5px',\n      '--epr-emoji-fullsize':\n        'calc(var(--epr-emoji-size) + var(--epr-emoji-padding) * 2)',\n      '--epr-emoji-hover-color': 'var(--epr-hover-bg-color)',\n      '--epr-emoji-variation-indicator-color': 'var(--epr-picker-border-color)',\n      '--epr-emoji-variation-indicator-color-hover': 'var(--epr-text-color)',\n\n      /* Z-Index */\n      '--epr-header-overlay-z-index': '3',\n      '--epr-emoji-variations-indictator-z-index': '1',\n      '--epr-category-label-z-index': '2',\n      '--epr-skin-variation-picker-z-index': '5',\n      '--epr-preview-z-index': '6',\n\n      /* Dark Theme Variables */\n      '--epr-dark': '#000',\n      '--epr-dark-emoji-variation-picker-bg-color': 'var(--epr-dark)',\n      '--epr-dark-highlight-color': '#c0c0c0',\n      '--epr-dark-text-color': 'var(--epr-highlight-color)',\n      '--epr-dark-hover-bg-color': '#363636f6',\n      '--epr-dark-hover-bg-color-reduced-opacity': '#36363680',\n      '--epr-dark-focus-bg-color': '#474747',\n      '--epr-dark-search-input-bg-color': '#333333',\n      '--epr-dark-category-label-bg-color': '#222222e6',\n      '--epr-dark-picker-border-color': '#151617',\n      '--epr-dark-bg-color': '#222222',\n      '--epr-dark-reactions-bg-color': '#22222290',\n      '--epr-dark-search-input-bg-color-active': 'var(--epr-dark)',\n      '--epr-dark-emoji-variation-indicator-color': '#444',\n      '--epr-dark-category-icon-active-color': '#3271b7',\n      '--epr-dark-skin-tone-picker-menu-color': '#22222295',\n      '--epr-dark-skin-tone-outer-border-color': 'var(--epr-dark-picker-border-color)',\n      '--epr-dark-skin-tone-inner-border-color': '#FFFFFF',\n    }\n  },\n  autoThemeDark: {\n    '.': ClassNames.autoTheme,\n    '@media (prefers-color-scheme: dark)': {\n      '--': DarkTheme\n    }\n  },\n  darkTheme: {\n    '.': ClassNames.darkTheme,\n    '--': DarkTheme\n  },\n  reactionsMenu: {\n    '.': 'epr-reactions',\n    height: '50px',\n    display: 'inline-flex',\n    backgroundColor: 'var(--epr-reactions-bg-color)',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(8px)',\n    '--': {\n      '--epr-picker-border-radius': '50px'\n    }\n  }\n});\n", "import { DEFAULT_LABEL_HEIGHT } from '../components/main/PickerMain';\n\nimport { ClassNames, asSelectors } from './classNames';\nimport { NullableElement } from './selectors';\n\nexport function elementCountInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const parentWidth = parent.getBoundingClientRect().width;\n  const elementWidth = element.getBoundingClientRect().width;\n  return Math.floor(parentWidth / elementWidth);\n}\n\nexport function elementIndexInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementWidth = element.getBoundingClientRect().width;\n  const elementLeft = element.getBoundingClientRect().left;\n  const parentLeft = parent.getBoundingClientRect().left;\n\n  return Math.floor((elementLeft - parentLeft) / elementWidth);\n}\n\nexport function rowNumber(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  return Math.round((elementTop - parentTop) / elementHeight);\n}\n\nexport function hasNextRow(\n  parent: NullableElement,\n  element: NullableElement\n): boolean {\n  if (!parent || !element) {\n    return false;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentHeight = parent.getBoundingClientRect().height;\n\n  return Math.round(elementTop - parentTop + elementHeight) < parentHeight;\n}\n\nfunction getRowElements(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number\n): HTMLElement[] {\n  if (row === -1) {\n    const lastRow = Math.floor((elements.length - 1) / elementsInRow);\n    const firstElementIndex = lastRow * elementsInRow;\n    const lastElementIndex = elements.length - 1;\n    return elements.slice(firstElementIndex, lastElementIndex + 1);\n  }\n\n  return elements.slice(row * elementsInRow, (row + 1) * elementsInRow);\n}\n\nfunction getNextRowElements(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number\n): HTMLElement[] {\n  const nextRow = currentRow + 1;\n\n  if (nextRow * elementsInRow > allElements.length) {\n    return [];\n  }\n\n  return getRowElements(allElements, nextRow, elementsInRow);\n}\n\nexport function getElementInRow(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number,\n  indexInRow: number\n): NullableElement {\n  const rowElements = getRowElements(elements, row, elementsInRow);\n  // get element, default to last\n  return rowElements[indexInRow] || rowElements[rowElements.length - 1] || null;\n}\n\nexport function getElementInNextRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const nextRowElements = getNextRowElements(\n    allElements,\n    currentRow,\n    elementsInRow\n  );\n\n  // return item in index, or last item in row\n  return (\n    nextRowElements[index] ||\n    nextRowElements[nextRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function getElementInPrevRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const prevRowElements = getRowElements(\n    allElements,\n    currentRow - 1,\n    elementsInRow\n  );\n\n  // default to last\n  return (\n    prevRowElements[index] ||\n    prevRowElements[prevRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function firstVisibleElementInContainer(\n  parent: NullableElement,\n  elements: HTMLElement[],\n  maxVisibilityDiffThreshold = 0\n): NullableElement {\n  if (!parent || !elements.length) {\n    return null;\n  }\n\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentBottom = parent.getBoundingClientRect().bottom;\n  const parentTopWithLabel = parentTop + getLabelHeight(parent);\n\n  const visibleElements = elements.find(element => {\n    const elementTop = element.getBoundingClientRect().top;\n    const elementBottom = element.getBoundingClientRect().bottom;\n    const maxVisibilityDiffPixels =\n      element.clientHeight * maxVisibilityDiffThreshold;\n\n    const elementTopWithAllowedDiff = elementTop + maxVisibilityDiffPixels;\n    const elementBottomWithAllowedDiff =\n      elementBottom - maxVisibilityDiffPixels;\n\n    if (elementTopWithAllowedDiff < parentTopWithLabel) {\n      return false;\n    }\n\n    return (\n      (elementTopWithAllowedDiff >= parentTop &&\n        elementTopWithAllowedDiff <= parentBottom) ||\n      (elementBottomWithAllowedDiff >= parentTop &&\n        elementBottomWithAllowedDiff <= parentBottom)\n    );\n  });\n\n  return visibleElements || null;\n}\n\nexport function hasNextElementSibling(element: HTMLElement) {\n  return !!element.nextElementSibling;\n}\n\nfunction getLabelHeight(parentNode: HTMLElement) {\n  const labels = Array.from(\n    parentNode.querySelectorAll(asSelectors(ClassNames.label))\n  );\n\n  for (const label of labels) {\n    const height = label.getBoundingClientRect().height;\n    // return height if label is not hidden\n    if (height > 0) {\n      return height;\n    }\n  }\n\n  return DEFAULT_LABEL_HEIGHT;\n}\n", "import { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  emojiByUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport { firstVisibleElementInContainer } from './elementPositionInRow';\n\nexport type NullableElement = HTMLElement | null;\n\nexport const EmojiButtonSelector = `button${asSelectors(ClassNames.emoji)}`;\nexport const VisibleEmojiSelector = [\n  EmojiButtonSelector,\n  asSelectors(ClassNames.visible),\n  `:not(${asSelectors(ClassNames.hidden)})`\n].join('');\n\nexport function buttonFromTarget(\n  emojiElement: NullableElement\n): HTMLButtonElement | null {\n  return emojiElement?.closest(EmojiButtonSelector) ?? null;\n}\n\nexport function isEmojiButton(element: NullableElement): boolean {\n  if (!element) {\n    return false;\n  }\n\n  return element.matches(EmojiButtonSelector);\n}\n\nexport function emojiFromElement(\n  element: NullableElement\n): [DataEmoji, string] | [] {\n  const originalUnified = originalUnifiedFromEmojiElement(element);\n  const unified = unifiedFromEmojiElement(element);\n\n  if (!originalUnified) {\n    return [];\n  }\n\n  const emoji = emojiByUnified(unified ?? originalUnified);\n\n  if (!emoji) {\n    return [];\n  }\n\n  return [emoji, unified as string];\n}\n\nexport function isEmojiElement(element: NullableElement): boolean {\n  return Boolean(\n    element?.matches(EmojiButtonSelector) ||\n      element?.parentElement?.matches(EmojiButtonSelector)\n  );\n}\n\nexport function categoryLabelFromCategory(\n  category: NullableElement\n): NullableElement {\n  return category?.querySelector(asSelectors(ClassNames.label)) ?? null;\n}\n\nexport function closestCategoryLabel(\n  element: NullableElement\n): NullableElement {\n  const category = closestCategory(element);\n  return categoryLabelFromCategory(category);\n}\n\nexport function elementHeight(element: NullableElement): number {\n  return element?.clientHeight ?? 0;\n}\n\nexport function emojiTrueOffsetTop(element: NullableElement): number {\n  if (!element) {\n    return 0;\n  }\n\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  // compensate for the label height\n  const labelHeight = categoryLabelHeight(category);\n\n  return elementOffsetTop(button) + elementOffsetTop(category) + labelHeight;\n}\n\nexport function categoryLabelHeight(category: NullableElement): number {\n  if (!category) {\n    return 0;\n  }\n\n  const categoryWithoutLabel = category.querySelector(\n    asSelectors(ClassNames.categoryContent)\n  );\n\n  return (\n    (category?.clientHeight ?? 0) - (categoryWithoutLabel?.clientHeight ?? 0)\n  );\n}\n\nexport function isEmojiBehindLabel(emoji: NullableElement): boolean {\n  if (!emoji) {\n    return false;\n  }\n\n  return (\n    emojiDistanceFromScrollTop(emoji) <\n    categoryLabelHeight(closestCategory(emoji))\n  );\n}\n\nexport function queryScrollBody(root: NullableElement): NullableElement {\n  if (!root) return null;\n\n  return root.matches(asSelectors(ClassNames.scrollBody))\n    ? root\n    : root.querySelector(asSelectors(ClassNames.scrollBody));\n}\n\nexport function emojiDistanceFromScrollTop(emoji: NullableElement): number {\n  if (!emoji) {\n    return 0;\n  }\n\n  return emojiTrueOffsetTop(emoji) - (closestScrollBody(emoji)?.scrollTop ?? 0);\n}\n\nexport function closestScrollBody(element: NullableElement): NullableElement {\n  if (!element) {\n    return null;\n  }\n\n  return element.closest(asSelectors(ClassNames.scrollBody)) ?? null;\n}\n\nexport function emojiTruOffsetLeft(element: NullableElement): number {\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  return elementOffsetLeft(button) + elementOffsetLeft(category);\n}\n\nfunction elementOffsetTop(element: NullableElement): number {\n  return element?.offsetTop ?? 0;\n}\n\nfunction elementOffsetLeft(element: NullableElement): number {\n  return element?.offsetLeft ?? 0;\n}\n\nexport function unifiedFromEmojiElement(emoji: NullableElement): string | null {\n  return elementDataSetKey(buttonFromTarget(emoji), 'unified') ?? null;\n}\n\nexport function originalUnifiedFromEmojiElement(\n  emoji: NullableElement\n): string | null {\n  const unified = unifiedFromEmojiElement(emoji);\n\n  if (unified) {\n    return unifiedWithoutSkinTone(unified);\n  }\n  return null;\n}\n\nexport function allUnifiedFromEmojiElement(\n  emoji: NullableElement\n): { unified: string | null; originalUnified: string | null } {\n  if (!emoji) {\n    return {\n      unified: null,\n      originalUnified: null\n    };\n  }\n\n  return {\n    unified: unifiedFromEmojiElement(emoji),\n    originalUnified: originalUnifiedFromEmojiElement(emoji)\n  };\n}\n\nfunction elementDataSetKey(\n  element: NullableElement,\n  key: string\n): string | null {\n  return elementDataSet(element)[key] ?? null;\n}\n\nfunction elementDataSet(element: NullableElement): DOMStringMap {\n  return element?.dataset ?? {};\n}\n\nexport function isVisibleEmoji(element: HTMLElement) {\n  return element.classList.contains(ClassNames.visible);\n}\n\nexport function isHidden(element: NullableElement) {\n  if (!element) return true;\n\n  return element.classList.contains(ClassNames.hidden);\n}\n\nexport function allVisibleEmojis(parent: NullableElement) {\n  if (!parent) {\n    return [];\n  }\n\n  return Array.from(\n    parent.querySelectorAll(VisibleEmojiSelector)\n  ) as HTMLElement[];\n}\n\nexport function lastVisibleEmoji(element: NullableElement): NullableElement {\n  if (!element) return null;\n\n  const allEmojis = allVisibleEmojis(element);\n  const [last] = allEmojis.slice(-1);\n  if (!last) {\n    return null;\n  }\n\n  if (!isVisibleEmoji(last)) {\n    return prevVisibleEmoji(last);\n  }\n\n  return last;\n}\n\nexport function nextVisibleEmoji(element: HTMLElement): NullableElement {\n  const next = element.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return firstVisibleEmoji(nextCategory(element));\n  }\n\n  if (!isVisibleEmoji(next)) {\n    return nextVisibleEmoji(next);\n  }\n\n  return next;\n}\n\nexport function prevVisibleEmoji(element: HTMLElement): NullableElement {\n  const prev = element.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return lastVisibleEmoji(prevCategory(element));\n  }\n\n  if (!isVisibleEmoji(prev)) {\n    return prevVisibleEmoji(prev);\n  }\n\n  return prev;\n}\n\nexport function firstVisibleEmoji(parent: NullableElement) {\n  if (!parent) {\n    return null;\n  }\n\n  const allEmojis = allVisibleEmojis(parent);\n\n  return firstVisibleElementInContainer(parent, allEmojis, 0.1);\n}\n\nexport function prevCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const prev = category.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return null;\n  }\n\n  if (isHidden(prev)) {\n    return prevCategory(prev);\n  }\n\n  return prev;\n}\n\nexport function nextCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const next = category.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return null;\n  }\n\n  if (isHidden(next)) {\n    return nextCategory(next);\n  }\n\n  return next;\n}\n\nexport function closestCategory(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(asSelectors(ClassNames.category)) as HTMLElement;\n}\n\nexport function closestCategoryContent(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(\n    asSelectors(ClassNames.categoryContent)\n  ) as HTMLElement;\n}\n", "export function parseNativeEmoji(unified: string): string {\n  return unified\n    .split('-')\n    .map(hex => String.fromCodePoint(parseInt(hex, 16)))\n    .join('');\n}\n", "import { SkinTones, SuggestionMode } from '../types/exposedTypes';\n\nimport { DataEmoji } from './DataTypes';\nimport { emojiUnified } from './emojiSelectors';\n\nconst SUGGESTED_LS_KEY = 'epr_suggested';\n\ntype SuggestedItem = {\n  unified: string;\n  original: string;\n  count: number;\n};\n\ntype Suggested = SuggestedItem[];\n\nexport function getSuggested(mode?: SuggestionMode): Suggested {\n  try {\n    if (!window?.localStorage) {\n      return [];\n    }\n    const recent = JSON.parse(\n      window?.localStorage.getItem(SUGGESTED_LS_KEY) ?? '[]'\n    ) as Suggested;\n\n    if (mode === SuggestionMode.FREQUENT) {\n      return recent.sort((a, b) => b.count - a.count);\n    }\n\n    return recent;\n  } catch {\n    return [];\n  }\n}\n\nexport function setSuggested(emoji: DataEmoji, skinTone: SkinTones) {\n  const recent = getSuggested();\n\n  const unified = emojiUnified(emoji, skinTone);\n  const originalUnified = emojiUnified(emoji);\n\n  let existing = recent.find(({ unified: u }) => u === unified);\n\n  let nextList: SuggestedItem[];\n\n  if (existing) {\n    nextList = [existing].concat(recent.filter(i => i !== existing));\n  } else {\n    existing = {\n      unified,\n      original: originalUnified,\n      count: 0\n    };\n    nextList = [existing, ...recent];\n  }\n\n  existing.count++;\n\n  nextList.length = Math.min(nextList.length, 14);\n\n  try {\n    window?.localStorage.setItem(SUGGESTED_LS_KEY, JSON.stringify(nextList));\n    // Prevents the change from being seen immediately.\n  } catch {\n    // ignore\n  }\n}\n", "import {\n  Categories,\n  CategoryConfig,\n  CustomCategoryConfig\n} from '../config/categoryConfig';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\n\nexport function isCustomCategory(\n  category: CategoryConfig | CustomCategoryConfig\n): category is CustomCategoryConfig {\n  return category.category === Categories.CUSTOM;\n}\n\nexport function isCustomEmoji(emoji: Partial<DataEmoji>): emoji is CustomEmoji {\n  return emoji.imgUrl !== undefined;\n}\n", "import * as React from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport {\n  emojiFromElement,\n  isEmojiElement,\n  NullableElement\n} from '../DomUtils/selectors';\nimport {\n  useActiveSkinToneState,\n  useDisallowClickRef,\n  useEmojiVariationPickerState,\n  useUpdateSuggested\n} from '../components/context/PickerContext';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useOnEmojiClickConfig\n} from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  activeVariationFromUnified,\n  emojiHasVariations,\n  emojiNames,\n  emojiUnified\n} from '../dataUtils/emojiSelectors';\nimport { parseNativeEmoji } from '../dataUtils/parseNativeEmoji';\nimport { setSuggested } from '../dataUtils/suggested';\nimport { isCustomEmoji } from '../typeRefinements/typeRefinements';\nimport { EmojiClickData, SkinTones, EmojiStyle } from '../types/exposedTypes';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\nimport useSetVariationPicker from './useSetVariationPicker';\n\nexport function useMouseDownHandlers(\n  ContainerRef: React.MutableRefObject<NullableElement>,\n  mouseEventSource: MOUSE_EVENT_SOURCE\n) {\n  const mouseDownTimerRef = useRef<undefined | number>();\n  const setVariationPicker = useSetVariationPicker();\n  const disallowClickRef = useDisallowClickRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const onEmojiClick = useOnEmojiClickConfig(mouseEventSource);\n  const [, updateSuggested] = useUpdateSuggested();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const activeEmojiStyle = useEmojiStyleConfig();\n\n  const onClick = React.useCallback(\n    function onClick(event: MouseEvent) {\n      if (disallowClickRef.current) {\n        return;\n      }\n\n      closeAllOpenToggles();\n\n      const [emoji, unified] = emojiFromEvent(event);\n\n      if (!emoji || !unified) {\n        return;\n      }\n\n      const skinToneToUse =\n        activeVariationFromUnified(unified) || activeSkinTone;\n\n      updateSuggested();\n      setSuggested(emoji, skinToneToUse);\n      onEmojiClick(\n        emojiClickOutput(emoji, skinToneToUse, activeEmojiStyle, getEmojiUrl),\n        event\n      );\n    },\n    [\n      activeSkinTone,\n      closeAllOpenToggles,\n      disallowClickRef,\n      onEmojiClick,\n      updateSuggested,\n      getEmojiUrl,\n      activeEmojiStyle\n    ]\n  );\n\n  const onMouseDown = React.useCallback(\n    function onMouseDown(event: MouseEvent) {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n      }\n\n      const [emoji] = emojiFromEvent(event);\n\n      if (!emoji || !emojiHasVariations(emoji)) {\n        return;\n      }\n\n      mouseDownTimerRef.current = window?.setTimeout(() => {\n        disallowClickRef.current = true;\n        mouseDownTimerRef.current = undefined;\n        closeAllOpenToggles();\n        setVariationPicker(event.target as HTMLElement);\n        setEmojiVariationPicker(emoji);\n      }, 500);\n    },\n    [\n      disallowClickRef,\n      closeAllOpenToggles,\n      setVariationPicker,\n      setEmojiVariationPicker\n    ]\n  );\n  const onMouseUp = React.useCallback(\n    function onMouseUp() {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n        mouseDownTimerRef.current = undefined;\n      } else if (disallowClickRef.current) {\n        // The problem we're trying to overcome here\n        // is that the emoji has both mouseup and click events\n        // and when releasing a mouseup event\n        // the click gets triggered too\n        // So we're disallowing the click event for a short time\n\n        requestAnimationFrame(() => {\n          disallowClickRef.current = false;\n        });\n      }\n    },\n    [disallowClickRef]\n  );\n\n  useEffect(() => {\n    if (!ContainerRef.current) {\n      return;\n    }\n    const confainerRef = ContainerRef.current;\n    confainerRef.addEventListener('click', onClick, {\n      passive: true\n    });\n\n    confainerRef.addEventListener('mousedown', onMouseDown, {\n      passive: true\n    });\n    confainerRef.addEventListener('mouseup', onMouseUp, {\n      passive: true\n    });\n\n    return () => {\n      confainerRef?.removeEventListener('click', onClick);\n      confainerRef?.removeEventListener('mousedown', onMouseDown);\n      confainerRef?.removeEventListener('mouseup', onMouseUp);\n    };\n  }, [ContainerRef, onClick, onMouseDown, onMouseUp]);\n}\n\nfunction emojiFromEvent(event: MouseEvent): [DataEmoji, string] | [] {\n  const target = event?.target as HTMLElement;\n  if (!isEmojiElement(target)) {\n    return [];\n  }\n\n  return emojiFromElement(target);\n}\n\nfunction emojiClickOutput(\n  emoji: DataEmoji,\n  activeSkinTone: SkinTones,\n  activeEmojiStyle: EmojiStyle,\n  getEmojiUrl: GetEmojiUrl\n): EmojiClickData {\n  const names = emojiNames(emoji);\n\n  if (isCustomEmoji(emoji)) {\n    const unified = emojiUnified(emoji);\n    return {\n      activeSkinTone,\n      emoji: unified,\n      getImageUrl() {\n        return emoji.imgUrl;\n      },\n      imageUrl: emoji.imgUrl,\n      isCustom: true,\n      names,\n      unified,\n      unifiedWithoutSkinTone: unified\n    };\n  }\n  const unified = emojiUnified(emoji, activeSkinTone);\n\n  return {\n    activeSkinTone,\n    emoji: parseNativeEmoji(unified),\n    getImageUrl(emojiStyle: EmojiStyle = activeEmojiStyle ?? EmojiStyle.APPLE) {\n      return getEmojiUrl(unified, emojiStyle);\n    },\n    imageUrl: getEmojiUrl(unified, activeEmojiStyle ?? EmojiStyle.APPLE),\n    isCustom: false,\n    names,\n    unified,\n    unifiedWithoutSkinTone: emojiUnified(emoji)\n  };\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\ninterface Props\n  extends React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  > {\n  className?: string;\n}\n\nexport function Button(props: Props) {\n  return (\n    <button\n      type=\"button\"\n      {...props}\n      className={cx(styles.button, props.className)}\n    >\n      {props.children}\n    </button>\n  );\n}\n\nconst styles = stylesheet.create({\n  button: {\n    '.': 'epr-btn',\n    cursor: 'pointer',\n    border: '0',\n    background: 'none',\n    outline: 'none'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\n\ntype ClickableEmojiButtonProps = Readonly<{\n  hidden?: boolean;\n  showVariations?: boolean;\n  hiddenOnSearch?: boolean;\n  emojiNames: string[];\n  children: React.ReactNode;\n  hasVariations: boolean;\n  unified?: string;\n  noBackground?: boolean;\n  className?: string;\n}>;\n\nexport function ClickableEmojiButton({\n  emojiNames,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  showVariations = true,\n  hasVariations,\n  children,\n  className,\n  noBackground = false\n}: ClickableEmojiButtonProps) {\n  return (\n    <Button\n      className={cx(\n        styles.emoji,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch,\n        {\n          [ClassNames.visible]: !hidden && !hiddenOnSearch\n        },\n        !!(hasVariations && showVariations) && styles.hasVariations,\n        noBackground && styles.noBackground,\n        className\n      )}\n      data-unified={unified}\n      aria-label={getAriaLabel(emojiNames)}\n      data-full-name={emojiNames}\n    >\n      {children}\n    </Button>\n  );\n}\n\nfunction getAriaLabel(emojiNames: string[]) {\n  return emojiNames[0].match('flag-')\n    ? emojiNames[1] ?? emojiNames[0]\n    : emojiNames[0];\n}\n\nconst styles = stylesheet.create({\n  emoji: {\n    '.': ClassNames.emoji,\n    position: 'relative',\n    width: 'var(--epr-emoji-fullsize)',\n    height: 'var(--epr-emoji-fullsize)',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    borderRadius: '8px',\n    overflow: 'hidden',\n    transition: 'background-color 0.2s',\n    ':hover': {\n      backgroundColor: 'var(--epr-emoji-hover-color)'\n    },\n    ':focus': {\n      backgroundColor: 'var(--epr-focus-bg-color)'\n    }\n  },\n  noBackground: {\n    background: 'none',\n    ':hover': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    },\n    ':focus': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    }\n  },\n  hasVariations: {\n    '.': ClassNames.emojiHasVariations,\n    ':after': {\n      content: '',\n      display: 'block',\n      width: '0',\n      height: '0',\n      right: '0px',\n      bottom: '1px',\n      position: 'absolute',\n      borderLeft: '4px solid transparent',\n      borderRight: '4px solid transparent',\n      transform: 'rotate(135deg)',\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color)',\n      zIndex: 'var(--epr-emoji-variations-indictator-z-index)'\n    },\n    ':hover:after': {\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color-hover)'\n    }\n  }\n});\n", "import { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport const emojiStyles = stylesheet.create({\n  external: {\n    '.': ClassNames.external,\n    fontSize: '0'\n  },\n  common: {\n    alignSelf: 'center',\n    justifySelf: 'center',\n    display: 'block'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function EmojiImg({\n  emojiName,\n  style,\n  lazyLoad = false,\n  imgUrl,\n  onError,\n  className\n}: {\n  emojiName: string;\n  emojiStyle: EmojiStyle;\n  style: React.CSSProperties;\n  lazyLoad?: boolean;\n  imgUrl: string;\n    onError: () => void;\n  className?: string;\n}) {\n  return (\n    <img\n      src={imgUrl}\n      alt={emojiName}\n      className={cx(styles.emojiImag, emojiStyles.external, emojiStyles.common, className)}\n      loading={lazyLoad ? 'lazy' : 'eager'}\n      onError={onError}\n      style={style}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiImag: {\n    '.': 'epr-emoji-img',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    minWidth: 'var(--epr-emoji-fullsize)',\n    minHeight: 'var(--epr-emoji-fullsize)',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { parseNativeEmoji } from '../../dataUtils/parseNativeEmoji';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function NativeEmoji({\n  unified,\n  style,\n  className\n}: {\n  unified: string;\n  style: React.CSSProperties;\n  className?: string;\n}) {\n  return (\n    <span\n      className={cx(\n        styles.nativeEmoji,\n        emojiStyles.common,\n        emojiStyles.external,\n        className\n      )}\n      data-unified={unified}\n      style={style}\n    >\n      {parseNativeEmoji(unified)}\n    </span>\n  );\n}\n\nconst styles = stylesheet.create({\n  nativeEmoji: {\n    '.': 'epr-emoji-native',\n    fontFamily:\n      '\"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\"!important',\n    position: 'relative',\n    lineHeight: '100%',\n    fontSize: 'var(--epr-emoji-size)',\n    textAlign: 'center',\n    alignSelf: 'center',\n    justifySelf: 'center',\n    letterSpacing: '0',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import * as React from 'react';\n\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUrlByUnified\n} from '../../dataUtils/emojiSelectors';\nimport { isCustomEmoji } from '../../typeRefinements/typeRefinements';\nimport { EmojiStyle } from '../../types/exposedTypes';\nimport { useEmojisThatFailedToLoadState } from '../context/PickerContext';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { EmojiImg } from './EmojiImg';\nimport { NativeEmoji } from './NativeEmoji';\n\nexport function ViewOnlyEmoji({\n  emoji,\n  unified,\n  emojiStyle,\n  size,\n  lazyLoad,\n  getEmojiUrl = emojiUrlByUnified,\n  className\n}: BaseEmojiProps) {\n  const [, setEmojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n\n  const style = {} as React.CSSProperties;\n  if (size) {\n    style.width = style.height = style.fontSize = `${size}px`;\n  }\n\n  const emojiToRender = emoji ? emoji : emojiByUnified(unified);\n\n  if (!emojiToRender) {\n    return null;\n  }\n\n  if (isCustomEmoji(emojiToRender)) {\n    return (\n      <EmojiImg\n        style={style}\n        emojiName={unified}\n        emojiStyle={EmojiStyle.NATIVE}\n        lazyLoad={lazyLoad}\n        imgUrl={emojiToRender.imgUrl}\n        onError={onError}\n        className={className}\n      />\n    );\n  }\n\n  return (\n    <>\n      {emojiStyle === EmojiStyle.NATIVE ? (\n        <NativeEmoji unified={unified} style={style} className={className} />\n      ) : (\n        <EmojiImg\n          style={style}\n          emojiName={emojiName(emojiToRender)}\n          emojiStyle={emojiStyle}\n          lazyLoad={lazyLoad}\n          imgUrl={getEmojiUrl(unified, emojiStyle)}\n          onError={onError}\n          className={className}\n        />\n      )}\n    </>\n  );\n\n  function onError() {\n    setEmojisThatFailedToLoad(prev => new Set(prev).add(unified));\n  }\n}\n", "import * as React from 'react';\n\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiHasVariations, emojiNames } from '../../dataUtils/emojiSelectors';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { ClickableEmojiButton } from './ClickableEmojiButton';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\ntype ClickableEmojiProps = Readonly<\n  BaseEmojiProps & {\n    hidden?: boolean;\n    showVariations?: boolean;\n    hiddenOnSearch?: boolean;\n    emoji: DataEmoji;\n    className?: string;\n    noBackground?: boolean;\n  }\n>;\n\nexport function ClickableEmoji({\n  emoji,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  emojiStyle,\n  showVariations = true,\n  size,\n  lazyLoad,\n  getEmojiUrl,\n  className,\n  noBackground = false\n}: ClickableEmojiProps) {\n  const hasVariations = emojiHasVariations(emoji);\n\n  return (\n    <ClickableEmojiButton\n      hasVariations={hasVariations}\n      showVariations={showVariations}\n      hidden={hidden}\n      hiddenOnSearch={hiddenOnSearch}\n      emojiNames={emojiNames(emoji)}\n      unified={unified}\n      noBackground={noBackground}\n    >\n      <ViewOnlyEmoji\n        unified={unified}\n        emoji={emoji}\n        size={size}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoad}\n        getEmojiUrl={getEmojiUrl}\n        className={className}\n      />\n    </ClickableEmojiButton>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\nimport { useReactionsModeState } from '../context/PickerContext';\n\nimport Plus from './svg/plus.svg';\n\nexport function BtnPlus() {\n  const [, setReactionsMode] = useReactionsModeState();\n  return (\n    <Button\n      aria-label=\"Show all Emojis\"\n      title=\"Show all Emojis\"\n      tabIndex={0}\n      className={cx(styles.plusSign)}\n      onClick={() => setReactionsMode(false)}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  plusSign: {\n    fontSize: '20px',\n    padding: '17px',\n    color: 'var(--epr-text-color)',\n    borderRadius: '50%',\n    textAlign: 'center',\n    lineHeight: '100%',\n    width: '20px',\n    height: '20px',\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    transition: 'background-color 0.2s ease-in-out',\n    ':after': {\n      content: '',\n      minWidth: '20px',\n      minHeight: '20px',\n      backgroundImage: `url(${Plus})`,\n      backgroundColor: 'transparent',\n      backgroundRepeat: 'no-repeat',\n      backgroundSize: '20px',\n      backgroundPositionY: '0'\n    },\n    ':hover': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-20px'\n      }\n    },\n    ':focus': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-40px'\n      }\n    }\n  },\n  ...darkMode('plusSign', {\n    ':after': { backgroundPositionY: '-40px' },\n    ':hover:after': { backgroundPositionY: '-60px' }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonStyles, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useReactionsConfig,\n  useAllowExpandReactions,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useReactionsRef } from '../context/ElementRefContext';\nimport { useReactionsModeState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { BtnPlus } from './BtnPlus';\n\nexport function Reactions() {\n  const [reactionsOpen] = useReactionsModeState();\n  const ReactionsRef = useReactionsRef();\n  const reactions = useReactionsConfig();\n  useMouseDownHandlers(ReactionsRef, MOUSE_EVENT_SOURCE.REACTIONS);\n  const emojiStyle = useEmojiStyleConfig();\n  const allowExpandReactions = useAllowExpandReactions();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  if (!reactionsOpen) {\n    return null;\n  }\n\n  return (\n    <ul\n      className={cx(styles.list, !reactionsOpen && commonStyles.hidden)}\n      ref={ReactionsRef}\n    >\n      {reactions.map(reaction => (\n        <li key={reaction}>\n          <ClickableEmoji\n            emoji={emojiByUnified(reaction) as DataEmoji}\n            emojiStyle={emojiStyle}\n            unified={reaction}\n            showVariations={false}\n            className={cx(styles.emojiButton)}\n            noBackground\n            getEmojiUrl={getEmojiUrl}\n          />\n        </li>\n      ))}\n      {allowExpandReactions ? (\n        <li>\n          <BtnPlus />\n        </li>\n      ) : null}\n    </ul>\n  );\n}\n\nconst styles = stylesheet.create({\n  list: {\n    listStyle: 'none',\n    margin: '0',\n    padding: '0 5px',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    height: '100%'\n  },\n  emojiButton: {\n    ':hover': {\n      transform: 'scale(1.2)'\n    },\n    ':focus': {\n      transform: 'scale(1.2)'\n    },\n    ':active': {\n      transform: 'scale(1.1)'\n    },\n    transition: 'transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.5)'\n  }\n});\n", "import { useEffect } from 'react';\n\nimport { ElementRef } from '../components/context/ElementRefContext';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\n\nexport function useOnScroll(BodyRef: ElementRef) {\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    if (!bodyRef) {\n      return;\n    }\n\n    bodyRef.addEventListener('scroll', onScroll, {\n      passive: true\n    });\n\n    function onScroll() {\n      closeAllOpenToggles();\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('scroll', onScroll);\n    };\n  }, [BodyRef, closeAllOpenToggles]);\n}\n", "import { useEmojisThatFailedToLoadState } from '../components/context/PickerContext';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified } from '../dataUtils/emojiSelectors';\n\nimport { useIsEmojiFiltered } from './useFilter';\n\nexport function useIsEmojiHidden(): (emoji: DataEmoji) => IsHiddenReturn {\n  const [emojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n  const isEmojiFiltered = useIsEmojiFiltered();\n\n  return (emoji: DataEmoji): IsHiddenReturn => {\n    const unified = emojiUnified(emoji);\n\n    const failedToLoad = emojisThatFailedToLoad.has(unified);\n    const filteredOut = isEmojiFiltered(unified);\n\n    return {\n      failedToLoad,\n      filteredOut,\n      hidden: failedToLoad || filteredOut\n    };\n  };\n}\n\ntype IsHiddenReturn = {\n  failedToLoad: boolean;\n  filteredOut: boolean;\n  hidden: boolean;\n};\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryFromCategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n  children?: React.ReactNode;\n  hidden?: boolean;\n  hiddenOnSearch?: boolean;\n}>;\n\nexport function EmojiCategory({\n  categoryConfig,\n  children,\n  hidden,\n  hiddenOnSearch\n}: Props) {\n  const category = categoryFromCategoryConfig(categoryConfig);\n  const categoryName = categoryNameFromCategoryConfig(categoryConfig);\n\n  return (\n    <li\n      className={cx(\n        styles.category,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch\n      )}\n      data-name={category}\n      aria-label={categoryName}\n    >\n      <h2 className={cx(styles.label)}>{categoryName}</h2>\n      <div className={cx(styles.categoryContent)}>{children}</div>\n    </li>\n  );\n}\n\nconst styles = stylesheet.create({\n  category: {\n    '.': ClassNames.category,\n    ':not(:has(.epr-visible))': {\n      display: 'none'\n    }\n  },\n  categoryContent: {\n    '.': ClassNames.categoryContent,\n    display: 'grid',\n    gridGap: '0',\n    gridTemplateColumns: 'repeat(auto-fill, var(--epr-emoji-fullsize))',\n    justifyContent: 'space-between',\n    margin: 'var(--epr-category-padding)',\n    position: 'relative'\n  },\n  label: {\n    '.': ClassNames.label,\n    alignItems: 'center',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(3px)',\n    backgroundColor: 'var(--epr-category-label-bg-color)',\n    color: 'var(--epr-category-label-text-color)',\n    display: 'flex',\n    fontSize: '16px',\n    fontWeight: 'bold',\n    height: 'var(--epr-category-label-height)',\n    margin: '0',\n    padding: 'var(--epr-category-label-padding)',\n    position: 'sticky',\n    textTransform: 'capitalize',\n    top: '0',\n    width: '100%',\n    zIndex: 'var(--epr-category-label-z-index)'\n  }\n});\n", "import * as React from 'react';\n\nlet isEverMounted = false;\n\nexport function useIsEverMounted() {\n  const [isMounted, setIsMounted] = React.useState(isEverMounted);\n\n  React.useEffect(() => {\n    setIsMounted(true);\n    isEverMounted = true;\n  }, []);\n\n  return isMounted || isEverMounted;\n}\n", "import * as React from 'react';\n\nimport { CategoryConfig } from '../../config/categoryConfig';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useSuggestedEmojisModeConfig\n} from '../../config/useConfig';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { getSuggested } from '../../dataUtils/suggested';\nimport { useIsEverMounted } from '../../hooks/useIsEverMounted';\nimport { useUpdateSuggested } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n}>;\n\nexport function Suggested({ categoryConfig }: Props) {\n  const [suggestedUpdated] = useUpdateSuggested();\n  const isMounted = useIsEverMounted();\n  const suggestedEmojisModeConfig = useSuggestedEmojisModeConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const suggested = React.useMemo(\n    () => getSuggested(suggestedEmojisModeConfig) ?? [],\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [suggestedUpdated, suggestedEmojisModeConfig]\n  );\n  const emojiStyle = useEmojiStyleConfig();\n\n  if (!isMounted) {\n    return null;\n  }\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      hiddenOnSearch\n      hidden={suggested.length === 0}\n    >\n      {suggested.map(suggestedItem => {\n        const emoji = emojiByUnified(suggestedItem.original);\n\n        if (!emoji) {\n          return null;\n        }\n\n        return (\n          <ClickableEmoji\n            showVariations={false}\n            unified={suggestedItem.unified}\n            emojiStyle={emojiStyle}\n            emoji={emoji}\n            key={suggestedItem.unified}\n            getEmojiUrl={getEmojiUrl}\n          />\n        );\n      })}\n    </EmojiCategory>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  Categories,\n  CategoryConfig,\n  categoryFromCategoryConfig\n} from '../../config/categoryConfig';\nimport {\n  useCategoriesConfig,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useLazyLoadEmojisConfig,\n  useSkinTonesDisabledConfig\n} from '../../config/useConfig';\nimport { emojisByCategory, emojiUnified } from '../../dataUtils/emojiSelectors';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEmojiHidden } from '../../hooks/useIsEmojiHidden';\nimport {\n  useActiveSkinToneState,\n  useIsPastInitialLoad\n} from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\nimport { Suggested } from './Suggested';\n\nexport function EmojiList() {\n  const categories = useCategoriesConfig();\n  const renderdCategoriesCountRef = React.useRef(0);\n\n  return (\n    <ul className={cx(styles.emojiList)}>\n      {categories.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n\n        if (category === Categories.SUGGESTED) {\n          return <Suggested key={category} categoryConfig={categoryConfig} />;\n        }\n\n        return (\n          <React.Suspense key={category}>\n            <RenderCategory\n              category={category}\n              categoryConfig={categoryConfig}\n              renderdCategoriesCountRef={renderdCategoriesCountRef}\n            />\n          </React.Suspense>\n        );\n      })}\n    </ul>\n  );\n}\n\nfunction RenderCategory({\n  category,\n  categoryConfig,\n  renderdCategoriesCountRef\n}: {\n  category: Categories;\n  categoryConfig: CategoryConfig;\n  renderdCategoriesCountRef: React.MutableRefObject<number>;\n}) {\n  const isEmojiHidden = useIsEmojiHidden();\n  const lazyLoadEmojis = useLazyLoadEmojisConfig();\n  const emojiStyle = useEmojiStyleConfig();\n  const isPastInitialLoad = useIsPastInitialLoad();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const showVariations = !useSkinTonesDisabledConfig();\n\n  // Small trick to defer the rendering of all emoji categories until the first category is visible\n  // This way the user gets to actually see something and not wait for the whole picker to render.\n  const emojisToPush =\n    !isPastInitialLoad && renderdCategoriesCountRef.current > 0\n      ? []\n      : emojisByCategory(category);\n\n  if (emojisToPush.length > 0) {\n    renderdCategoriesCountRef.current++;\n  }\n\n  let hiddenCounter = 0;\n\n  const emojis = emojisToPush.map(emoji => {\n    const unified = emojiUnified(emoji, activeSkinTone);\n    const { failedToLoad, filteredOut, hidden } = isEmojiHidden(emoji);\n\n    const isDisallowed = isEmojiDisallowed(emoji);\n\n    if (hidden || isDisallowed) {\n      hiddenCounter++;\n    }\n\n    if (isDisallowed) {\n      return null;\n    }\n\n    return (\n      <ClickableEmoji\n        showVariations={showVariations}\n        key={unified}\n        emoji={emoji}\n        unified={unified}\n        hidden={failedToLoad}\n        hiddenOnSearch={filteredOut}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoadEmojis}\n        getEmojiUrl={getEmojiUrl}\n      />\n    );\n  });\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      // Indicates that there are no visible emojis\n      // Hence, the category should be hidden\n      hidden={hiddenCounter === emojis.length}\n    >\n      {emojis}\n    </EmojiCategory>\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiList: {\n    '.': ClassNames.emojiList,\n    listStyle: 'none',\n    margin: '0',\n    padding: '0'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { focusFirstVisibleEmoji } from '../../DomUtils/keyboardNavigation';\nimport {\n  buttonFromTarget,\n  elementHeight,\n  emojiTrueOffsetTop,\n  emojiTruOffsetLeft\n} from '../../DomUtils/selectors';\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport {\n  emojiHasVariations,\n  emojiUnified,\n  emojiVariations\n} from '../../dataUtils/emojiSelectors';\nimport {\n  useAnchoredEmojiRef,\n  useBodyRef,\n  useSetAnchoredEmojiRef,\n  useVariationPickerRef\n} from '../context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport SVGTriangle from './svg/triangle.svg';\n\nenum Direction {\n  Up,\n  Down\n}\n\n// eslint-disable-next-line complexity\nexport function EmojiVariationPicker() {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const VariationPickerRef = useVariationPickerRef();\n  const [emoji] = useEmojiVariationPickerState();\n  const emojiStyle = useEmojiStyleConfig();\n\n  const { getTop, getMenuDirection } = useVariationPickerTop(\n    VariationPickerRef\n  );\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const getPointerStyle = usePointerStyle(VariationPickerRef);\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n  const visible = Boolean(\n    emoji &&\n      button &&\n      emojiHasVariations(emoji) &&\n      button.classList.contains(ClassNames.emojiHasVariations)\n  );\n\n  useEffect(() => {\n    if (!visible) {\n      return;\n    }\n\n    focusFirstVisibleEmoji(VariationPickerRef.current);\n  }, [VariationPickerRef, visible, AnchoredEmojiRef]);\n\n  let top, pointerStyle;\n\n  if (!visible && AnchoredEmojiRef.current) {\n    setAnchoredEmojiRef(null);\n  } else {\n    top = getTop();\n    pointerStyle = getPointerStyle();\n  }\n\n  return (\n    <div\n      ref={VariationPickerRef}\n      className={cx(\n        styles.variationPicker,\n        getMenuDirection() === Direction.Down && styles.pointingUp,\n        visible && styles.visible\n      )}\n      style={{ top }}\n    >\n      {visible && emoji\n        ? [emojiUnified(emoji)]\n            .concat(emojiVariations(emoji))\n            .slice(0, 6)\n            .map(unified => (\n              <ClickableEmoji\n                key={unified}\n                emoji={emoji}\n                unified={unified}\n                emojiStyle={emojiStyle}\n                showVariations={false}\n                getEmojiUrl={getEmojiUrl}\n              />\n            ))\n        : null}\n      <div className={cx(styles.pointer)} style={pointerStyle} />\n    </div>\n  );\n}\n\nfunction usePointerStyle(VariationPickerRef: React.RefObject<HTMLElement>) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return function getPointerStyle() {\n    const style: React.CSSProperties = {};\n    if (!VariationPickerRef.current) {\n      return style;\n    }\n\n    if (AnchoredEmojiRef.current) {\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const offsetLeft = emojiTruOffsetLeft(button);\n\n      if (!button) {\n        return style;\n      }\n\n      // half of the button\n      style.left = offsetLeft + button?.clientWidth / 2;\n    }\n\n    return style;\n  };\n}\n\nfunction useVariationPickerTop(\n  VariationPickerRef: React.RefObject<HTMLElement>\n) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const BodyRef = useBodyRef();\n  let direction = Direction.Up;\n\n  return {\n    getMenuDirection,\n    getTop\n  };\n\n  function getMenuDirection() {\n    return direction;\n  }\n\n  function getTop() {\n    direction = Direction.Up;\n    let emojiOffsetTop = 0;\n\n    if (!VariationPickerRef.current) {\n      return 0;\n    }\n\n    const height = elementHeight(VariationPickerRef.current);\n\n    if (AnchoredEmojiRef.current) {\n      const bodyRef = BodyRef.current;\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const buttonHeight = elementHeight(button);\n\n      emojiOffsetTop = emojiTrueOffsetTop(button);\n\n      const scrollTop = bodyRef?.scrollTop ?? 0;\n\n      if (scrollTop > emojiOffsetTop - height) {\n        direction = Direction.Down;\n        emojiOffsetTop += buttonHeight + height;\n      }\n    }\n\n    return emojiOffsetTop - height;\n  }\n}\n\nconst styles = stylesheet.create({\n  variationPicker: {\n    '.': ClassNames.variationPicker,\n    position: 'absolute',\n    right: '15px',\n    left: '15px',\n    padding: '5px',\n    boxShadow: '0px 2px 5px rgba(0, 0, 0, 0.2)',\n    borderRadius: '3px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-around',\n    opacity: '0',\n    visibility: 'hidden',\n    pointerEvents: 'none',\n    top: '-100%',\n    border: '1px solid var(--epr-picker-border-color)',\n    height: 'var(--epr-emoji-variation-picker-height)',\n    zIndex: 'var(--epr-skin-variation-picker-z-index)',\n    background: 'var(--epr-emoji-variation-picker-bg-color)',\n    transform: 'scale(0.9)',\n    transition: 'transform 0.1s ease-out, opacity 0.2s ease-out'\n  },\n  visible: {\n    opacity: '1',\n    visibility: 'visible',\n    pointerEvents: 'all',\n    transform: 'scale(1)'\n  },\n  pointingUp: {\n    '.': 'pointing-up',\n    transformOrigin: 'center 0%',\n    transform: 'scale(0.9)'\n  },\n  '.pointing-up': {\n    pointer: {\n      top: '0',\n      transform: 'rotate(180deg) translateY(100%) translateX(18px)'\n    }\n  },\n  pointer: {\n    '.': 'epr-emoji-pointer',\n    content: '',\n    position: 'absolute',\n    width: '25px',\n    height: '15px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '50px 15px',\n    top: '100%',\n    transform: 'translateX(-18px)',\n    backgroundImage: `url(${SVGTriangle})`\n  },\n  ...darkMode('pointer', {\n    backgroundPosition: '-25px 0'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { MOUSE_EVENT_SOURCE } from '../../config/useConfig';\nimport { useOnMouseMove } from '../../hooks/useDisallowMouseMove';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useOnScroll } from '../../hooks/useOnScroll';\nimport { useBodyRef } from '../context/ElementRefContext';\n\nimport { EmojiList } from './EmojiList';\nimport { EmojiVariationPicker } from './EmojiVariationPicker';\n\nexport function Body() {\n  const BodyRef = useBodyRef();\n  useOnScroll(BodyRef);\n  useMouseDownHandlers(BodyRef, MOUSE_EVENT_SOURCE.PICKER);\n  useOnMouseMove();\n\n  return (\n    <div\n      className={cx(styles.body, commonInteractionStyles.hiddenOnReactions)}\n      ref={BodyRef}\n    >\n      <EmojiVariationPicker />\n      <EmojiList />\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  body: {\n    '.': ClassNames.scrollBody,\n    flex: '1',\n    overflowY: 'scroll',\n    overflowX: 'hidden',\n    position: 'relative'\n  }\n});\n", "import { NullableElement } from './selectors';\n\nexport function detectEmojyPartiallyBelowFold(\n  button: HTMLButtonElement,\n  bodyRef: NullableElement\n): number {\n  if (!button || !bodyRef) {\n    return 0;\n  }\n\n  const buttonRect = button.getBoundingClientRect();\n  const bodyRect = bodyRef.getBoundingClientRect();\n\n  // If the element is obscured by at least half of its size\n  return bodyRect.height - (buttonRect.y - bodyRect.y);\n}\n", "import * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { detectEmojyPartiallyBelowFold } from '../DomUtils/detectEmojyPartiallyBelowFold';\nimport { focusElement } from '../DomUtils/focusElement';\nimport {\n  allUnifiedFromEmojiElement,\n  buttonFromTarget\n} from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { PreviewEmoji } from '../components/footer/Preview';\n\nimport {\n  useAllowMouseMove,\n  useIsMouseDisallowed\n} from './useDisallowMouseMove';\n\nexport function useEmojiPreviewEvents(\n  allow: boolean,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const BodyRef = useBodyRef();\n  const isMouseDisallowed = useIsMouseDisallowed();\n  const allowMouseMove = useAllowMouseMove();\n\n  useEffect(() => {\n    if (!allow) {\n      return;\n    }\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('keydown', onEscape, {\n      passive: true\n    });\n\n    bodyRef?.addEventListener('mouseover', onMouseOver, true);\n\n    bodyRef?.addEventListener('focus', onEnter, true);\n\n    bodyRef?.addEventListener('mouseout', onLeave, {\n      passive: true\n    });\n    bodyRef?.addEventListener('blur', onLeave, true);\n\n    function onEnter(e: FocusEvent) {\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (!button) {\n        return onLeave();\n      }\n\n      const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n      if (!unified || !originalUnified) {\n        return onLeave();\n      }\n\n      setPreviewEmoji({\n        unified,\n        originalUnified\n      });\n    }\n    function onLeave(e?: FocusEvent | MouseEvent) {\n      if (e) {\n        const relatedTarget = e.relatedTarget as HTMLElement;\n\n        if (!buttonFromTarget(relatedTarget)) {\n          return setPreviewEmoji(null);\n        }\n      }\n\n      setPreviewEmoji(null);\n    }\n    function onEscape(e: KeyboardEvent) {\n      if (e.key === 'Escape') {\n        setPreviewEmoji(null);\n      }\n    }\n\n    function onMouseOver(e: MouseEvent) {\n      if (isMouseDisallowed()) {\n        return;\n      }\n\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (button) {\n        const belowFoldByPx = detectEmojyPartiallyBelowFold(button, bodyRef);\n        const buttonHeight = button.getBoundingClientRect().height;\n        if (belowFoldByPx < buttonHeight) {\n          return handlePartiallyVisibleElementFocus(button, setPreviewEmoji);\n        }\n\n        focusElement(button);\n      }\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('mouseover', onMouseOver);\n      bodyRef?.removeEventListener('mouseout', onLeave);\n      bodyRef?.removeEventListener('focus', onEnter, true);\n      bodyRef?.removeEventListener('blur', onLeave, true);\n      bodyRef?.removeEventListener('keydown', onEscape);\n    };\n  }, [BodyRef, allow, setPreviewEmoji, isMouseDisallowed, allowMouseMove]);\n}\n\nfunction handlePartiallyVisibleElementFocus(\n  button: HTMLElement,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n  if (!unified || !originalUnified) {\n    return;\n  }\n\n  (document.activeElement as HTMLElement)?.blur?.();\n\n  setPreviewEmoji({\n    unified,\n    originalUnified\n  });\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport enum FlexDirection {\n  ROW = 'FlexRow',\n  COLUMN = 'FlexColumn'\n}\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  direction?: FlexDirection;\n}>;\n\nexport default function Flex({\n  children,\n  className,\n  style = {},\n  direction = FlexDirection.ROW\n}: Props) {\n  return (\n    <div\n      style={{ ...style }}\n      className={cx(styles.flex, className, styles[direction])}\n    >\n      {children}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  flex: {\n    display: 'flex'\n  },\n  [FlexDirection.ROW]: {\n    flexDirection: 'row'\n  },\n  [FlexDirection.COLUMN]: {\n    flexDirection: 'column'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\n\ntype Props = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Space({ className, style = {} }: Props) {\n  return <div style={{ flex: 1, ...style }} className={cx(className)} />;\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Absolute({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'absolute' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Relative({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'relative' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport { skinTonesNamed } from '../../../data/skinToneVariations';\nimport { SkinTones } from '../../../types/exposedTypes';\nimport { Button } from '../../atoms/Button';\n\ntype Props = {\n  isOpen: boolean;\n  onClick: () => void;\n  isActive: boolean;\n  skinToneVariation: SkinTones;\n  style?: React.CSSProperties;\n};\n\n// eslint-disable-next-line complexity\nexport function BtnSkinToneVariation({\n  isOpen,\n  onClick,\n  isActive,\n  skinToneVariation,\n  style\n}: Props) {\n  return (\n    <Button\n      style={style}\n      onClick={onClick}\n      className={cx(\n        `epr-tone-${skinToneVariation}`,\n        styles.tone,\n        !isOpen && styles.closedTone,\n        isActive && styles.active\n      )}\n      aria-pressed={isActive}\n      aria-label={`Skin tone ${skinTonesNamed[skinToneVariation as SkinTones]}`}\n    ></Button>\n  );\n}\n\nconst styles = stylesheet.create({\n  closedTone: {\n    opacity: '0',\n    zIndex: '0'\n  },\n  active: {\n    '.': 'epr-active',\n    zIndex: '1',\n    opacity: '1'\n  },\n  tone: {\n    '.': 'epr-tone',\n    width: 'var(--epr-skin-tone-size)',\n    display: 'block',\n    cursor: 'pointer',\n    borderRadius: '4px',\n    height: 'var(--epr-skin-tone-size)',\n    position: 'absolute',\n    right: '0',\n    transition: 'transform 0.3s ease-in-out, opacity 0.35s ease-in-out',\n    zIndex: '0',\n    border: '1px solid var(--epr-skin-tone-outer-border-color)',\n    boxShadow: 'inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)',\n    ':hover': {\n      boxShadow: '0 0 0 3px var(--epr-active-skin-hover-color), inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)'\n    },\n    ':focus': {\n      boxShadow: '0 0 0 3px var(--epr-focus-bg-color)'\n    },\n    '&.epr-tone-neutral': {\n      backgroundColor: '#ffd225'\n    },\n    '&.epr-tone-1f3fb': {\n      backgroundColor: '#ffdfbd'\n    },\n    '&.epr-tone-1f3fc': {\n      backgroundColor: '#e9c197'\n    },\n    '&.epr-tone-1f3fd': {\n      backgroundColor: '#c88e62'\n    },\n    '&.epr-tone-1f3fe': {\n      backgroundColor: '#a86637'\n    },\n    '&.epr-tone-1f3ff': {\n      backgroundColor: '#60463a'\n    }\n  }\n});\n", "/* eslint-disable complexity */\nimport { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../../DomUtils/classNames';\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useOnSkinToneChangeConfig,\n  useSkinTonesDisabledConfig\n} from '../../../config/useConfig';\nimport skinToneVariations from '../../../data/skinToneVariations';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFocusSearchInput } from '../../../hooks/useFocus';\nimport Absolute from '../../Layout/Absolute';\nimport Relative from '../../Layout/Relative';\nimport { useSkinTonePickerRef } from '../../context/ElementRefContext';\nimport {\n  useActiveSkinToneState,\n  useSkinToneFanOpenState\n} from '../../context/PickerContext';\n\nimport { BtnSkinToneVariation } from './BtnSkinToneVariation';\n\nconst ITEM_SIZE = 28;\n\ntype Props = {\n  direction?: SkinTonePickerDirection;\n};\n\nexport function SkinTonePickerMenu() {\n  return (\n    <Relative style={{ height: ITEM_SIZE }}>\n      <Absolute style={{ bottom: 0, right: 0 }}>\n        <SkinTonePicker direction={SkinTonePickerDirection.VERTICAL} />\n      </Absolute>\n    </Relative>\n  );\n}\n\nexport function SkinTonePicker({\n  direction = SkinTonePickerDirection.HORIZONTAL\n}: Props) {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const isDisabled = useSkinTonesDisabledConfig();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const [activeSkinTone, setActiveSkinTone] = useActiveSkinToneState();\n  const onSkinToneChange = useOnSkinToneChangeConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const focusSearchInput = useFocusSearchInput();\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const fullWidth = `${ITEM_SIZE * skinToneVariations.length}px`;\n\n  const expandedSize = isOpen ? fullWidth : ITEM_SIZE + 'px';\n\n  const vertical = direction === SkinTonePickerDirection.VERTICAL;\n\n  return (\n    <Relative\n      className={cx(\n        styles.skinTones,\n        vertical && styles.vertical,\n        isOpen && styles.open,\n        vertical && isOpen && styles.verticalShadow\n      )}\n      style={\n        vertical\n          ? { flexBasis: expandedSize, height: expandedSize }\n          : { flexBasis: expandedSize }\n      }\n    >\n      <div className={cx(styles.select)} ref={SkinTonePickerRef}>\n        {skinToneVariations.map((skinToneVariation, i) => {\n          const active = skinToneVariation === activeSkinTone;\n\n          return (\n            <BtnSkinToneVariation\n              key={skinToneVariation}\n              skinToneVariation={skinToneVariation}\n              isOpen={isOpen}\n              style={{\n                transform: cx(\n                  vertical\n                    ? `translateY(-${i * (isOpen ? ITEM_SIZE : 0)}px)`\n                    : `translateX(-${i * (isOpen ? ITEM_SIZE : 0)}px)`,\n                  isOpen && active && 'scale(1.3)'\n                )\n              }}\n              isActive={active}\n              onClick={() => {\n                if (isOpen) {\n                  setActiveSkinTone(skinToneVariation);\n                  onSkinToneChange(skinToneVariation);\n                  focusSearchInput();\n                } else {\n                  setIsOpen(true);\n                }\n                closeAllOpenToggles();\n              }}\n            />\n          );\n        })}\n      </div>\n    </Relative>\n  );\n}\n\nexport enum SkinTonePickerDirection {\n  VERTICAL = ClassNames.vertical,\n  HORIZONTAL = ClassNames.horizontal\n}\n\nconst styles = stylesheet.create({\n  skinTones: {\n    '.': 'epr-skin-tones',\n    '--': {\n      '--epr-skin-tone-size': '15px'\n    },\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    transition: 'all 0.3s ease-in-out',\n    padding: '10px 0'\n  },\n  vertical: {\n    padding: '9px',\n    alignItems: 'flex-end',\n    flexDirection: 'column',\n    borderRadius: '6px',\n    border: '1px solid var(--epr-bg-color)'\n  },\n  verticalShadow: {\n    boxShadow: '0px 0 7px var(--epr-picker-border-color)'\n  },\n  open: {\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(5px)',\n    background: 'var(--epr-skin-tone-picker-menu-color)',\n    '.epr-active': {\n      border: '1px solid var(--epr-active-skin-tone-indicator-border-color)'\n    }\n  },\n  select: {\n    '.': 'epr-skin-tone-select',\n    position: 'relative',\n    width: 'var(--epr-skin-tone-size)',\n    height: 'var(--epr-skin-tone-size)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  usePreviewConfig\n} from '../../config/useConfig';\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUnified\n} from '../../dataUtils/emojiSelectors';\nimport { useEmojiPreviewEvents } from '../../hooks/useEmojiPreviewEvents';\nimport { useIsSkinToneInPreview } from '../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../Layout/Flex';\nimport Space from '../Layout/Space';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ViewOnlyEmoji } from '../emoji/ViewOnlyEmoji';\nimport { SkinTonePickerMenu } from '../header/SkinTonePicker/SkinTonePicker';\n\nexport function Preview() {\n  const previewConfig = usePreviewConfig();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n\n  if (!previewConfig.showPreview) {\n    return null;\n  }\n\n  return (\n    <Flex\n      className={cx(styles.preview, commonInteractionStyles.hiddenOnReactions)}\n    >\n      <PreviewBody />\n      <Space />\n      {isSkinToneInPreview ? <SkinTonePickerMenu /> : null}\n    </Flex>\n  );\n}\n\nexport function PreviewBody() {\n  const previewConfig = usePreviewConfig();\n  const [previewEmoji, setPreviewEmoji] = useState<PreviewEmoji>(null);\n  const emojiStyle = useEmojiStyleConfig();\n  const [variationPickerEmoji] = useEmojiVariationPickerState();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEmojiPreviewEvents(previewConfig.showPreview, setPreviewEmoji);\n\n  const emoji = emojiByUnified(\n    previewEmoji?.unified ?? previewEmoji?.originalUnified\n  );\n\n  const show = emoji != null && previewEmoji != null;\n\n  return <PreviewContent />;\n\n  function PreviewContent() {\n    const defaultEmoji =\n      variationPickerEmoji ?? emojiByUnified(previewConfig.defaultEmoji);\n    if (!defaultEmoji) {\n      return null;\n    }\n    const defaultText = variationPickerEmoji\n      ? emojiName(variationPickerEmoji)\n      : previewConfig.defaultCaption;\n\n    return (\n      <>\n        <div>\n          {show ? (\n            <ViewOnlyEmoji\n              unified={previewEmoji?.unified as string}\n              emoji={emoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : defaultEmoji ? (\n            <ViewOnlyEmoji\n              unified={emojiUnified(defaultEmoji)}\n              emoji={defaultEmoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : null}\n        </div>\n        <div className={cx(styles.label)}>\n          {show ? emojiName(emoji) : defaultText}\n        </div>\n      </>\n    );\n  }\n}\n\nexport type PreviewEmoji = null | {\n  unified: string;\n  originalUnified: string;\n};\n\nconst styles = stylesheet.create({\n  preview: {\n    alignItems: 'center',\n    borderTop: '1px solid var(--epr-preview-border-color)',\n    height: 'var(--epr-preview-height)',\n    padding: '0 var(--epr-horizontal-padding)',\n    position: 'relative',\n    zIndex: 'var(--epr-preview-z-index)'\n  },\n  label: {\n    color: 'var(--epr-preview-text-color)',\n    fontSize: 'var(--epr-preview-text-size)',\n    padding: 'var(--epr-preview-text-padding)',\n    textTransform: 'capitalize'\n  },\n  emoji: {\n    padding: '0'\n  }\n});\n", "export function categoryNameFromDom($category: Element | null): string | null {\n  return $category?.getAttribute('data-name') ?? null;\n}\n", "import { useEffect } from 'react';\n\nimport { categoryNameFromDom } from '../DomUtils/categoryNameFromDom';\nimport { asSelectors, ClassNames } from '../DomUtils/classNames';\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nexport function useActiveCategoryScrollDetection(\n  setActiveCategory: (category: string) => void\n) {\n  const BodyRef = useBodyRef();\n\n  useEffect(() => {\n    const visibleCategories = new Map();\n    const bodyRef = BodyRef.current;\n    const observer = new IntersectionObserver(\n      entries => {\n        if (!bodyRef) {\n          return;\n        }\n\n        for (const entry of entries) {\n          const id = categoryNameFromDom(entry.target);\n          visibleCategories.set(id, entry.intersectionRatio);\n        }\n\n        const ratios = Array.from(visibleCategories);\n        const lastCategory = ratios[ratios.length - 1];\n\n        if (lastCategory[1] == 1) {\n          return setActiveCategory(lastCategory[0]);\n        }\n\n        for (const [id, ratio] of ratios) {\n          if (ratio) {\n            setActiveCategory(id);\n            break;\n          }\n        }\n      },\n      {\n        threshold: [0, 1]\n      }\n    );\n    bodyRef?.querySelectorAll(asSelectors(ClassNames.category)).forEach(el => {\n      observer.observe(el);\n    });\n  }, [BodyRef, setActiveCategory]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport { NullableElement } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  usePickerMainRef\n} from '../components/context/ElementRefContext';\n\nexport function useScrollCategoryIntoView() {\n  const BodyRef = useBodyRef();\n  const PickerMainRef = usePickerMainRef();\n\n  return function scrollCategoryIntoView(category: string): void {\n    if (!BodyRef.current) {\n      return;\n    }\n    const $category = BodyRef.current?.querySelector(\n      `[data-name=\"${category}\"]`\n    ) as NullableElement;\n\n    if (!$category) {\n      return;\n    }\n\n    const offsetTop = $category.offsetTop || 0;\n\n    scrollTo(PickerMainRef.current, offsetTop);\n  };\n}\n", "import { useCustomEmojisConfig } from '../config/useConfig';\n\nexport function useShouldHideCustomEmojis() {\n  const customCategoryConfig = useCustomEmojisConfig();\n\n  if (!customCategoryConfig) {\n    return false;\n  }\n\n  return customCategoryConfig.length === 0;\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\nimport { Button } from '../atoms/Button';\n\nimport SVGNavigation from './svg/CategoryNav.svg';\n\ntype Props = {\n  isActiveCategory: boolean;\n  category: string;\n  allowNavigation: boolean;\n  onClick: () => void;\n  categoryConfig: CategoryConfig;\n};\n\nexport function CategoryButton({\n  isActiveCategory,\n  category,\n  allowNavigation,\n  categoryConfig,\n  onClick\n}: Props) {\n  return (\n    <Button\n      tabIndex={allowNavigation ? 0 : -1}\n      className={cx(\n        styles.catBtn,\n        commonInteractionStyles.categoryBtn,\n        `epr-icn-${category}`,\n        {\n          [ClassNames.active]: isActiveCategory\n        }\n      )}\n      onClick={onClick}\n      aria-label={categoryNameFromCategoryConfig(categoryConfig)}\n      aria-selected={isActiveCategory}\n      role=\"tab\"\n      aria-controls=\"epr-category-nav-id\"\n    />\n  );\n}\n\nconst DarkActivePositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 3)'\n};\nconst DarkPositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 2)'\n};\n\nconst DarkInactivePosition = {\n  ':not(.epr-search-active)': {\n    catBtn: {\n      ':hover': DarkActivePositionY,\n      '&.epr-active': DarkActivePositionY\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  catBtn: {\n    '.': 'epr-cat-btn',\n    display: 'inline-block',\n    transition: 'opacity 0.2s ease-in-out',\n    position: 'relative',\n    height: 'var(--epr-category-navigation-button-size)',\n    width: 'var(--epr-category-navigation-button-size)',\n    backgroundSize: 'calc(var(--epr-category-navigation-button-size) * 10)',\n    outline: 'none',\n    backgroundPosition: '0 0',\n    backgroundImage: `url(${SVGNavigation})`,\n    ':focus:before': {\n      content: '',\n      position: 'absolute',\n      top: '-2px',\n      left: '-2px',\n      right: '-2px',\n      bottom: '-2px',\n      border: '2px solid var(--epr-category-icon-active-color)',\n      borderRadius: '50%'\n    },\n    '&.epr-icn-suggested': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -8)'\n    },\n    '&.epr-icn-custom': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -9)'\n    },\n    '&.epr-icn-activities': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -4)'\n    },\n    '&.epr-icn-animals_nature': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -1)'\n    },\n    '&.epr-icn-flags': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -7)'\n    },\n    '&.epr-icn-food_drink': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -2)'\n    },\n    '&.epr-icn-objects': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -5)'\n    },\n    '&.epr-icn-smileys_people': {\n      backgroundPositionX: '0px'\n    },\n    '&.epr-icn-symbols': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -6)'\n    },\n    '&.epr-icn-travel_places': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -3)'\n    }\n  },\n  ...darkMode('catBtn', DarkPositionY),\n  '.epr-dark-theme': {\n    ...DarkInactivePosition\n  },\n  '.epr-auto-theme': {\n    ...DarkInactivePosition\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { categoryFromCategoryConfig } from '../../config/categoryConfig';\nimport { useCategoriesConfig } from '../../config/useConfig';\nimport { useActiveCategoryScrollDetection } from '../../hooks/useActiveCategoryScrollDetection';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useScrollCategoryIntoView } from '../../hooks/useScrollCategoryIntoView';\nimport { useShouldHideCustomEmojis } from '../../hooks/useShouldHideCustomEmojis';\nimport { isCustomCategory } from '../../typeRefinements/typeRefinements';\nimport { useCategoryNavigationRef } from '../context/ElementRefContext';\n\nimport { CategoryButton } from './CategoryButton';\n\nexport function CategoryNavigation() {\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\n  const scrollCategoryIntoView = useScrollCategoryIntoView();\n  useActiveCategoryScrollDetection(setActiveCategory);\n  const isSearchMode = useIsSearchMode();\n\n  const categoriesConfig = useCategoriesConfig();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const hideCustomCategory = useShouldHideCustomEmojis();\n\n  return (\n    <div\n      className={cx(styles.nav)}\n      role=\"tablist\"\n      aria-label=\"Category navigation\"\n      id=\"epr-category-nav-id\"\n      ref={CategoryNavigationRef}\n    >\n      {categoriesConfig.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n        const isActiveCategory = category === activeCategory;\n\n        if (isCustomCategory(categoryConfig) && hideCustomCategory) {\n          return null;\n        }\n\n        const allowNavigation = !isSearchMode && !isActiveCategory;\n\n        return (\n          <CategoryButton\n            key={category}\n            category={category}\n            isActiveCategory={isActiveCategory}\n            allowNavigation={allowNavigation}\n            categoryConfig={categoryConfig}\n            onClick={() => {\n              setActiveCategory(category);\n              scrollCategoryIntoView(category);\n            }}\n          />\n        );\n      })}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  nav: {\n    '.': 'epr-category-nav',\n    display: 'flex',\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n    padding: 'var(--epr-header-padding)'\n  },\n  '.epr-search-active': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  },\n  '.epr-main:has(input:not(:placeholder-shown))': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../../Stylesheet/stylesheet';\nimport { useClearSearch } from '../../../hooks/useFilter';\nimport { Button } from '../../atoms/Button';\n\nimport SVGTimes from './svg/times.svg';\n\nexport function BtnClearSearch() {\n  const clearSearch = useClearSearch();\n\n  return (\n    <Button\n      className={cx(\n        styles.btnClearSearch,\n        commonInteractionStyles.visibleOnSearchOnly\n      )}\n      onClick={clearSearch}\n      aria-label=\"Clear\"\n      title=\"Clear\"\n    >\n      <div className={cx(styles.icnClearnSearch)} />\n    </Button>\n  );\n}\n\nconst HoverDark = {\n  ':hover': {\n    '> .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', HoverDark)\n});\n", "import * as React from 'react';\n\nimport { ClassNames, asSelectors } from '../../../DomUtils/classNames';\nimport { getNormalizedSearchTerm } from '../../../hooks/useFilter';\n\nconst SCOPE = `${asSelectors(ClassNames.emojiPicker)} ${asSelectors(\n  ClassNames.emojiList\n)}`;\n\nconst EMOJI_BUTTON = ['button', asSelectors(ClassNames.emoji)].join('');\nconst CATEGORY = asSelectors(ClassNames.category);\n\nexport function CssSearch({ value }: { value: undefined | string }) {\n  if (!value) {\n    return null;\n  }\n\n  const q = genQuery(value);\n\n  return (\n    <style>{`\n    ${SCOPE} ${EMOJI_BUTTON} {\n      display: none;\n    }\n\n\n    ${SCOPE} ${q} {\n      display: flex;\n    }\n\n    ${SCOPE} ${CATEGORY}:not(:has(${q})) {\n      display: none;\n    }\n  `}</style>\n  );\n}\n\nfunction genQuery(value: string): string {\n  return [\n    EMOJI_BUTTON,\n    '[data-full-name*=\"',\n    getNormalizedSearchTerm(value),\n    '\"]'\n  ].join('');\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\n\nimport SVGMagnifier from './svg/magnifier.svg';\n\nexport function IcnSearch() {\n  return <div className={cx(styles.icnSearch)} />;\n}\n\nconst styles = stylesheet.create({\n  icnSearch: {\n    '.': 'epr-icn-search',\n    content: '',\n    position: 'absolute',\n    top: '50%',\n    left: 'var(--epr-search-bar-inner-padding)',\n    transform: 'translateY(-50%)',\n    width: '20px',\n    height: '20px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '20px',\n    backgroundImage: `url(${SVGMagnifier})`\n  },\n  ...darkMode('icnSearch', {\n    backgroundPositionY: '-20px'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useAutoFocusSearchConfig,\n  useSearchDisabledConfig,\n  useSearchPlaceHolderConfig\n} from '../../../config/useConfig';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFilter } from '../../../hooks/useFilter';\nimport { useIsSkinToneInSearch } from '../../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../../Layout/Flex';\nimport Relative from '../../Layout/Relative';\nimport { useSearchInputRef } from '../../context/ElementRefContext';\nimport { SkinTonePicker } from '../SkinTonePicker/SkinTonePicker';\n\nimport { BtnClearSearch } from './BtnClearSearch';\nimport { CssSearch } from './CssSearch';\nimport { IcnSearch } from './IcnSearch';\nimport SVGTimes from './svg/times.svg';\n\nexport function SearchContainer() {\n  const searchDisabled = useSearchDisabledConfig();\n\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  if (searchDisabled) {\n    return null;\n  }\n\n  return (\n    <Flex className={cx(styles.overlay)}>\n      <Search />\n\n      {isSkinToneInSearch ? <SkinTonePicker /> : null}\n    </Flex>\n  );\n}\n\nexport function Search() {\n  const [inc, setInc] = useState(0);\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const SearchInputRef = useSearchInputRef();\n  const placeholder = useSearchPlaceHolderConfig();\n  const autoFocus = useAutoFocusSearchConfig();\n  const { statusSearchResults, searchTerm, onChange } = useFilter();\n\n  const input = SearchInputRef?.current;\n  const value = input?.value;\n\n  return (\n    <Relative className={cx(styles.searchContainer)}>\n      <CssSearch value={value} />\n      <input\n        // eslint-disable-next-line jsx-a11y/no-autofocus\n        autoFocus={autoFocus}\n        aria-label={'Type to search for an emoji'}\n        onFocus={closeAllOpenToggles}\n        className={cx(styles.search)}\n        type=\"text\"\n        aria-controls=\"epr-search-id\"\n        placeholder={placeholder}\n        onChange={event => {\n          setInc(inc + 1);\n          setTimeout(() => {\n            onChange(event?.target?.value ?? value);\n          });\n        }}\n        ref={SearchInputRef}\n      />\n      {searchTerm ? (\n        <div\n          role=\"status\"\n          className={cx('epr-status-search-results', styles.visuallyHidden)}\n          aria-live=\"polite\"\n          id=\"epr-search-id\"\n          aria-atomic=\"true\"\n        >\n          {statusSearchResults}\n        </div>\n      ) : null}\n      <IcnSearch />\n      <BtnClearSearch />\n    </Relative>\n  );\n}\n\nconst styles = stylesheet.create({\n  overlay: {\n    padding: 'var(--epr-header-padding)',\n    zIndex: 'var(--epr-header-overlay-z-index)'\n  },\n  searchContainer: {\n    '.': 'epr-search-container',\n    flex: '1',\n    display: 'block',\n    minWidth: '0'\n  },\n  visuallyHidden: {\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(50%)',\n    height: '1px',\n    overflow: 'hidden',\n    position: 'absolute',\n    whiteSpace: 'nowrap',\n    width: '1px'\n  },\n  search: {\n    outline: 'none',\n    transition: 'all 0.2s ease-in-out',\n    color: 'var(--epr-search-input-text-color)',\n    borderRadius: 'var(--epr-search-input-border-radius)',\n    padding: 'var(--epr-search-input-padding)',\n    height: 'var(--epr-search-input-height)',\n    backgroundColor: 'var(--epr-search-input-bg-color)',\n    border: '1px solid var(--epr-search-input-bg-color)',\n    width: '100%',\n    ':focus': {\n      backgroundColor: 'var(--epr-search-input-bg-color-active)',\n      border: '1px solid var(--epr-search-border-color)'\n    },\n    '::placeholder': {\n      color: 'var(--epr-search-input-placeholder-color)'\n    }\n  },\n\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', {\n    ':hover > .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonInteractionStyles } from '../../Stylesheet/stylesheet';\nimport Relative from '../Layout/Relative';\nimport { CategoryNavigation } from '../navigation/CategoryNavigation';\n\nimport { SearchContainer } from './Search/Search';\n\nexport function Header() {\n  return (\n    <Relative\n      className={cx('epr-header', commonInteractionStyles.hiddenOnReactions)}\n    >\n      <SearchContainer />\n      <CategoryNavigation />\n    </Relative>\n  );\n}\n", "import * as React from 'react';\n\nimport { PickerStyleTag } from './Stylesheet/stylesheet';\nimport { Reactions } from './components/Reactions/Reactions';\nimport { Body } from './components/body/Body';\nimport { ElementRefContextProvider } from './components/context/ElementRefContext';\nimport { PickerConfigProvider } from './components/context/PickerConfigContext';\nimport { useReactionsModeState } from './components/context/PickerContext';\nimport { Preview } from './components/footer/Preview';\nimport { Header } from './components/header/Header';\nimport PickerMain from './components/main/PickerMain';\nimport { compareConfig } from './config/compareConfig';\nimport { useAllowExpandReactions, useOpenConfig } from './config/useConfig';\n\nimport { PickerProps } from './index';\n\nfunction EmojiPicker(props: PickerProps) {\n  return (\n    <ElementRefContextProvider>\n      <PickerStyleTag />\n      <PickerConfigProvider {...props}>\n        <ContentControl />\n      </PickerConfigProvider>\n    </ElementRefContextProvider>\n  );\n}\n\nfunction ContentControl() {\n  const [reactionsDefaultOpen] = useReactionsModeState();\n  const allowExpandReactions = useAllowExpandReactions();\n\n  const [renderAll, setRenderAll] = React.useState(!reactionsDefaultOpen);\n  const isOpen = useOpenConfig();\n\n  React.useEffect(() => {\n    if (reactionsDefaultOpen && !allowExpandReactions) {\n      return;\n    }\n\n    if (!renderAll) {\n      setRenderAll(true);\n    }\n  }, [renderAll, allowExpandReactions, reactionsDefaultOpen]);\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <PickerMain>\n      <Reactions />\n      <ExpandedPickerContent renderAll={renderAll} />\n    </PickerMain>\n  );\n}\n\nfunction ExpandedPickerContent({ renderAll }: { renderAll: boolean }) {\n  if (!renderAll) {\n    return null;\n  }\n\n  return (\n    <>\n      <Header />\n      <Body />\n      <Preview />\n    </>\n  );\n}\n\n// eslint-disable-next-line complexity\nexport default React.memo(EmojiPicker, compareConfig);\n", "import * as React from 'react';\n\nexport default class ErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError() {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    // eslint-disable-next-line no-console\n    console.error('Emoji Picker React failed to render:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null;\n    }\n\n    return this.props.children;\n  }\n}\n", "import * as React from 'react';\n\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { GetEmojiUrl } from './BaseEmojiProps';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\nexport function ExportedEmoji({\n  unified,\n  size = 32,\n  emojiStyle = EmojiStyle.APPLE,\n  lazyLoad = false,\n  getEmojiUrl,\n  emojiUrl\n}: {\n  unified: string;\n  emojiStyle?: EmojiStyle;\n  size?: number;\n  lazyLoad?: boolean;\n  getEmojiUrl?: GetEmojiUrl;\n  emojiUrl?: string;\n}) {\n  if (!unified && !emojiUrl && !getEmojiUrl) {\n    return null;\n  }\n\n  return (\n    <ViewOnlyEmoji\n      unified={unified}\n      size={size}\n      emojiStyle={emojiStyle}\n      lazyLoad={lazyLoad}\n      getEmojiUrl={emojiUrl ? () => emojiUrl : getEmojiUrl}\n    />\n  );\n}\n", "import * as React from 'react';\n\nimport EmojiPickerReact from './EmojiPickerReact';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { PickerConfig } from './config/config';\nimport {\n  MutableConfigContext,\n  useDefineMutableConfig\n} from './config/mutableConfig';\n\nexport { ExportedEmoji as Emoji } from './components/emoji/ExportedEmoji';\n\nexport {\n  EmojiStyle,\n  SkinTones,\n  Theme,\n  Categories,\n  EmojiClickData,\n  SuggestionMode,\n  SkinTonePickerLocation\n} from './types/exposedTypes';\n\nexport interface PickerProps extends PickerConfig {}\n\nexport default function EmojiPicker(props: PickerProps) {\n  const MutableConfigRef = useDefineMutableConfig({\n    onEmojiClick: props.onEmojiClick,\n    onReactionClick: props.onReactionClick,\n    onSkinToneChange: props.onSkinToneChange,\n  });\n\n  return (\n    <ErrorBoundary>\n      <MutableConfigContext.Provider value={MutableConfigRef}>\n        <EmojiPickerReact {...props} />\n      </MutableConfigContext.Provider>\n    </ErrorBoundary>\n  );\n}\n"], "names": ["ClassNames", "asSelectors", "classNames", "Array", "_len", "_key", "arguments", "map", "c", "join", "stylesheet", "createSheet", "hidden", "display", "opacity", "pointerEvents", "visibility", "overflow", "commonStyles", "create", "_extends", "PickerStyleTag", "React", "suppressHydrationWarning", "dangerouslySetInnerHTML", "__html", "getStyle", "commonInteractionStyles", "categoryBtn", "backgroundPositionY", "hiddenOnSearch", "visibleOnSearchOnly", "hiddenOnReactions", "transition", "height", "width", "darkMode", "key", "value", "_eprDarkTheme", "_eprAutoTheme", "compareConfig", "prev", "next", "prevCustomEmojis", "_prev$customEmojis", "customEmojis", "nextCustomEmojis", "_next$customEmojis", "open", "emojiVersion", "reactionsDefaultOpen", "searchPlaceHolder", "searchPlaceholder", "defaultSkinTone", "skinTonesDisabled", "autoFocusSearch", "emojiStyle", "theme", "suggestedEmojisMode", "lazyLoadEmojis", "className", "style", "searchDisabled", "skinTonePickerLocation", "length", "DEFAULT_REACTIONS", "SuggestionMode", "EmojiStyle", "Theme", "SkinTones", "Categories", "SkinTonePickerLocation", "categoriesOrdered", "SUGGESTED", "CUSTOM", "SMILEYS_PEOPLE", "ANIMALS_NATURE", "FOOD_DRINK", "TRAVEL_PLACES", "ACTIVITIES", "OBJECTS", "SYMBOLS", "FLAGS", "SuggestedRecent", "name", "category", "configByCategory", "_configByCategory", "baseCategoriesConfig", "modifiers", "categoryFromCategoryConfig", "categoryNameFromCategoryConfig", "mergeCategoriesConfig", "userCategoriesConfig", "extra", "suggestionMode", "RECENT", "base", "_userCategoriesConfig", "getBaseConfigByCategory", "modifier", "Object", "assign", "CDN_URL_APPLE", "CDN_URL_FACEBOOK", "CDN_URL_TWITTER", "CDN_URL_GOOGLE", "cdnUrl", "TWITTER", "GOOGLE", "FACEBOOK", "APPLE", "skinToneVariations", "NEUTRAL", "LIGHT", "MEDIUM_LIGHT", "MEDIUM", "MEDIUM_DARK", "DARK", "skinTonesNamed", "entries", "reduce", "acc", "_ref", "skinTonesMapped", "mapped", "skinTone", "_Object$assign", "EmojiProperties", "alphaNumericEmojiIndex", "setTimeout", "allEmojis", "searchIndex", "emoji", "indexEmoji", "joinedNameString", "emojiNames", "flat", "toLowerCase", "replace", "split", "for<PERSON>ach", "char", "_alphaNumericEmojiInd", "emojiUnified", "_emoji$EmojiPropertie", "addedIn", "parseFloat", "added_in", "emojiName", "unifiedWithoutSkinTone", "unified", "splat", "_splat$splice", "splice", "emojiHasVariations", "_emojiVariationUnifie", "emojiVariationUnified", "emojisByCategory", "_emojis$category", "emojis", "emojiUrlByUnified", "emojiVariations", "_emoji$EmojiPropertie2", "variations", "find", "variation", "includes", "emojiByUnified", "allEmojisByUnified", "withoutSkinTone", "values", "setCustomEmojis", "emojiData", "customToRegularEmoji", "push", "names", "id", "imgUrl", "<PERSON><PERSON><PERSON>", "activeVariationFromUnified", "_unified$split", "suspectedSkinTone", "KNOWN_FAILING_EMOJIS", "DEFAULT_SEARCH_PLACEHOLDER", "SEARCH_RESULTS_NO_RESULTS_FOUND", "SEARCH_RESULTS_SUFFIX", "SEARCH_RESULTS_ONE_RESULT_FOUND", "SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND", "mergeConfig", "userConfig", "basePickerConfig", "previewConfig", "_userConfig$previewCo", "config", "categories", "hidden<PERSON><PERSON><PERSON><PERSON>", "unicodeToHide", "add", "_config$customEmojis", "PREVIEW", "getEmojiUrl", "basePreviewConfig", "SEARCH", "FREQUENT", "Set", "reactions", "allowExpandReactions", "defaultEmoji", "defaultCaption", "showPreview", "ConfigContext", "PickerConfigProvider", "children", "_objectWithoutPropertiesLoose", "_excluded", "mergedConfig", "useSetConfig", "Provider", "_React$useState", "setMergedConfig", "usePickerConfig", "MutableConfigContext", "createContext", "useMutableConfig", "mutableConfig", "useContext", "useDefineMutableConfig", "MutableConfigRef", "useRef", "onEmojiClick", "emptyFunc", "onReactionClick", "onSkinToneChange", "useEffect", "current", "MOUSE_EVENT_SOURCE", "useSearchPlaceHolderConfig", "_usePickerConfig", "_find", "p", "useDefaultSkinToneConfig", "_usePickerConfig2", "useAllowExpandReactions", "_usePickerConfig3", "useSkinTonesDisabledConfig", "_usePickerConfig4", "useEmojiStyleConfig", "_usePickerConfig5", "useAutoFocusSearchConfig", "_usePickerConfig6", "useCategoriesConfig", "_usePickerConfig7", "useCustomEmojisConfig", "_usePickerConfig8", "useOpenConfig", "_usePickerConfig9", "useOnEmojiClickConfig", "mouseEventSource", "_useMutableConfig", "handler", "REACTIONS", "useOnSkinToneChangeConfig", "_useMutableConfig2", "usePreviewConfig", "_usePickerConfig10", "useThemeConfig", "_usePickerConfig11", "useSuggestedEmojisModeConfig", "_usePickerConfig12", "useLazyLoadEmojisConfig", "_usePickerConfig13", "useClassNameConfig", "_usePickerConfig14", "useStyleConfig", "_usePickerConfig15", "getDimension", "useReactionsOpenConfig", "_usePickerConfig16", "useEmojiVersionConfig", "_usePickerConfig17", "useSearchDisabledConfig", "_usePickerConfig18", "useSkinTonePickerLocationConfig", "_usePickerConfig19", "useUnicodeToHide", "_usePickerConfig20", "useReactionsConfig", "_usePickerConfig21", "useGetEmojiUrlConfig", "_usePickerConfig22", "dimensionConfig", "useSearchResultsConfig", "searchResultsCount", "hasResults", "isPlural", "toString", "useDebouncedState", "initialValue", "delay", "_useState", "useState", "state", "setState", "timer", "debouncedSetState", "Promise", "resolve", "clearTimeout", "_window", "window", "useIsUnicodeHidden", "has", "useDisallowedEmojis", "DisallowedEmojisRef", "emojiVersionConfig", "useMemo", "Number", "isNaN", "disallowed<PERSON><PERSON><PERSON><PERSON>", "addedInNewerVersion", "useIsEmojiDisallowed", "isUnicodeHidden", "isEmojiDisallowed", "Boolean", "supportedLevel", "useMarkInitialLoad", "dispatch", "PickerContextProvider", "filterRef", "disallowClickRef", "disallowMouseRef", "disallowedEmojisRef", "suggestedUpdateState", "Date", "now", "searchTerm", "skinToneFanOpenState", "activeSkinTone", "activeCategoryState", "emojisThatFailedToLoadState", "emojiVariationPickerState", "reactionsModeState", "isPastInitialLoad", "setIsPastInitialLoad", "<PERSON>er<PERSON>ontext", "undefined", "useFilterRef", "_React$useContext", "useDisallowClickRef", "_React$useContext2", "useDisallowMouseRef", "_React$useContext3", "useReactionsModeState", "_React$useContext4", "useSearchTermState", "_React$useContext5", "useActiveSkinToneState", "_React$useContext6", "useEmojisThatFailedToLoadState", "_React$useContext7", "useIsPastInitialLoad", "_React$useContext8", "useEmojiVariationPickerState", "_React$useContext9", "useSkinToneFanOpenState", "_React$useContext10", "useUpdateSuggested", "_React$useContext12", "suggestedUpdated", "setsuggestedUpdate", "updateSuggested", "useIsSearchMode", "_useSearchTermState", "focusElement", "element", "requestAnimationFrame", "focus", "focusPrevElementSibling", "previousElementSibling", "focusNextElementSibling", "nextElement<PERSON><PERSON>ling", "focusFirstElementChild", "first", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getActiveElement", "document", "activeElement", "ElementRefContextProvider", "Picker<PERSON>ain<PERSON><PERSON>", "AnchoredEmojiRef", "BodyRef", "SearchInputRef", "SkinTonePickerRef", "CategoryNavigationRef", "VariationPickerRef", "ReactionsRef", "ElementRefContext", "useElementRef", "usePickerMainRef", "useAnchoredEmojiRef", "useSetAnchoredEmojiRef", "target", "useBodyRef", "useReactionsRef", "useSearchInputRef", "useSkinTonePickerRef", "useCategoryNavigationRef", "useVariationPickerRef", "scrollTo", "root", "top", "$eprBody", "queryScrollBody", "scrollTop", "scrollBy", "by", "useScrollTo", "useCallback", "scrollEmojiAboveLabel", "isEmojiBehindLabel", "closest", "variationPicker", "scrollBody", "closestScrollBody", "emojiDistanceFromScrollTop", "categoryLabelHeight", "closestCategory", "focusFirstVisibleEmoji", "parent", "firstVisibleEmoji", "focusAndClickFirstVisibleEmoji", "first<PERSON><PERSON><PERSON>", "click", "focusLastVisibleEmoji", "lastVisibleEmoji", "focusNextVisibleEmoji", "nextVisibleEmoji", "nextCategory", "focusPrevVisibleEmoji", "prevVisibleEmoji", "prevCategory", "focusVisibleEmojiOneRowUp", "exitUp", "visibleEmojiOneRowUp", "focusVisibleEmojiOneRowDown", "visibleEmojiOneRowDown", "categoryContent", "closestCategoryContent", "indexInRow", "elementIndexInRow", "row", "rowNumber", "countInRow", "elementCountInRow", "prevVisibleCategory", "getElementInRow", "allVisibleEmojis", "getElementInPrevRow", "hasNextRow", "nextVisibleCategory", "itemInNextRow", "getElementInNextRow", "useCloseAllOpenToggles", "_useEmojiVariationPic", "setVariationPicker", "_useSkinToneFanOpenSt", "skinToneFanOpen", "setSkinToneFanOpen", "closeAllOpenToggles", "useHasOpenToggles", "_useEmojiVariationPic2", "_useSkinToneFanOpenSt2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useDisallowMouseMove", "DisallowMouseRef", "disallowMouseMove", "useAllowMouseMove", "allowMouseMove", "useIsMouseDisallowed", "isMouseDisallowed", "useOnMouseMove", "bodyRef", "addEventListener", "onMouseMove", "passive", "removeEventListener", "useFocusSearchInput", "useFocusSkinTonePicker", "useFocusCategoryNavigation", "useSetFilterRef", "setFilter", "setter", "useClearSearch", "applySearch", "useApplySearch", "focusSearchInput", "clearSearch", "useAppendSearch", "appendSearch", "str", "getNormalizedSearchTerm", "useFilter", "setFilterRef", "statusSearchResults", "getStatusSearchResults", "onChange", "inputValue", "filter", "nextValue", "longestMatch", "findLongestMatch", "filterEmojiObjectByKeyword", "_useSearchTermState2", "setSearchTerm", "then", "keyword", "filtered", "hasMatch", "some", "useIsEmojiFiltered", "_useFilterRef", "_useSearchTermState3", "isEmojiFilteredBySearchTerm", "_filter$searchTerm", "dict", "longestMatchingKey", "keys", "sort", "a", "b", "trim", "filterState", "_Object$entries", "useSetVariationPicker", "setAnchoredEmojiRef", "setEmojiVariationPicker", "_emojiFromElement", "emojiFromElement", "useIsSkinToneInSearch", "skinTonePickerLocationConfig", "useIsSkinToneInPreview", "KeyboardEvents", "useKeyboardNavigation", "usePickerMainKeyboardEvents", "useSearchInputKeyboardEvents", "useSkinTonePickerKeyboardEvents", "useCategoryNavigationKeyboardEvents", "useBodyKeyboardEvents", "onKeyDown", "event", "Escape", "preventDefault", "focusSkinTonePicker", "setSkinToneFanOpenState", "goDownFromSearchInput", "useGoDownFromSearchInput", "isSkinToneInSearch", "ArrowRight", "ArrowDown", "Enter", "isOpen", "setIsOpen", "isSkinToneInPreview", "onType", "useOnType", "ArrowLeft", "focusNextSkinTone", "focusPrevSkinTone", "ArrowUp", "goUpFromBody", "useGoUpFromBody", "buttonFromTarget", "Space", "focusCategoryNavigation", "isSearchMode", "goUpFromEmoji", "exitLeft", "currentSkinTone", "hasNextElementSibling", "hasModifier", "match", "metaKey", "ctrl<PERSON>ey", "altKey", "preloadEmoji", "NATIVE", "preloadedEmojs", "emojiUrl", "preloadImage", "url", "image", "Image", "src", "useOnFocus", "onFocus", "button", "DEFAULT_LABEL_HEIGHT", "<PERSON><PERSON><PERSON><PERSON>", "PickerRootElement", "_ref2", "_useReactionsModeStat", "reactionsMode", "searchModeActive", "_ref3", "styleProps", "cx", "styles", "main", "baseVariables", "darkTheme", "AUTO", "autoThemeDark", "_cx", "searchActive", "reactionsMenu", "ref", "DarkTheme", "emojiPicker", "position", "flexDirection", "borderWidth", "borderStyle", "borderRadius", "borderColor", "backgroundColor", "boxSizing", "fontFamily", "autoTheme", "<PERSON><PERSON>ilter", "parentWidth", "getBoundingClientRect", "elementWidth", "Math", "floor", "elementLeft", "left", "parentLeft", "elementHeight", "elementTop", "parentTop", "round", "parentHeight", "getRowElements", "elements", "elementsInRow", "lastRow", "firstElementIndex", "lastElementIndex", "slice", "getNextRowElements", "allElements", "currentRow", "nextRow", "rowElements", "index", "nextRowElements", "prevRowElements", "firstVisibleElementInContainer", "maxVisibilityDiffThreshold", "parentBottom", "bottom", "parentTopWithLabel", "getLabelHeight", "visibleElements", "elementBottom", "maxVisibilityDiffPixels", "clientHeight", "elementTopWithAllowedDiff", "elementBottomWithAllowedDiff", "parentNode", "labels", "from", "querySelectorAll", "label", "_i", "_labels", "EmojiButtonSelector", "VisibleEmojiSelector", "visible", "emojiElement", "_emojiElement$closest", "originalUnified", "originalUnifiedFromEmojiElement", "unifiedFromEmojiElement", "isEmojiElement", "matches", "_element$parentElemen", "parentElement", "_element$clientHeight", "emojiTrueOffsetTop", "labelHeight", "elementOffsetTop", "categoryWithoutLabel", "querySelector", "_category$clientHeigh", "_categoryWithoutLabel", "_closestScrollBody$sc", "_closestScrollBody", "_element$closest", "emojiTruOffsetLeft", "elementOffsetLeft", "_element$offsetTop", "offsetTop", "_element$offsetLeft", "offsetLeft", "_elementDataSetKey", "elementDataSetKey", "allUnifiedFromEmojiElement", "_elementDataSet$key", "elementDataSet", "_element$dataset", "dataset", "isVisibleEmoji", "classList", "contains", "isHidden", "_allEmojis$slice", "last", "parseNative<PERSON><PERSON><PERSON>", "hex", "String", "fromCodePoint", "parseInt", "SUGGESTED_LS_KEY", "getSuggested", "mode", "_window$localStorage$", "_window2", "localStorage", "recent", "JSON", "parse", "getItem", "count", "_unused", "setSuggested", "existing", "u", "nextList", "concat", "i", "original", "min", "_window3", "setItem", "stringify", "_unused2", "isCustomCategory", "isCustomEmoji", "useMouseDownHandlers", "ContainerRef", "mouseDownTimerRef", "_useActiveSkinToneSta", "_useUpdateSuggested", "activeEmojiStyle", "onClick", "_emojiFromEvent", "emojiFromEvent", "skinToneToUse", "emojiClickOutput", "onMouseDown", "_emojiFromEvent2", "onMouseUp", "confainerRef", "getImageUrl", "imageUrl", "isCustom", "<PERSON><PERSON>", "props", "type", "cursor", "border", "background", "outline", "ClickableEmojiButton", "_ref$showVariations", "showVariations", "hasVariations", "_ref$noBackground", "noBackground", "getAriaLabel", "_emojiNames$", "alignItems", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", "content", "right", "borderLeft", "borderRight", "transform", "borderBottom", "zIndex", "emojiStyles", "external", "fontSize", "common", "alignSelf", "justifySelf", "EmojiImg", "_ref$lazyLoad", "lazyLoad", "onError", "alt", "emojiImag", "loading", "min<PERSON><PERSON><PERSON>", "minHeight", "padding", "NativeEmoji", "native<PERSON><PERSON>ji", "lineHeight", "textAlign", "letterSpacing", "View<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "size", "_ref$getEmojiUrl", "_useEmojisThatFailedT", "setEmojisThatFailedToLoad", "emojiToRender", "ClickableEmoji", "BtnPlus", "setReactionsMode", "title", "tabIndex", "plusSign", "color", "backgroundImage", "Plus", "backgroundRepeat", "backgroundSize", "Reactions", "reactionsOpen", "list", "reaction", "emojiButton", "listStyle", "margin", "useOnScroll", "onScroll", "useIsEmojiHidden", "emojisThatFailedToLoad", "isEmojiFiltered", "failedToLoad", "filteredOut", "EmojiCategory", "categoryConfig", "categoryName", "gridGap", "gridTemplateColumns", "fontWeight", "textTransform", "isEverMounted", "useIsEverMounted", "isMounted", "setIsMounted", "Suggested", "suggestedEmojisModeConfig", "suggested", "_getSuggested", "suggestedItem", "EmojiList", "renderdCategoriesCountRef", "emojiList", "RenderCategory", "isEmojiHidden", "emojisToPush", "hiddenCounter", "_isEmojiHidden", "isDisallowed", "Direction", "EmojiVariationPicker", "_useVariationPickerTo", "useVariationPickerTop", "getTop", "getMenuDirection", "getPointerStyle", "usePointerStyle", "pointerStyle", "Down", "pointingUp", "pointer", "clientWidth", "direction", "Up", "emojiOffsetTop", "_bodyRef$scrollTop", "buttonHeight", "boxShadow", "transform<PERSON><PERSON>in", "backgroundPosition", "SVGTriangle", "Body", "PICKER", "body", "flex", "overflowY", "overflowX", "detectEmojyPartiallyBelowFold", "buttonRect", "bodyRect", "y", "useEmojiPreviewEvents", "allow", "setPreviewEmoji", "onEscape", "onMouseOver", "onEnter", "onLeave", "e", "_allUnifiedFromEmojiE", "relatedTarget", "belowFoldByPx", "handlePartiallyVisibleElementFocus", "_allUnifiedFromEmojiE2", "_document$activeEleme", "blur", "FlexDirection", "Flex", "_ref$style", "_ref$direction", "ROW", "_stylesheet$create", "COLUMN", "Absolute", "Relative", "BtnSkinToneVariation", "isActive", "skinToneVariation", "tone", "closedTone", "active", "ITEM_SIZE", "SkinTonePickerMenu", "SkinTonePicker", "SkinTonePickerDirection", "VERTICAL", "HORIZONTAL", "isDisabled", "setActiveSkinTone", "fullWidth", "expandedSize", "vertical", "skinTones", "verticalShadow", "flexBasis", "select", "Preview", "preview", "PreviewBody", "previewEmoji", "variationPickerEmoji", "_previewEmoji$unified", "show", "PreviewContent", "defaultText", "borderTop", "categoryNameFromDom", "$category", "_$category$getAttribu", "getAttribute", "useActiveCategoryScrollDetection", "setActiveCategory", "visibleCategories", "Map", "observer", "IntersectionObserver", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "entry", "set", "intersectionRatio", "ratios", "lastCategory", "_ratios", "_ratios$_i", "ratio", "threshold", "el", "observe", "useScrollCategoryIntoView", "scrollCategoryIntoView", "_BodyRef$current", "useShouldHideCustomEmojis", "customCategoryConfig", "CategoryButton", "isActiveCategory", "allowNavigation", "catBtn", "role", "DarkActivePositionY", "DarkPositionY", "DarkInactivePosition", "SVGNavigation", "backgroundPositionX", "CategoryNavigation", "activeCategory", "categoriesConfig", "hideCustomCategory", "nav", "BtnClearSearch", "btnClearSearch", "icnClearnSearch", "HoverDark", "SVGTimes", "SCOPE", "EMOJI_BUTTON", "CATEGORY", "CssSearch", "q", "gen<PERSON><PERSON><PERSON>", "IcnSearch", "icnSearch", "SVGMagnifier", "SearchContainer", "overlay", "Search", "inc", "setInc", "placeholder", "autoFocus", "_useFilter", "input", "searchContainer", "search", "_event$target$value", "_event$target", "visuallyHidden", "clip", "clipPath", "whiteSpace", "Header", "EmojiPicker", "ContentControl", "renderAll", "setRenderAll", "ExpandedPickerContent", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_inherits<PERSON><PERSON>e", "_this", "call", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "_proto", "prototype", "componentDidCatch", "error", "errorInfo", "console", "render", "ExportedEmoji", "_ref$size", "_ref$emojiStyle", "EmojiPickerReact"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAYA,UAqBX;AArBD,CAAA,SAAYA,UAAU;IACpBA,UAAAA,CAAAA,iBAAAA,GAAAA,sBAAuC;IACvCA,UAAAA,CAAAA,eAAAA,GAAAA,mBAAkC;IAClCA,UAAAA,CAAAA,SAAAA,GAAAA,YAAqB;IACrBA,UAAAA,CAAAA,UAAAA,GAAAA,aAAuB;IACvBA,UAAAA,CAAAA,SAAAA,GAAAA,YAAqB;IACrBA,UAAAA,CAAAA,QAAAA,GAAAA,WAAmB;IACnBA,UAAAA,CAAAA,WAAAA,GAAAA,oBAA+B;IAC/BA,UAAAA,CAAAA,QAAAA,GAAAA,0BAAkC;IAClCA,UAAAA,CAAAA,kBAAAA,GAAAA,4BAA8C;IAC9CA,UAAAA,CAAAA,qBAAAA,GAAAA,0BAA+C;IAC/CA,UAAAA,CAAAA,aAAAA,GAAAA,UAAuB;IACvBA,UAAAA,CAAAA,YAAAA,GAAAA,gBAA4B;IAC5BA,UAAAA,CAAAA,WAAAA,GAAAA,iBAA4B;IAC5BA,UAAAA,CAAAA,cAAAA,GAAAA,kBAAgC;IAChCA,UAAAA,CAAAA,OAAAA,GAAAA,UAAiB;IACjBA,UAAAA,CAAAA,WAAAA,GAAAA,cAAyB;IACzBA,UAAAA,CAAAA,aAAAA,GAAAA,gBAA6B;IAC7BA,UAAAA,CAAAA,kBAAAA,GAAAA,4BAA8C;IAC9CA,UAAAA,CAAAA,YAAAA,GAAAA,gBAA4B;IAC5BA,UAAAA,CAAAA,YAAAA,GAAAA,gBAA4B;AAC9B,CAAC,EArBWA,UAAU,IAAA,CAAVA,UAAU,GAAA,CAAA,CAAA;SAuBNC,WAAWA;qCAAIC,UAAwB,GAAA,IAAAC,KAAA,CAAAC,IAAA,GAAAC,IAAA,GAAA,GAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA,GAAA;QAAxBH,UAAwB,CAAAG,IAAA,CAAA,GAAAC,SAAA,CAAAD,IAAA,CAAA;;IACrD,OAAOH,UAAU,CAACK,GAAG,CAAC,SAAAC,CAAC;QAAA,OAAA,MAAQA,CAAC;KAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC9C;ACpBO,IAAMC,UAAU,GAAA,WAAA,uJAAGC,cAAAA,AAAW,EAAC,KAAK,EAAE,IAAI,CAAC;AAElD,IAAMC,MAAM,GAAG;IACbC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,GAAG;IACZC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;CACX;AAEM,IAAMC,YAAY,GAAA,WAAA,GAAGR,UAAU,CAACS,MAAM,CAAC;IAC5CP,MAAM,EAAA,WAAA,GAAAQ,QAAA,CAAA;QACJ,GAAG,EAAEpB,UAAU,CAACY,MAAAA;OACbA,MAAM;CAEZ,CAAC;AAEK,IAAMS,cAAc,GAAA,WAAA,6MAAGC,OAAAA,AAAU,EAAC,SAASD,cAAcA;IAC9D,iNACEC,gBAAAA,EAAAA,SAAAA;QACEC,wBAAwB,EAAA;QACxBC,uBAAuB,EAAE;YAAEC,MAAM,EAAEf,UAAU,CAACgB,QAAQ;;MACtD;AAEN,CAAC,CAAC;AAEK,IAAMC,uBAAuB,GAAA,WAAA,GAAGjB,UAAU,CAACS,MAAM,CAAC;IACvD,WAAW,EAAE;QACX,qCAAqC,EAAE;YACrCS,WAAW,EAAE;gBACX,QAAQ,EAAE;oBACRd,OAAO,EAAE,GAAG;oBACZe,mBAAmB,EAAE;;aAExB;YACDC,cAAc,EAAA,WAAA,GAAAV,QAAA,CAAA;gBACZ,GAAG,EAAEpB,UAAU,CAAC8B,cAAAA;eACblB,MAAM;SAEZ;QACD,iCAAiC,EAAE;YACjCmB,mBAAmB,EAAEnB;;KAExB;IACDoB,iBAAiB,EAAE;QACjBC,UAAU,EAAE;KACb;IACD,gBAAgB,EAAE;QAChBD,iBAAiB,EAAE;YACjBE,MAAM,EAAE,KAAK;YACbC,KAAK,EAAE,KAAK;YACZrB,OAAO,EAAE,GAAG;YACZC,aAAa,EAAE,MAAM;YACrBE,QAAQ,EAAE;;KAEb;IACD,2CAA2C,EAAE;QAC3CW,WAAW,EAAE;YACX,QAAQ,EAAE;gBACRd,OAAO,EAAE,GAAG;gBACZe,mBAAmB,EAAE;aACtB;YACD,cAAc,EAAE;gBACdf,OAAO,EAAE,GAAG;gBACZe,mBAAmB,EAAE;;SAExB;QACDE,mBAAmB,EAAA,WAAA,GAAAX,QAAA,CAAA;YACjB,GAAG,EAAE;WACFR,MAAM;;CAGd,CAAC;AAEF,SAAgBwB,QAAQA,CAACC,GAAW,EAAEC,KAAa;;IACjD,OAAO;QACL,iBAAiB,EAAA,CAAAC,aAAA,GAAA,CAAA,GAAAA,aAAA,CACdF,GAAG,CAAA,GAAGC,KAAK,EAAAC,aAAA,CACb;QACD,iBAAiB,EAAA,CAAAC,aAAA,GAAA,CAAA,GAAAA,aAAA,CACdH,GAAG,CAAA,GAAG;YACL,qCAAqC,EAAEC;SACxC,EAAAE,aAAA;KAEJ;AACH;ACxFA,sCAAA;AACA,SAAgBC,aAAaA,CAACC,IAAkB,EAAEC,IAAkB;;IAClE,IAAMC,gBAAgB,GAAA,CAAAC,kBAAA,GAAGH,IAAI,CAACI,YAAY,KAAA,OAAAD,kBAAA,GAAI,EAAE;IAChD,IAAME,gBAAgB,GAAA,CAAAC,kBAAA,GAAGL,IAAI,CAACG,YAAY,KAAA,OAAAE,kBAAA,GAAI,EAAE;IAChD,OACEN,IAAI,CAACO,IAAI,KAAKN,IAAI,CAACM,IAAI,IACvBP,IAAI,CAACQ,YAAY,KAAKP,IAAI,CAACO,YAAY,IACvCR,IAAI,CAACS,oBAAoB,KAAKR,IAAI,CAACQ,oBAAoB,IACvDT,IAAI,CAACU,iBAAiB,KAAKT,IAAI,CAACS,iBAAiB,IACjDV,IAAI,CAACW,iBAAiB,KAAKV,IAAI,CAACU,iBAAiB,IACjDX,IAAI,CAACY,eAAe,KAAKX,IAAI,CAACW,eAAe,IAC7CZ,IAAI,CAACa,iBAAiB,KAAKZ,IAAI,CAACY,iBAAiB,IACjDb,IAAI,CAACc,eAAe,KAAKb,IAAI,CAACa,eAAe,IAC7Cd,IAAI,CAACe,UAAU,KAAKd,IAAI,CAACc,UAAU,IACnCf,IAAI,CAACgB,KAAK,KAAKf,IAAI,CAACe,KAAK,IACzBhB,IAAI,CAACiB,mBAAmB,KAAKhB,IAAI,CAACgB,mBAAmB,IACrDjB,IAAI,CAACkB,cAAc,KAAKjB,IAAI,CAACiB,cAAc,IAC3ClB,IAAI,CAACmB,SAAS,KAAKlB,IAAI,CAACkB,SAAS,IACjCnB,IAAI,CAACR,MAAM,KAAKS,IAAI,CAACT,MAAM,IAC3BQ,IAAI,CAACP,KAAK,KAAKQ,IAAI,CAACR,KAAK,IACzBO,IAAI,CAACoB,KAAK,KAAKnB,IAAI,CAACmB,KAAK,IACzBpB,IAAI,CAACqB,cAAc,KAAKpB,IAAI,CAACoB,cAAc,IAC3CrB,IAAI,CAACsB,sBAAsB,KAAKrB,IAAI,CAACqB,sBAAsB,IAC3DpB,gBAAgB,CAACqB,MAAM,KAAKlB,gBAAgB,CAACkB,MAAM;AAEvD;AC3BO,IAAMC,iBAAiB,GAAG;IAC/B,OAAO;IACP,WAAW;IACX,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO,CAAA,KAAA;CACR;ICGWC,cAGX;AAHD,CAAA,SAAYA,cAAc;IACxBA,cAAAA,CAAAA,SAAAA,GAAAA,QAAiB;IACjBA,cAAAA,CAAAA,WAAAA,GAAAA,UAAqB;AACvB,CAAC,EAHWA,cAAc,IAAA,CAAdA,cAAc,GAAA,CAAA,CAAA;AAK1B,IAAYC,UAMX;AAND,CAAA,SAAYA,UAAU;IACpBA,UAAAA,CAAAA,SAAAA,GAAAA,QAAiB;IACjBA,UAAAA,CAAAA,QAAAA,GAAAA,OAAe;IACfA,UAAAA,CAAAA,UAAAA,GAAAA,SAAmB;IACnBA,UAAAA,CAAAA,SAAAA,GAAAA,QAAiB;IACjBA,UAAAA,CAAAA,WAAAA,GAAAA,UAAqB;AACvB,CAAC,EANWA,UAAU,IAAA,CAAVA,UAAU,GAAA,CAAA,CAAA;AAQtB,IAAYC,KAIX;AAJD,CAAA,SAAYA,KAAK;IACfA,KAAAA,CAAAA,OAAAA,GAAAA,MAAa;IACbA,KAAAA,CAAAA,QAAAA,GAAAA,OAAe;IACfA,KAAAA,CAAAA,OAAAA,GAAAA,MAAa;AACf,CAAC,EAJWA,KAAK,IAAA,CAALA,KAAK,GAAA,CAAA,CAAA;AAMjB,IAAYC,SAOX;AAPD,CAAA,SAAYA,SAAS;IACnBA,SAAAA,CAAAA,UAAAA,GAAAA,SAAmB;IACnBA,SAAAA,CAAAA,QAAAA,GAAAA,OAAe;IACfA,SAAAA,CAAAA,eAAAA,GAAAA,OAAsB;IACtBA,SAAAA,CAAAA,SAAAA,GAAAA,OAAgB;IAChBA,SAAAA,CAAAA,cAAAA,GAAAA,OAAqB;IACrBA,SAAAA,CAAAA,OAAAA,GAAAA,OAAc;AAChB,CAAC,EAPWA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA;AASrB,IAAYC,UAWX;AAXD,CAAA,SAAYA,UAAU;IACpBA,UAAAA,CAAAA,YAAAA,GAAAA,WAAuB;IACvBA,UAAAA,CAAAA,SAAAA,GAAAA,QAAiB;IACjBA,UAAAA,CAAAA,iBAAAA,GAAAA,gBAAiC;IACjCA,UAAAA,CAAAA,iBAAAA,GAAAA,gBAAiC;IACjCA,UAAAA,CAAAA,aAAAA,GAAAA,YAAyB;IACzBA,UAAAA,CAAAA,gBAAAA,GAAAA,eAA+B;IAC/BA,UAAAA,CAAAA,aAAAA,GAAAA,YAAyB;IACzBA,UAAAA,CAAAA,UAAAA,GAAAA,SAAmB;IACnBA,UAAAA,CAAAA,UAAAA,GAAAA,SAAmB;IACnBA,UAAAA,CAAAA,QAAAA,GAAAA,OAAe;AACjB,CAAC,EAXWA,UAAU,IAAA,CAAVA,UAAU,GAAA,CAAA,CAAA;AAatB,IAAYC,sBAGX;AAHD,CAAA,SAAYA,sBAAsB;IAChCA,sBAAAA,CAAAA,SAAAA,GAAAA,QAAiB;IACjBA,sBAAAA,CAAAA,UAAAA,GAAAA,SAAmB;AACrB,CAAC,EAHWA,sBAAsB,IAAA,CAAtBA,sBAAsB,GAAA,CAAA,CAAA;;AChDlC,IAAMC,iBAAiB,GAAiB;IACtCF,UAAU,CAACG,SAAS;IACpBH,UAAU,CAACI,MAAM;IACjBJ,UAAU,CAACK,cAAc;IACzBL,UAAU,CAACM,cAAc;IACzBN,UAAU,CAACO,UAAU;IACrBP,UAAU,CAACQ,aAAa;IACxBR,UAAU,CAACS,UAAU;IACrBT,UAAU,CAACU,OAAO;IAClBV,UAAU,CAACW,OAAO;IAClBX,UAAU,CAACY,KAAK;CACjB;AAEM,IAAMC,eAAe,GAAmB;IAC7CC,IAAI,EAAE,eAAe;IACrBC,QAAQ,EAAEf,UAAU,CAACG,SAAAA;CACtB;AAOD,IAAMa,gBAAgB,GAAA,CAAAC,iBAAA,GAAA,CAAA,GAAAA,iBAAA,CACnBjB,UAAU,CAACG,SAAS,CAAA,GAAG;IACtBY,QAAQ,EAAEf,UAAU,CAACG,SAAS;IAC9BW,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACI,MAAM,CAAA,GAAG;IACnBW,QAAQ,EAAEf,UAAU,CAACI,MAAM;IAC3BU,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACK,cAAc,CAAA,GAAG;IAC3BU,QAAQ,EAAEf,UAAU,CAACK,cAAc;IACnCS,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACM,cAAc,CAAA,GAAG;IAC3BS,QAAQ,EAAEf,UAAU,CAACM,cAAc;IACnCQ,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACO,UAAU,CAAA,GAAG;IACvBQ,QAAQ,EAAEf,UAAU,CAACO,UAAU;IAC/BO,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACQ,aAAa,CAAA,GAAG;IAC1BO,QAAQ,EAAEf,UAAU,CAACQ,aAAa;IAClCM,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACS,UAAU,CAAA,GAAG;IACvBM,QAAQ,EAAEf,UAAU,CAACS,UAAU;IAC/BK,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACU,OAAO,CAAA,GAAG;IACpBK,QAAQ,EAAEf,UAAU,CAACU,OAAO;IAC5BI,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACW,OAAO,CAAA,GAAG;IACpBI,QAAQ,EAAEf,UAAU,CAACW,OAAO;IAC5BG,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACY,KAAK,CAAA,GAAG;IAClBG,QAAQ,EAAEf,UAAU,CAACY,KAAK;IAC1BE,IAAI,EAAE;CACP,EAAAG,iBAAA,CACF;AAED,SAAgBC,oBAAoBA,CAClCC,SAA8C;IAE9C,OAAOjB,iBAAiB,CAAClE,GAAG,CAAC,SAAA+E,QAAQ;QACnC,OAAAlE,QAAA,CAAA,CAAA,GACKmE,gBAAgB,CAACD,QAAQ,CAAC,EACzBI,SAAS,IAAIA,SAAS,CAACJ,QAAQ,CAAC,IAAII,SAAS,CAACJ,QAAQ,CAAC;KAE9D,CAAC;AACJ;AAEA,SAAgBK,0BAA0BA,CAACL,QAAwB;IACjE,OAAOA,QAAQ,CAACA,QAAQ;AAC1B;AAEA,SAAgBM,8BAA8BA,CAACN,QAAwB;IACrE,OAAOA,QAAQ,CAACD,IAAI;AACtB;AAWA,SAAgBQ,qBAAqBA,CACnCC,oBAAAA,EACAJ,SAAAA;;QADAI,yBAAAA,KAAAA,GAAAA;QAAAA,uBAA2C,EAAE;;IAAA,IAC7CJ,cAAAA,KAAAA,GAAAA;QAAAA,YAAqC,CAAA,CAAE;;IAEvC,IAAMK,KAAK,GAAG,CAAA,CAAwC;IAEtD,IAAIL,SAAS,CAACM,cAAc,KAAK7B,cAAc,CAAC8B,MAAM,EAAE;QACtDF,KAAK,CAACxB,UAAU,CAACG,SAAS,CAAC,GAAGU,eAAe;;IAG/C,IAAMc,IAAI,GAAGT,oBAAoB,CAACM,KAAK,CAAC;IACxC,IAAI,CAAA,CAAA,CAAAI,qBAAA,GAACL,oBAAoB,KAAA,QAApBK,qBAAA,CAAsBlC,MAAM,GAAE;QACjC,OAAOiC,IAAI;;IAGb,OAAOJ,oBAAoB,CAACvF,GAAG,CAAC,SAAA+E,QAAQ;QACtC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;YAChC,OAAOc,uBAAuB,CAACd,QAAQ,EAAES,KAAK,CAACT,QAAQ,CAAC,CAAC;;QAG3D,OAAAlE,QAAA,CAAA,CAAA,GACKgF,uBAAuB,CAACd,QAAQ,CAACA,QAAQ,EAAES,KAAK,CAACT,QAAQ,CAACA,QAAQ,CAAC,CAAC,EACpEA,QAAQ;KAEd,CAAC;AACJ;AAEA,SAASc,uBAAuBA,CAC9Bd,QAAoB,EACpBe,QAAAA;QAAAA,aAAAA,KAAAA,GAAAA;QAAAA,WAA2B,CAAA,CAAoB;;IAE/C,OAAOC,MAAM,CAACC,MAAM,CAAChB,gBAAgB,CAACD,QAAQ,CAAC,EAAEe,QAAQ,CAAC;AAC5D;AChIA,IAAMG,aAAa,GACjB,mEAAmE;AACrE,IAAMC,gBAAgB,GACpB,yEAAyE;AAC3E,IAAMC,eAAe,GACnB,uEAAuE;AACzE,IAAMC,cAAc,GAClB,qEAAqE;AAEvE,SAAgBC,MAAMA,CAACnD,UAAsB;IAC3C,OAAQA,UAAU;QAChB,KAAKW,UAAU,CAACyC,OAAO;YACrB,OAAOH,eAAe;QACxB,KAAKtC,UAAU,CAAC0C,MAAM;YACpB,OAAOH,cAAc;QACvB,KAAKvC,UAAU,CAAC2C,QAAQ;YACtB,OAAON,gBAAgB;QACzB,KAAKrC,UAAU,CAAC4C,KAAK;QACrB;YACE,OAAOR,aAAa;;AAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA,IAAMS,kBAAkB,GAAG;IACzB3C,SAAS,CAAC4C,OAAO;IACjB5C,SAAS,CAAC6C,KAAK;IACf7C,SAAS,CAAC8C,YAAY;IACtB9C,SAAS,CAAC+C,MAAM;IAChB/C,SAAS,CAACgD,WAAW;IACrBhD,SAAS,CAACiD,IAAI;CACf;AAEM,IAAMC,cAAc,GAAA,WAAA,GAAGlB,MAAM,CAACmB,OAAO,CAACnD,SAAS,CAAC,CAACoD,MAAM,CAC5D,SAACC,GAAG,EAAAC,IAAA;QAAGvF,GAAG,GAAAuF,IAAA,CAAA,EAAA,EAAEtF,KAAK,GAAAsF,IAAA,CAAA,EAAA;IACfD,GAAG,CAACrF,KAAK,CAAC,GAAGD,GAAG;IAChB,OAAOsF,GAAG;AACZ,CAAC,EACD,CAAA,CAA4B,CAC7B;AAEM,IAAME,eAAe,GAAA,WAAA,GAGxBZ,kBAAkB,CAACS,MAAM,CAC3B,SAACI,MAAM,EAAEC,QAAQ;IAAA,IAAAC,cAAA;IAAA,OACf1B,MAAM,CAACC,MAAM,CAACuB,MAAM,EAAA,CAAAE,cAAA,GAAA,CAAA,GAAAA,cAAA,CACjBD,QAAQ,CAAA,GAAGA,QAAQ,EAAAC,cAAA,EACpB;AAAA,GACJ,CAAA,CAAE,CACH;AC1BD,IAAYC,eAMX;AAND,CAAA,SAAYA,eAAe;IACzBA,eAAAA,CAAAA,OAAAA,GAAAA,GAAU;IACVA,eAAAA,CAAAA,UAAAA,GAAAA,GAAa;IACbA,eAAAA,CAAAA,aAAAA,GAAAA,GAAgB;IAChBA,eAAAA,CAAAA,WAAAA,GAAAA,GAAc;IACdA,eAAAA,CAAAA,SAAAA,GAAAA,QAAiB;AACnB,CAAC,EANWA,eAAe,IAAA,CAAfA,eAAe,GAAA,CAAA,CAAA;ACCpB,IAAMC,sBAAsB,GAAc,CAAA,CAAE;AAEnDC,UAAU,CAAC;IACTC,SAAS,CAACV,MAAM,CAAC,SAACW,WAAW,EAAEC,KAAK;QAClCC,UAAU,CAACD,KAAK,CAAC;QACjB,OAAOD,WAAW;KACnB,EAAEH,sBAAmC,CAAC;AACzC,CAAC,CAAC;AAIF,SAAgBK,UAAUA,CAACD,KAAgB;IACzC,IAAME,gBAAgB,GAAGC,UAAU,CAACH,KAAK,CAAC,CACvCI,IAAI,EAAE,CACNjI,IAAI,CAAC,EAAE,CAAC,CACRkI,WAAW,EAAE,CACbC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAC3BC,KAAK,CAAC,EAAE,CAAC;IAEZL,gBAAgB,CAACM,OAAO,CAAC,SAAAC,KAAI;;QAC3Bb,sBAAsB,CAACa,KAAI,CAAC,GAAA,CAAAC,qBAAA,GAAGd,sBAAsB,CAACa,KAAI,CAAC,KAAA,OAAAC,qBAAA,GAAI,CAAA,CAAE;QAEjEd,sBAAsB,CAACa,KAAI,CAAC,CAACE,YAAY,CAACX,KAAK,CAAC,CAAC,GAAGA,KAAK;KAC1D,CAAC;AACJ;SCfgBG,UAAUA,CAACH,KAAe;;IACxC,OAAA,CAAAY,qBAAA,GAAOZ,KAAK,CAACL,eAAe,CAAC5C,IAAI,CAAC,KAAA,OAAA6D,qBAAA,GAAI,EAAE;AAC1C;AAEA,SAAgBC,OAAOA,CAACb,KAAgB;IACtC,OAAOc,UAAU,CAACd,KAAK,CAACL,eAAe,CAACoB,QAAQ,CAAC,CAAC;AACpD;AAEA,SAAgBC,SAASA,CAAChB,KAAgB;IACxC,IAAI,CAACA,KAAK,EAAE;QACV,OAAO,EAAE;;IAGX,OAAOG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B;AAEA,SAAgBiB,sBAAsBA,CAACC,OAAe;IACpD,IAAMC,KAAK,GAAGD,OAAO,CAACX,KAAK,CAAC,GAAG,CAAC;IAChC,IAAAa,aAAA,GAAmBD,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAA9B5B,QAAQ,GAAA2B,aAAA,CAAA,EAAA;IAEf,IAAI7B,eAAe,CAACE,QAAQ,CAAC,EAAE;QAC7B,OAAO0B,KAAK,CAAChJ,IAAI,CAAC,GAAG,CAAC;;IAGxB,OAAO+I,OAAO;AAChB;AAEA,SAAgBP,YAAYA,CAACX,KAAgB,EAAEP,QAAiB;;IAC9D,IAAMyB,OAAO,GAAGlB,KAAK,CAACL,eAAe,CAACuB,OAAO,CAAC;IAE9C,IAAI,CAACzB,QAAQ,IAAI,CAAC6B,kBAAkB,CAACtB,KAAK,CAAC,EAAE;QAC3C,OAAOkB,OAAO;;IAGhB,OAAA,CAAAK,qBAAA,GAAOC,qBAAqB,CAACxB,KAAK,EAAEP,QAAQ,CAAC,KAAA,OAAA8B,qBAAA,GAAIL,OAAO;AAC1D;AAEA,SAAgBO,gBAAgBA,CAACzE,QAAoB;;;IAEnD,OAAA,CAAA0E,gBAAA,GAAOC,MAAM,IAAA,OAAA,KAAA,IAANA,MAAM,CAAG3E,QAAQ,CAAC,KAAA,OAAA0E,gBAAA,GAAI,EAAE;AACjC;AAEA,+BAAA;AACA,SAAgBE,iBAAiBA,CAC/BV,OAAe,EACf/F,UAAsB;IAEtB,OAAA,KAAUmD,MAAM,CAACnD,UAAU,CAAC,GAAG+F,OAAO,GAAA;AACxC;AAEA,SAAgBW,eAAeA,CAAC7B,KAAgB;;IAC9C,OAAA,CAAA8B,sBAAA,GAAO9B,KAAK,CAACL,eAAe,CAACoC,UAAU,CAAC,KAAA,OAAAD,sBAAA,GAAI,EAAE;AAChD;AAEA,SAAgBR,kBAAkBA,CAACtB,KAAgB;IACjD,OAAO6B,eAAe,CAAC7B,KAAK,CAAC,CAACrE,MAAM,GAAG,CAAC;AAC1C;AAEA,SAAgB6F,qBAAqBA,CACnCxB,KAAgB,EAChBP,QAAiB;IAEjB,OAAOA,QAAQ,GACXoC,eAAe,CAAC7B,KAAK,CAAC,CAACgC,IAAI,CAAC,SAAAC,SAAS;QAAA,OAAIA,SAAS,CAACC,QAAQ,CAACzC,QAAQ,CAAC;MAAC,GACtEkB,YAAY,CAACX,KAAK,CAAC;AACzB;AAEA,SAAgBmC,cAAcA,CAACjB,OAAgB;IAC7C,IAAI,CAACA,OAAO,EAAE;QACZ;;IAGF,IAAIkB,kBAAkB,CAAClB,OAAO,CAAC,EAAE;QAC/B,OAAOkB,kBAAkB,CAAClB,OAAO,CAAC;;IAGpC,IAAMmB,eAAe,GAAGpB,sBAAsB,CAACC,OAAO,CAAC;IACvD,OAAOkB,kBAAkB,CAACC,eAAe,CAAC;AAC5C;AAEO,IAAMvC,SAAS,GAAA,WAAA,GAAe9B,MAAM,CAACsE,MAAM,CAACX,MAAM,CAAC,CAACvB,IAAI,EAAE;AAEjE,SAAgBmC,eAAeA,CAAC/H,YAA2B;IACzDmH,MAAM,CAAC1F,UAAU,CAACI,MAAM,CAAC,CAACV,MAAM,GAAG,CAAC;IAEpCnB,YAAY,CAACgG,OAAO,CAAC,SAAAR,KAAK;QACxB,IAAMwC,SAAS,GAAGC,oBAAoB,CAACzC,KAAK,CAAC;QAE7C2B,MAAM,CAAC1F,UAAU,CAACI,MAAM,CAAC,CAACqG,IAAI,CAACF,SAAkB,CAAC;QAElD,IAAIJ,kBAAkB,CAACI,SAAS,CAAC7C,eAAe,CAACuB,OAAO,CAAC,CAAC,EAAE;YAC1D;;QAGFpB,SAAS,CAAC4C,IAAI,CAACF,SAAS,CAAC;QACzBJ,kBAAkB,CAACI,SAAS,CAAC7C,eAAe,CAACuB,OAAO,CAAC,CAAC,GAAGsB,SAAS;QAClEvC,UAAU,CAACuC,SAAS,CAAC;KACtB,CAAC;AACJ;AAEA,SAASC,oBAAoBA,CAACzC,KAAkB;;IAC9C,OAAAV,IAAA,GAAA,CAAA,GAAAA,IAAA,CACGK,eAAe,CAAC5C,IAAI,CAAA,GAAGiD,KAAK,CAAC2C,KAAK,CAAC1K,GAAG,CAAC,SAAA8E,IAAI;QAAA,OAAIA,IAAI,CAACsD,WAAW,EAAE;MAAC,EAAAf,IAAA,CAClEK,eAAe,CAACuB,OAAO,CAAA,GAAGlB,KAAK,CAAC4C,EAAE,CAACvC,WAAW,EAAE,EAAAf,IAAA,CAChDK,eAAe,CAACoB,QAAQ,CAAA,GAAG,GAAG,EAAAzB,IAAA,CAC9BK,eAAe,CAACkD,MAAM,CAAA,GAAG7C,KAAK,CAAC6C,MAAM,EAAAvD,IAAA;AAE1C;AAEA,IAAM8C,kBAAkB,GAEpB,CAAA,CAAE;AAENvC,UAAU,CAAC;IACTC,SAAS,CAACV,MAAM,CAAC,SAACU,SAAS,EAAEgD,KAAK;QAChChD,SAAS,CAACa,YAAY,CAACmC,KAAK,CAAC,CAAC,GAAGA,KAAK;QAEtC,IAAIxB,kBAAkB,CAACwB,KAAK,CAAC,EAAE;YAC7BjB,eAAe,CAACiB,KAAK,CAAC,CAACtC,OAAO,CAAC,SAAAyB,SAAS;gBACtCnC,SAAS,CAACmC,SAAS,CAAC,GAAGa,KAAK;aAC7B,CAAC;;QAGJ,OAAOhD,SAAS;KACjB,EAAEsC,kBAAkB,CAAC;AACxB,CAAC,CAAC;AAEF,SAAgBW,0BAA0BA,CAAC7B,OAAe;IACxD,IAAA8B,cAAA,GAA8B9B,OAAO,CAACX,KAAK,CAAC,GAAG,CAAwB,EAA9D0C,iBAAiB,GAAAD,cAAA,CAAA,EAAA;IAC1B,OAAOrE,kBAAkB,CAACuD,QAAQ,CAACe,iBAAiB,CAAC,GACjDA,iBAAiB,GACjB,IAAI;AACV;ACxHA,IAAMC,oBAAoB,GAAG;IAAC,WAAW;IAAE,WAAW;IAAE,WAAW;CAAC;AAE7D,IAAMC,0BAA0B,GAAG,QAAQ;AAC3C,IAAMC,+BAA+B,GAAG,kBAAkB;AAC1D,IAAMC,qBAAqB,GAChC,iDAAiD;AAC5C,IAAMC,+BAA+B,GAC1C,UAAU,GAAGD,qBAAqB;AAC7B,IAAME,qCAAqC,GAChD,YAAY,GAAGF,qBAAqB;AAEtC,SAAgBG,WAAWA,CACzBC,UAAAA;;QAAAA,eAAAA,KAAAA,GAAAA;QAAAA,aAA2B,CAAA,CAAE;;IAE7B,IAAM7F,IAAI,GAAG8F,gBAAgB,EAAE;IAE/B,IAAMC,aAAa,GAAG3F,MAAM,CAACC,MAAM,CACjCL,IAAI,CAAC+F,aAAa,EAAA,CAAAC,qBAAA,GAClBH,UAAU,CAACE,aAAa,KAAA,OAAAC,qBAAA,GAAI,CAAA,CAAE,CAC/B;IACD,IAAMC,MAAM,GAAG7F,MAAM,CAACC,MAAM,CAACL,IAAI,EAAE6F,UAAU,CAAC;IAE9C,IAAMK,UAAU,GAAGvG,qBAAqB,CAACkG,UAAU,CAACK,UAAU,EAAE;QAC9DpG,cAAc,EAAEmG,MAAM,CAACxI,mBAAAA;KACxB,CAAC;IAEFwI,MAAM,CAACE,YAAY,CAACvD,OAAO,CAAC,SAAAR,KAAK;QAC/B6D,MAAM,CAACG,aAAa,CAACC,GAAG,CAACjE,KAAK,CAAC;KAChC,CAAC;IAEFuC,eAAe,CAAA,CAAA2B,oBAAA,GAACL,MAAM,CAACrJ,YAAY,KAAA,OAAA0J,oBAAA,GAAI,EAAE,CAAC;IAE1C,IAAMxI,sBAAsB,GAAGmI,MAAM,CAACpI,cAAc,GAChDS,sBAAsB,CAACiI,OAAO,GAC9BN,MAAM,CAACnI,sBAAsB;IAEjC,OAAA5C,QAAA,CAAA,CAAA,GACK+K,MAAM,EAAA;QACTC,UAAU,EAAVA,UAAU;QACVH,aAAa,EAAbA,aAAa;QACbjI,sBAAsB,EAAtBA;;AAEJ;AAEA,SAAgBgI,gBAAgBA;IAC9B,OAAO;QACLxI,eAAe,EAAE,IAAI;QACrB4I,UAAU,EAAE3G,oBAAoB,EAAE;QAClC5B,SAAS,EAAE,EAAE;QACbf,YAAY,EAAE,EAAE;QAChBQ,eAAe,EAAEgB,SAAS,CAAC4C,OAAO;QAClCzD,UAAU,EAAEW,UAAU,CAAC4C,KAAK;QAC5B9D,YAAY,EAAE,IAAI;QAClBwJ,WAAW,EAAExC,iBAAiB;QAC9BhI,MAAM,EAAE,GAAG;QACX0B,cAAc,EAAE,KAAK;QACrBqI,aAAa,EAAA7K,QAAA,CAAA,CAAA,GACRuL,iBAAiB,CACrB;QACD5I,cAAc,EAAE,KAAK;QACrBX,iBAAiB,EAAEqI,0BAA0B;QAC7CpI,iBAAiB,EAAEoI,0BAA0B;QAC7CzH,sBAAsB,EAAEQ,sBAAsB,CAACoI,MAAM;QACrDrJ,iBAAiB,EAAE,KAAK;QACxBO,KAAK,EAAE,CAAA,CAAE;QACTH,mBAAmB,EAAEQ,cAAc,CAAC0I,QAAQ;QAC5CnJ,KAAK,EAAEW,KAAK,CAAC8C,KAAK;QAClBmF,aAAa,EAAE,IAAIQ,GAAG,CAAStB,oBAAoB,CAAC;QACpDrJ,KAAK,EAAE,GAAG;QACVgB,oBAAoB,EAAE,KAAK;QAC3B4J,SAAS,EAAE7I,iBAAiB;QAC5BjB,IAAI,EAAE,IAAI;QACV+J,oBAAoB,EAAE,IAAI;QAC1BX,YAAY,EAAE,EAAA;KACf;AACH;AAqCA,IAAMM,iBAAiB,GAAkB;IACvCM,YAAY,EAAE,OAAO;IACrBC,cAAc,EAAE,mBAAmB;IACnCC,WAAW,EAAE;CACd;;;;AC7HD,IAAMC,aAAa,GAAA,WAAA,4MAAG9L,iBAAAA,AAAmB,EAAA,WAAA,GACvC0K,gBAAgB,EAAE,CACnB;AAED,SAAgBqB,oBAAoBA,CAAAzF,IAAA;QAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ,EAAKnB,MAAM,GAAAoB,6BAAA,CAAA3F,IAAA,EAAA4F,SAAA;IACxD,IAAMC,YAAY,GAAGC,YAAY,CAACvB,MAAM,CAAC;IAEzC,OACE7K,0NAAAA,EAAC8L,aAAa,CAACO,QAAQ,EAAA;QAACrL,KAAK,EAAEmL;OAC5BH,QAAQ,CACc;AAE7B;AAEA,SAAgBI,YAAYA,CAACvB,MAAoB;;IAC/C,IAAAyB,eAAA,6MAAwCtM,WAAAA,AAAc,EAAC;QAAA,OACrDwK,WAAW,CAACK,MAAM,CAAC;MACpB,EAFMsB,YAAY,GAAAG,eAAA,CAAA,EAAA,EAAEC,eAAe,GAAAD,eAAA,CAAA,EAAA;6MAIpCtM,aAAAA,AAAe,EAAC;QACd,IAAImB,aAAa,CAACgL,YAAY,EAAEtB,MAAM,CAAC,EAAE;YACvC;;QAEF0B,eAAe,CAAC/B,WAAW,CAACK,MAAM,CAAC,CAAC;;;KAGrC,EAAE;QAAA,CAAAK,oBAAA,GACDL,MAAM,CAACrJ,YAAY,KAAA,OAAA,KAAA,IAAnB0J,oBAAA,CAAqBvI,MAAM;QAC3BkI,MAAM,CAAClJ,IAAI;QACXkJ,MAAM,CAACjJ,YAAY;QACnBiJ,MAAM,CAAChJ,oBAAoB;QAC3BgJ,MAAM,CAAC/I,iBAAiB;QACxB+I,MAAM,CAAC9I,iBAAiB;QACxB8I,MAAM,CAAC7I,eAAe;QACtB6I,MAAM,CAAC5I,iBAAiB;QACxB4I,MAAM,CAAC3I,eAAe;QACtB2I,MAAM,CAAC1I,UAAU;QACjB0I,MAAM,CAACzI,KAAK;QACZyI,MAAM,CAACxI,mBAAmB;QAC1BwI,MAAM,CAACvI,cAAc;QACrBuI,MAAM,CAACtI,SAAS;QAChBsI,MAAM,CAACjK,MAAM;QACbiK,MAAM,CAAChK,KAAK;QACZgK,MAAM,CAACpI,cAAc;QACrBoI,MAAM,CAACnI,sBAAsB;QAC7BmI,MAAM,CAACa,oBAAoB;KAC5B,CAAC;IAEF,OAAOS,YAAY;AACrB;AAEA,SAAgBK,eAAeA;IAC7B,OAAOxM,uNAAAA,AAAgB,EAAC8L,aAAa,CAAC;AACxC;AC1DO,IAAMW,oBAAoB,GAAA,WAAA,yMAAGzM,UAAK,CAAC0M,aAAa,CAErD,CAAA,CAA2C,CAAC;AAE9C,SAAgBC,gBAAgBA;IAC9B,IAAMC,aAAa,GAAG5M,gNAAK,CAAC6M,UAAU,CAACJ,oBAAoB,CAAC;IAC5D,OAAOG,aAAa;AACtB;AAEA,SAAgBE,sBAAsBA,CACpCjC,MAAqB;IAErB,IAAMkC,gBAAgB,yMAAG/M,UAAK,CAACgN,MAAM,CAAgB;QACnDC,YAAY,EAAEpC,MAAM,CAACoC,YAAY,IAAIC,SAAS;QAC9CC,eAAe,EAAEtC,MAAM,CAACsC,eAAe,IAAItC,MAAM,CAACoC,YAAY;QAC9DG,gBAAgB,EAAEvC,MAAM,CAACuC,gBAAgB,IAAIF;KAC9C,CAAC;0MAEFlN,UAAK,CAACqN,SAAS,CAAC;QACdN,gBAAgB,CAACO,OAAO,CAACL,YAAY,GAAGpC,MAAM,CAACoC,YAAY,IAAIC,SAAS;QACxEH,gBAAgB,CAACO,OAAO,CAACH,eAAe,GACtCtC,MAAM,CAACsC,eAAe,IAAItC,MAAM,CAACoC,YAAY;KAChD,EAAE;QAACpC,MAAM,CAACoC,YAAY;QAAEpC,MAAM,CAACsC,eAAe;KAAC,CAAC;IAEjDnN,gNAAK,CAACqN,SAAS,CAAC;QACdN,gBAAgB,CAACO,OAAO,CAACF,gBAAgB,GAAGvC,MAAM,CAACuC,gBAAgB,IAAIF,SAAS;KACjF,EAAE;QAACrC,MAAM,CAACuC,gBAAgB;KAAC,CAAC;IAE7B,OAAOL,gBAAgB;AACzB;AAEA,SAASG,SAASA,IAAAA;ACjBlB,IAAYK,kBAGX;AAHD,CAAA,SAAYA,kBAAkB;IAC5BA,kBAAAA,CAAAA,YAAAA,GAAAA,WAAuB;IACvBA,kBAAAA,CAAAA,SAAAA,GAAAA,QAAiB;AACnB,CAAC,EAHWA,kBAAkB,IAAA,CAAlBA,kBAAkB,GAAA,CAAA,CAAA;AAK9B,SAAgBC,0BAA0BA;;IACxC,IAAAC,gBAAA,GAAiDjB,eAAe,EAAE,EAA1D1K,iBAAiB,GAAA2L,gBAAA,CAAjB3L,iBAAiB,EAAEC,iBAAiB,GAAA0L,gBAAA,CAAjB1L,iBAAiB;IAC5C,OAAA,CAAA2L,KAAA,GACE;QAAC5L,iBAAiB;QAAEC,iBAAiB;KAAC,CAACiH,IAAI,CACzC,SAAA2E,CAAC;QAAA,OAAIA,CAAC,KAAKxD,0BAA0B;MACtC,KAAA,OAAAuD,KAAA,GAAIvD,0BAA0B;AAEnC;AAEA,SAAgByD,wBAAwBA;IACtC,IAAAC,iBAAA,GAA4BrB,eAAe,EAAE,EAArCxK,eAAe,GAAA6L,iBAAA,CAAf7L,eAAe;IACvB,OAAOA,eAAe;AACxB;AAEA,SAAgB8L,uBAAuBA;IACrC,IAAAC,iBAAA,GAAiCvB,eAAe,EAAE,EAA1Cd,oBAAoB,GAAAqC,iBAAA,CAApBrC,oBAAoB;IAC5B,OAAOA,oBAAoB;AAC7B;AAEA,SAAgBsC,0BAA0BA;IACxC,IAAAC,iBAAA,GAA8BzB,eAAe,EAAE,EAAvCvK,iBAAiB,GAAAgM,iBAAA,CAAjBhM,iBAAiB;IACzB,OAAOA,iBAAiB;AAC1B;AAEA,SAAgBiM,mBAAmBA;IACjC,IAAAC,iBAAA,GAAuB3B,eAAe,EAAE,EAAhCrK,UAAU,GAAAgM,iBAAA,CAAVhM,UAAU;IAClB,OAAOA,UAAU;AACnB;AAEA,SAAgBiM,wBAAwBA;IACtC,IAAAC,iBAAA,GAA4B7B,eAAe,EAAE,EAArCtK,eAAe,GAAAmM,iBAAA,CAAfnM,eAAe;IACvB,OAAOA,eAAe;AACxB;AAEA,SAAgBoM,mBAAmBA;IACjC,IAAAC,iBAAA,GAAuB/B,eAAe,EAAE,EAAhC1B,UAAU,GAAAyD,iBAAA,CAAVzD,UAAU;IAClB,OAAOA,UAAU;AACnB;AAEA,SAAgB0D,qBAAqBA;IACnC,IAAAC,iBAAA,GAAyBjC,eAAe,EAAE,EAAlChL,YAAY,GAAAiN,iBAAA,CAAZjN,YAAY;IACpB,OAAOA,YAAY;AACrB;AAEA,SAAgBkN,aAAaA;IAC3B,IAAAC,iBAAA,GAAiBnC,eAAe,EAAE,EAA1B7K,IAAI,GAAAgN,iBAAA,CAAJhN,IAAI;IACZ,OAAOA,IAAI;AACb;AAEA,SAAgBiN,qBAAqBA,CACnCC,gBAAoC;;IAEpC,IAAAC,iBAAA,GAAoBnC,gBAAgB,EAAE,EAA9BW,OAAO,GAAAwB,iBAAA,CAAPxB,OAAO;IAEf,IAAMyB,OAAO,GAAA,CAAAzI,IAAA,GACVuI,gBAAgB,KAAKtB,kBAAkB,CAACyB,SAAS,GAC9C1B,OAAO,CAACH,eAAe,GACvBG,OAAO,CAACL,YAAY,KAAA,OAAA3G,IAAA,GAAKgH,OAAO,CAACL,YAAY;IAEnD,OAAO8B,OAAO,IAAK,YAAA,CAAS;AAC9B;AAEA,SAAgBE,yBAAyBA;IAEvC,IAAAC,kBAAA,GAAoBvC,gBAAgB,EAAE,EAA9BW,OAAO,GAAA4B,kBAAA,CAAP5B,OAAO;IAEf,OAAOA,OAAO,CAACF,gBAAgB,IAAK,YAAA,CAAS;AAC/C;AAEA,SAAgB+B,gBAAgBA;IAC9B,IAAAC,kBAAA,GAA0B5C,eAAe,EAAE,EAAnC7B,aAAa,GAAAyE,kBAAA,CAAbzE,aAAa;IACrB,OAAOA,aAAa;AACtB;AAEA,SAAgB0E,cAAcA;IAC5B,IAAAC,kBAAA,GAAkB9C,eAAe,EAAE,EAA3BpK,KAAK,GAAAkN,kBAAA,CAALlN,KAAK;IAEb,OAAOA,KAAK;AACd;AAEA,SAAgBmN,4BAA4BA;IAC1C,IAAAC,kBAAA,GAAgChD,eAAe,EAAE,EAAzCnK,mBAAmB,GAAAmN,kBAAA,CAAnBnN,mBAAmB;IAC3B,OAAOA,mBAAmB;AAC5B;AAEA,SAAgBoN,uBAAuBA;IACrC,IAAAC,kBAAA,GAA2BlD,eAAe,EAAE,EAApClK,cAAc,GAAAoN,kBAAA,CAAdpN,cAAc;IACtB,OAAOA,cAAc;AACvB;AAEA,SAAgBqN,kBAAkBA;IAChC,IAAAC,kBAAA,GAAsBpD,eAAe,EAAE,EAA/BjK,SAAS,GAAAqN,kBAAA,CAATrN,SAAS;IACjB,OAAOA,SAAS;AAClB;AAEA,SAAgBsN,cAAcA;IAC5B,IAAAC,kBAAA,GAAiCtD,eAAe,EAAE,EAA1C5L,MAAM,GAAAkP,kBAAA,CAANlP,MAAM,EAAEC,KAAK,GAAAiP,kBAAA,CAALjP,KAAK,EAAE2B,KAAK,GAAAsN,kBAAA,CAALtN,KAAK;IAC5B,OAAA1C,QAAA,CAAA;QAASc,MAAM,EAAEmP,YAAY,CAACnP,MAAM,CAAC;QAAEC,KAAK,EAAEkP,YAAY,CAAClP,KAAK;OAAM2B,KAAK;AAC7E;AAEA,SAAgBwN,sBAAsBA;IACpC,IAAAC,kBAAA,GAAiCzD,eAAe,EAAE,EAA1C3K,oBAAoB,GAAAoO,kBAAA,CAApBpO,oBAAoB;IAC5B,OAAOA,oBAAoB;AAC7B;AAEA,SAAgBqO,qBAAqBA;IACnC,IAAAC,kBAAA,GAAyB3D,eAAe,EAAE,EAAlC5K,YAAY,GAAAuO,kBAAA,CAAZvO,YAAY;IACpB,OAAOA,YAAY;AACrB;AAEA,SAAgBwO,uBAAuBA;IACrC,IAAAC,kBAAA,GAA2B7D,eAAe,EAAE,EAApC/J,cAAc,GAAA4N,kBAAA,CAAd5N,cAAc;IACtB,OAAOA,cAAc;AACvB;AAEA,SAAgB6N,+BAA+BA;IAC7C,IAAAC,kBAAA,GAAmC/D,eAAe,EAAE,EAA5C9J,sBAAsB,GAAA6N,kBAAA,CAAtB7N,sBAAsB;IAC9B,OAAOA,sBAAsB;AAC/B;AAEA,SAAgB8N,gBAAgBA;IAC9B,IAAAC,kBAAA,GAA0BjE,eAAe,EAAE,EAAnCxB,aAAa,GAAAyF,kBAAA,CAAbzF,aAAa;IACrB,OAAOA,aAAa;AACtB;AAEA,SAAgB0F,kBAAkBA;IAChC,IAAAC,kBAAA,GAAsBnE,eAAe,EAAE,EAA/Bf,SAAS,GAAAkF,kBAAA,CAATlF,SAAS;IACjB,OAAOA,SAAS;AAClB;AAEA,SAAgBmF,oBAAoBA;IAIlC,IAAAC,kBAAA,GAAwBrE,eAAe,EAAE,EAAjCpB,WAAW,GAAAyF,kBAAA,CAAXzF,WAAW;IACnB,OAAOA,WAAW;AACpB;AAEA,SAAS2E,YAAYA,CAACe,eAAiC;IACrD,OAAO,OAAOA,eAAe,KAAK,QAAQ,GACnCA,eAAe,GAAA,OAClBA,eAAe;AACrB;AAEA,SAAgBC,sBAAsBA,CAACC,kBAA0B;IAC/D,IAAMC,UAAU,GAAGD,kBAAkB,GAAG,CAAC;IACzC,IAAME,QAAQ,GAAGF,kBAAkB,GAAG,CAAC;IAEvC,IAAIC,UAAU,EAAE;QACd,OAAOC,QAAQ,GACX3G,qCAAqC,CAACjD,OAAO,CAC3C,IAAI,EACJ0J,kBAAkB,CAACG,QAAQ,EAAE,CAC9B,GACD7G,+BAA+B;;IAGrC,OAAOF,+BAA+B;AACxC;SCzLgBgH,iBAAiBA,CAC/BC,YAAe,EACfC,KAAAA;QAAAA,UAAAA,KAAAA,GAAAA;QAAAA,QAAgB,CAAC;;IAEjB,IAAAC,SAAA,6MAA0BC,WAAAA,AAAQ,EAAIH,YAAY,CAAC,EAA5CI,KAAK,GAAAF,SAAA,CAAA,EAAA,EAAEG,QAAQ,GAAAH,SAAA,CAAA,EAAA;IACtB,IAAMI,KAAK,GAAG3E,mNAAAA,AAAM,EAAgB,IAAI,CAAC;IAEzC,SAAS4E,iBAAiBA,CAAC5Q,KAAQ;QACjC,OAAO,IAAI6Q,OAAO,CAAI,SAAAC,OAAO;;YAC3B,IAAIH,KAAK,CAACrE,OAAO,EAAE;gBACjByE,YAAY,CAACJ,KAAK,CAACrE,OAAO,CAAC;;YAG7BqE,KAAK,CAACrE,OAAO,GAAA,CAAA0E,OAAA,GAAGC,MAAM,KAAA,OAAA,KAAA,IAAND,OAAA,CAAQnL,UAAU,CAAC;gBACjC6K,QAAQ,CAAC1Q,KAAK,CAAC;gBACf8Q,OAAO,CAAC9Q,KAAK,CAAC;aACf,EAAEsQ,KAAK,CAAC;SACV,CAAC;;IAGJ,OAAO;QAACG,KAAK;QAAEG,iBAAiB;KAAC;AACnC;SCrBgBM,kBAAkBA;IAC9B,IAAMlH,aAAa,GAAGwF,gBAAgB,EAAE;IACxC,OAAO,SAAC7I,YAAoB;QAAA,OAAKqD,aAAa,CAACmH,GAAG,CAACxK,YAAY,CAAC;;AAClE;SCQcyK,mBAAmBA;IACjC,IAAMC,mBAAmB,4MAAGrF,UAAAA,AAAM,EAA0B,CAAA,CAAE,CAAC;IAC/D,IAAMsF,kBAAkB,GAAGpC,qBAAqB,EAAE;IAElD,iNAAOqC,UAAAA,AAAO,EAAC;QACb,IAAM3Q,YAAY,GAAGkG,UAAU,CAAA,KAAIwK,kBAAoB,CAAC;QAExD,IAAI,CAACA,kBAAkB,IAAIE,MAAM,CAACC,KAAK,CAAC7Q,YAAY,CAAC,EAAE;YACrD,OAAOyQ,mBAAmB,CAAC/E,OAAO;;QAGpC,OAAOxG,SAAS,CAACV,MAAM,CAAC,SAACsM,gBAAgB,EAAE1L,KAAK;YAC9C,IAAI2L,mBAAmB,CAAC3L,KAAK,EAAEpF,YAAY,CAAC,EAAE;gBAC5C8Q,gBAAgB,CAAC/K,YAAY,CAACX,KAAK,CAAC,CAAC,GAAG,IAAI;;YAG9C,OAAO0L,gBAAgB;SACxB,EAAEL,mBAAmB,CAAC/E,OAAO,CAAC;KAChC,EAAE;QAACgF,kBAAkB;KAAC,CAAC;AAC1B;AAEA,SAAgBM,oBAAoBA;IAClC,IAAMF,gBAAgB,GAAGN,mBAAmB,EAAE;IAC9C,IAAMS,eAAe,GAAGX,kBAAkB,EAAE;IAE5C,OAAO,SAASY,iBAAiBA,CAAC9L,KAAgB;QAChD,IAAMkB,OAAO,GAAGD,sBAAsB,CAACN,YAAY,CAACX,KAAK,CAAC,CAAC;QAE3D,OAAO+L,OAAO,CAACL,gBAAgB,CAACxK,OAAO,CAAC,IAAI2K,eAAe,CAAC3K,OAAO,CAAC,CAAC;KACtE;AACH;AAEA,SAASyK,mBAAmBA,CAC1B3L,KAAgB,EAChBgM,cAAsB;IAEtB,OAAOnL,OAAO,CAACb,KAAK,CAAC,GAAGgM,cAAc;AACxC;SC/CgBC,kBAAkBA,CAChCC,QAAuD;8MAEvD7F,YAAAA,AAAS,EAAC;QACR6F,QAAQ,CAAC,IAAI,CAAC;KACf,EAAE;QAACA,QAAQ;KAAC,CAAC;AAChB;SCMgBC,qBAAqBA,CAAA7M,IAAA;QAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IAC9C,IAAM0G,gBAAgB,GAAGN,mBAAmB,EAAE;IAC9C,IAAMpQ,eAAe,GAAG4L,wBAAwB,EAAE;IAClD,IAAM/L,oBAAoB,GAAGmO,sBAAsB,EAAE;;IAGrD,IAAMoD,SAAS,6MAAGpT,SAAAA,AAAY,EAAc4G,sBAAsB,CAAC;IACnE,IAAMyM,gBAAgB,6MAAGrT,SAAAA,AAAY,EAAU,KAAK,CAAC;IACrD,IAAMsT,gBAAgB,6MAAGtT,SAAAA,AAAY,EAAU,KAAK,CAAC;IACrD,IAAMuT,mBAAmB,6MAAGvT,SAAAA,AAAY,EACtC0S,gBAAgB,CACjB;IAED,IAAMc,oBAAoB,GAAGpC,iBAAiB,CAACqC,IAAI,CAACC,GAAG,EAAE,EAAE,GAAG,CAAC;IAC/D,IAAMC,UAAU,GAAGvC,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC;IAC7C,IAAMwC,oBAAoB,6MAAGpC,WAAAA,AAAQ,EAAU,KAAK,CAAC;IACrD,IAAMqC,cAAc,6MAAGrC,WAAAA,AAAQ,EAAYxP,eAAe,CAAC;IAC3D,IAAM8R,mBAAmB,4MAAGtC,YAAAA,AAAQ,EAAsB,IAAI,CAAC;IAC/D,IAAMuC,2BAA2B,6MAAGvC,WAAAA,AAAQ,EAAc,IAAIhG,GAAG,EAAE,CAAC;IACpE,IAAMwI,yBAAyB,6MAAGxC,WAAAA,AAAQ,EAAmB,IAAI,CAAC;IAClE,IAAMyC,kBAAkB,GAAGzC,qNAAAA,AAAQ,EAAC3P,oBAAoB,CAAC;IACzD,IAAA0P,SAAA,6MAAkDC,WAAAA,AAAQ,EAAC,KAAK,CAAC,EAA1D0C,iBAAiB,GAAA3C,SAAA,CAAA,EAAA,EAAE4C,oBAAoB,GAAA5C,SAAA,CAAA,EAAA;IAE9C0B,kBAAkB,CAACkB,oBAAoB,CAAC;IAExC,iNACEnU,gBAAAA,EAACoU,aAAa,CAAC/H,QAAQ,EAAA;QACrBrL,KAAK,EAAE;YACL8S,mBAAmB,EAAnBA,mBAAmB;YACnBD,cAAc,EAAdA,cAAc;YACdR,gBAAgB,EAAhBA,gBAAgB;YAChBC,gBAAgB,EAAhBA,gBAAgB;YAChBC,mBAAmB,EAAnBA,mBAAmB;YACnBS,yBAAyB,EAAzBA,yBAAyB;YACzBD,2BAA2B,EAA3BA,2BAA2B;YAC3BX,SAAS,EAATA,SAAS;YACTc,iBAAiB,EAAjBA,iBAAiB;YACjBP,UAAU,EAAVA,UAAU;YACVC,oBAAoB,EAApBA,oBAAoB;YACpBJ,oBAAoB,EAApBA,oBAAoB;YACpBS,kBAAkB,EAAlBA;;OAGDjI,QAAQ,CACc;AAE7B;AAIA,IAAMoI,aAAa,GAAA,WAAA,6MAAGpU,gBAAAA,AAAmB,EActC;IACD8T,mBAAmB,EAAE;QAAC,IAAI;QAAE,YAAA,CAAQ;KAAC;IACrCD,cAAc,EAAE;QAAC7Q,SAAS,CAAC4C,OAAO;QAAE,YAAA,CAAQ;KAAC;IAC7CyN,gBAAgB,EAAE;QAAE/F,OAAO,EAAE;KAAO;IACpCgG,gBAAgB,EAAE;QAAEhG,OAAO,EAAE;KAAO;IACpCiG,mBAAmB,EAAE;QAAEjG,OAAO,EAAE,CAAA;KAAI;IACpC0G,yBAAyB,EAAE;QAAC,IAAI;QAAE,YAAA,CAAQ;KAAC;IAC3CD,2BAA2B,EAAE;QAAA,WAAA,GAAC,IAAIvI,GAAG,EAAE;QAAE,YAAA,CAAQ;KAAC;IAClD4H,SAAS,EAAE;QAAE9F,OAAO,EAAE,CAAA;KAAI;IAC1B4G,iBAAiB,EAAE,IAAI;IACvBP,UAAU,EAAE;QAAC,EAAE;QAAE;YAAA,OAAM,IAAI9B,OAAO,CAAS;gBAAA,OAAMwC,SAAS;cAAC;;KAAC;IAC5DT,oBAAoB,EAAE;QAAC,KAAK;QAAE,YAAA,CAAQ;KAAC;IACvCJ,oBAAoB,EAAE;QAAA,WAAA,GAACC,IAAI,CAACC,GAAG,EAAE;QAAE,YAAA,CAAQ;KAAC;IAC5CO,kBAAkB,EAAE;QAAC,KAAK;QAAE,YAAA,CAAQ;KAAA;CACrC,CAAC;AAMF,SAAgBK,YAAYA;IAC1B,IAAAC,iBAAA,6MAAsBvU,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAA7ChB,SAAS,GAAAmB,iBAAA,CAATnB,SAAS;IACjB,OAAOA,SAAS;AAClB;AAEA,SAAgBoB,mBAAmBA;IACjC,IAAAC,kBAAA,6MAA6BzU,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAApDf,gBAAgB,GAAAoB,kBAAA,CAAhBpB,gBAAgB;IACxB,OAAOA,gBAAgB;AACzB;AAEA,SAAgBqB,mBAAmBA;IACjC,IAAAC,kBAAA,OAA6B3U,mNAAAA,AAAgB,EAACoU,aAAa,CAAC,EAApDd,gBAAgB,GAAAqB,kBAAA,CAAhBrB,gBAAgB;IACxB,OAAOA,gBAAgB;AACzB;AAEA,SAAgBsB,qBAAqBA;IACnC,IAAAC,kBAAA,6MAA+B7U,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAAtDH,kBAAkB,GAAAY,kBAAA,CAAlBZ,kBAAkB;IAC1B,OAAOA,kBAAkB;AAC3B;AAEA,SAAgBa,kBAAkBA;IAChC,IAAAC,kBAAA,6MAAuB/U,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAA9CT,UAAU,GAAAoB,kBAAA,CAAVpB,UAAU;IAClB,OAAOA,UAAU;AACnB;AAEA,SAAgBqB,sBAAsBA;IAIpC,IAAAC,kBAAA,6MAA2BjV,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAAlDP,cAAc,GAAAoB,kBAAA,CAAdpB,cAAc;IACtB,OAAOA,cAAc;AACvB;AAEA,SAAgBqB,8BAA8BA;IAC5C,IAAAC,kBAAA,4MAAwCnV,cAAAA,AAAgB,EAACoU,aAAa,CAAC,EAA/DL,2BAA2B,GAAAoB,kBAAA,CAA3BpB,2BAA2B;IACnC,OAAOA,2BAA2B;AACpC;AAEA,SAAgBqB,oBAAoBA;IAClC,IAAAC,kBAAA,6MAA8BrV,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAArDF,iBAAiB,GAAAmB,kBAAA,CAAjBnB,iBAAiB;IACzB,OAAOA,iBAAiB;AAC1B;AAEA,SAAgBoB,4BAA4BA;IAC1C,IAAAC,kBAAA,6MAAsCvV,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAA7DJ,yBAAyB,GAAAuB,kBAAA,CAAzBvB,yBAAyB;IACjC,OAAOA,yBAAyB;AAClC;AAEA,SAAgBwB,uBAAuBA;IACrC,IAAAC,mBAAA,4MAAiCzV,cAAAA,AAAgB,EAACoU,aAAa,CAAC,EAAxDR,oBAAoB,GAAA6B,mBAAA,CAApB7B,oBAAoB;IAC5B,OAAOA,oBAAoB;AAC7B;AAEA,SAKgB8B,kBAAkBA;IAChC,IAAAC,mBAAA,6MAAiC3V,aAAAA,AAAgB,EAACoU,aAAa,CAAC,EAAxDZ,oBAAoB,GAAAmC,mBAAA,CAApBnC,oBAAoB;IAE5B,IAAOoC,gBAAgB,GAAwBpC,oBAAoB,CAAA,EAAA,EAA1CqC,kBAAkB,GAAIrC,oBAAoB,CAAA,EAAA;IACnE,OAAO;QACLoC,gBAAgB;QAChB,SAASE,eAAeA;YACtBD,kBAAkB,CAACpC,IAAI,CAACC,GAAG,EAAE,CAAC;SAC/B;KACF;AACH;SCrKwBqC,eAAeA;IACrC,IAAAC,mBAAA,GAAqBlB,kBAAkB,EAAE,EAAlCnB,UAAU,GAAAqC,mBAAA,CAAA,EAAA;IAEjB,OAAO,CAAC,CAACrC,UAAU;AACrB;SCJgBsC,YAAYA,CAACC,OAAwB;IACnD,IAAI,CAACA,OAAO,EAAE;QACZ;;IAGFC,qBAAqB,CAAC;QACpBD,OAAO,CAACE,KAAK,EAAE;KAChB,CAAC;AACJ;AAEA,SAAgBC,uBAAuBA,CAACH,OAAwB;IAC9D,IAAI,CAACA,OAAO,EAAE;IAEd,IAAM9U,IAAI,GAAG8U,OAAO,CAACI,sBAAqC;IAE1DL,YAAY,CAAC7U,IAAI,CAAC;AACpB;AAEA,SAAgBmV,uBAAuBA,CAACL,OAAwB;IAC9D,IAAI,CAACA,OAAO,EAAE;IAEd,IAAM7U,IAAI,GAAG6U,OAAO,CAACM,kBAAiC;IAEtDP,YAAY,CAAC5U,IAAI,CAAC;AACpB;AAEA,SAAgBoV,sBAAsBA,CAACP,OAAwB;IAC7D,IAAI,CAACA,OAAO,EAAE;IAEd,IAAMQ,KAAK,GAAGR,OAAO,CAACS,iBAAgC;IAEtDV,YAAY,CAACS,KAAK,CAAC;AACrB;SChCgBE,gBAAgBA;IAC9B,OAAOC,QAAQ,CAACC,aAAgC;AAClD;SCCgBC,yBAAyBA,CAAAzQ,IAAA;QACvC0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IAIR,IAAMgL,aAAa,6MAAGhX,SAAAA,AAAY,EAAc,IAAI,CAAC;IACrD,IAAMiX,gBAAgB,6MAAGjX,SAAAA,AAAY,EAAc,IAAI,CAAC;IACxD,IAAMkX,OAAO,6MAAGlX,SAAAA,AAAY,EAAiB,IAAI,CAAC;IAClD,IAAMmX,cAAc,6MAAGnX,SAAAA,AAAY,EAAmB,IAAI,CAAC;IAC3D,IAAMoX,iBAAiB,GAAGpX,mNAAAA,AAAY,EAAiB,IAAI,CAAC;IAC5D,IAAMqX,qBAAqB,6MAAGrX,SAAAA,AAAY,EAAiB,IAAI,CAAC;IAChE,IAAMsX,kBAAkB,4MAAGtX,UAAAA,AAAY,EAAiB,IAAI,CAAC;IAC7D,IAAMuX,YAAY,6MAAGvX,SAAAA,AAAY,EAAmB,IAAI,CAAC;IAEzD,iNACEA,gBAAAA,EAACwX,iBAAiB,CAACnL,QAAQ,EAAA;QACzBrL,KAAK,EAAE;YACLiW,gBAAgB,EAAhBA,gBAAgB;YAChBC,OAAO,EAAPA,OAAO;YACPG,qBAAqB,EAArBA,qBAAqB;YACrBL,aAAa,EAAbA,aAAa;YACbG,cAAc,EAAdA,cAAc;YACdC,iBAAiB,EAAjBA,iBAAiB;YACjBE,kBAAkB,EAAlBA,kBAAkB;YAClBC,YAAY,EAAZA;;OAGDvL,QAAQ,CACkB;AAEjC;AAiBA,IAAMwL,iBAAiB,GAAA,WAAA,6MAAGxX,gBAAAA,AAAmB,EAAc;IACzDiX,gBAAgB,EAAA,WAAA,6MAAEjX,YAAAA,AAAe,EAAE;IACnCkX,OAAO,EAAA,WAAA,GAAElX,sNAAAA,AAAe,EAAE;IAC1BqX,qBAAqB,EAAA,WAAA,6MAAErX,YAAAA,AAAe,EAAE;IACxCgX,aAAa,EAAA,WAAA,6MAAEhX,YAAAA,AAAe,EAAE;IAChCmX,cAAc,EAAA,WAAA,IAAEnX,qNAAAA,AAAe,EAAE;IACjCoX,iBAAiB,EAAA,WAAA,6MAAEpX,YAAAA,AAAe,EAAE;IACpCsX,kBAAkB,EAAA,WAAA,6MAAEtX,YAAAA,AAAe,EAAE;IACrCuX,YAAY,EAAA,WAAA,6MAAEvX,YAAAA,AAAe;CAC9B,CAAC;AAEF,SAASyX,aAAaA;IACpB,iNAAOzX,aAAAA,AAAgB,EAACwX,iBAAiB,CAAC;AAC5C;AAEA,SAAgBE,gBAAgBA;IAC9B,OAAOD,aAAa,EAAE,CAAC,eAAe,CAAC;AACzC;AAEA,SAAgBE,mBAAmBA;IACjC,OAAOF,aAAa,EAAE,CAAC,kBAAkB,CAAC;AAC5C;AAEA,SAAgBG,sBAAsBA;IACpC,IAAMX,gBAAgB,GAAGU,mBAAmB,EAAE;IAC9C,OAAO,SAACE,MAAuB;QAC7B,IAAIA,MAAM,KAAK,IAAI,IAAIZ,gBAAgB,CAAC3J,OAAO,KAAK,IAAI,EAAE;YACxD2I,YAAY,CAACgB,gBAAgB,CAAC3J,OAAO,CAAC;;QAGxC2J,gBAAgB,CAAC3J,OAAO,GAAGuK,MAAM;KAClC;AACH;AAEA,SAAgBC,UAAUA;IACxB,OAAOL,aAAa,EAAE,CAAC,SAAS,CAAC;AACnC;AAEA,SAAgBM,eAAeA;IAC7B,OAAON,aAAa,EAAE,CAAC,cAAc,CAAC;AACxC;AAEA,SAAgBO,iBAAiBA;IAC/B,OAAOP,aAAa,EAAE,CAAC,gBAAgB,CAAC;AAC1C;AAEA,SAAgBQ,oBAAoBA;IAClC,OAAOR,aAAa,EAAE,CAAC,mBAAmB,CAAC;AAC7C;AAEA,SAAgBS,wBAAwBA;IACtC,OAAOT,aAAa,EAAE,CAAC,uBAAuB,CAAC;AACjD;AAEA,SAAgBU,qBAAqBA;IACnC,OAAOV,aAAa,EAAE,CAAC,oBAAoB,CAAC;AAC9C;SC7FgBW,QAAQA,CAACC,IAAqB,EAAEC,GAAAA;QAAAA,QAAAA,KAAAA,GAAAA;QAAAA,MAAc,CAAC;;IAC7D,IAAMC,QAAQ,GAAGC,eAAe,CAACH,IAAI,CAAC;IAEtC,IAAI,CAACE,QAAQ,EAAE;QACb;;IAGFpC,qBAAqB,CAAC;QACpBoC,QAAQ,CAACE,SAAS,GAAGH,GAAG;KACzB,CAAC;AACJ;AAEA,SAAgBI,QAAQA,CAACL,IAAqB,EAAEM,EAAU;IACxD,IAAMJ,QAAQ,GAAGC,eAAe,CAACH,IAAI,CAAC;IAEtC,IAAI,CAACE,QAAQ,EAAE;QACb;;IAGFpC,qBAAqB,CAAC;QACpBoC,QAAQ,CAACE,SAAS,GAAGF,QAAQ,CAACE,SAAS,GAAGE,EAAE;KAC7C,CAAC;AACJ;AAEA,SAAgBC,WAAWA;IACzB,IAAM1B,OAAO,GAAGY,UAAU,EAAE;IAE5B,gNAAOe,eAAAA,AAAW,EAChB,SAACP,GAAW;QACVnC,qBAAqB,CAAC;YACpB,IAAIe,OAAO,CAAC5J,OAAO,EAAE;gBACnB4J,OAAO,CAAC5J,OAAO,CAACmL,SAAS,GAAGH,GAAG;;SAElC,CAAC;KACH,EACD;QAACpB,OAAO;KAAC,CACV;AACH;AAEA,SAAgB4B,qBAAqBA,CAAC9R,KAAsB;IAC1D,IAAI,CAACA,KAAK,IAAI,CAAC+R,kBAAkB,CAAC/R,KAAK,CAAC,EAAE;QACxC;;IAGF,IAAIA,KAAK,CAACgS,OAAO,CAACra,WAAW,CAACD,UAAU,CAACua,eAAe,CAAC,CAAC,EAAE;QAC1D;;IAGF,IAAMC,UAAU,GAAGC,iBAAiB,CAACnS,KAAK,CAAC;IAC3C,IAAM2R,EAAE,GAAGS,0BAA0B,CAACpS,KAAK,CAAC;IAC5C0R,QAAQ,CAACQ,UAAU,EAAE,CAAA,CAAEG,mBAAmB,CAACC,eAAe,CAACtS,KAAK,CAAC,CAAC,GAAG2R,EAAE,CAAC,CAAC;AAC3E;SC1CgBY,sBAAsBA,CAACC,MAAuB;IAC5D,IAAMxS,KAAK,GAAGyS,iBAAiB,CAACD,MAAM,CAAC;IACvCvD,YAAY,CAACjP,KAAK,CAAC;IACnB8R,qBAAqB,CAAC9R,KAAK,CAAC;AAC9B;AAEA,SAAgB0S,8BAA8BA,CAACF,MAAuB;IACpE,IAAMG,UAAU,GAAGF,iBAAiB,CAACD,MAAM,CAAC;IAE5CvD,YAAY,CAAC0D,UAAU,CAAC;IACxBA,UAAU,IAAA,OAAA,KAAA,IAAVA,UAAU,CAAEC,KAAK,EAAE;AACrB;AAEA,SAAgBC,qBAAqBA,CAACL,MAAuB;IAC3DvD,YAAY,CAAC6D,gBAAgB,CAACN,MAAM,CAAC,CAAC;AACxC;AAEA,SAAgBO,qBAAqBA,CAAC7D,OAAwB;IAC5D,IAAI,CAACA,OAAO,EAAE;QACZ;;IAGF,IAAM7U,IAAI,GAAG2Y,gBAAgB,CAAC9D,OAAO,CAAC;IAEtC,IAAI,CAAC7U,IAAI,EAAE;QACT,OAAOkY,sBAAsB,CAACU,YAAY,CAAC/D,OAAO,CAAC,CAAC;;IAGtDD,YAAY,CAAC5U,IAAI,CAAC;IAClByX,qBAAqB,CAACzX,IAAI,CAAC;AAC7B;AAEA,SAAgB6Y,qBAAqBA,CAAChE,OAAwB;IAC5D,IAAI,CAACA,OAAO,EAAE;QACZ;;IAGF,IAAM9U,IAAI,GAAG+Y,gBAAgB,CAACjE,OAAO,CAAC;IAEtC,IAAI,CAAC9U,IAAI,EAAE;QACT,OAAOyY,qBAAqB,CAACO,YAAY,CAAClE,OAAO,CAAC,CAAC;;IAGrDD,YAAY,CAAC7U,IAAI,CAAC;IAClB0X,qBAAqB,CAAC1X,IAAI,CAAC;AAC7B;AAEA,SAAgBiZ,yBAAyBA,CACvCnE,OAAwB,EACxBoE,MAAkB;IAElB,IAAI,CAACpE,OAAO,EAAE;QACZ;;IAGF,IAAM9U,IAAI,GAAGmZ,oBAAoB,CAACrE,OAAO,CAAC;IAE1C,IAAI,CAAC9U,IAAI,EAAE;QACT,OAAOkZ,MAAM,EAAE;;IAGjBrE,YAAY,CAAC7U,IAAI,CAAC;IAClB0X,qBAAqB,CAAC1X,IAAI,CAAC;AAC7B;AAEA,SAAgBoZ,2BAA2BA,CAACtE,OAAwB;IAClE,IAAI,CAACA,OAAO,EAAE;QACZ;;IAGF,IAAM7U,IAAI,GAAGoZ,sBAAsB,CAACvE,OAAO,CAAC;IAE5C,OAAOD,YAAY,CAAC5U,IAAI,CAAC;AAC3B;AAEA,SAASkZ,oBAAoBA,CAACrE,OAAoB;IAChD,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,IAAI;;IAGb,IAAMwE,eAAe,GAAGC,sBAAsB,CAACzE,OAAO,CAAC;IACvD,IAAMlS,QAAQ,GAAGsV,eAAe,CAACoB,eAAe,CAAC;IACjD,IAAME,UAAU,GAAGC,iBAAiB,CAACH,eAAe,EAAExE,OAAO,CAAC;IAC9D,IAAM4E,GAAG,GAAGC,SAAS,CAACL,eAAe,EAAExE,OAAO,CAAC;IAC/C,IAAM8E,UAAU,GAAGC,iBAAiB,CAACP,eAAe,EAAExE,OAAO,CAAC;IAE9D,IAAI4E,GAAG,KAAK,CAAC,EAAE;QACb,IAAMI,mBAAmB,GAAGd,YAAY,CAACpW,QAAQ,CAAC;QAElD,IAAI,CAACkX,mBAAmB,EAAE;YACxB,OAAO,IAAI;;QAGb,OAAOC,eAAe,CACpBC,gBAAgB,CAACF,mBAAmB,CAAC,EACrC,CAAC,CAAC;QACFF,UAAU,EACVJ,UAAU,CACX;;IAGH,OAAOS,mBAAmB,CACxBD,gBAAgB,CAACV,eAAe,CAAC,EACjCI,GAAG,EACHE,UAAU,EACVJ,UAAU,CACX;AACH;AAEA,SAASH,sBAAsBA,CAACvE,OAAoB;IAClD,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,IAAI;;IAGb,IAAMwE,eAAe,GAAGC,sBAAsB,CAACzE,OAAO,CAAC;IACvD,IAAMlS,QAAQ,GAAGsV,eAAe,CAACoB,eAAe,CAAC;IACjD,IAAME,UAAU,GAAGC,iBAAiB,CAACH,eAAe,EAAExE,OAAO,CAAC;IAC9D,IAAM4E,GAAG,GAAGC,SAAS,CAACL,eAAe,EAAExE,OAAO,CAAC;IAC/C,IAAM8E,UAAU,GAAGC,iBAAiB,CAACP,eAAe,EAAExE,OAAO,CAAC;IAC9D,IAAI,CAACoF,UAAU,CAACZ,eAAe,EAAExE,OAAO,CAAC,EAAE;QACzC,IAAMqF,mBAAmB,GAAGtB,YAAY,CAACjW,QAAQ,CAAC;QAElD,IAAI,CAACuX,mBAAmB,EAAE;YACxB,OAAO,IAAI;;QAGb,OAAOJ,eAAe,CACpBC,gBAAgB,CAACG,mBAAmB,CAAC,EACrC,CAAC,EACDP,UAAU,EACVJ,UAAU,CACX;;IAGH,IAAMY,aAAa,GAAGC,mBAAmB,CACvCL,gBAAgB,CAACV,eAAe,CAAC,EACjCI,GAAG,EACHE,UAAU,EACVJ,UAAU,CACX;IAED,OAAOY,aAAa;AACtB;SC/JgBE,sBAAsBA;IACpC,IAAAC,qBAAA,GAA8CrG,4BAA4B,EAAE,EAArE2D,eAAe,GAAA0C,qBAAA,CAAA,EAAA,EAAEC,kBAAkB,GAAAD,qBAAA,CAAA,EAAA;IAC1C,IAAAE,qBAAA,GAA8CrG,uBAAuB,EAAE,EAAhEsG,eAAe,GAAAD,qBAAA,CAAA,EAAA,EAAEE,kBAAkB,GAAAF,qBAAA,CAAA,EAAA;IAE1C,IAAMG,mBAAmB,6MAAGnD,cAAAA,AAAW,EAAC;QACtC,IAAII,eAAe,EAAE;YACnB2C,kBAAkB,CAAC,IAAI,CAAC;;QAG1B,IAAIE,eAAe,EAAE;YACnBC,kBAAkB,CAAC,KAAK,CAAC;;KAE5B,EAAE;QACD9C,eAAe;QACf6C,eAAe;QACfF,kBAAkB;QAClBG,kBAAkB;KACnB,CAAC;IAEF,OAAOC,mBAAmB;AAC5B;AAEA,SAAgBC,iBAAiBA;IAC/B,IAAAC,sBAAA,GAA0B5G,4BAA4B,EAAE,EAAjD2D,eAAe,GAAAiD,sBAAA,CAAA,EAAA;IACtB,IAAAC,sBAAA,GAA0B3G,uBAAuB,EAAE,EAA5CsG,eAAe,GAAAK,sBAAA,CAAA,EAAA;IAEtB,OAAO,SAASC,cAAcA;QAC5B,OAAO,CAAC,CAACnD,eAAe,IAAI6C,eAAe;KAC5C;AACH;SC/BgBO,oBAAoBA;IAClC,IAAMC,gBAAgB,GAAG5H,mBAAmB,EAAE;IAC9C,OAAO,SAAS6H,iBAAiBA;QAC/BD,gBAAgB,CAAChP,OAAO,GAAG,IAAI;KAChC;AACH;AAEA,SAAgBkP,iBAAiBA;IAC/B,IAAMF,gBAAgB,GAAG5H,mBAAmB,EAAE;IAC9C,OAAO,SAAS+H,cAAcA;QAC5BH,gBAAgB,CAAChP,OAAO,GAAG,KAAK;KACjC;AACH;AAEA,SAAgBoP,oBAAoBA;IAClC,IAAMJ,gBAAgB,GAAG5H,mBAAmB,EAAE;IAC9C,OAAO,SAASiI,iBAAiBA;QAC/B,OAAOL,gBAAgB,CAAChP,OAAO;KAChC;AACH;AAEA,SAAgBsP,cAAcA;IAC5B,IAAM1F,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAM2E,cAAc,GAAGD,iBAAiB,EAAE;IAC1C,IAAMG,iBAAiB,GAAGD,oBAAoB,EAAE;8MAEhDrP,YAAAA,AAAS,EAAC;QACR,IAAMwP,OAAO,GAAG3F,OAAO,CAAC5J,OAAO;QAC/BuP,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,WAAW,EAAEC,WAAW,EAAE;YAClDC,OAAO,EAAE;SACV,CAAC;QAEF,SAASD,WAAWA;YAClB,IAAIJ,iBAAiB,EAAE,EAAE;gBACvBF,cAAc,EAAE;;;QAGpB,OAAO;YACLI,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,WAAW,EAAEF,WAAW,CAAC;SACvD;KACF,EAAE;QAAC7F,OAAO;QAAEuF,cAAc;QAAEE,iBAAiB;KAAC,CAAC;AAClD;SCrCgBO,mBAAmBA;IACjC,IAAM/F,cAAc,GAAGa,iBAAiB,EAAE;IAE1C,OAAOa,wNAAAA,AAAW,EAAC;QACjB5C,YAAY,CAACkB,cAAc,CAAC7J,OAAO,CAAC;KACrC,EAAE;QAAC6J,cAAc;KAAC,CAAC;AACtB;AAEA,SAAgBgG,sBAAsBA;IACpC,IAAM/F,iBAAiB,GAAGa,oBAAoB,EAAE;IAEhD,iNAAOY,cAAAA,AAAW,EAAC;QACjB,IAAI,CAACzB,iBAAiB,CAAC9J,OAAO,EAAE;YAC9B;;QAGFmJ,sBAAsB,CAACW,iBAAiB,CAAC9J,OAAO,CAAC;KAClD,EAAE;QAAC8J,iBAAiB;KAAC,CAAC;AACzB;AAEA,SAAgBgG,0BAA0BA;IACxC,IAAM/F,qBAAqB,GAAGa,wBAAwB,EAAE;IAExD,OAAOW,wNAAAA,AAAW,EAAC;QACjB,IAAI,CAACxB,qBAAqB,CAAC/J,OAAO,EAAE;YAClC;;QAGFmJ,sBAAsB,CAACY,qBAAqB,CAAC/J,OAAO,CAAC;KACtD,EAAE;QAAC+J,qBAAqB;KAAC,CAAC;AAC7B;ACvBA,SAASgG,eAAeA;IACtB,IAAMjK,SAAS,GAAGkB,YAAY,EAAE;IAEhC,OAAO,SAASgJ,SAASA,CACvBC,MAA6D;QAE7D,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;YAChC,OAAOD,SAAS,CAACC,MAAM,CAACnK,SAAS,CAAC9F,OAAO,CAAC,CAAC;;QAG7C8F,SAAS,CAAC9F,OAAO,GAAGiQ,MAAM;KAC3B;AACH;AAEA,SAAgBC,cAAcA;IAC5B,IAAMC,WAAW,GAAGC,cAAc,EAAE;IACpC,IAAMvG,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAM2F,gBAAgB,GAAGT,mBAAmB,EAAE;IAE9C,OAAO,SAASU,WAAWA;QACzB,IAAIzG,cAAc,CAAC7J,OAAO,EAAE;YAC1B6J,cAAc,CAAC7J,OAAO,CAACtM,KAAK,GAAG,EAAE;;QAGnCyc,WAAW,CAAC,EAAE,CAAC;QACfE,gBAAgB,EAAE;KACnB;AACH;AAEA,SAAgBE,eAAeA;IAC7B,IAAM1G,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAMyF,WAAW,GAAGC,cAAc,EAAE;IAEpC,OAAO,SAASI,YAAYA,CAACC,GAAW;QACtC,IAAI5G,cAAc,CAAC7J,OAAO,EAAE;YAC1B6J,cAAc,CAAC7J,OAAO,CAACtM,KAAK,GAAA,KAAMmW,cAAc,CAAC7J,OAAO,CAACtM,KAAK,GAAG+c,GAAK;YACtEN,WAAW,CAACO,uBAAuB,CAAC7G,cAAc,CAAC7J,OAAO,CAACtM,KAAK,CAAC,CAAC;SACnE,MAAM;YACLyc,WAAW,CAACO,uBAAuB,CAACD,GAAG,CAAC,CAAC;;KAE5C;AACH;AAEA,SAAgBE,SAASA;IACvB,IAAM9G,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAM5E,SAAS,GAAGkB,YAAY,EAAE;IAChC,IAAM4J,YAAY,GAAGb,eAAe,EAAE;IACtC,IAAMI,WAAW,GAAGC,cAAc,EAAE;IAEpC,IAAA1H,mBAAA,GAAqBlB,kBAAkB,EAAE,EAAlCnB,UAAU,GAAAqC,mBAAA,CAAA,EAAA;IACjB,IAAMmI,mBAAmB,GAAGC,sBAAsB,CAChDhL,SAAS,CAAC9F,OAAO,EACjBqG,UAAU,CACX;IAED,OAAO;QACL0K,QAAQ,EAARA,QAAQ;QACR1K,UAAU,EAAVA,UAAU;QACVwD,cAAc,EAAdA,cAAc;QACdgH,mBAAmB,EAAnBA;KACD;;IAED,SAASE,QAAQA,CAACC,UAAkB;QAClC,IAAMC,MAAM,GAAGnL,SAAS,CAAC9F,OAAO;QAEhC,IAAMkR,SAAS,GAAGF,UAAU,CAACjX,WAAW,EAAE;QAE1C,IAAIkX,MAAM,IAAA,QAANA,MAAM,CAAGC,SAAS,CAAC,IAAIA,SAAS,CAAC7b,MAAM,IAAI,CAAC,EAAE;YAChD,OAAO8a,WAAW,CAACe,SAAS,CAAC;;QAG/B,IAAMC,YAAY,GAAGC,gBAAgB,CAACF,SAAS,EAAED,MAAM,CAAC;QAExD,IAAI,CAACE,YAAY,EAAE;;;YAGjB,OAAOhB,WAAW,CAACe,SAAS,CAAC;;QAG/BN,YAAY,CAAC,SAAA5Q,OAAO;YAAA,IAAA5G,cAAA;YAAA,OAClB1B,MAAM,CAACC,MAAM,CAACqI,OAAO,EAAA,CAAA5G,cAAA,GAAA,CAAA,GAAAA,cAAA,CAClB8X,SAAS,CAAA,GAAGG,0BAA0B,CAACF,YAAY,EAAED,SAAS,CAAC,EAAA9X,cAAA,EAChE;UACH;QACD+W,WAAW,CAACe,SAAS,CAAC;;AAE1B;AAEA,SAASd,cAAcA;IACrB,IAAAkB,oBAAA,GAA0B9J,kBAAkB,EAAE,EAArC+J,aAAa,GAAAD,oBAAA,CAAA,EAAA;IACtB,IAAM5H,aAAa,GAAGU,gBAAgB,EAAE;IAExC,OAAO,SAAS+F,WAAWA,CAAC9J,UAAkB;QAC5CwC,qBAAqB,CAAC;YACpB0I,aAAa,CAAClL,UAAU,GAAGA,UAAU,IAAA,OAAA,KAAA,IAAVA,UAAU,CAAEtM,WAAW,EAAE,GAAGsM,UAAU,CAAC,CAACmL,IAAI,CACrE;gBACE1G,QAAQ,CAACpB,aAAa,CAAC1J,OAAO,EAAE,CAAC,CAAC;aACnC,CACF;SACF,CAAC;KACH;AACH;AAEA,SAASqR,0BAA0BA,CACjChW,MAAkB,EAClBoW,OAAe;IAEf,IAAMC,QAAQ,GAAe,CAAA,CAAE;IAE/B,IAAK,IAAM9W,OAAO,IAAIS,MAAM,CAAE;QAC5B,IAAM3B,KAAK,GAAG2B,MAAM,CAACT,OAAO,CAAC;QAE7B,IAAI+W,QAAQ,CAACjY,KAAK,EAAE+X,OAAO,CAAC,EAAE;YAC5BC,QAAQ,CAAC9W,OAAO,CAAC,GAAGlB,KAAK;;;IAI7B,OAAOgY,QAAQ;AACjB;AAEA,SAASC,QAAQA,CAACjY,KAAgB,EAAE+X,OAAe;IACjD,OAAO5X,UAAU,CAACH,KAAK,CAAC,CAACkY,IAAI,CAAC,SAAAnb,IAAI;QAAA,OAAIA,IAAI,CAACmF,QAAQ,CAAC6V,OAAO,CAAC;MAAC;AAC/D;AAEA,SAAgBI,kBAAkBA;IAChC,IAAAC,aAAA,GAA4B9K,YAAY,EAAE,EAAzBiK,MAAM,GAAAa,aAAA,CAAf9R,OAAO;IACf,IAAA+R,oBAAA,GAAqBvK,kBAAkB,EAAE,EAAlCnB,UAAU,GAAA0L,oBAAA,CAAA,EAAA;IAEjB,OAAO,SAAAnX,OAAO;QAAA,OAAIoX,2BAA2B,CAACpX,OAAO,EAAEqW,MAAM,EAAE5K,UAAU,CAAC;;AAC5E;AAEA,SAAS2L,2BAA2BA,CAClCpX,OAAe,EACfqW,MAAmB,EACnB5K,UAAkB;;IAElB,IAAI,CAAC4K,MAAM,IAAI,CAAC5K,UAAU,EAAE;QAC1B,OAAO,KAAK;;IAGd,OAAO,CAAA,CAAA,CAAA4L,kBAAA,GAAChB,MAAM,CAAC5K,UAAU,CAAC,KAAA,QAAlB4L,kBAAA,CAAqBrX,OAAO,CAAC;AACvC;AAIA,SAASwW,gBAAgBA,CACvBK,OAAe,EACfS,IAAuC;IAEvC,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,IAAIA,IAAI,CAACT,OAAO,CAAC,EAAE;QACjB,OAAOS,IAAI,CAACT,OAAO,CAAC;;IAGtB,IAAMU,kBAAkB,GAAGza,MAAM,CAAC0a,IAAI,CAACF,IAAI,CAAC,CACzCG,IAAI,CAAC,SAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACld,MAAM,GAAGid,CAAC,CAACjd,MAAM;MAAC,CACnCqG,IAAI,CAAC,SAAAjI,GAAG;QAAA,OAAIge,OAAO,CAAC7V,QAAQ,CAACnI,GAAG,CAAC;MAAC;IAErC,IAAI0e,kBAAkB,EAAE;QACtB,OAAOD,IAAI,CAACC,kBAAkB,CAAC;;IAGjC,OAAO,IAAI;AACb;AAEA,SAAgBzB,uBAAuBA,CAACD,GAAW;IACjD,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACnC,OAAO,EAAE;;IAGX,OAAOA,GAAG,CAAC+B,IAAI,EAAE,CAACzY,WAAW,EAAE;AACjC;AAEA,SAAS+W,sBAAsBA,CAC7B2B,WAAwB,EACxBpM,UAAkB;;IAElB,IAAI,CAAA,CAACoM,WAAW,IAAA,QAAXA,WAAW,CAAGpM,UAAU,CAAC,GAAE,OAAO,EAAE;IAEzC,IAAM3C,kBAAkB,GACtB,CAAA,CAAAgP,eAAA,GAAAhb,MAAM,CAACmB,OAAO,CAAC4Z,WAAW,IAAA,OAAA,KAAA,IAAXA,WAAW,CAAGpM,UAAU,CAAC,CAAC,KAAA,OAAA,KAAA,IAAzCqM,eAAA,CAA2Crd,MAAM,KAAI,CAAC;;IAExD,OAAOoO,sBAAsB,CAACC,kBAAkB,CAAC;AACnD;SCtMwBiP,qBAAqBA;IAC3C,IAAMC,mBAAmB,GAAGtI,sBAAsB,EAAE;IACpD,IAAA+D,qBAAA,GAAoCrG,4BAA4B,EAAE,EAAzD6K,uBAAuB,GAAAxE,qBAAA,CAAA,EAAA;IAEhC,OAAO,SAASC,kBAAkBA,CAAC1F,OAAwB;QACzD,IAAAkK,iBAAA,GAAgBC,gBAAgB,CAACnK,OAAO,CAAC,EAAlClP,KAAK,GAAAoZ,iBAAA,CAAA,EAAA;QAEZ,IAAIpZ,KAAK,EAAE;YACTkZ,mBAAmB,CAAChK,OAAO,CAAC;YAC5BiK,uBAAuB,CAACnZ,KAAK,CAAC;;KAEjC;AACH;SCLgBsZ,qBAAqBA;IACnC,IAAMC,4BAA4B,GAAGjQ,+BAA+B,EAAE;IAEtE,OAAOiQ,4BAA4B,KAAKrd,sBAAsB,CAACoI,MAAM;AACvE;AAEA,SAAgBkV,sBAAsBA;IACpC,IAAMD,4BAA4B,GAAGjQ,+BAA+B,EAAE;IAEtE,OAAOiQ,4BAA4B,KAAKrd,sBAAsB,CAACiI,OAAO;AACxE;ACyBA,IAAKsV,cAQJ;AARD,CAAA,SAAKA,cAAc;IACjBA,cAAAA,CAAAA,YAAAA,GAAAA,WAAuB;IACvBA,cAAAA,CAAAA,UAAAA,GAAAA,SAAmB;IACnBA,cAAAA,CAAAA,YAAAA,GAAAA,WAAuB;IACvBA,cAAAA,CAAAA,aAAAA,GAAAA,YAAyB;IACzBA,cAAAA,CAAAA,SAAAA,GAAAA,QAAiB;IACjBA,cAAAA,CAAAA,QAAAA,GAAAA,OAAe;IACfA,cAAAA,CAAAA,QAAAA,GAAAA,GAAW;AACb,CAAC,EARIA,cAAc,IAAA,CAAdA,cAAc,GAAA,CAAA,CAAA;AAUnB,SAAgBC,qBAAqBA;IACnCC,2BAA2B,EAAE;IAC7BC,4BAA4B,EAAE;IAC9BC,+BAA+B,EAAE;IACjCC,mCAAmC,EAAE;IACrCC,qBAAqB,EAAE;AACzB;AAEA,SAASJ,2BAA2BA;IAClC,IAAM3J,aAAa,GAAGU,gBAAgB,EAAE;IACxC,IAAMkG,WAAW,GAAGJ,cAAc,EAAE;IACpC,IAAMpF,QAAQ,GAAGQ,WAAW,EAAE;IAC9B,IAAMzB,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAM2F,gBAAgB,GAAGT,mBAAmB,EAAE;IAC9C,IAAMd,cAAc,GAAGH,iBAAiB,EAAE;IAC1C,IAAMM,iBAAiB,GAAGF,oBAAoB,EAAE;IAEhD,IAAML,mBAAmB,GAAGN,sBAAsB,EAAE;IAEpD,IAAMsF,SAAS,6MAAGzO,UAAAA,AAAO,EACvB;QAAA,OACE,SAASyO,SAASA,CAACC,KAAoB;YACrC,IAAQlgB,GAAG,GAAKkgB,KAAK,CAAblgB,GAAG;YAEXwb,iBAAiB,EAAE;YACnB,OAAQxb,GAAG;;gBAET,KAAK0f,cAAc,CAACS,MAAM;oBACxBD,KAAK,CAACE,cAAc,EAAE;oBACtB,IAAI/E,cAAc,EAAE,EAAE;wBACpBJ,mBAAmB,EAAE;wBACrB;;oBAEF4B,WAAW,EAAE;oBACbxF,QAAQ,CAAC,CAAC,CAAC;oBACXuF,gBAAgB,EAAE;oBAClB;;SAEL;OACH;QACEvF,QAAQ;QACRwF,WAAW;QACX5B,mBAAmB;QACnB2B,gBAAgB;QAChBvB,cAAc;QACdG,iBAAiB;KAClB,CACF;8MAEDlP,YAAAA,AAAS,EAAC;QACR,IAAMC,OAAO,GAAG0J,aAAa,CAAC1J,OAAO;QAErC,IAAI,CAACA,OAAO,EAAE;YACZ;;QAGFA,OAAO,CAACwP,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;QAE9C,OAAO;YACL1T,OAAO,CAAC2P,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;SAClD;KACF,EAAE;QAAChK,aAAa;QAAEG,cAAc;QAAEiB,QAAQ;QAAE4I,SAAS;KAAC,CAAC;AAC1D;AAEA,SAASJ,4BAA4BA;IACnC,IAAMQ,mBAAmB,GAAGjE,sBAAsB,EAAE;IACpD,IAAMnG,aAAa,GAAGU,gBAAgB,EAAE;IACxC,IAAMR,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAMX,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAA6D,qBAAA,GAAoCrG,uBAAuB,EAAE,EAApD6L,uBAAuB,GAAAxF,qBAAA,CAAA,EAAA;IAChC,IAAMyF,qBAAqB,GAAGC,wBAAwB,EAAE;IACxD,IAAMC,kBAAkB,GAAGlB,qBAAqB,EAAE;IAElD,IAAMU,SAAS,GAAGzO,oNAAAA,AAAO,EACvB;QAAA,OACE,SAASyO,SAASA,CAACC,KAAoB;YACrC,IAAQlgB,GAAG,GAAKkgB,KAAK,CAAblgB,GAAG;YAEX,OAAQA,GAAG;gBACT,KAAK0f,cAAc,CAACgB,UAAU;oBAC5B,IAAI,CAACD,kBAAkB,EAAE;wBACvB;;oBAEFP,KAAK,CAACE,cAAc,EAAE;oBACtBE,uBAAuB,CAAC,IAAI,CAAC;oBAC7BD,mBAAmB,EAAE;oBACrB;gBACF,KAAKX,cAAc,CAACiB,SAAS;oBAC3BT,KAAK,CAACE,cAAc,EAAE;oBACtBG,qBAAqB,EAAE;oBACvB;gBACF,KAAKb,cAAc,CAACkB,KAAK;oBACvBV,KAAK,CAACE,cAAc,EAAE;oBACtBzH,8BAA8B,CAACxC,OAAO,CAAC5J,OAAO,CAAC;oBAC/C;;SAEL;OACH;QACE8T,mBAAmB;QACnBE,qBAAqB;QACrBD,uBAAuB;QACvBnK,OAAO;QACPsK,kBAAkB;KACnB,CACF;8MAEDnU,YAAAA,AAAS,EAAC;QACR,IAAMC,OAAO,GAAG6J,cAAc,CAAC7J,OAAO;QAEtC,IAAI,CAACA,OAAO,EAAE;YACZ;;QAGFA,OAAO,CAACwP,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;QAE9C,OAAO;YACL1T,OAAO,CAAC2P,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;SAClD;KACF,EAAE;QAAChK,aAAa;QAAEG,cAAc;QAAE6J,SAAS;KAAC,CAAC;AAChD;AAEA,SAASH,+BAA+BA;IACtC,IAAMzJ,iBAAiB,GAAGa,oBAAoB,EAAE;IAChD,IAAM0F,gBAAgB,GAAGT,mBAAmB,EAAE;IAC9C,IAAM/F,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAMsJ,qBAAqB,GAAGC,wBAAwB,EAAE;IACxD,IAAApF,sBAAA,GAA4B3G,uBAAuB,EAAE,EAA9CoM,MAAM,GAAAzF,sBAAA,CAAA,EAAA,EAAE0F,SAAS,GAAA1F,sBAAA,CAAA,EAAA;IACxB,IAAM2F,mBAAmB,GAAGtB,sBAAsB,EAAE;IACpD,IAAMgB,kBAAkB,GAAGlB,qBAAqB,EAAE;IAClD,IAAMyB,MAAM,GAAGC,SAAS,EAAE;IAE1B,IAAMhB,SAAS,IAAGzO,mNAAAA,AAAO,EACvB;QAAA;QAEE,SAASyO,SAASA,CAACC,KAAoB;YACrC,IAAQlgB,GAAG,GAAKkgB,KAAK,CAAblgB,GAAG;YAEX,IAAIygB,kBAAkB,EAAE;gBACtB,OAAQzgB,GAAG;oBACT,KAAK0f,cAAc,CAACwB,SAAS;wBAC3BhB,KAAK,CAACE,cAAc,EAAE;wBACtB,IAAI,CAACS,MAAM,EAAE;4BACX,OAAOjE,gBAAgB,EAAE;;wBAE3BuE,iBAAiB,CAACvE,gBAAgB,CAAC;wBACnC;oBACF,KAAK8C,cAAc,CAACgB,UAAU;wBAC5BR,KAAK,CAACE,cAAc,EAAE;wBACtB,IAAI,CAACS,MAAM,EAAE;4BACX,OAAOjE,gBAAgB,EAAE;;wBAE3BwE,iBAAiB,EAAE;wBACnB;oBACF,KAAK1B,cAAc,CAACiB,SAAS;wBAC3BT,KAAK,CAACE,cAAc,EAAE;wBACtB,IAAIS,MAAM,EAAE;4BACVC,SAAS,CAAC,KAAK,CAAC;;wBAElBP,qBAAqB,EAAE;wBACvB;oBACF;wBACES,MAAM,CAACd,KAAK,CAAC;wBACb;;;YAIN,IAAIa,mBAAmB,EAAE;gBACvB,OAAQ/gB,GAAG;oBACT,KAAK0f,cAAc,CAAC2B,OAAO;wBACzBnB,KAAK,CAACE,cAAc,EAAE;wBACtB,IAAI,CAACS,MAAM,EAAE;4BACX,OAAOjE,gBAAgB,EAAE;;wBAE3BuE,iBAAiB,CAACvE,gBAAgB,CAAC;wBACnC;oBACF,KAAK8C,cAAc,CAACiB,SAAS;wBAC3BT,KAAK,CAACE,cAAc,EAAE;wBACtB,IAAI,CAACS,MAAM,EAAE;4BACX,OAAOjE,gBAAgB,EAAE;;wBAE3BwE,iBAAiB,EAAE;wBACnB;oBACF;wBACEJ,MAAM,CAACd,KAAK,CAAC;wBACb;;;;OAIV;QACEW,MAAM;QACNjE,gBAAgB;QAChBkE,SAAS;QACTP,qBAAqB;QACrBS,MAAM;QACND,mBAAmB;QACnBN,kBAAkB;KACnB,CACF;8MAEDnU,YAAAA,AAAS,EAAC;QACR,IAAMC,OAAO,GAAG8J,iBAAiB,CAAC9J,OAAO;QAEzC,IAAI,CAACA,OAAO,EAAE;YACZ;;QAGFA,OAAO,CAACwP,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;QAE9C,OAAO;YACL1T,OAAO,CAAC2P,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;SAClD;KACF,EAAE;QAAC5J,iBAAiB;QAAED,cAAc;QAAEyK,MAAM;QAAEZ,SAAS;KAAC,CAAC;AAC5D;AAEA,SAASF,mCAAmCA;IAC1C,IAAMnD,gBAAgB,GAAGT,mBAAmB,EAAE;IAC9C,IAAM7F,qBAAqB,GAAGa,wBAAwB,EAAE;IACxD,IAAMhB,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAMiK,MAAM,GAAGC,SAAS,EAAE;IAE1B,IAAMhB,SAAS,6MAAGzO,UAAAA,AAAO,EACvB;QAAA,OACE,SAASyO,SAASA,CAACC,KAAoB;YACrC,IAAQlgB,GAAG,GAAKkgB,KAAK,CAAblgB,GAAG;YAEX,OAAQA,GAAG;gBACT,KAAK0f,cAAc,CAAC2B,OAAO;oBACzBnB,KAAK,CAACE,cAAc,EAAE;oBACtBxD,gBAAgB,EAAE;oBAClB;gBACF,KAAK8C,cAAc,CAACgB,UAAU;oBAC5BR,KAAK,CAACE,cAAc,EAAE;oBACtB5K,uBAAuB,CAACK,gBAAgB,EAAE,CAAC;oBAC3C;gBACF,KAAK6J,cAAc,CAACwB,SAAS;oBAC3BhB,KAAK,CAACE,cAAc,EAAE;oBACtB9K,uBAAuB,CAACO,gBAAgB,EAAE,CAAC;oBAC3C;gBACF,KAAK6J,cAAc,CAACiB,SAAS;oBAC3BT,KAAK,CAACE,cAAc,EAAE;oBACtB5H,sBAAsB,CAACrC,OAAO,CAAC5J,OAAO,CAAC;oBACvC;gBACF;oBACEyU,MAAM,CAACd,KAAK,CAAC;oBACb;;SAEL;OACH;QAAC/J,OAAO;QAAEyG,gBAAgB;QAAEoE,MAAM;KAAC,CACpC;8MAED1U,YAAAA,AAAS,EAAC;QACR,IAAMC,OAAO,GAAG+J,qBAAqB,CAAC/J,OAAO;QAE7C,IAAI,CAACA,OAAO,EAAE;YACZ;;QAGFA,OAAO,CAACwP,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;QAE9C,OAAO;YACL1T,OAAO,CAAC2P,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;SAClD;KACF,EAAE;QAAC3J,qBAAqB;QAAEH,OAAO;QAAE8J,SAAS;KAAC,CAAC;AACjD;AAEA,SAASD,qBAAqBA;IAC5B,IAAM7J,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAMuK,YAAY,GAAGC,eAAe,EAAE;IACtC,IAAM1G,kBAAkB,GAAGqE,qBAAqB,EAAE;IAClD,IAAM7D,cAAc,GAAGH,iBAAiB,EAAE;IAC1C,IAAMD,mBAAmB,GAAGN,sBAAsB,EAAE;IAEpD,IAAMqG,MAAM,GAAGC,SAAS,EAAE;IAE1B,IAAMhB,SAAS,6MAAGzO,UAAAA,AAAO,EACvB;QAAA;QAEE,SAASyO,SAASA,CAACC,KAAoB;YACrC,IAAQlgB,GAAG,GAAKkgB,KAAK,CAAblgB,GAAG;YAEX,IAAM+V,aAAa,GAAGyL,gBAAgB,CAAC3L,gBAAgB,EAAE,CAAC;YAE1D,OAAQ7V,GAAG;gBACT,KAAK0f,cAAc,CAACgB,UAAU;oBAC5BR,KAAK,CAACE,cAAc,EAAE;oBACtBpH,qBAAqB,CAACjD,aAAa,CAAC;oBACpC;gBACF,KAAK2J,cAAc,CAACwB,SAAS;oBAC3BhB,KAAK,CAACE,cAAc,EAAE;oBACtBjH,qBAAqB,CAACpD,aAAa,CAAC;oBACpC;gBACF,KAAK2J,cAAc,CAACiB,SAAS;oBAC3BT,KAAK,CAACE,cAAc,EAAE;oBACtB,IAAI/E,cAAc,EAAE,EAAE;wBACpBJ,mBAAmB,EAAE;wBACrB;;oBAEFxB,2BAA2B,CAAC1D,aAAa,CAAC;oBAC1C;gBACF,KAAK2J,cAAc,CAAC2B,OAAO;oBACzBnB,KAAK,CAACE,cAAc,EAAE;oBACtB,IAAI/E,cAAc,EAAE,EAAE;wBACpBJ,mBAAmB,EAAE;wBACrB;;oBAEF3B,yBAAyB,CAACvD,aAAa,EAAEuL,YAAY,CAAC;oBACtD;gBACF,KAAK5B,cAAc,CAAC+B,KAAK;oBACvBvB,KAAK,CAACE,cAAc,EAAE;oBACtBvF,kBAAkB,CAACqF,KAAK,CAACpJ,MAAqB,CAAC;oBAC/C;gBACF;oBACEkK,MAAM,CAACd,KAAK,CAAC;oBACb;;;OAGR;QACEoB,YAAY;QACZN,MAAM;QACNnG,kBAAkB;QAClBQ,cAAc;QACdJ,mBAAmB;KACpB,CACF;8MAED3O,YAAAA,AAAS,EAAC;QACR,IAAMC,OAAO,GAAG4J,OAAO,CAAC5J,OAAO;QAE/B,IAAI,CAACA,OAAO,EAAE;YACZ;;QAGFA,OAAO,CAACwP,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;QAE9C,OAAO;YACL1T,OAAO,CAAC2P,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;SAClD;KACF,EAAE;QAAC9J,OAAO;QAAE8J,SAAS;KAAC,CAAC;AAC1B;AAEA,SAASO,wBAAwBA;IAC/B,IAAMkB,uBAAuB,GAAGrF,0BAA0B,EAAE;IAC5D,IAAMsF,YAAY,GAAG3M,eAAe,EAAE;IACtC,IAAMmB,OAAO,GAAGY,UAAU,EAAE;IAE5B,iNAAOe,cAAAA,AAAW,EAChB,SAASyI,qBAAqBA;QAC5B,IAAIoB,YAAY,EAAE;YAChB,OAAOnJ,sBAAsB,CAACrC,OAAO,CAAC5J,OAAO,CAAC;;QAEhD,OAAOmV,uBAAuB,EAAE;KACjC,EACD;QAACvL,OAAO;QAAEuL,uBAAuB;QAAEC,YAAY;KAAC,CACjD;AACH;AAEA,SAASJ,eAAeA;IACtB,IAAM3E,gBAAgB,GAAGT,mBAAmB,EAAE;IAC9C,IAAMuF,uBAAuB,GAAGrF,0BAA0B,EAAE;IAC5D,IAAMsF,YAAY,GAAG3M,eAAe,EAAE;IAEtC,iNAAO8C,cAAAA,AAAW,EAChB,SAAS8J,aAAaA;QACpB,IAAID,YAAY,EAAE;YAChB,OAAO/E,gBAAgB,EAAE;;QAE3B,OAAO8E,uBAAuB,EAAE;KACjC,EACD;QAAC9E,gBAAgB;QAAE+E,YAAY;QAAED,uBAAuB;KAAC,CAC1D;AACH;AAEA,SAASP,iBAAiBA,CAACU,QAAoB;IAC7C,IAAMC,eAAe,GAAGjM,gBAAgB,EAAE;IAE1C,IAAI,CAACiM,eAAe,EAAE;QACpB;;IAGF,IAAI,CAACC,qBAAqB,CAACD,eAAe,CAAC,EAAE;QAC3CD,QAAQ,EAAE;;IAGZrM,uBAAuB,CAACsM,eAAe,CAAC;AAC1C;AAEA,SAASV,iBAAiBA;IACxB,IAAMU,eAAe,GAAGjM,gBAAgB,EAAE;IAE1C,IAAI,CAACiM,eAAe,EAAE;QACpB;;IAGFxM,uBAAuB,CAACwM,eAAe,CAAC;AAC1C;AAEA,SAASb,SAASA;IAChB,IAAMlE,YAAY,GAAGD,eAAe,EAAE;IACtC,IAAMF,gBAAgB,GAAGT,mBAAmB,EAAE;IAC9C,IAAMza,cAAc,GAAG2N,uBAAuB,EAAE;IAChD,IAAM4L,mBAAmB,GAAGN,sBAAsB,EAAE;IAEpD,OAAO,SAASqG,MAAMA,CAACd,KAAoB;QACzC,IAAQlgB,GAAG,GAAKkgB,KAAK,CAAblgB,GAAG;QAEX,IAAIgiB,WAAW,CAAC9B,KAAK,CAAC,IAAIxe,cAAc,EAAE;YACxC;;QAGF,IAAI1B,GAAG,CAACiiB,KAAK,CAAC,oBAAoB,CAAC,EAAE;YACnC/B,KAAK,CAACE,cAAc,EAAE;YACtBnF,mBAAmB,EAAE;YACrB2B,gBAAgB,EAAE;YAClBG,YAAY,CAAC/c,GAAG,CAAC;;KAEpB;AACH;AAEA,SAASgiB,WAAWA,CAAC9B,KAAoB;IACvC,IAAQgC,OAAO,GAAsBhC,KAAK,CAAlCgC,OAAO,EAAEC,OAAO,GAAajC,KAAK,CAAzBiC,OAAO,EAAEC,MAAM,GAAKlC,KAAK,CAAhBkC,MAAM;IAEhC,OAAOF,OAAO,IAAIC,OAAO,IAAIC,MAAM;AACrC;SCzdgBC,YAAYA,CAC1BhY,WAAwB,EACxBpE,KAA4B,EAC5B7E,UAAsB;IAEtB,IAAI,CAAC6E,KAAK,EAAE;QACV;;IAGF,IAAI7E,UAAU,KAAKW,UAAU,CAACugB,MAAM,EAAE;QACpC;;IAGF,IAAMnb,OAAO,GAAGP,YAAY,CAACX,KAAK,CAAC;IAEnC,IAAIsc,cAAc,CAACnR,GAAG,CAACjK,OAAO,CAAC,EAAE;QAC/B;;IAGFW,eAAe,CAAC7B,KAAK,CAAC,CAACQ,OAAO,CAAC,SAACyB,SAAS;QACvC,IAAMsa,QAAQ,GAAGnY,WAAW,CAACnC,SAAS,EAAE9G,UAAU,CAAC;QACnDqhB,YAAY,CAACD,QAAQ,CAAC;KACvB,CAAC;IAEFD,cAAc,CAACrY,GAAG,CAAC/C,OAAO,CAAC;AAC7B;AAEO,IAAMob,cAAc,GAAA,WAAA,GAAgB,IAAI9X,GAAG,EAAE;AAEpD,SAASgY,YAAYA,CAACC,GAAW;IAC/B,IAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,GAAG,GAAGH,GAAG;AACjB;SC3BgBI,UAAUA;IACxB,IAAM3M,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAM3V,UAAU,GAAG+L,mBAAmB,EAAE;IACxC,IAAM9C,WAAW,GAAGwF,oBAAoB,EAAE;QAE1CvD,kNAAAA,AAAS,EAAC;QACR,IAAIlL,UAAU,KAAKW,UAAU,CAACugB,MAAM,EAAE;YACpC;;QAGF,IAAMxG,OAAO,GAAG3F,OAAO,CAAC5J,OAAO;QAE/BuP,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,SAAS,EAAEgH,OAAO,CAAC;QAE7C,OAAO;YACLjH,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,SAAS,EAAE6G,OAAO,CAAC;SACjD;;QAED,SAASA,OAAOA,CAAC7C,KAAiB;YAChC,IAAM8C,MAAM,GAAGxB,gBAAgB,CAACtB,KAAK,CAACpJ,MAAqB,CAAC;YAE5D,IAAI,CAACkM,MAAM,EAAE;gBACX;;YAGF,IAAA3D,iBAAA,GAAgBC,gBAAgB,CAAC0D,MAAM,CAAC,EAAjC/c,KAAK,GAAAoZ,iBAAA,CAAA,EAAA;YAEZ,IAAI,CAACpZ,KAAK,EAAE;gBACV;;YAGF,IAAIsB,kBAAkB,CAACtB,KAAK,CAAC,EAAE;gBAC7Boc,YAAY,CAAChY,WAAW,EAAEpE,KAAK,EAAE7E,UAAU,CAAC;;;KAGjD,EAAE;QAAC+U,OAAO;QAAE/U,UAAU;QAAEiJ,WAAW;KAAC,CAAC;AACxC;;;;;ACtBO,IAAM4Y,oBAAoB,GAAG,EAAE;AAEtC,SAAwBC,UAAUA,CAAA3d,IAAA;QAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IAC3C,iNACEhM,gBAAAA,EAACmT,qBAAqB,EAAA,gNACpBnT,gBAAAA,EAACkkB,iBAAiB,EAAA,MAAElY,QAAQ,CAAqB,CAC3B;AAE5B;AAQA,SAASkY,iBAAiBA,CAAAC,KAAA;;QAAGnY,QAAQ,GAAAmY,KAAA,CAARnY,QAAQ;IACnC,IAAAoY,qBAAA,GAAwBxP,qBAAqB,EAAE,EAAxCyP,aAAa,GAAAD,qBAAA,CAAA,EAAA;IACpB,IAAMhiB,KAAK,GAAGiN,cAAc,EAAE;IAC9B,IAAMiV,gBAAgB,GAAGvO,eAAe,EAAE;IAC1C,IAAMiB,aAAa,GAAGU,gBAAgB,EAAE;IACxC,IAAMnV,SAAS,GAAGoN,kBAAkB,EAAE;IACtC,IAAMnN,KAAK,GAAGqN,cAAc,EAAE;IAE9B6Q,qBAAqB,EAAE;IACvBmD,UAAU,EAAE;IAEZ,IAAAU,KAAA,GAAyC/hB,KAAK,IAAI,CAAA,CAAE,EAA5C3B,KAAK,GAAA0jB,KAAA,CAAL1jB,KAAK,EAAED,MAAM,GAAA2jB,KAAA,CAAN3jB,MAAM,EAAK4jB,UAAU,GAAAvY,6BAAA,CAAAsY,KAAA,EAAArY,WAAA;IAEpC,gNACElM,iBAAAA,EAAAA,SAAAA;QACEuC,SAAS,sJAAEkiB,KAAAA,AAAE,EACXC,MAAM,CAACC,IAAI,EACXD,MAAM,CAACE,aAAa,EACpBxiB,KAAK,KAAKW,KAAK,CAACkD,IAAI,IAAIye,MAAM,CAACG,SAAS,EACxCziB,KAAK,KAAKW,KAAK,CAAC+hB,IAAI,IAAIJ,MAAM,CAACK,aAAa,EAAA,CAAAC,GAAA,GAAA,CAAA,GAAAA,GAAA,CAEzCtmB,UAAU,CAACumB,YAAY,CAAA,GAAGX,gBAAgB,EAAAU,GAAA,GAE7CX,aAAa,IAAIK,MAAM,CAACQ,aAAa,EACrC3iB,SAAS,CACV;QACD4iB,GAAG,EAAEnO,aAAa;QAClBxU,KAAK,EAAA1C,QAAA,CAAA,CAAA,GACA0kB,UAAU,EACT,CAACH,aAAa,IAAI;YAAEzjB,MAAM,EAANA,MAAM;YAAEC,KAAK,EAALA;SAAO;OAGxCmL,QAAQ,CACH;AAEZ;AAEA,IAAMoZ,SAAS,GAAG;IAChB,uCAAuC,EACrC,iDAAiD;IACnD,sCAAsC,EACpC,gDAAgD;IAClD,uBAAuB,EAAE,iCAAiC;IAC1D,kBAAkB,EAAE,4BAA4B;IAChD,sBAAsB,EAAE,gCAAgC;IACxD,sBAAsB,EAAE,gCAAgC;IACxD,6BAA6B,EAAE,uCAAuC;IACtE,+BAA+B,EAAE,yCAAyC;IAC1E,2BAA2B,EAAE,qCAAqC;IAClE,gBAAgB,EAAE,0BAA0B;IAC5C,0BAA0B,EAAE,oCAAoC;IAChE,oCAAoC,EAClC,8CAA8C;IAChD,uCAAuC,EACrC,iDAAiD;IACnD,kCAAkC,EAChC,4CAA4C;IAC9C,mCAAmC,EACjC,6CAA6C;IAC/C,oCAAoC,EAAE,8CAA8C;IACpF,oCAAoC,EAAE;CACvC;AAED,IAAMV,MAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/B8kB,IAAI,EAAE;QACJ,GAAG,EAAE;YAAC,UAAU;YAAEjmB,UAAU,CAAC2mB,WAAW;SAAC;QACzCC,QAAQ,EAAE,UAAU;QACpB/lB,OAAO,EAAE,MAAM;QACfgmB,aAAa,EAAE,QAAQ;QACvBC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,OAAO;QACpBC,YAAY,EAAE,iCAAiC;QAC/CC,WAAW,EAAE,gCAAgC;QAC7CC,eAAe,EAAE,qBAAqB;QACtCjmB,QAAQ,EAAE,QAAQ;QAClBgB,UAAU,EAAE,yDAAyD;QACrE,GAAG,EAAE;YACHklB,SAAS,EAAE,YAAY;YACvBC,UAAU,EAAE;;KAEf;IACDlB,aAAa,EAAE;QACb,IAAI,EAAE;YACJ,uBAAuB,EAAE,SAAS;YAClC,sBAAsB,EAAE,SAAS;YACjC,sCAAsC,EAAE,WAAW;YACnD,sBAAsB,EAAE,SAAS;YACjC,kBAAkB,EAAE,SAAS;YAC7B,6BAA6B,EAAE,SAAS;YACxC,2BAA2B,EAAE,SAAS;YACtC,gBAAgB,EAAE,MAAM;YACxB,0BAA0B,EAAE,WAAW;YACvC,kCAAkC,EAAE,SAAS;YAC7C,mCAAmC,EAAE,WAAW;YAChD,oCAAoC,EAAE,SAAS;YAC/C,oCAAoC,EAAE,qBAAqB;YAE3D,0BAA0B,EAAE,MAAM;YAElC,4BAA4B,EAAE,KAAK;yBAGnC,2BAA2B,EAAE,4BAA4B;YACzD,sBAAsB,EAAE,oCAAoC;mCAG5D,+CAA+C,EAC7C,4BAA4B;YAC9B,+BAA+B,EAAE,2BAA2B;yBAG5D,oCAAoC,EAAE,kCAAkC;YACxE,4BAA4B,EAAE,QAAQ;YACtC,kCAAkC,EAAE,KAAK;YACzC,2BAA2B,EAAE,MAAM;YACnC,+BAA+B,EAAE,uBAAuB;YACxD,sCAAsC,EAAE,uBAAuB;YAC/D,gCAAgC,EAAE,+BAA+B;uCAGjE,uCAAuC,EAAE,MAAM;mCAG/C,qCAAqC,EAAE,MAAM;YAC7C,uCAAuC,EAAE,qBAAqB;2BAG9D,sBAAsB,EAAE,MAAM;YAC9B,yBAAyB,EAAE,MAAM;YACjC,4BAA4B,EAAE,iCAAiC;YAC/D,4BAA4B,EAAE,gCAAgC;YAC9D,0BAA0B,EAAE,uBAAuB;2BAGnD,wBAAwB,EAAE,iCAAiC;kCAG3D,+BAA+B,EAAE,WAAW;YAC5C,iCAAiC,EAAE,uBAAuB;YAC1D,8BAA8B,EAAE,iCAAiC;YACjE,6BAA6B,EAAKZ,oBAAoB,GAAA,IAAI;yBAG1D,kBAAkB,EAAE,MAAM;YAC1B,qBAAqB,EAAE,KAAK;YAC5B,sBAAsB,EACpB,4DAA4D;YAC9D,yBAAyB,EAAE,2BAA2B;YACtD,uCAAuC,EAAE,gCAAgC;YACzE,6CAA6C,EAAE,uBAAuB;0BAGtE,8BAA8B,EAAE,GAAG;YACnC,2CAA2C,EAAE,GAAG;YAChD,8BAA8B,EAAE,GAAG;YACnC,qCAAqC,EAAE,GAAG;YAC1C,uBAAuB,EAAE,GAAG;uCAG5B,YAAY,EAAE,MAAM;YACpB,4CAA4C,EAAE,iBAAiB;YAC/D,4BAA4B,EAAE,SAAS;YACvC,uBAAuB,EAAE,4BAA4B;YACrD,2BAA2B,EAAE,WAAW;YACxC,2CAA2C,EAAE,WAAW;YACxD,2BAA2B,EAAE,SAAS;YACtC,kCAAkC,EAAE,SAAS;YAC7C,oCAAoC,EAAE,WAAW;YACjD,gCAAgC,EAAE,SAAS;YAC3C,qBAAqB,EAAE,SAAS;YAChC,+BAA+B,EAAE,WAAW;YAC5C,yCAAyC,EAAE,iBAAiB;YAC5D,4CAA4C,EAAE,MAAM;YACpD,uCAAuC,EAAE,SAAS;YAClD,wCAAwC,EAAE,WAAW;YACrD,yCAAyC,EAAE,qCAAqC;YAChF,yCAAyC,EAAE;;KAE9C;IACDe,aAAa,EAAE;QACb,GAAG,EAAErmB,UAAU,CAACqnB,SAAS;QACzB,qCAAqC,EAAE;YACrC,IAAI,EAAEX;;KAET;IACDP,SAAS,EAAE;QACT,GAAG,EAAEnmB,UAAU,CAACmmB,SAAS;QACzB,IAAI,EAAEO;KACP;IACDF,aAAa,EAAE;QACb,GAAG,EAAE,eAAe;QACpBtkB,MAAM,EAAE,MAAM;QACdrB,OAAO,EAAE,aAAa;QACtBqmB,eAAe,EAAE,+BAA+B;;QAEhDI,cAAc,EAAE,WAAW;QAC3B,IAAI,EAAE;YACJ,4BAA4B,EAAE;;;CAGnC,CAAC;SC3Oc/K,iBAAiBA,CAC/BzB,MAAuB,EACvBtD,OAAwB;IAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;QACvB,OAAO,CAAC;;IAGV,IAAM+P,WAAW,GAAGzM,MAAM,CAAC0M,qBAAqB,EAAE,CAACrlB,KAAK;IACxD,IAAMslB,YAAY,GAAGjQ,OAAO,CAACgQ,qBAAqB,EAAE,CAACrlB,KAAK;IAC1D,OAAOulB,IAAI,CAACC,KAAK,CAACJ,WAAW,GAAGE,YAAY,CAAC;AAC/C;AAEA,SAAgBtL,iBAAiBA,CAC/BrB,MAAuB,EACvBtD,OAAwB;IAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;QACvB,OAAO,CAAC;;IAGV,IAAMiQ,YAAY,GAAGjQ,OAAO,CAACgQ,qBAAqB,EAAE,CAACrlB,KAAK;IAC1D,IAAMylB,WAAW,GAAGpQ,OAAO,CAACgQ,qBAAqB,EAAE,CAACK,IAAI;IACxD,IAAMC,UAAU,GAAGhN,MAAM,CAAC0M,qBAAqB,EAAE,CAACK,IAAI;IAEtD,OAAOH,IAAI,CAACC,KAAK,CAAC,CAACC,WAAW,GAAGE,UAAU,IAAIL,YAAY,CAAC;AAC9D;AAEA,SAAgBpL,SAASA,CACvBvB,MAAuB,EACvBtD,OAAwB;IAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;QACvB,OAAO,CAAC;;IAGV,IAAMuQ,aAAa,GAAGvQ,OAAO,CAACgQ,qBAAqB,EAAE,CAACtlB,MAAM;IAC5D,IAAM8lB,UAAU,GAAGxQ,OAAO,CAACgQ,qBAAqB,EAAE,CAAC5N,GAAG;IACtD,IAAMqO,SAAS,GAAGnN,MAAM,CAAC0M,qBAAqB,EAAE,CAAC5N,GAAG;IACpD,OAAO8N,IAAI,CAACQ,KAAK,CAAC,CAACF,UAAU,GAAGC,SAAS,IAAIF,aAAa,CAAC;AAC7D;AAEA,SAAgBnL,UAAUA,CACxB9B,MAAuB,EACvBtD,OAAwB;IAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;QACvB,OAAO,KAAK;;IAGd,IAAMuQ,aAAa,GAAGvQ,OAAO,CAACgQ,qBAAqB,EAAE,CAACtlB,MAAM;IAC5D,IAAM8lB,UAAU,GAAGxQ,OAAO,CAACgQ,qBAAqB,EAAE,CAAC5N,GAAG;IACtD,IAAMqO,SAAS,GAAGnN,MAAM,CAAC0M,qBAAqB,EAAE,CAAC5N,GAAG;IACpD,IAAMuO,YAAY,GAAGrN,MAAM,CAAC0M,qBAAqB,EAAE,CAACtlB,MAAM;IAE1D,OAAOwlB,IAAI,CAACQ,KAAK,CAACF,UAAU,GAAGC,SAAS,GAAGF,aAAa,CAAC,GAAGI,YAAY;AAC1E;AAEA,SAASC,cAAcA,CACrBC,QAAuB,EACvBjM,GAAW,EACXkM,aAAqB;IAErB,IAAIlM,GAAG,KAAK,CAAC,CAAC,EAAE;QACd,IAAMmM,OAAO,GAAGb,IAAI,CAACC,KAAK,CAAC,CAACU,QAAQ,CAACpkB,MAAM,GAAG,CAAC,IAAIqkB,aAAa,CAAC;QACjE,IAAME,iBAAiB,GAAGD,OAAO,GAAGD,aAAa;QACjD,IAAMG,gBAAgB,GAAGJ,QAAQ,CAACpkB,MAAM,GAAG,CAAC;QAC5C,OAAOokB,QAAQ,CAACK,KAAK,CAACF,iBAAiB,EAAEC,gBAAgB,GAAG,CAAC,CAAC;;IAGhE,OAAOJ,QAAQ,CAACK,KAAK,CAACtM,GAAG,GAAGkM,aAAa,EAAE,CAAClM,GAAG,GAAG,CAAC,IAAIkM,aAAa,CAAC;AACvE;AAEA,SAASK,kBAAkBA,CACzBC,WAA0B,EAC1BC,UAAkB,EAClBP,aAAqB;IAErB,IAAMQ,OAAO,GAAGD,UAAU,GAAG,CAAC;IAE9B,IAAIC,OAAO,GAAGR,aAAa,GAAGM,WAAW,CAAC3kB,MAAM,EAAE;QAChD,OAAO,EAAE;;IAGX,OAAOmkB,cAAc,CAACQ,WAAW,EAAEE,OAAO,EAAER,aAAa,CAAC;AAC5D;AAEA,SAAgB7L,eAAeA,CAC7B4L,QAAuB,EACvBjM,GAAW,EACXkM,aAAqB,EACrBpM,UAAkB;IAElB,IAAM6M,WAAW,GAAGX,cAAc,CAACC,QAAQ,EAAEjM,GAAG,EAAEkM,aAAa,CAAC;;IAEhE,OAAOS,WAAW,CAAC7M,UAAU,CAAC,IAAI6M,WAAW,CAACA,WAAW,CAAC9kB,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;AAC/E;AAEA,SAAgB8Y,mBAAmBA,CACjC6L,WAA0B,EAC1BC,UAAkB,EAClBP,aAAqB,EACrBU,KAAa;IAEb,IAAMC,eAAe,GAAGN,kBAAkB,CACxCC,WAAW,EACXC,UAAU,EACVP,aAAa,CACd;;IAGD,OACEW,eAAe,CAACD,KAAK,CAAC,IACtBC,eAAe,CAACA,eAAe,CAAChlB,MAAM,GAAG,CAAC,CAAC,IAC3C,IAAI;AAER;AAEA,SAAgB0Y,mBAAmBA,CACjCiM,WAA0B,EAC1BC,UAAkB,EAClBP,aAAqB,EACrBU,KAAa;IAEb,IAAME,eAAe,GAAGd,cAAc,CACpCQ,WAAW,EACXC,UAAU,GAAG,CAAC,EACdP,aAAa,CACd;;IAGD,OACEY,eAAe,CAACF,KAAK,CAAC,IACtBE,eAAe,CAACA,eAAe,CAACjlB,MAAM,GAAG,CAAC,CAAC,IAC3C,IAAI;AAER;AAEA,SAAgBklB,8BAA8BA,CAC5CrO,MAAuB,EACvBuN,QAAuB,EACvBe,0BAA0B;QAA1BA,0BAA0B,KAAA,KAAA,GAAA;QAA1BA,0BAA0B,GAAG,CAAC;;IAE9B,IAAI,CAACtO,MAAM,IAAI,CAACuN,QAAQ,CAACpkB,MAAM,EAAE;QAC/B,OAAO,IAAI;;IAGb,IAAMgkB,SAAS,GAAGnN,MAAM,CAAC0M,qBAAqB,EAAE,CAAC5N,GAAG;IACpD,IAAMyP,YAAY,GAAGvO,MAAM,CAAC0M,qBAAqB,EAAE,CAAC8B,MAAM;IAC1D,IAAMC,kBAAkB,GAAGtB,SAAS,GAAGuB,cAAc,CAAC1O,MAAM,CAAC;IAE7D,IAAM2O,eAAe,GAAGpB,QAAQ,CAAC/d,IAAI,CAAC,SAAAkN,OAAO;QAC3C,IAAMwQ,UAAU,GAAGxQ,OAAO,CAACgQ,qBAAqB,EAAE,CAAC5N,GAAG;QACtD,IAAM8P,aAAa,GAAGlS,OAAO,CAACgQ,qBAAqB,EAAE,CAAC8B,MAAM;QAC5D,IAAMK,uBAAuB,GAC3BnS,OAAO,CAACoS,YAAY,GAAGR,0BAA0B;QAEnD,IAAMS,yBAAyB,GAAG7B,UAAU,GAAG2B,uBAAuB;QACtE,IAAMG,4BAA4B,GAChCJ,aAAa,GAAGC,uBAAuB;QAEzC,IAAIE,yBAAyB,GAAGN,kBAAkB,EAAE;YAClD,OAAO,KAAK;;QAGd,OACGM,yBAAyB,IAAI5B,SAAS,IACrC4B,yBAAyB,IAAIR,YAAY,IAC1CS,4BAA4B,IAAI7B,SAAS,IACxC6B,4BAA4B,IAAIT,YAAa;KAElD,CAAC;IAEF,OAAOI,eAAe,IAAI,IAAI;AAChC;AAEA,SAAgBrF,qBAAqBA,CAAC5M,OAAoB;IACxD,OAAO,CAAC,CAACA,OAAO,CAACM,kBAAkB;AACrC;AAEA,SAAS0R,cAAcA,CAACO,UAAuB;IAC7C,IAAMC,MAAM,GAAG7pB,KAAK,CAAC8pB,IAAI,CACvBF,UAAU,CAACG,gBAAgB,CAACjqB,WAAW,CAACD,UAAU,CAACmqB,KAAK,CAAC,CAAC,CAC3D;IAED,IAAA,IAAAC,EAAA,GAAA,GAAAC,OAAA,GAAoBL,MAAM,EAAAI,EAAA,GAAAC,OAAA,CAAApmB,MAAA,EAAAmmB,EAAA,GAAE;QAAvB,IAAMD,KAAK,GAAAE,OAAA,CAAAD,EAAA,CAAA;QACd,IAAMloB,MAAM,GAAGioB,KAAK,CAAC3C,qBAAqB,EAAE,CAACtlB,MAAM;;QAEnD,IAAIA,MAAM,GAAG,CAAC,EAAE;YACd,OAAOA,MAAM;;;IAIjB,OAAOojB,oBAAoB;AAC7B;AC5LO,IAAMgF,mBAAmB,GAAA,WAAA,WAAA,GAAYrqB,WAAW,CAACD,UAAU,CAACsI,KAAK,CAAG;AACpE,IAAMiiB,oBAAoB,GAAA,WAAA,GAAG;IAClCD,mBAAmB;IACnBrqB,WAAW,CAACD,UAAU,CAACwqB,OAAO,CAAC;IAAA,UACvBvqB,WAAW,CAACD,UAAU,CAACY,MAAM,CAAC,GAAA;CACvC,CAACH,IAAI,CAAC,EAAE,CAAC;AAEV,SAAgBojB,gBAAgBA,CAC9B4G,YAA6B;;IAE7B,OAAA,CAAAC,qBAAA,GAAOD,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAEnQ,OAAO,CAACgQ,mBAAmB,CAAC,KAAA,OAAAI,qBAAA,GAAI,IAAI;AAC3D;AAEA,SAQgB/I,gBAAgBA,CAC9BnK,OAAwB;IAExB,IAAMmT,eAAe,GAAGC,+BAA+B,CAACpT,OAAO,CAAC;IAChE,IAAMhO,OAAO,GAAGqhB,uBAAuB,CAACrT,OAAO,CAAC;IAEhD,IAAI,CAACmT,eAAe,EAAE;QACpB,OAAO,EAAE;;IAGX,IAAMriB,KAAK,GAAGmC,cAAc,CAACjB,OAAO,IAAA,OAAPA,OAAO,GAAImhB,eAAe,CAAC;IAExD,IAAI,CAACriB,KAAK,EAAE;QACV,OAAO,EAAE;;IAGX,OAAO;QAACA,KAAK;QAAEkB,OAAiB;KAAC;AACnC;AAEA,SAAgBshB,cAAcA,CAACtT,OAAwB;;IACrD,OAAOnD,OAAO,CACZ,CAAAmD,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEuT,OAAO,CAACT,mBAAmB,CAAC,KAAA,CACnC9S,OAAO,IAAA,OAAA,KAAA,IAAA,CAAAwT,qBAAA,GAAPxT,OAAO,CAAEyT,aAAa,KAAA,OAAA,KAAA,IAAtBD,qBAAA,CAAwBD,OAAO,CAACT,mBAAmB,CAAC,EACvD;AACH;AAEA,SAagBvC,aAAaA,CAACvQ,OAAwB;;IACpD,OAAA,CAAA0T,qBAAA,GAAO1T,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEoS,YAAY,KAAA,OAAAsB,qBAAA,GAAI,CAAC;AACnC;AAEA,SAAgBC,kBAAkBA,CAAC3T,OAAwB;IACzD,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,CAAC;;IAGV,IAAM6N,MAAM,GAAGxB,gBAAgB,CAACrM,OAAO,CAAC;IACxC,IAAMlS,QAAQ,GAAGsV,eAAe,CAACyK,MAAM,CAAC;;IAGxC,IAAM+F,WAAW,GAAGzQ,mBAAmB,CAACrV,QAAQ,CAAC;IAEjD,OAAO+lB,gBAAgB,CAAChG,MAAM,CAAC,GAAGgG,gBAAgB,CAAC/lB,QAAQ,CAAC,GAAG8lB,WAAW;AAC5E;AAEA,SAAgBzQ,mBAAmBA,CAACrV,QAAyB;;IAC3D,IAAI,CAACA,QAAQ,EAAE;QACb,OAAO,CAAC;;IAGV,IAAMgmB,oBAAoB,GAAGhmB,QAAQ,CAACimB,aAAa,CACjDtrB,WAAW,CAACD,UAAU,CAACgc,eAAe,CAAC,CACxC;IAED,OACE,CAAA,CAAAwP,qBAAA,GAAClmB,QAAQ,IAAA,OAAA,KAAA,IAARA,QAAQ,CAAEskB,YAAY,KAAA,OAAA4B,qBAAA,GAAI,CAAC,IAAA,CAAA,CAAAC,qBAAA,GAAKH,oBAAoB,IAAA,OAAA,KAAA,IAApBA,oBAAoB,CAAE1B,YAAY,KAAA,OAAA6B,qBAAA,GAAI,CAAC,CAAC;AAE7E;AAEA,SAAgBpR,kBAAkBA,CAAC/R,KAAsB;IACvD,IAAI,CAACA,KAAK,EAAE;QACV,OAAO,KAAK;;IAGd,OACEoS,0BAA0B,CAACpS,KAAK,CAAC,GACjCqS,mBAAmB,CAACC,eAAe,CAACtS,KAAK,CAAC,CAAC;AAE/C;AAEA,SAAgBwR,eAAeA,CAACH,IAAqB;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,OAAOA,IAAI,CAACoR,OAAO,CAAC9qB,WAAW,CAACD,UAAU,CAACwa,UAAU,CAAC,CAAC,GACnDb,IAAI,GACJA,IAAI,CAAC4R,aAAa,CAACtrB,WAAW,CAACD,UAAU,CAACwa,UAAU,CAAC,CAAC;AAC5D;AAEA,SAAgBE,0BAA0BA,CAACpS,KAAsB;;IAC/D,IAAI,CAACA,KAAK,EAAE;QACV,OAAO,CAAC;;IAGV,OAAO6iB,kBAAkB,CAAC7iB,KAAK,CAAC,GAAA,CAAA,CAAAojB,qBAAA,GAAA,CAAAC,kBAAA,GAAIlR,iBAAiB,CAACnS,KAAK,CAAC,KAAA,OAAA,KAAA,IAAxBqjB,kBAAA,CAA0B5R,SAAS,KAAA,OAAA2R,qBAAA,GAAI,CAAC,CAAC;AAC/E;AAEA,SAAgBjR,iBAAiBA,CAACjD,OAAwB;;IACxD,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,IAAI;;IAGb,OAAA,CAAAoU,gBAAA,GAAOpU,OAAO,CAAC8C,OAAO,CAACra,WAAW,CAACD,UAAU,CAACwa,UAAU,CAAC,CAAC,KAAA,OAAAoR,gBAAA,GAAI,IAAI;AACpE;AAEA,SAAgBC,kBAAkBA,CAACrU,OAAwB;IACzD,IAAM6N,MAAM,GAAGxB,gBAAgB,CAACrM,OAAO,CAAC;IACxC,IAAMlS,QAAQ,GAAGsV,eAAe,CAACyK,MAAM,CAAC;IAExC,OAAOyG,iBAAiB,CAACzG,MAAM,CAAC,GAAGyG,iBAAiB,CAACxmB,QAAQ,CAAC;AAChE;AAEA,SAAS+lB,gBAAgBA,CAAC7T,OAAwB;;IAChD,OAAA,CAAAuU,kBAAA,GAAOvU,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEwU,SAAS,KAAA,OAAAD,kBAAA,GAAI,CAAC;AAChC;AAEA,SAASD,iBAAiBA,CAACtU,OAAwB;;IACjD,OAAA,CAAAyU,mBAAA,GAAOzU,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAE0U,UAAU,KAAA,OAAAD,mBAAA,GAAI,CAAC;AACjC;AAEA,SAAgBpB,uBAAuBA,CAACviB,KAAsB;;IAC5D,OAAA,CAAA6jB,kBAAA,GAAOC,iBAAiB,CAACvI,gBAAgB,CAACvb,KAAK,CAAC,EAAE,SAAS,CAAC,KAAA,OAAA6jB,kBAAA,GAAI,IAAI;AACtE;AAEA,SAAgBvB,+BAA+BA,CAC7CtiB,KAAsB;IAEtB,IAAMkB,OAAO,GAAGqhB,uBAAuB,CAACviB,KAAK,CAAC;IAE9C,IAAIkB,OAAO,EAAE;QACX,OAAOD,sBAAsB,CAACC,OAAO,CAAC;;IAExC,OAAO,IAAI;AACb;AAEA,SAAgB6iB,0BAA0BA,CACxC/jB,KAAsB;IAEtB,IAAI,CAACA,KAAK,EAAE;QACV,OAAO;YACLkB,OAAO,EAAE,IAAI;YACbmhB,eAAe,EAAE;SAClB;;IAGH,OAAO;QACLnhB,OAAO,EAAEqhB,uBAAuB,CAACviB,KAAK,CAAC;QACvCqiB,eAAe,EAAEC,+BAA+B,CAACtiB,KAAK;KACvD;AACH;AAEA,SAAS8jB,iBAAiBA,CACxB5U,OAAwB,EACxBnV,GAAW;;IAEX,OAAA,CAAAiqB,mBAAA,GAAOC,cAAc,CAAC/U,OAAO,CAAC,CAACnV,GAAG,CAAC,KAAA,OAAAiqB,mBAAA,GAAI,IAAI;AAC7C;AAEA,SAASC,cAAcA,CAAC/U,OAAwB;;IAC9C,OAAA,CAAAgV,gBAAA,GAAOhV,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEiV,OAAO,KAAA,OAAAD,gBAAA,GAAI,CAAA,CAAE;AAC/B;AAEA,SAAgBE,cAAcA,CAAClV,OAAoB;IACjD,OAAOA,OAAO,CAACmV,SAAS,CAACC,QAAQ,CAAC5sB,UAAU,CAACwqB,OAAO,CAAC;AACvD;AAEA,SAAgBqC,QAAQA,CAACrV,OAAwB;IAC/C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAOA,OAAO,CAACmV,SAAS,CAACC,QAAQ,CAAC5sB,UAAU,CAACY,MAAM,CAAC;AACtD;AAEA,SAAgB8b,gBAAgBA,CAAC5B,MAAuB;IACtD,IAAI,CAACA,MAAM,EAAE;QACX,OAAO,EAAE;;IAGX,OAAO3a,KAAK,CAAC8pB,IAAI,CACfnP,MAAM,CAACoP,gBAAgB,CAACK,oBAAoB,CAAC,CAC7B;AACpB;AAEA,SAAgBnP,gBAAgBA,CAAC5D,OAAwB;IACvD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,IAAMpP,SAAS,GAAGsU,gBAAgB,CAAClF,OAAO,CAAC;IAC3C,IAAAsV,gBAAA,GAAe1kB,SAAS,CAACsgB,KAAK,CAAC,CAAC,CAAC,CAAC,EAA3BqE,IAAI,GAAAD,gBAAA,CAAA,EAAA;IACX,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,IAAI,CAACL,cAAc,CAACK,IAAI,CAAC,EAAE;QACzB,OAAOtR,gBAAgB,CAACsR,IAAI,CAAC;;IAG/B,OAAOA,IAAI;AACb;AAEA,SAAgBzR,gBAAgBA,CAAC9D,OAAoB;IACnD,IAAM7U,IAAI,GAAG6U,OAAO,CAACM,kBAAiC;IAEtD,IAAI,CAACnV,IAAI,EAAE;QACT,OAAOoY,iBAAiB,CAACQ,YAAY,CAAC/D,OAAO,CAAC,CAAC;;IAGjD,IAAI,CAACkV,cAAc,CAAC/pB,IAAI,CAAC,EAAE;QACzB,OAAO2Y,gBAAgB,CAAC3Y,IAAI,CAAC;;IAG/B,OAAOA,IAAI;AACb;AAEA,SAAgB8Y,gBAAgBA,CAACjE,OAAoB;IACnD,IAAM9U,IAAI,GAAG8U,OAAO,CAACI,sBAAqC;IAE1D,IAAI,CAAClV,IAAI,EAAE;QACT,OAAO0Y,gBAAgB,CAACM,YAAY,CAAClE,OAAO,CAAC,CAAC;;IAGhD,IAAI,CAACkV,cAAc,CAAChqB,IAAI,CAAC,EAAE;QACzB,OAAO+Y,gBAAgB,CAAC/Y,IAAI,CAAC;;IAG/B,OAAOA,IAAI;AACb;AAEA,SAAgBqY,iBAAiBA,CAACD,MAAuB;IACvD,IAAI,CAACA,MAAM,EAAE;QACX,OAAO,IAAI;;IAGb,IAAM1S,SAAS,GAAGsU,gBAAgB,CAAC5B,MAAM,CAAC;IAE1C,OAAOqO,8BAA8B,CAACrO,MAAM,EAAE1S,SAAS,EAAE,GAAG,CAAC;AAC/D;AAEA,SAAgBsT,YAAYA,CAAClE,OAAwB;IACnD,IAAMlS,QAAQ,GAAGsV,eAAe,CAACpD,OAAO,CAAC;IAEzC,IAAI,CAAClS,QAAQ,EAAE;QACb,OAAO,IAAI;;IAGb,IAAM5C,IAAI,GAAG4C,QAAQ,CAACsS,sBAAqC;IAE3D,IAAI,CAAClV,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,IAAImqB,QAAQ,CAACnqB,IAAI,CAAC,EAAE;QAClB,OAAOgZ,YAAY,CAAChZ,IAAI,CAAC;;IAG3B,OAAOA,IAAI;AACb;AAEA,SAAgB6Y,YAAYA,CAAC/D,OAAwB;IACnD,IAAMlS,QAAQ,GAAGsV,eAAe,CAACpD,OAAO,CAAC;IAEzC,IAAI,CAAClS,QAAQ,EAAE;QACb,OAAO,IAAI;;IAGb,IAAM3C,IAAI,GAAG2C,QAAQ,CAACwS,kBAAiC;IAEvD,IAAI,CAACnV,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,IAAIkqB,QAAQ,CAAClqB,IAAI,CAAC,EAAE;QAClB,OAAO4Y,YAAY,CAAC5Y,IAAI,CAAC;;IAG3B,OAAOA,IAAI;AACb;AAEA,SAAgBiY,eAAeA,CAACpD,OAAwB;IACtD,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,IAAI;;IAEb,OAAOA,OAAO,CAAC8C,OAAO,CAACra,WAAW,CAACD,UAAU,CAACsF,QAAQ,CAAC,CAAgB;AACzE;AAEA,SAAgB2W,sBAAsBA,CAACzE,OAAwB;IAC7D,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,IAAI;;IAEb,OAAOA,OAAO,CAAC8C,OAAO,CACpBra,WAAW,CAACD,UAAU,CAACgc,eAAe,CAAC,CACzB;AAClB;SCnUgBgR,gBAAgBA,CAACxjB,OAAe;IAC9C,OAAOA,OAAO,CACXX,KAAK,CAAC,GAAG,CAAC,CACVtI,GAAG,CAAC,SAAA0sB,GAAG;QAAA,OAAIC,MAAM,CAACC,aAAa,CAACC,QAAQ,CAACH,GAAG,EAAE,EAAE,CAAC,CAAC;MAAC,CACnDxsB,IAAI,CAAC,EAAE,CAAC;AACb;ACAA,IAAM4sB,gBAAgB,GAAG,eAAe;AAUxC,SAAgBC,YAAYA,CAACC,IAAqB;IAChD,IAAI;QAAA,IAAAja,OAAA,EAAAka,qBAAA,EAAAC,QAAA;QACF,IAAI,CAAA,CAAA,CAAAna,OAAA,GAACC,MAAM,KAAA,QAAND,OAAA,CAAQoa,YAAY,GAAE;YACzB,OAAO,EAAE;;QAEX,IAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAA,CAAAL,qBAAA,GAAA,CAAAC,QAAA,GACvBla,MAAM,KAAA,OAAA,KAAA,IAANka,QAAA,CAAQC,YAAY,CAACI,OAAO,CAACT,gBAAgB,CAAC,KAAA,OAAAG,qBAAA,GAAI,IAAI,CAC1C;QAEd,IAAID,IAAI,KAAKppB,cAAc,CAAC0I,QAAQ,EAAE;YACpC,OAAO8gB,MAAM,CAAC1M,IAAI,CAAC,SAACC,CAAC,EAAEC,CAAC;gBAAA,OAAKA,CAAC,CAAC4M,KAAK,GAAG7M,CAAC,CAAC6M,KAAK;cAAC;;QAGjD,OAAOJ,MAAM;KACd,CAAC,OAAAK,OAAA,EAAM;QACN,OAAO,EAAE;;AAEb;AAEA,SAAgBC,YAAYA,CAAC3lB,KAAgB,EAAEP,QAAmB;IAChE,IAAM4lB,MAAM,GAAGL,YAAY,EAAE;IAE7B,IAAM9jB,OAAO,GAAGP,YAAY,CAACX,KAAK,EAAEP,QAAQ,CAAC;IAC7C,IAAM4iB,eAAe,GAAG1hB,YAAY,CAACX,KAAK,CAAC;IAE3C,IAAI4lB,QAAQ,GAAGP,MAAM,CAACrjB,IAAI,CAAC,SAAA1C,IAAA;QAAA,IAAYumB,CAAC,GAAAvmB,IAAA,CAAV4B,OAAO;QAAA,OAAU2kB,CAAC,KAAK3kB,OAAO;MAAC;IAE7D,IAAI4kB,QAAyB;IAE7B,IAAIF,QAAQ,EAAE;QACZE,QAAQ,GAAG;YAACF,QAAQ;SAAC,CAACG,MAAM,CAACV,MAAM,CAAC9N,MAAM,CAAC,SAAAyO,CAAC;YAAA,OAAIA,CAAC,KAAKJ,QAAQ;UAAC,CAAC;KACjE,MAAM;QACLA,QAAQ,GAAG;YACT1kB,OAAO,EAAPA,OAAO;YACP+kB,QAAQ,EAAE5D,eAAe;YACzBoD,KAAK,EAAE;SACR;QACDK,QAAQ,GAAA;YAAIF,QAAQ;SAAA,CAAAG,MAAA,CAAKV,MAAM,CAAC;;IAGlCO,QAAQ,CAACH,KAAK,EAAE;IAEhBK,QAAQ,CAACnqB,MAAM,GAAGyjB,IAAI,CAAC8G,GAAG,CAACJ,QAAQ,CAACnqB,MAAM,EAAE,EAAE,CAAC;IAE/C,IAAI;QAAA,IAAAwqB,QAAA;QACF,CAAAA,QAAA,GAAAlb,MAAM,KAAA,OAAA,KAAA,IAANkb,QAAA,CAAQf,YAAY,CAACgB,OAAO,CAACrB,gBAAgB,EAAEO,IAAI,CAACe,SAAS,CAACP,QAAQ,CAAC,CAAC;;KAEzE,CAAC,OAAAQ,QAAA,EAAM;;;AAGV;SCzDgBC,gBAAgBA,CAC9BvpB,QAA+C;IAE/C,OAAOA,QAAQ,CAACA,QAAQ,KAAKf,UAAU,CAACI,MAAM;AAChD;AAEA,SAAgBmqB,aAAaA,CAACxmB,KAAyB;IACrD,OAAOA,KAAK,CAAC6C,MAAM,KAAKwK,SAAS;AACnC;SCoBgBoZ,oBAAoBA,CAClCC,YAAqD,EACrD7e,gBAAoC;IAEpC,IAAM8e,iBAAiB,6MAAG3gB,SAAAA,AAAM,EAAsB;IACtD,IAAM4O,kBAAkB,GAAGqE,qBAAqB,EAAE;IAClD,IAAM5M,gBAAgB,GAAGmB,mBAAmB,EAAE;IAC9C,IAAAmH,qBAAA,GAAoCrG,4BAA4B,EAAE,EAAzD6K,uBAAuB,GAAAxE,qBAAA,CAAA,EAAA;IAChC,IAAMK,mBAAmB,GAAGN,sBAAsB,EAAE;IACpD,IAAAkS,qBAAA,GAAyB5Y,sBAAsB,EAAE,EAA1CnB,cAAc,GAAA+Z,qBAAA,CAAA,EAAA;IACrB,IAAM3gB,YAAY,GAAG2B,qBAAqB,CAACC,gBAAgB,CAAC;IAC5D,IAAAgf,mBAAA,GAA4BnY,kBAAkB,EAAE,EAAvCI,eAAe,GAAA+X,mBAAA,CAAA,EAAA;IACxB,IAAMziB,WAAW,GAAGwF,oBAAoB,EAAE;IAC1C,IAAMkd,gBAAgB,GAAG5f,mBAAmB,EAAE;IAE9C,IAAM6f,OAAO,6MAAG/tB,cAAAA,AAAiB,EAC/B,SAAS+tB,OAAOA,CAAC9M,KAAiB;QAChC,IAAI5N,gBAAgB,CAAC/F,OAAO,EAAE;YAC5B;;QAGF0O,mBAAmB,EAAE;QAErB,IAAAgS,eAAA,GAAyBC,cAAc,CAAChN,KAAK,CAAC,EAAvCja,KAAK,GAAAgnB,eAAA,CAAA,EAAA,EAAE9lB,OAAO,GAAA8lB,eAAA,CAAA,EAAA;QAErB,IAAI,CAAChnB,KAAK,IAAI,CAACkB,OAAO,EAAE;YACtB;;QAGF,IAAMgmB,aAAa,GACjBnkB,0BAA0B,CAAC7B,OAAO,CAAC,IAAI2L,cAAc;QAEvDiC,eAAe,EAAE;QACjB6W,YAAY,CAAC3lB,KAAK,EAAEknB,aAAa,CAAC;QAClCjhB,YAAY,CACVkhB,gBAAgB,CAACnnB,KAAK,EAAEknB,aAAa,EAAEJ,gBAAgB,EAAE1iB,WAAW,CAAC,EACrE6V,KAAK,CACN;KACF,EACD;QACEpN,cAAc;QACdmI,mBAAmB;QACnB3I,gBAAgB;QAChBpG,YAAY;QACZ6I,eAAe;QACf1K,WAAW;QACX0iB,gBAAgB;KACjB,CACF;IAED,IAAMM,WAAW,OAAGpuB,oNAAAA,AAAiB,EACnC,SAASouB,WAAWA,CAACnN,KAAiB;;QACpC,IAAI0M,iBAAiB,CAACrgB,OAAO,EAAE;YAC7ByE,YAAY,CAAC4b,iBAAiB,CAACrgB,OAAO,CAAC;;QAGzC,IAAA+gB,gBAAA,GAAgBJ,cAAc,CAAChN,KAAK,CAAC,EAA9Bja,KAAK,GAAAqnB,gBAAA,CAAA,EAAA;QAEZ,IAAI,CAACrnB,KAAK,IAAI,CAACsB,kBAAkB,CAACtB,KAAK,CAAC,EAAE;YACxC;;QAGF2mB,iBAAiB,CAACrgB,OAAO,GAAA,CAAA0E,OAAA,GAAGC,MAAM,KAAA,OAAA,KAAA,IAAND,OAAA,CAAQnL,UAAU,CAAC;YAC7CwM,gBAAgB,CAAC/F,OAAO,GAAG,IAAI;YAC/BqgB,iBAAiB,CAACrgB,OAAO,GAAG+G,SAAS;YACrC2H,mBAAmB,EAAE;YACrBJ,kBAAkB,CAACqF,KAAK,CAACpJ,MAAqB,CAAC;YAC/CsI,uBAAuB,CAACnZ,KAAK,CAAC;SAC/B,EAAE,GAAG,CAAC;KACR,EACD;QACEqM,gBAAgB;QAChB2I,mBAAmB;QACnBJ,kBAAkB;QAClBuE,uBAAuB;KACxB,CACF;IACD,IAAMmO,SAAS,6MAAGtuB,cAAAA,AAAiB,EACjC,SAASsuB,SAASA;QAChB,IAAIX,iBAAiB,CAACrgB,OAAO,EAAE;YAC7ByE,YAAY,CAAC4b,iBAAiB,CAACrgB,OAAO,CAAC;YACvCqgB,iBAAiB,CAACrgB,OAAO,GAAG+G,SAAS;SACtC,MAAM,IAAIhB,gBAAgB,CAAC/F,OAAO,EAAE;;;;;;YAOnC6I,qBAAqB,CAAC;gBACpB9C,gBAAgB,CAAC/F,OAAO,GAAG,KAAK;aACjC,CAAC;;KAEL,EACD;QAAC+F,gBAAgB;KAAC,CACnB;8MAEDhG,YAAAA,AAAS,EAAC;QACR,IAAI,CAACqgB,YAAY,CAACpgB,OAAO,EAAE;YACzB;;QAEF,IAAMihB,YAAY,GAAGb,YAAY,CAACpgB,OAAO;QACzCihB,YAAY,CAACzR,gBAAgB,CAAC,OAAO,EAAEiR,OAAO,EAAE;YAC9C/Q,OAAO,EAAE;SACV,CAAC;QAEFuR,YAAY,CAACzR,gBAAgB,CAAC,WAAW,EAAEsR,WAAW,EAAE;YACtDpR,OAAO,EAAE;SACV,CAAC;QACFuR,YAAY,CAACzR,gBAAgB,CAAC,SAAS,EAAEwR,SAAS,EAAE;YAClDtR,OAAO,EAAE;SACV,CAAC;QAEF,OAAO;YACLuR,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAEtR,mBAAmB,CAAC,OAAO,EAAE8Q,OAAO,CAAC;YACnDQ,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAEtR,mBAAmB,CAAC,WAAW,EAAEmR,WAAW,CAAC;YAC3DG,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAEtR,mBAAmB,CAAC,SAAS,EAAEqR,SAAS,CAAC;SACxD;KACF,EAAE;QAACZ,YAAY;QAAEK,OAAO;QAAEK,WAAW;QAAEE,SAAS;KAAC,CAAC;AACrD;AAEA,SAASL,cAAcA,CAAChN,KAAiB;IACvC,IAAMpJ,MAAM,GAAGoJ,KAAK,IAAA,OAAA,KAAA,IAALA,KAAK,CAAEpJ,MAAqB;IAC3C,IAAI,CAAC2R,cAAc,CAAC3R,MAAM,CAAC,EAAE;QAC3B,OAAO,EAAE;;IAGX,OAAOwI,gBAAgB,CAACxI,MAAM,CAAC;AACjC;AAEA,SAASsW,gBAAgBA,CACvBnnB,KAAgB,EAChB6M,cAAyB,EACzBia,gBAA4B,EAC5B1iB,WAAwB;IAExB,IAAMzB,KAAK,GAAGxC,UAAU,CAACH,KAAK,CAAC;IAE/B,IAAIwmB,aAAa,CAACxmB,KAAK,CAAC,EAAE;QACxB,IAAMkB,QAAO,GAAGP,YAAY,CAACX,KAAK,CAAC;QACnC,OAAO;YACL6M,cAAc,EAAdA,cAAc;YACd7M,KAAK,EAAEkB,QAAO;YACdsmB,WAAW,EAAA,SAAAA;gBACT,OAAOxnB,KAAK,CAAC6C,MAAM;aACpB;YACD4kB,QAAQ,EAAEznB,KAAK,CAAC6C,MAAM;YACtB6kB,QAAQ,EAAE,IAAI;YACd/kB,KAAK,EAALA,KAAK;YACLzB,OAAO,EAAPA,QAAO;YACPD,sBAAsB,EAAEC;SACzB;;IAEH,IAAMA,OAAO,GAAGP,YAAY,CAACX,KAAK,EAAE6M,cAAc,CAAC;IAEnD,OAAO;QACLA,cAAc,EAAdA,cAAc;QACd7M,KAAK,EAAE0kB,gBAAgB,CAACxjB,OAAO,CAAC;QAChCsmB,WAAW,EAAA,SAAAA,YAACrsB,UAAAA;gBAAAA,eAAAA,KAAAA,GAAAA;gBAAAA,aAAyB2rB,gBAAgB,IAAA,OAAhBA,gBAAgB,GAAIhrB,UAAU,CAAC4C,KAAK;;YACvE,OAAO0F,WAAW,CAAClD,OAAO,EAAE/F,UAAU,CAAC;SACxC;QACDssB,QAAQ,EAAErjB,WAAW,CAAClD,OAAO,EAAE4lB,gBAAgB,IAAA,OAAhBA,gBAAgB,GAAIhrB,UAAU,CAAC4C,KAAK,CAAC;QACpEgpB,QAAQ,EAAE,KAAK;QACf/kB,KAAK,EAALA,KAAK;QACLzB,OAAO,EAAPA,OAAO;QACPD,sBAAsB,EAAEN,YAAY,CAACX,KAAK;KAC3C;AACH;SC9LgB2nB,MAAMA,CAACC,KAAY;IACjC,iNACE5uB,gBAAAA,EAAAA,UAAAA,OAAAA,MAAAA,CAAAA;QACE6uB,IAAI,EAAC;OACDD,KAAK,EAAA;QACTrsB,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACX,MAAM,EAAE6K,KAAK,CAACrsB,SAAS;QAE3CqsB,KAAK,CAAC5iB,QAAQ,CACR;AAEb;AAEA,IAAM0Y,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/BkkB,MAAM,EAAE;QACN,GAAG,EAAE,SAAS;QACd+K,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,MAAM;QAClBC,OAAO,EAAE;;CAEZ,CAAC;SCVcC,oBAAoBA,CAAA5oB,IAAA;;QAClCa,UAAU,GAAAb,IAAA,CAAVa,UAAU,EACVe,OAAO,GAAA5B,IAAA,CAAP4B,OAAO,EACP5I,MAAM,GAAAgH,IAAA,CAANhH,MAAM,EACNkB,cAAc,GAAA8F,IAAA,CAAd9F,cAAc,EAAA2uB,mBAAA,GAAA7oB,IAAA,CACd8oB,cAAc,EAAdA,cAAc,GAAAD,mBAAA,KAAA,KAAA,IAAG,IAAI,GAAAA,mBAAA,EACrBE,aAAa,GAAA/oB,IAAA,CAAb+oB,aAAa,EACbrjB,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ,EACRzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS,EAAA+sB,iBAAA,GAAAhpB,IAAA,CACTipB,YAAY,EAAZA,YAAY,GAAAD,iBAAA,KAAA,KAAA,IAAG,KAAK,GAAAA,iBAAA;IAEpB,OACEtvB,0NAAAA,EAAC2uB,MAAM,EAAA;QACLpsB,SAAS,GAAEkiB,wJAAAA,AAAE,EACXC,QAAM,CAAC1d,KAAK,EACZ1H,MAAM,IAAIM,YAAY,CAACN,MAAM,EAC7BkB,cAAc,IAAIH,uBAAuB,CAACG,cAAc,EAAA,CAAAwkB,GAAA,GAAA,CAAA,GAAAA,GAAA,CAErDtmB,UAAU,CAACwqB,OAAO,CAAA,GAAG,CAAC5pB,MAAM,IAAI,CAACkB,cAAc,EAAAwkB,GAAA,GAElD,CAAC,CAAA,CAAEqK,aAAa,IAAID,cAAc,CAAC,IAAI1K,QAAM,CAAC2K,aAAa,EAC3DE,YAAY,IAAI7K,QAAM,CAAC6K,YAAY,EACnChtB,SAAS,CACV;wBACa2F,OAAO;sBACTsnB,YAAY,CAACroB,UAAU,CAAC;0BACpBA;OAEf6E,QAAQ,CACF;AAEb;AAEA,SAASwjB,YAAYA,CAACroB,UAAoB;;IACxC,OAAOA,UAAU,CAAC,CAAC,CAAC,CAAC6b,KAAK,CAAC,OAAO,CAAC,GAAA,CAAAyM,YAAA,GAC/BtoB,UAAU,CAAC,CAAC,CAAC,KAAA,OAAAsoB,YAAA,GAAItoB,UAAU,CAAC,CAAC,CAAC,GAC9BA,UAAU,CAAC,CAAC,CAAC;AACnB;AAEA,IAAMud,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/BmH,KAAK,EAAE;QACL,GAAG,EAAEtI,UAAU,CAACsI,KAAK;QACrBse,QAAQ,EAAE,UAAU;QACpBzkB,KAAK,EAAE,2BAA2B;QAClCD,MAAM,EAAE,2BAA2B;QACnCilB,SAAS,EAAE,YAAY;QACvBtmB,OAAO,EAAE,MAAM;QACfmwB,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,QAAQ,EAAE,2BAA2B;QACrCC,SAAS,EAAE,2BAA2B;QACtCnK,YAAY,EAAE,KAAK;QACnB/lB,QAAQ,EAAE,QAAQ;QAClBgB,UAAU,EAAE,uBAAuB;QACnC,QAAQ,EAAE;YACRilB,eAAe,EAAE;SAClB;QACD,QAAQ,EAAE;YACRA,eAAe,EAAE;;KAEpB;IACD2J,YAAY,EAAE;QACZP,UAAU,EAAE,MAAM;QAClB,QAAQ,EAAE;YACRpJ,eAAe,EAAE,aAAa;YAC9BoJ,UAAU,EAAE;SACb;QACD,QAAQ,EAAE;YACRpJ,eAAe,EAAE,aAAa;YAC9BoJ,UAAU,EAAE;;KAEf;IACDK,aAAa,EAAE;QACb,GAAG,EAAE3wB,UAAU,CAAC4J,kBAAkB;QAClC,QAAQ,EAAE;YACRwnB,OAAO,EAAE,EAAE;YACXvwB,OAAO,EAAE,OAAO;YAChBsB,KAAK,EAAE,GAAG;YACVD,MAAM,EAAE,GAAG;YACXmvB,KAAK,EAAE,KAAK;YACZ/H,MAAM,EAAE,KAAK;YACb1C,QAAQ,EAAE,UAAU;YACpB0K,UAAU,EAAE,uBAAuB;YACnCC,WAAW,EAAE,uBAAuB;YACpCC,SAAS,EAAE,gBAAgB;YAC3BC,YAAY,EAAE,sDAAsD;YACpEC,MAAM,EAAE;SACT;QACD,cAAc,EAAE;YACdD,YAAY,EAAE;;;CAGnB,CAAC;AChHK,IAAME,WAAW,GAAA,WAAA,GAAGjxB,UAAU,CAACS,MAAM,CAAC;IAC3CywB,QAAQ,EAAE;QACR,GAAG,EAAE5xB,UAAU,CAAC4xB,QAAQ;QACxBC,QAAQ,EAAE;KACX;IACDC,MAAM,EAAE;QACNC,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,QAAQ;QACrBnxB,OAAO,EAAE;;CAEZ,CAAC;SCLcoxB,QAAQA,CAAArqB,IAAA;QACtB0B,SAAS,GAAA1B,IAAA,CAAT0B,SAAS,EACTxF,KAAK,GAAA8D,IAAA,CAAL9D,KAAK,EAAAouB,aAAA,GAAAtqB,IAAA,CACLuqB,QAAQ,EAARA,QAAQ,GAAAD,aAAA,KAAA,KAAA,IAAG,KAAK,GAAAA,aAAA,EAChB/mB,MAAM,GAAAvD,IAAA,CAANuD,MAAM,EACNinB,OAAO,GAAAxqB,IAAA,CAAPwqB,OAAO,EACPvuB,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAUT,iNACEvC,gBAAAA,EAAAA,OAAAA;QACE4jB,GAAG,EAAE/Z,MAAM;QACXknB,GAAG,EAAE/oB,SAAS;QACdzF,SAAS,EAAEkiB,yJAAAA,AAAE,EAACC,QAAM,CAACsM,SAAS,EAAEX,WAAW,CAACC,QAAQ,EAAED,WAAW,CAACG,MAAM,EAAEjuB,SAAS,CAAC;QACpF0uB,OAAO,EAAEJ,QAAQ,GAAG,MAAM,GAAG,OAAO;QACpCC,OAAO,EAAEA,OAAO;QAChBtuB,KAAK,EAAEA;MACP;AAEN;AAEA,IAAMkiB,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/BmxB,SAAS,EAAE;QACT,GAAG,EAAE,eAAe;QACpBpB,QAAQ,EAAE,2BAA2B;QACrCC,SAAS,EAAE,2BAA2B;QACtCqB,QAAQ,EAAE,2BAA2B;QACrCC,SAAS,EAAE,2BAA2B;QACtCC,OAAO,EAAE;;CAEZ,CAAC;SCrCcC,WAAWA,CAAA/qB,IAAA;QACzB4B,OAAO,GAAA5B,IAAA,CAAP4B,OAAO,EACP1F,KAAK,GAAA8D,IAAA,CAAL9D,KAAK,EACLD,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAMT,iNACEvC,gBAAAA,EAAAA,QAAAA;QACEuC,SAAS,EAAEkiB,yJAAAA,AAAE,EACXC,QAAM,CAAC4M,WAAW,EAClBjB,WAAW,CAACG,MAAM,EAClBH,WAAW,CAACC,QAAQ,EACpB/tB,SAAS,CACV;wBACa2F,OAAO;QACrB1F,KAAK,EAAEA;OAENkpB,gBAAgB,CAACxjB,OAAO,CAAC,CACrB;AAEX;AAEA,IAAMwc,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/ByxB,WAAW,EAAE;QACX,GAAG,EAAE,kBAAkB;QACvBxL,UAAU,EACR,0JAA0J;QAC5JR,QAAQ,EAAE,UAAU;QACpBiM,UAAU,EAAE,MAAM;QAClBhB,QAAQ,EAAE,uBAAuB;QACjCiB,SAAS,EAAE,QAAQ;QACnBf,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,QAAQ;QACrBe,aAAa,EAAE,GAAG;QAClBL,OAAO,EAAE;;CAEZ,CAAC;SChCcM,aAAaA,CAAAprB,IAAA;QAC3BU,KAAK,GAAAV,IAAA,CAALU,KAAK,EACLkB,OAAO,GAAA5B,IAAA,CAAP4B,OAAO,EACP/F,UAAU,GAAAmE,IAAA,CAAVnE,UAAU,EACVwvB,IAAI,GAAArrB,IAAA,CAAJqrB,IAAI,EACJd,QAAQ,GAAAvqB,IAAA,CAARuqB,QAAQ,EAAAe,gBAAA,GAAAtrB,IAAA,CACR8E,WAAW,EAAXA,WAAW,GAAAwmB,gBAAA,KAAA,KAAA,IAAGhpB,iBAAiB,GAAAgpB,gBAAA,EAC/BrvB,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAET,IAAAsvB,qBAAA,GAAsC3c,8BAA8B,EAAE,EAA7D4c,yBAAyB,GAAAD,qBAAA,CAAA,EAAA;IAElC,IAAMrvB,KAAK,GAAG,CAAA,CAAyB;IACvC,IAAImvB,IAAI,EAAE;QACRnvB,KAAK,CAAC3B,KAAK,GAAG2B,KAAK,CAAC5B,MAAM,GAAG4B,KAAK,CAAC+tB,QAAQ,GAAMoB,IAAI,GAAA,IAAI;;IAG3D,IAAMI,aAAa,GAAG/qB,KAAK,GAAGA,KAAK,GAAGmC,cAAc,CAACjB,OAAO,CAAC;IAE7D,IAAI,CAAC6pB,aAAa,EAAE;QAClB,OAAO,IAAI;;IAGb,IAAIvE,aAAa,CAACuE,aAAa,CAAC,EAAE;QAChC,iNACE/xB,gBAAAA,EAAC2wB,QAAQ,EAAA;YACPnuB,KAAK,EAAEA,KAAK;YACZwF,SAAS,EAAEE,OAAO;YAClB/F,UAAU,EAAEW,UAAU,CAACugB,MAAM;YAC7BwN,QAAQ,EAAEA,QAAQ;YAClBhnB,MAAM,EAAEkoB,aAAa,CAACloB,MAAM;YAC5BinB,OAAO,EAAEA,OAAO;YAChBvuB,SAAS,EAAEA;UACX;;IAIN,gNACEvC,iBAAAA,EAAAA,qMAAAA,CAAAA,WAAAA,EAAAA,MACGmC,UAAU,KAAKW,UAAU,CAACugB,MAAM,6MAC/BrjB,gBAAAA,EAACqxB,WAAW,EAAA;QAACnpB,OAAO,EAAEA,OAAO;QAAE1F,KAAK,EAAEA,KAAK;QAAED,SAAS,EAAEA;MAAa,6MAErEvC,gBAAAA,EAAC2wB,QAAQ,EAAA;QACPnuB,KAAK,EAAEA,KAAK;QACZwF,SAAS,EAAEA,SAAS,CAAC+pB,aAAa,CAAC;QACnC5vB,UAAU,EAAEA,UAAU;QACtB0uB,QAAQ,EAAEA,QAAQ;QAClBhnB,MAAM,EAAEuB,WAAW,CAAClD,OAAO,EAAE/F,UAAU,CAAC;QACxC2uB,OAAO,EAAEA,OAAO;QAChBvuB,SAAS,EAAEA;MAEd,CACA;;IAGL,SAASuuB,OAAOA;QACdgB,yBAAyB,CAAC,SAAA1wB,IAAI;YAAA,OAAI,IAAIoK,GAAG,CAACpK,IAAI,CAAC,CAAC6J,GAAG,CAAC/C,OAAO,CAAC;UAAC;;AAEjE;SCpDgB8pB,cAAcA,CAAA1rB,IAAA;QAC5BU,KAAK,GAAAV,IAAA,CAALU,KAAK,EACLkB,OAAO,GAAA5B,IAAA,CAAP4B,OAAO,EACP5I,MAAM,GAAAgH,IAAA,CAANhH,MAAM,EACNkB,cAAc,GAAA8F,IAAA,CAAd9F,cAAc,EACd2B,UAAU,GAAAmE,IAAA,CAAVnE,UAAU,EAAAgtB,mBAAA,GAAA7oB,IAAA,CACV8oB,cAAc,EAAdA,cAAc,GAAAD,mBAAA,KAAA,KAAA,IAAG,IAAI,GAAAA,mBAAA,EACrBwC,IAAI,GAAArrB,IAAA,CAAJqrB,IAAI,EACJd,QAAQ,GAAAvqB,IAAA,CAARuqB,QAAQ,EACRzlB,WAAW,GAAA9E,IAAA,CAAX8E,WAAW,EACX7I,SAAS,GAAA+D,IAAA,CAAT/D,SAAS,EAAA+sB,iBAAA,GAAAhpB,IAAA,CACTipB,YAAY,EAAZA,YAAY,GAAAD,iBAAA,KAAA,KAAA,IAAG,KAAK,GAAAA,iBAAA;IAEpB,IAAMD,aAAa,GAAG/mB,kBAAkB,CAACtB,KAAK,CAAC;IAE/C,QACEhH,yNAAAA,EAACkvB,oBAAoB,EAAA;QACnBG,aAAa,EAAEA,aAAa;QAC5BD,cAAc,EAAEA,cAAc;QAC9B9vB,MAAM,EAAEA,MAAM;QACdkB,cAAc,EAAEA,cAAc;QAC9B2G,UAAU,EAAEA,UAAU,CAACH,KAAK,CAAC;QAC7BkB,OAAO,EAAEA,OAAO;QAChBqnB,YAAY,EAAEA;iNAEdvvB,gBAAAA,EAAC0xB,aAAa,EAAA;QACZxpB,OAAO,EAAEA,OAAO;QAChBlB,KAAK,EAAEA,KAAK;QACZ2qB,IAAI,EAAEA,IAAI;QACVxvB,UAAU,EAAEA,UAAU;QACtB0uB,QAAQ,EAAEA,QAAQ;QAClBzlB,WAAW,EAAEA,WAAW;QACxB7I,SAAS,EAAEA;MACX,CACmB;AAE3B;;SC/CgB0vB,OAAOA;IACrB,IAAA7N,qBAAA,GAA6BxP,qBAAqB,EAAE,EAA3Csd,gBAAgB,GAAA9N,qBAAA,CAAA,EAAA;IACzB,iNACEpkB,gBAAAA,EAAC2uB,MAAM,EAAA;sBACM,iBAAiB;QAC5BwD,KAAK,EAAC,iBAAiB;QACvBC,QAAQ,EAAE,CAAC;QACX7vB,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAAC2N,QAAQ,CAAC;QAC9BtE,OAAO,EAAE,SAAAA;YAAA,OAAMmE,gBAAgB,CAAC,KAAK,CAAC;;MACtC;AAEN;AAEA,IAAMxN,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,WAAA,GAAAC,QAAA,CAAA;IAC9BuyB,QAAQ,EAAE;QACR9B,QAAQ,EAAE,MAAM;QAChBa,OAAO,EAAE,MAAM;QACfkB,KAAK,EAAE,uBAAuB;QAC9B5M,YAAY,EAAE,KAAK;QACnB8L,SAAS,EAAE,QAAQ;QACnBD,UAAU,EAAE,MAAM;QAClB1wB,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdrB,OAAO,EAAE,MAAM;QACfowB,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpB/uB,UAAU,EAAE,mCAAmC;QAC/C,QAAQ,EAAE;YACRmvB,OAAO,EAAE,EAAE;YACXoB,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,MAAM;YACjBoB,eAAe,EAAA,SAASC,IAAI,GAAA,GAAG;YAC/B5M,eAAe,EAAE,aAAa;YAC9B6M,gBAAgB,EAAE,WAAW;YAC7BC,cAAc,EAAE,MAAM;YACtBnyB,mBAAmB,EAAE;SACtB;QACD,QAAQ,EAAE;YACR+xB,KAAK,EAAE,4BAA4B;YACnC1M,eAAe,EAAE,2CAA2C;YAC5D,QAAQ,EAAE;gBACRrlB,mBAAmB,EAAE;;SAExB;QACD,QAAQ,EAAE;YACR+xB,KAAK,EAAE,4BAA4B;YACnC1M,eAAe,EAAE,2CAA2C;YAC5D,QAAQ,EAAE;gBACRrlB,mBAAmB,EAAE;;;;AAG1B,GAAA,WAAA,GACEO,QAAQ,CAAC,UAAU,EAAE;IACtB,QAAQ,EAAE;QAAEP,mBAAmB,EAAE;KAAS;IAC1C,cAAc,EAAE;QAAEA,mBAAmB,EAAE;;CACxC,CAAC,CACH,CAAC;SC7CcoyB,SAASA;IACvB,IAAAvO,qBAAA,GAAwBxP,qBAAqB,EAAE,EAAxCge,aAAa,GAAAxO,qBAAA,CAAA,EAAA;IACpB,IAAM7M,YAAY,GAAGQ,eAAe,EAAE;IACtC,IAAMtM,SAAS,GAAGiF,kBAAkB,EAAE;IACtC+c,oBAAoB,CAAClW,YAAY,EAAEhK,kBAAkB,CAACyB,SAAS,CAAC;IAChE,IAAM7M,UAAU,GAAG+L,mBAAmB,EAAE;IACxC,IAAMxC,oBAAoB,GAAGoC,uBAAuB,EAAE;IACtD,IAAM1C,WAAW,GAAGwF,oBAAoB,EAAE;IAE1C,IAAI,CAACgiB,aAAa,EAAE;QAClB,OAAO,IAAI;;IAGb,iNACE5yB,gBAAAA,EAAAA,MAAAA;QACEuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACmO,IAAI,EAAE,CAACD,aAAa,IAAIhzB,YAAY,CAACN,MAAM,CAAC;QACjE6lB,GAAG,EAAE5N;OAEJ9L,SAAS,CAACxM,GAAG,CAAC,SAAA6zB,QAAQ;QAAA,iNACrB9yB,gBAAAA,EAAAA,MAAAA;YAAIe,GAAG,EAAE+xB;WACP9yB,0NAAAA,EAACgyB,cAAc,EAAA;YACbhrB,KAAK,EAAEmC,cAAc,CAAC2pB,QAAQ,CAAc;YAC5C3wB,UAAU,EAAEA,UAAU;YACtB+F,OAAO,EAAE4qB,QAAQ;YACjB1D,cAAc,EAAE,KAAK;YACrB7sB,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACqO,WAAW,CAAC;YACjCxD,YAAY,EAAA;YACZnkB,WAAW,EAAEA;UACb,CACC;KACN,CAAC,EACDM,oBAAoB,6MACnB1L,gBAAAA,EAAAA,MAAAA,gNACEA,gBAAAA,EAACiyB,OAAO,EAAA,KAAG,CACR,GACH,IAAI,CACL;AAET;AAEA,IAAMvN,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/BgzB,IAAI,EAAE;QACJG,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAE,GAAG;QACX7B,OAAO,EAAE,OAAO;QAChB7xB,OAAO,EAAE,MAAM;QACfowB,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpB9uB,MAAM,EAAE;KACT;IACDmyB,WAAW,EAAE;QACX,QAAQ,EAAE;YACR7C,SAAS,EAAE;SACZ;QACD,QAAQ,EAAE;YACRA,SAAS,EAAE;SACZ;QACD,SAAS,EAAE;YACTA,SAAS,EAAE;SACZ;QACDvvB,UAAU,EAAE;;CAEf,CAAC;SC5EcuyB,WAAWA,CAAChc,OAAmB;IAC7C,IAAM8E,mBAAmB,GAAGN,sBAAsB,EAAE;IAEpDrO,sNAAAA,AAAS,EAAC;QACR,IAAMwP,OAAO,GAAG3F,OAAO,CAAC5J,OAAO;QAC/B,IAAI,CAACuP,OAAO,EAAE;YACZ;;QAGFA,OAAO,CAACC,gBAAgB,CAAC,QAAQ,EAAEqW,QAAQ,EAAE;YAC3CnW,OAAO,EAAE;SACV,CAAC;QAEF,SAASmW,QAAQA;YACfnX,mBAAmB,EAAE;;QAGvB,OAAO;YACLa,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,QAAQ,EAAEkW,QAAQ,CAAC;SACjD;KACF,EAAE;QAACjc,OAAO;QAAE8E,mBAAmB;KAAC,CAAC;AACpC;SCrBgBoX,gBAAgBA;IAC9B,IAAAvB,qBAAA,GAAiC3c,8BAA8B,EAAE,EAA1Dme,sBAAsB,GAAAxB,qBAAA,CAAA,EAAA;IAC7B,IAAMyB,eAAe,GAAGnU,kBAAkB,EAAE;IAE5C,OAAO,SAACnY,KAAgB;QACtB,IAAMkB,OAAO,GAAGP,YAAY,CAACX,KAAK,CAAC;QAEnC,IAAMusB,YAAY,GAAGF,sBAAsB,CAAClhB,GAAG,CAACjK,OAAO,CAAC;QACxD,IAAMsrB,WAAW,GAAGF,eAAe,CAACprB,OAAO,CAAC;QAE5C,OAAO;YACLqrB,YAAY,EAAZA,YAAY;YACZC,WAAW,EAAXA,WAAW;YACXl0B,MAAM,EAAEi0B,YAAY,IAAIC;SACzB;KACF;AACH;SCAgBC,aAAaA,CAAAntB,IAAA;QAC3BotB,cAAc,GAAAptB,IAAA,CAAdotB,cAAc,EACd1nB,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ,EACR1M,MAAM,GAAAgH,IAAA,CAANhH,MAAM,EACNkB,cAAc,GAAA8F,IAAA,CAAd9F,cAAc;IAEd,IAAMwD,QAAQ,GAAGK,0BAA0B,CAACqvB,cAAc,CAAC;IAC3D,IAAMC,YAAY,GAAGrvB,8BAA8B,CAACovB,cAAc,CAAC;IAEnE,gNACE1zB,iBAAAA,EAAAA,MAAAA;QACEuC,SAAS,sJAAEkiB,KAAAA,AAAE,EACXC,QAAM,CAAC1gB,QAAQ,EACf1E,MAAM,IAAIM,YAAY,CAACN,MAAM,EAC7BkB,cAAc,IAAIH,uBAAuB,CAACG,cAAc,CACzD;qBACUwD,QAAQ;sBACP2vB;iNAEZ3zB,gBAAAA,EAAAA,MAAAA;QAAIuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACmE,KAAK;OAAI8K,YAAY,CAAM,4MACpD3zB,gBAAAA,EAAAA,OAAAA;QAAKuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAAChK,eAAe;OAAI1O,QAAQ,CAAO,CACzD;AAET;AAEA,IAAM0Y,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/BmE,QAAQ,EAAE;QACR,GAAG,EAAEtF,UAAU,CAACsF,QAAQ;QACxB,0BAA0B,EAAE;YAC1BzE,OAAO,EAAE;;KAEZ;IACDmb,eAAe,EAAE;QACf,GAAG,EAAEhc,UAAU,CAACgc,eAAe;QAC/Bnb,OAAO,EAAE,MAAM;QACfq0B,OAAO,EAAE,GAAG;QACZC,mBAAmB,EAAE,8CAA8C;QACnElE,cAAc,EAAE,eAAe;QAC/BsD,MAAM,EAAE,6BAA6B;QACrC3N,QAAQ,EAAE;KACX;IACDuD,KAAK,EAAE;QACL,GAAG,EAAEnqB,UAAU,CAACmqB,KAAK;QACrB6G,UAAU,EAAE,QAAQ;;QAEpB1J,cAAc,EAAE,WAAW;QAC3BJ,eAAe,EAAE,oCAAoC;QACrD0M,KAAK,EAAE,sCAAsC;QAC7C/yB,OAAO,EAAE,MAAM;QACfgxB,QAAQ,EAAE,MAAM;QAChBuD,UAAU,EAAE,MAAM;QAClBlzB,MAAM,EAAE,kCAAkC;QAC1CqyB,MAAM,EAAE,GAAG;QACX7B,OAAO,EAAE,mCAAmC;QAC5C9L,QAAQ,EAAE,QAAQ;QAClByO,aAAa,EAAE,YAAY;QAC3Bzb,GAAG,EAAE,GAAG;QACRzX,KAAK,EAAE,MAAM;QACbuvB,MAAM,EAAE;;CAEX,CAAC;AChFF,IAAI4D,aAAa,GAAG,KAAK;AAEzB,SAAgBC,gBAAgBA;IAC9B,IAAA3nB,eAAA,6MAAkCtM,WAAAA,AAAc,EAACg0B,aAAa,CAAC,EAAxDE,SAAS,GAAA5nB,eAAA,CAAA,EAAA,EAAE6nB,YAAY,GAAA7nB,eAAA,CAAA,EAAA;8MAE9BtM,YAAAA,AAAe,EAAC;QACdm0B,YAAY,CAAC,IAAI,CAAC;QAClBH,aAAa,GAAG,IAAI;KACrB,EAAE,EAAE,CAAC;IAEN,OAAOE,SAAS,IAAIF,aAAa;AACnC;SCOgBI,SAASA,CAAA9tB,IAAA;QAAGotB,cAAc,GAAAptB,IAAA,CAAdotB,cAAc;IACxC,IAAA7F,mBAAA,GAA2BnY,kBAAkB,EAAE,EAAxCE,gBAAgB,GAAAiY,mBAAA,CAAA,EAAA;IACvB,IAAMqG,SAAS,GAAGD,gBAAgB,EAAE;IACpC,IAAMI,yBAAyB,GAAG9kB,4BAA4B,EAAE;IAChE,IAAMnE,WAAW,GAAGwF,oBAAoB,EAAE;IAC1C,IAAM0jB,SAAS,6MAAGt0B,UAAAA,AAAa,EAC7B;QAAA,IAAAu0B,aAAA;QAAA,OAAA,CAAAA,aAAA,GAAMvI,YAAY,CAACqI,yBAAyB,CAAC,KAAA,OAAAE,aAAA,GAAI,EAAE;;IAEnD;QAAC3e,gBAAgB;QAAEye,yBAAyB;KAAC,CAC9C;IACD,IAAMlyB,UAAU,GAAG+L,mBAAmB,EAAE;IAExC,IAAI,CAACgmB,SAAS,EAAE;QACd,OAAO,IAAI;;IAGb,iNACEl0B,gBAAAA,EAACyzB,aAAa,EAAA;QACZC,cAAc,EAAEA,cAAc;QAC9BlzB,cAAc,EAAA;QACdlB,MAAM,EAAEg1B,SAAS,CAAC3xB,MAAM,KAAK;OAE5B2xB,SAAS,CAACr1B,GAAG,CAAC,SAAAu1B,aAAa;QAC1B,IAAMxtB,KAAK,GAAGmC,cAAc,CAACqrB,aAAa,CAACvH,QAAQ,CAAC;QAEpD,IAAI,CAACjmB,KAAK,EAAE;YACV,OAAO,IAAI;;QAGb,iNACEhH,gBAAAA,EAACgyB,cAAc,EAAA;YACb5C,cAAc,EAAE,KAAK;YACrBlnB,OAAO,EAAEssB,aAAa,CAACtsB,OAAO;YAC9B/F,UAAU,EAAEA,UAAU;YACtB6E,KAAK,EAAEA,KAAK;YACZjG,GAAG,EAAEyzB,aAAa,CAACtsB,OAAO;YAC1BkD,WAAW,EAAEA;UACb;KAEL,CAAC,CACY;AAEpB;SCjCgBqpB,SAASA;IACvB,IAAM3pB,UAAU,GAAGwD,mBAAmB,EAAE;IACxC,IAAMomB,yBAAyB,6MAAG10B,SAAAA,AAAY,EAAC,CAAC,CAAC;IAEjD,iNACEA,gBAAAA,EAAAA,MAAAA;QAAIuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACiQ,SAAS;OAC/B7pB,UAAU,CAAC7L,GAAG,CAAC,SAAAy0B,cAAc;QAC5B,IAAM1vB,QAAQ,GAAGK,0BAA0B,CAACqvB,cAAc,CAAC;QAE3D,IAAI1vB,QAAQ,KAAKf,UAAU,CAACG,SAAS,EAAE;YACrC,iNAAOpD,gBAAAA,EAACo0B,SAAS,EAAA;gBAACrzB,GAAG,EAAEiD,QAAQ;gBAAE0vB,cAAc,EAAEA;cAAkB;;QAGrE,iNACE1zB,gBAAAA,wMAACA,WAAc,EAAA;YAACe,GAAG,EAAEiD;WACnBhE,0NAAAA,EAAC40B,cAAc,EAAA;YACb5wB,QAAQ,EAAEA,QAAQ;YAClB0vB,cAAc,EAAEA,cAAc;YAC9BgB,yBAAyB,EAAEA;UAC3B,CACa;KAEpB,CAAC,CACC;AAET;AAEA,SAASE,cAAcA,CAAAtuB,IAAA;QACrBtC,QAAQ,GAAAsC,IAAA,CAARtC,QAAQ,EACR0vB,cAAc,GAAAptB,IAAA,CAAdotB,cAAc,EACdgB,yBAAyB,GAAApuB,IAAA,CAAzBouB,yBAAyB;IAMzB,IAAMG,aAAa,GAAGzB,gBAAgB,EAAE;IACxC,IAAM9wB,cAAc,GAAGmN,uBAAuB,EAAE;IAChD,IAAMtN,UAAU,GAAG+L,mBAAmB,EAAE;IACxC,IAAMgG,iBAAiB,GAAGkB,oBAAoB,EAAE;IAChD,IAAAwY,qBAAA,GAAyB5Y,sBAAsB,EAAE,EAA1CnB,cAAc,GAAA+Z,qBAAA,CAAA,EAAA;IACrB,IAAM9a,iBAAiB,GAAGF,oBAAoB,EAAE;IAChD,IAAMxH,WAAW,GAAGwF,oBAAoB,EAAE;IAC1C,IAAMwe,cAAc,GAAG,CAACphB,0BAA0B,EAAE;;;IAIpD,IAAM8mB,YAAY,GAChB,CAAC5gB,iBAAiB,IAAIwgB,yBAAyB,CAACpnB,OAAO,GAAG,CAAC,GACvD,EAAE,GACF7E,gBAAgB,CAACzE,QAAQ,CAAC;IAEhC,IAAI8wB,YAAY,CAACnyB,MAAM,GAAG,CAAC,EAAE;QAC3B+xB,yBAAyB,CAACpnB,OAAO,EAAE;;IAGrC,IAAIynB,aAAa,GAAG,CAAC;IAErB,IAAMpsB,MAAM,GAAGmsB,YAAY,CAAC71B,GAAG,CAAC,SAAA+H,KAAK;QACnC,IAAMkB,OAAO,GAAGP,YAAY,CAACX,KAAK,EAAE6M,cAAc,CAAC;QACnD,IAAAmhB,cAAA,GAA8CH,aAAa,CAAC7tB,KAAK,CAAC,EAA1DusB,YAAY,GAAAyB,cAAA,CAAZzB,YAAY,EAAEC,WAAW,GAAAwB,cAAA,CAAXxB,WAAW,EAAEl0B,MAAM,GAAA01B,cAAA,CAAN11B,MAAM;QAEzC,IAAM21B,YAAY,GAAGniB,iBAAiB,CAAC9L,KAAK,CAAC;QAE7C,IAAI1H,MAAM,IAAI21B,YAAY,EAAE;YAC1BF,aAAa,EAAE;;QAGjB,IAAIE,YAAY,EAAE;YAChB,OAAO,IAAI;;QAGb,iNACEj1B,gBAAAA,EAACgyB,cAAc,EAAA;YACb5C,cAAc,EAAEA,cAAc;YAC9BruB,GAAG,EAAEmH,OAAO;YACZlB,KAAK,EAAEA,KAAK;YACZkB,OAAO,EAAEA,OAAO;YAChB5I,MAAM,EAAEi0B,YAAY;YACpB/yB,cAAc,EAAEgzB,WAAW;YAC3BrxB,UAAU,EAAEA,UAAU;YACtB0uB,QAAQ,EAAEvuB,cAAc;YACxB8I,WAAW,EAAEA;UACb;KAEL,CAAC;IAEF,iNACEpL,gBAAAA,EAACyzB,aAAa,EAAA;QACZC,cAAc,EAAEA,cAAc;;;QAG9Bp0B,MAAM,EAAEy1B,aAAa,KAAKpsB,MAAM,CAAChG,MAAAA;OAEhCgG,MAAM,CACO;AAEpB;AAEA,IAAM+b,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/B80B,SAAS,EAAE;QACT,GAAG,EAAEj2B,UAAU,CAACi2B,SAAS;QACzB3B,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAE,GAAG;QACX7B,OAAO,EAAE;;CAEZ,CAAC;;ACtGF,IAAK8D,SAGJ;AAHD,CAAA,SAAKA,SAAS;IACZA,SAAAA,CAAAA,SAAAA,CAAAA,KAAAA,GAAAA,EAAAA,GAAAA,IAAE;IACFA,SAAAA,CAAAA,SAAAA,CAAAA,OAAAA,GAAAA,EAAAA,GAAAA,MAAI;AACN,CAAC,EAHIA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA;AAKd,sCAAA;AACA,SAAgBC,oBAAoBA;IAClC,IAAMle,gBAAgB,GAAGU,mBAAmB,EAAE;IAC9C,IAAML,kBAAkB,GAAGa,qBAAqB,EAAE;IAClD,IAAAwD,qBAAA,GAAgBrG,4BAA4B,EAAE,EAAvCtO,KAAK,GAAA2U,qBAAA,CAAA,EAAA;IACZ,IAAMxZ,UAAU,GAAG+L,mBAAmB,EAAE;IAExC,IAAAknB,qBAAA,GAAqCC,qBAAqB,CACxD/d,kBAAkB,CACnB,EAFOge,MAAM,GAAAF,qBAAA,CAANE,MAAM,EAAEC,gBAAgB,GAAAH,qBAAA,CAAhBG,gBAAgB;IAGhC,IAAMrV,mBAAmB,GAAGtI,sBAAsB,EAAE;IACpD,IAAM4d,eAAe,GAAGC,eAAe,CAACne,kBAAkB,CAAC;IAC3D,IAAMlM,WAAW,GAAGwF,oBAAoB,EAAE;IAE1C,IAAMmT,MAAM,GAAGxB,gBAAgB,CAACtL,gBAAgB,CAAC3J,OAAO,CAAC;IAEzD,IAAM4b,OAAO,GAAGnW,OAAO,CACrB/L,KAAK,IACH+c,MAAM,IACNzb,kBAAkB,CAACtB,KAAK,CAAC,IACzB+c,MAAM,CAACsH,SAAS,CAACC,QAAQ,CAAC5sB,UAAU,CAAC4J,kBAAkB,CAAC,CAC3D;QAED+E,kNAAAA,AAAS,EAAC;QACR,IAAI,CAAC6b,OAAO,EAAE;YACZ;;QAGF3P,sBAAsB,CAACjC,kBAAkB,CAAChK,OAAO,CAAC;KACnD,EAAE;QAACgK,kBAAkB;QAAE4R,OAAO;QAAEjS,gBAAgB;KAAC,CAAC;IAEnD,IAAIqB,GAAG,EAAEod,YAAY;IAErB,IAAI,CAACxM,OAAO,IAAIjS,gBAAgB,CAAC3J,OAAO,EAAE;QACxC4S,mBAAmB,CAAC,IAAI,CAAC;KAC1B,MAAM;QACL5H,GAAG,GAAGgd,MAAM,EAAE;QACdI,YAAY,GAAGF,eAAe,EAAE;;IAGlC,iNACEx1B,gBAAAA,EAAAA,OAAAA;QACEmlB,GAAG,EAAE7N,kBAAkB;QACvB/U,SAAS,EAAEkiB,yJAAAA,AAAE,EACXC,QAAM,CAACzL,eAAe,EACtBsc,gBAAgB,EAAE,KAAKL,SAAS,CAACS,IAAI,IAAIjR,QAAM,CAACkR,UAAU,EAC1D1M,OAAO,IAAIxE,QAAM,CAACwE,OAAO,CAC1B;QACD1mB,KAAK,EAAE;YAAE8V,GAAG,EAAHA;;OAER4Q,OAAO,IAAIliB,KAAK,GACb;QAACW,YAAY,CAACX,KAAK,CAAC;KAAC,CAClB+lB,MAAM,CAAClkB,eAAe,CAAC7B,KAAK,CAAC,CAAC,CAC9BogB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXnoB,GAAG,CAAC,SAAAiJ,OAAO;QAAA,iNACVlI,gBAAAA,EAACgyB,cAAc,EAAA;YACbjxB,GAAG,EAAEmH,OAAO;YACZlB,KAAK,EAAEA,KAAK;YACZkB,OAAO,EAAEA,OAAO;YAChB/F,UAAU,EAAEA,UAAU;YACtBitB,cAAc,EAAE,KAAK;YACrBhkB,WAAW,EAAEA;UACb;KACH,CAAC,GACJ,IAAI,4MACRpL,gBAAAA,EAAAA,OAAAA;QAAKuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACmR,OAAO,CAAC;QAAErzB,KAAK,EAAEkzB;MAAgB,CACvD;AAEV;AAEA,SAASD,eAAeA,CAACne,kBAAgD;IACvE,IAAML,gBAAgB,GAAGU,mBAAmB,EAAE;IAC9C,OAAO,SAAS6d,eAAeA;QAC7B,IAAMhzB,KAAK,GAAwB,CAAA,CAAE;QACrC,IAAI,CAAC8U,kBAAkB,CAAChK,OAAO,EAAE;YAC/B,OAAO9K,KAAK;;QAGd,IAAIyU,gBAAgB,CAAC3J,OAAO,EAAE;YAC5B,IAAMyW,MAAM,GAAGxB,gBAAgB,CAACtL,gBAAgB,CAAC3J,OAAO,CAAC;YAEzD,IAAMsd,UAAU,GAAGL,kBAAkB,CAACxG,MAAM,CAAC;YAE7C,IAAI,CAACA,MAAM,EAAE;gBACX,OAAOvhB,KAAK;;;YAIdA,KAAK,CAAC+jB,IAAI,GAAGqE,UAAU,GAAG,CAAA7G,MAAM,IAAA,OAAA,KAAA,IAANA,MAAM,CAAE+R,WAAW,IAAG,CAAC;;QAGnD,OAAOtzB,KAAK;KACb;AACH;AAEA,SAAS6yB,qBAAqBA,CAC5B/d,kBAAgD;IAEhD,IAAML,gBAAgB,GAAGU,mBAAmB,EAAE;IAC9C,IAAMT,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAIie,SAAS,GAAGb,SAAS,CAACc,EAAE;IAE5B,OAAO;QACLT,gBAAgB,EAAhBA,gBAAgB;QAChBD,MAAM,EAANA;KACD;;IAED,SAASC,gBAAgBA;QACvB,OAAOQ,SAAS;;IAGlB,SAAST,MAAMA;QACbS,SAAS,GAAGb,SAAS,CAACc,EAAE;QACxB,IAAIC,cAAc,GAAG,CAAC;QAEtB,IAAI,CAAC3e,kBAAkB,CAAChK,OAAO,EAAE;YAC/B,OAAO,CAAC;;QAGV,IAAM1M,MAAM,GAAG6lB,aAAa,CAACnP,kBAAkB,CAAChK,OAAO,CAAC;QAExD,IAAI2J,gBAAgB,CAAC3J,OAAO,EAAE;YAAA,IAAA4oB,kBAAA;YAC5B,IAAMrZ,OAAO,GAAG3F,OAAO,CAAC5J,OAAO;YAC/B,IAAMyW,MAAM,GAAGxB,gBAAgB,CAACtL,gBAAgB,CAAC3J,OAAO,CAAC;YAEzD,IAAM6oB,YAAY,GAAG1P,aAAa,CAAC1C,MAAM,CAAC;YAE1CkS,cAAc,GAAGpM,kBAAkB,CAAC9F,MAAM,CAAC;YAE3C,IAAMtL,SAAS,GAAA,CAAAyd,kBAAA,GAAGrZ,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEpE,SAAS,KAAA,OAAAyd,kBAAA,GAAI,CAAC;YAEzC,IAAIzd,SAAS,GAAGwd,cAAc,GAAGr1B,MAAM,EAAE;gBACvCm1B,SAAS,GAAGb,SAAS,CAACS,IAAI;gBAC1BM,cAAc,IAAIE,YAAY,GAAGv1B,MAAM;;;QAI3C,OAAOq1B,cAAc,GAAGr1B,MAAM;;AAElC;AAEA,IAAM8jB,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,WAAA,GAAAC,QAAA,CAAA;IAC9BmZ,eAAe,EAAE;QACf,GAAG,EAAEva,UAAU,CAACua,eAAe;QAC/BqM,QAAQ,EAAE,UAAU;QACpByK,KAAK,EAAE,MAAM;QACbxJ,IAAI,EAAE,MAAM;QACZ6K,OAAO,EAAE,KAAK;QACdgF,SAAS,EAAE,gCAAgC;QAC3C1Q,YAAY,EAAE,KAAK;QACnBnmB,OAAO,EAAE,MAAM;QACfmwB,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,cAAc;QAC9BnwB,OAAO,EAAE,GAAG;QACZE,UAAU,EAAE,QAAQ;QACpBD,aAAa,EAAE,MAAM;QACrB6Y,GAAG,EAAE,OAAO;QACZyW,MAAM,EAAE,0CAA0C;QAClDnuB,MAAM,EAAE,0CAA0C;QAClDwvB,MAAM,EAAE,0CAA0C;QAClDpB,UAAU,EAAE,4CAA4C;QACxDkB,SAAS,EAAE,YAAY;QACvBvvB,UAAU,EAAE;KACb;IACDuoB,OAAO,EAAE;QACP1pB,OAAO,EAAE,GAAG;QACZE,UAAU,EAAE,SAAS;QACrBD,aAAa,EAAE,KAAK;QACpBywB,SAAS,EAAE;KACZ;IACD0F,UAAU,EAAE;QACV,GAAG,EAAE,aAAa;QAClBS,eAAe,EAAE,WAAW;QAC5BnG,SAAS,EAAE;KACZ;IACD,cAAc,EAAE;QACd2F,OAAO,EAAE;YACPvd,GAAG,EAAE,GAAG;YACR4X,SAAS,EAAE;;KAEd;IACD2F,OAAO,EAAE;QACP,GAAG,EAAE,mBAAmB;QACxB/F,OAAO,EAAE,EAAE;QACXxK,QAAQ,EAAE,UAAU;QACpBzkB,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACd6xB,gBAAgB,EAAE,WAAW;QAC7B6D,kBAAkB,EAAE,KAAK;QACzB5D,cAAc,EAAE,WAAW;QAC3Bpa,GAAG,EAAE,MAAM;QACX4X,SAAS,EAAE,mBAAmB;QAC9BqC,eAAe,EAAA,SAASgE,WAAW,GAAA;;AACpC,GAAA,WAAA,GACEz1B,QAAQ,CAAC,SAAS,EAAE;IACrBw1B,kBAAkB,EAAE;CACrB,CAAC,CACH,CAAC;SC1NcE,IAAIA;IAClB,IAAMtf,OAAO,GAAGY,UAAU,EAAE;IAC5Bob,WAAW,CAAChc,OAAO,CAAC;IACpBuW,oBAAoB,CAACvW,OAAO,EAAE3J,kBAAkB,CAACkpB,MAAM,CAAC;IACxD7Z,cAAc,EAAE;IAEhB,iNACE5c,gBAAAA,EAAAA,OAAAA;QACEuC,SAAS,EAAEkiB,yJAAAA,AAAE,EAACC,QAAM,CAACgS,IAAI,EAAEr2B,uBAAuB,CAACK,iBAAiB,CAAC;QACrEykB,GAAG,EAAEjO;iNAELlX,gBAAAA,EAACm1B,oBAAoB,EAAA,KAAG,4MACxBn1B,gBAAAA,EAACy0B,SAAS,EAAA,KAAG,CACT;AAEV;AAEA,IAAM/P,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/B62B,IAAI,EAAE;QACJ,GAAG,EAAEh4B,UAAU,CAACwa,UAAU;QAC1Byd,IAAI,EAAE,GAAG;QACTC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,QAAQ;QACnBvR,QAAQ,EAAE;;CAEb,CAAC;SCxCcwR,6BAA6BA,CAC3C/S,MAAyB,EACzBlH,OAAwB;IAExB,IAAI,CAACkH,MAAM,IAAI,CAAClH,OAAO,EAAE;QACvB,OAAO,CAAC;;IAGV,IAAMka,UAAU,GAAGhT,MAAM,CAACmC,qBAAqB,EAAE;IACjD,IAAM8Q,QAAQ,GAAGna,OAAO,CAACqJ,qBAAqB,EAAE;;IAGhD,OAAO8Q,QAAQ,CAACp2B,MAAM,GAAA,CAAIm2B,UAAU,CAACE,CAAC,GAAGD,QAAQ,CAACC,CAAC,CAAC;AACtD;SCEgBC,qBAAqBA,CACnCC,KAAc,EACdC,eAAmE;IAEnE,IAAMlgB,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAM6E,iBAAiB,GAAGD,oBAAoB,EAAE;IAChD,IAAMD,cAAc,GAAGD,iBAAiB,EAAE;8MAE1CnP,YAAAA,AAAS,EAAC;QACR,IAAI,CAAC8pB,KAAK,EAAE;YACV;;QAEF,IAAMta,OAAO,GAAG3F,OAAO,CAAC5J,OAAO;QAE/BuP,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,SAAS,EAAEua,QAAQ,EAAE;YAC7Cra,OAAO,EAAE;SACV,CAAC;QAEFH,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,WAAW,EAAEwa,WAAW,EAAE,IAAI,CAAC;QAEzDza,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,OAAO,EAAEya,OAAO,EAAE,IAAI,CAAC;QAEjD1a,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,UAAU,EAAE0a,OAAO,EAAE;YAC7Cxa,OAAO,EAAE;SACV,CAAC;QACFH,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEC,gBAAgB,CAAC,MAAM,EAAE0a,OAAO,EAAE,IAAI,CAAC;QAEhD,SAASD,OAAOA,CAACE,CAAa;YAC5B,IAAM1T,MAAM,GAAGxB,gBAAgB,CAACkV,CAAC,CAAC5f,MAAqB,CAAC;YAExD,IAAI,CAACkM,MAAM,EAAE;gBACX,OAAOyT,OAAO,EAAE;;YAGlB,IAAAE,qBAAA,GAAqC3M,0BAA0B,CAAChH,MAAM,CAAC,EAA/D7b,OAAO,GAAAwvB,qBAAA,CAAPxvB,OAAO,EAAEmhB,eAAe,GAAAqO,qBAAA,CAAfrO,eAAe;YAEhC,IAAI,CAACnhB,OAAO,IAAI,CAACmhB,eAAe,EAAE;gBAChC,OAAOmO,OAAO,EAAE;;YAGlBJ,eAAe,CAAC;gBACdlvB,OAAO,EAAPA,OAAO;gBACPmhB,eAAe,EAAfA;aACD,CAAC;;QAEJ,SAASmO,OAAOA,CAACC,CAA2B;YAC1C,IAAIA,CAAC,EAAE;gBACL,IAAME,aAAa,GAAGF,CAAC,CAACE,aAA4B;gBAEpD,IAAI,CAACpV,gBAAgB,CAACoV,aAAa,CAAC,EAAE;oBACpC,OAAOP,eAAe,CAAC,IAAI,CAAC;;;YAIhCA,eAAe,CAAC,IAAI,CAAC;;QAEvB,SAASC,QAAQA,CAACI,CAAgB;YAChC,IAAIA,CAAC,CAAC12B,GAAG,KAAK,QAAQ,EAAE;gBACtBq2B,eAAe,CAAC,IAAI,CAAC;;;QAIzB,SAASE,WAAWA,CAACG,CAAa;YAChC,IAAI9a,iBAAiB,EAAE,EAAE;gBACvB;;YAGF,IAAMoH,MAAM,GAAGxB,gBAAgB,CAACkV,CAAC,CAAC5f,MAAqB,CAAC;YAExD,IAAIkM,MAAM,EAAE;gBACV,IAAM6T,aAAa,GAAGd,6BAA6B,CAAC/S,MAAM,EAAElH,OAAO,CAAC;gBACpE,IAAMsZ,YAAY,GAAGpS,MAAM,CAACmC,qBAAqB,EAAE,CAACtlB,MAAM;gBAC1D,IAAIg3B,aAAa,GAAGzB,YAAY,EAAE;oBAChC,OAAO0B,kCAAkC,CAAC9T,MAAM,EAAEqT,eAAe,CAAC;;gBAGpEnhB,YAAY,CAAC8N,MAAM,CAAC;;;QAIxB,OAAO;YACLlH,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,WAAW,EAAEqa,WAAW,CAAC;YACtDza,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,UAAU,EAAEua,OAAO,CAAC;YACjD3a,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,OAAO,EAAEsa,OAAO,EAAE,IAAI,CAAC;YACpD1a,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,MAAM,EAAEua,OAAO,EAAE,IAAI,CAAC;YACnD3a,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAEI,mBAAmB,CAAC,SAAS,EAAEoa,QAAQ,CAAC;SAClD;KACF,EAAE;QAACngB,OAAO;QAAEigB,KAAK;QAAEC,eAAe;QAAEza,iBAAiB;QAAEF,cAAc;KAAC,CAAC;AAC1E;AAEA,SAASob,kCAAkCA,CACzC9T,MAAmB,EACnBqT,eAAmE;;IAEnE,IAAAU,sBAAA,GAAqC/M,0BAA0B,CAAChH,MAAM,CAAC,EAA/D7b,OAAO,GAAA4vB,sBAAA,CAAP5vB,OAAO,EAAEmhB,eAAe,GAAAyO,sBAAA,CAAfzO,eAAe;IAEhC,IAAI,CAACnhB,OAAO,IAAI,CAACmhB,eAAe,EAAE;QAChC;;IAGD,CAAA0O,qBAAA,GAAAlhB,QAAQ,CAACC,aAA6B,KAAA,OAAA,KAAA,IAAtCihB,qBAAA,CAAwCC,IAAI,IAAA,OAAA,KAAA,IAA5CD,qBAAA,CAAwCC,IAAI,EAAI;IAEjDZ,eAAe,CAAC;QACdlvB,OAAO,EAAPA,OAAO;QACPmhB,eAAe,EAAfA;KACD,CAAC;AACJ;;ACtHA,IAAY4O,aAGX;AAHD,CAAA,SAAYA,aAAa;IACvBA,aAAAA,CAAAA,MAAAA,GAAAA,SAAe;IACfA,aAAAA,CAAAA,SAAAA,GAAAA,YAAqB;AACvB,CAAC,EAHWA,aAAa,IAAA,CAAbA,aAAa,GAAA,CAAA,CAAA;AAYzB,SAAwBC,IAAIA,CAAA5xB,IAAA;QAC1B0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ,EACRzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS,EAAA41B,UAAA,GAAA7xB,IAAA,CACT9D,KAAK,EAALA,KAAK,GAAA21B,UAAA,KAAA,KAAA,IAAG,CAAA,CAAE,GAAAA,UAAA,EAAAC,cAAA,GAAA9xB,IAAA,CACVyvB,SAAS,EAATA,SAAS,GAAAqC,cAAA,KAAA,KAAA,IAAGH,aAAa,CAACI,GAAG,GAAAD,cAAA;IAE7B,iNACEp4B,gBAAAA,EAAAA,OAAAA;QACEwC,KAAK,EAAA1C,QAAA,CAAA,CAAA,GAAO0C,KAAK,CAAE;QACnBD,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACiS,IAAI,EAAEp0B,SAAS,EAAEmiB,QAAM,CAACqR,SAAS,CAAC;OAEtD/pB,QAAQ,CACL;AAEV;AAEA,IAAM0Y,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,CAAAy4B,kBAAA,GAAA;IAC9B3B,IAAI,EAAE;QACJp3B,OAAO,EAAE;;AACV,GAAA+4B,kBAAA,CACAL,aAAa,CAACI,GAAG,CAAA,GAAG;IACnB9S,aAAa,EAAE;CAChB,EAAA+S,kBAAA,CACAL,aAAa,CAACM,MAAM,CAAA,GAAG;IACtBhT,aAAa,EAAE;CAChB,EAAA+S,kBAAA,EACD;SClCsB9V,KAAKA,CAAAlc,IAAA;QAAG/D,SAAS,GAAA+D,IAAA,CAAT/D,SAAS,EAAA41B,UAAA,GAAA7xB,IAAA,CAAE9D,KAAK,EAALA,KAAK,GAAA21B,UAAA,KAAA,KAAA,IAAG,CAAA,CAAE,GAAAA,UAAA;IACnD,iNAAOn4B,gBAAAA,EAAAA,OAAAA;QAAKwC,KAAK,EAAA1C,QAAA,CAAA;YAAI62B,IAAI,EAAE;WAAMn0B,KAAK,CAAE;QAAED,SAAS,EAAEkiB,yJAAAA,AAAE,EAACliB,SAAS;MAAK;AACxE;SCHwBi2B,QAAQA,CAAAlyB,IAAA;QAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ,EAAEzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS,EAAEC,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;IAC3D,iNACExC,gBAAAA,EAAAA,OAAAA;QAAKwC,KAAK,EAAA1C,QAAA,CAAA,CAAA,GAAO0C,KAAK,EAAA;YAAE8iB,QAAQ,EAAE;UAAY;QAAE/iB,SAAS,EAAEA;OACxDyJ,QAAQ,CACL;AAEV;SCNwBysB,QAAQA,CAAAnyB,IAAA;QAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ,EAAEzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS,EAAEC,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;IAC3D,iNACExC,gBAAAA,EAAAA,OAAAA;QAAKwC,KAAK,EAAA1C,QAAA,CAAA,CAAA,GAAO0C,KAAK,EAAA;YAAE8iB,QAAQ,EAAE;UAAY;QAAE/iB,SAAS,EAAEA;OACxDyJ,QAAQ,CACL;AAEV;ACEA,sCAAA;AACA,SAAgB0sB,oBAAoBA,CAAApyB,IAAA;QAClCsb,MAAM,GAAAtb,IAAA,CAANsb,MAAM,EACNmM,OAAO,GAAAznB,IAAA,CAAPynB,OAAO,EACP4K,QAAQ,GAAAryB,IAAA,CAARqyB,QAAQ,EACRC,iBAAiB,GAAAtyB,IAAA,CAAjBsyB,iBAAiB,EACjBp2B,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;IAEL,iNACExC,gBAAAA,EAAC2uB,MAAM,EAAA;QACLnsB,KAAK,EAAEA,KAAK;QACZurB,OAAO,EAAEA,OAAO;QAChBxrB,SAAS,sJAAEkiB,KAAAA,AAAE,EAAA,cACCmU,iBAAiB,EAC7BlU,QAAM,CAACmU,IAAI,EACX,CAACjX,MAAM,IAAI8C,QAAM,CAACoU,UAAU,EAC5BH,QAAQ,IAAIjU,QAAM,CAACqU,MAAM,CAC1B;wBACaJ,QAAQ;qCACGzyB,cAAc,CAAC0yB,iBAA8B,CAAA;MAC9D;AAEd;AAEA,IAAMlU,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/Bi5B,UAAU,EAAE;QACVt5B,OAAO,EAAE,GAAG;QACZ4wB,MAAM,EAAE;KACT;IACD2I,MAAM,EAAE;QACN,GAAG,EAAE,YAAY;QACjB3I,MAAM,EAAE,GAAG;QACX5wB,OAAO,EAAE;KACV;IACDq5B,IAAI,EAAE;QACJ,GAAG,EAAE,UAAU;QACfh4B,KAAK,EAAE,2BAA2B;QAClCtB,OAAO,EAAE,OAAO;QAChBuvB,MAAM,EAAE,SAAS;QACjBpJ,YAAY,EAAE,KAAK;QACnB9kB,MAAM,EAAE,2BAA2B;QACnC0kB,QAAQ,EAAE,UAAU;QACpByK,KAAK,EAAE,GAAG;QACVpvB,UAAU,EAAE,uDAAuD;QACnEyvB,MAAM,EAAE,GAAG;QACXrB,MAAM,EAAE,mDAAmD;QAC3DqH,SAAS,EAAE,6DAA6D;QACxE,QAAQ,EAAE;YACRA,SAAS,EAAE;SACZ;QACD,QAAQ,EAAE;YACRA,SAAS,EAAE;SACZ;QACD,oBAAoB,EAAE;YACpBxQ,eAAe,EAAE;SAClB;QACD,kBAAkB,EAAE;YAClBA,eAAe,EAAE;SAClB;QACD,kBAAkB,EAAE;YAClBA,eAAe,EAAE;SAClB;QACD,kBAAkB,EAAE;YAClBA,eAAe,EAAE;SAClB;QACD,kBAAkB,EAAE;YAClBA,eAAe,EAAE;SAClB;QACD,kBAAkB,EAAE;YAClBA,eAAe,EAAE;;;CAGtB,CAAC;ACxFF,6BAAA,GAuBA,IAAMoT,SAAS,GAAG,EAAE;AAMpB,SAAgBC,kBAAkBA;IAChC,iNACEj5B,gBAAAA,EAACy4B,QAAQ,EAAA;QAACj2B,KAAK,EAAE;YAAE5B,MAAM,EAAEo4B;;iNACzBh5B,gBAAAA,EAACw4B,QAAQ,EAAA;QAACh2B,KAAK,EAAE;YAAEwlB,MAAM,EAAE,CAAC;YAAE+H,KAAK,EAAE;;iNACnC/vB,gBAAAA,EAACk5B,cAAc,EAAA;QAACnD,SAAS,EAAEoD,uBAAuB,CAACC,QAAAA;MAAY,CACtD,CACF;AAEf;AAEA,SAAgBF,cAAcA,CAAA5yB,IAAA;8BAC5ByvB,SAAS,EAATA,SAAS,GAAAqC,cAAA,KAAA,KAAA,IAAGe,uBAAuB,CAACE,UAAU,GAAAjB,cAAA;IAE9C,IAAMhhB,iBAAiB,GAAGa,oBAAoB,EAAE;IAChD,IAAMqhB,UAAU,GAAGtrB,0BAA0B,EAAE;IAC/C,IAAA6N,qBAAA,GAA4BrG,uBAAuB,EAAE,EAA9CoM,MAAM,GAAA/F,qBAAA,CAAA,EAAA,EAAEgG,SAAS,GAAAhG,qBAAA,CAAA,EAAA;IACxB,IAAA+R,qBAAA,GAA4C5Y,sBAAsB,EAAE,EAA7DnB,cAAc,GAAA+Z,qBAAA,CAAA,EAAA,EAAE2L,iBAAiB,GAAA3L,qBAAA,CAAA,EAAA;IACxC,IAAMxgB,gBAAgB,GAAG6B,yBAAyB,EAAE;IACpD,IAAM+M,mBAAmB,GAAGN,sBAAsB,EAAE;IACpD,IAAMiC,gBAAgB,GAAGT,mBAAmB,EAAE;IAE9C,IAAIoc,UAAU,EAAE;QACd,OAAO,IAAI;;IAGb,IAAME,SAAS,GAAMR,SAAS,GAAGrzB,kBAAkB,CAAChD,MAAM,GAAA,IAAI;IAE9D,IAAM82B,YAAY,GAAG7X,MAAM,GAAG4X,SAAS,GAAGR,SAAS,GAAG,IAAI;IAE1D,IAAMU,QAAQ,GAAG3D,SAAS,KAAKoD,uBAAuB,CAACC,QAAQ;IAE/D,iNACEp5B,gBAAAA,EAACy4B,QAAQ,EAAA;QACPl2B,SAAS,EAAEkiB,yJAAAA,AAAE,EACXC,QAAM,CAACiV,SAAS,EAChBD,QAAQ,IAAIhV,QAAM,CAACgV,QAAQ,EAC3B9X,MAAM,IAAI8C,QAAM,CAAC/iB,IAAI,EACrB+3B,QAAQ,IAAI9X,MAAM,IAAI8C,QAAM,CAACkV,cAAc,CAC5C;QACDp3B,KAAK,EACHk3B,QAAQ,GACJ;YAAEG,SAAS,EAAEJ,YAAY;YAAE74B,MAAM,EAAE64B;SAAc,GACjD;YAAEI,SAAS,EAAEJ;;iNAGnBz5B,gBAAAA,EAAAA,OAAAA;QAAKuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACoV,MAAM,CAAC;QAAE3U,GAAG,EAAE/N;OACrCzR,kBAAkB,CAAC1G,GAAG,CAAC,SAAC25B,iBAAiB,EAAE5L,CAAC;QAC3C,IAAM+L,MAAM,GAAGH,iBAAiB,KAAK/kB,cAAc;QAEnD,iNACE7T,gBAAAA,EAAC04B,oBAAoB,EAAA;YACnB33B,GAAG,EAAE63B,iBAAiB;YACtBA,iBAAiB,EAAEA,iBAAiB;YACpChX,MAAM,EAAEA,MAAM;YACdpf,KAAK,EAAE;gBACL0tB,SAAS,EAAEzL,yJAAAA,AAAE,EACXiV,QAAQ,GAAA,iBACW1M,CAAC,GAAA,CAAIpL,MAAM,GAAGoX,SAAS,GAAG,CAAC,CAAC,GAAA,QAAA,iBAC5BhM,CAAC,GAAA,CAAIpL,MAAM,GAAGoX,SAAS,GAAG,CAAC,CAAC,GAAA,KAAK,EACpDpX,MAAM,IAAImX,MAAM,IAAI,YAAY;aAEnC;YACDJ,QAAQ,EAAEI,MAAM;YAChBhL,OAAO,EAAE,SAAAA;gBACP,IAAInM,MAAM,EAAE;oBACV2X,iBAAiB,CAACX,iBAAiB,CAAC;oBACpCxrB,gBAAgB,CAACwrB,iBAAiB,CAAC;oBACnCjb,gBAAgB,EAAE;iBACnB,MAAM;oBACLkE,SAAS,CAAC,IAAI,CAAC;;gBAEjB7F,mBAAmB,EAAE;;UAEvB;KAEL,CAAC,CACE,CACG;AAEf;AAEA,IAAYmd,uBAGX;AAHD,CAAA,SAAYA,uBAAuB;IACjCA,uBAAAA,CAAAA,WAAAA,GAAAA,cAA8B;IAC9BA,uBAAAA,CAAAA,aAAAA,GAAAA,gBAAkC;AACpC,CAAC,EAHWA,uBAAuB,IAAA,CAAvBA,uBAAuB,GAAA,CAAA,CAAA;AAKnC,IAAMzU,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/B85B,SAAS,EAAE;QACT,GAAG,EAAE,gBAAgB;QACrB,IAAI,EAAE;YACJ,sBAAsB,EAAE;SACzB;QACDp6B,OAAO,EAAE,MAAM;QACfmwB,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,UAAU;QAC1BhvB,UAAU,EAAE,sBAAsB;QAClCywB,OAAO,EAAE;KACV;IACDsI,QAAQ,EAAE;QACRtI,OAAO,EAAE,KAAK;QACd1B,UAAU,EAAE,UAAU;QACtBnK,aAAa,EAAE,QAAQ;QACvBG,YAAY,EAAE,KAAK;QACnBqJ,MAAM,EAAE;KACT;IACD6K,cAAc,EAAE;QACdxD,SAAS,EAAE;KACZ;IACDz0B,IAAI,EAAE;;QAEJqkB,cAAc,EAAE,WAAW;QAC3BgJ,UAAU,EAAE,wCAAwC;QACpD,aAAa,EAAE;YACbD,MAAM,EAAE;;KAEX;IACD+K,MAAM,EAAE;QACN,GAAG,EAAE,sBAAsB;QAC3BxU,QAAQ,EAAE,UAAU;QACpBzkB,KAAK,EAAE,2BAA2B;QAClCD,MAAM,EAAE;;CAEX,CAAC;SC7Hcm5B,OAAOA;IACrB,IAAMpvB,aAAa,GAAGwE,gBAAgB,EAAE;IACxC,IAAM2S,mBAAmB,GAAGtB,sBAAsB,EAAE;IAEpD,IAAI,CAAC7V,aAAa,CAACkB,WAAW,EAAE;QAC9B,OAAO,IAAI;;IAGb,iNACE7L,gBAAAA,EAACk4B,IAAI,EAAA;QACH31B,SAAS,qJAAEkiB,MAAAA,AAAE,EAACC,QAAM,CAACsV,OAAO,EAAE35B,uBAAuB,CAACK,iBAAiB;iNAEvEV,gBAAAA,EAACi6B,WAAW,EAAA,KAAG,4MACfj6B,gBAAAA,EAACwiB,KAAK,EAAA,KAAG,EACRV,mBAAmB,6MAAG9hB,gBAAAA,EAACi5B,kBAAkB,EAAA,KAAG,GAAG,IAAI,CAC/C;AAEX;AAEA,SAAgBgB,WAAWA;;IACzB,IAAMtvB,aAAa,GAAGwE,gBAAgB,EAAE;IACxC,IAAAoC,SAAA,GAAwCC,qNAAAA,AAAQ,EAAe,IAAI,CAAC,EAA7D0oB,YAAY,GAAA3oB,SAAA,CAAA,EAAA,EAAE6lB,eAAe,GAAA7lB,SAAA,CAAA,EAAA;IACpC,IAAMpP,UAAU,GAAG+L,mBAAmB,EAAE;IACxC,IAAAyN,qBAAA,GAA+BrG,4BAA4B,EAAE,EAAtD6kB,oBAAoB,GAAAxe,qBAAA,CAAA,EAAA;IAC3B,IAAMvQ,WAAW,GAAGwF,oBAAoB,EAAE;IAE1CsmB,qBAAqB,CAACvsB,aAAa,CAACkB,WAAW,EAAEurB,eAAe,CAAC;IAEjE,IAAMpwB,KAAK,GAAGmC,cAAc,CAAA,CAAAixB,qBAAA,GAC1BF,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAEhyB,OAAO,KAAA,OAAAkyB,qBAAA,GAAIF,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAE7Q,eAAe,CACvD;IAED,IAAMgR,IAAI,GAAGrzB,KAAK,IAAI,IAAI,IAAIkzB,YAAY,IAAI,IAAI;IAElD,iNAAOl6B,gBAAAA,EAACs6B,cAAc,EAAA,KAAG;;IAEzB,SAASA,cAAcA;QACrB,IAAM3uB,YAAY,GAChBwuB,oBAAoB,IAAA,OAApBA,oBAAoB,GAAIhxB,cAAc,CAACwB,aAAa,CAACgB,YAAY,CAAC;QACpE,IAAI,CAACA,YAAY,EAAE;YACjB,OAAO,IAAI;;QAEb,IAAM4uB,WAAW,GAAGJ,oBAAoB,GACpCnyB,SAAS,CAACmyB,oBAAoB,CAAC,GAC/BxvB,aAAa,CAACiB,cAAc;QAEhC,iNACE5L,gBAAAA,EAAAA,qMAAAA,CAAAA,WAAAA,EAAAA,gNACEA,gBAAAA,EAAAA,OAAAA,MACGq6B,IAAI,6MACHr6B,gBAAAA,EAAC0xB,aAAa,EAAA;YACZxpB,OAAO,EAAEgyB,YAAY,IAAA,OAAA,KAAA,IAAZA,YAAY,CAAEhyB,OAAiB;YACxClB,KAAK,EAAEA,KAAK;YACZ7E,UAAU,EAAEA,UAAU;YACtBwvB,IAAI,EAAE,EAAE;YACRvmB,WAAW,EAAEA,WAAW;YACxB7I,SAAS,MAAEkiB,qJAAAA,AAAE,EAACC,QAAM,CAAC1d,KAAK;UAC1B,GACA2E,YAAY,6MACd3L,gBAAAA,EAAC0xB,aAAa,EAAA;YACZxpB,OAAO,EAAEP,YAAY,CAACgE,YAAY,CAAC;YACnC3E,KAAK,EAAE2E,YAAY;YACnBxJ,UAAU,EAAEA,UAAU;YACtBwvB,IAAI,EAAE,EAAE;YACRvmB,WAAW,EAAEA,WAAW;YACxB7I,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAAC1d,KAAK;UAC1B,GACA,IAAI,CACJ,EACNhH,0NAAAA,EAAAA,OAAAA;YAAKuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACmE,KAAK;WAC5BwR,IAAI,GAAGryB,SAAS,CAAChB,KAAK,CAAC,GAAGuzB,WAAW,CAClC,CACL;;AAGT;AAOA,IAAM7V,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/Bm6B,OAAO,EAAE;QACPtK,UAAU,EAAE,QAAQ;QACpB8K,SAAS,EAAE,2CAA2C;QACtD55B,MAAM,EAAE,2BAA2B;QACnCwwB,OAAO,EAAE,iCAAiC;QAC1C9L,QAAQ,EAAE,UAAU;QACpB8K,MAAM,EAAE;KACT;IACDvH,KAAK,EAAE;QACLyJ,KAAK,EAAE,+BAA+B;QACtC/B,QAAQ,EAAE,8BAA8B;QACxCa,OAAO,EAAE,iCAAiC;QAC1C2C,aAAa,EAAE;KAChB;IACD/sB,KAAK,EAAE;QACLoqB,OAAO,EAAE;;CAEZ,CAAC;SC9HcqJ,mBAAmBA,CAACC,SAAyB;;IAC3D,OAAA,CAAAC,qBAAA,GAAOD,SAAS,IAAA,OAAA,KAAA,IAATA,SAAS,CAAEE,YAAY,CAAC,WAAW,CAAC,KAAA,OAAAD,qBAAA,GAAI,IAAI;AACrD;SCIgBE,gCAAgCA,CAC9CC,iBAA6C;IAE7C,IAAM5jB,OAAO,GAAGY,UAAU,EAAE;8MAE5BzK,YAAAA,AAAS,EAAC;QACR,IAAM0tB,iBAAiB,GAAG,IAAIC,GAAG,EAAE;QACnC,IAAMne,OAAO,GAAG3F,OAAO,CAAC5J,OAAO;QAC/B,IAAM2tB,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,SAAA/0B,OAAO;YACL,IAAI,CAAC0W,OAAO,EAAE;gBACZ;;YAGF,IAAA,IAAAse,SAAA,GAAAC,+BAAA,CAAoBj1B,OAAO,GAAAk1B,KAAA,EAAA,CAAA,CAAAA,KAAA,GAAAF,SAAA,EAAA,EAAAG,IAAA,EAAE;gBAAA,IAAlBC,KAAK,GAAAF,KAAA,CAAAr6B,KAAA;gBACd,IAAM4I,GAAE,GAAG6wB,mBAAmB,CAACc,KAAK,CAAC1jB,MAAM,CAAC;gBAC5CkjB,iBAAiB,CAACS,GAAG,CAAC5xB,GAAE,EAAE2xB,KAAK,CAACE,iBAAiB,CAAC;;YAGpD,IAAMC,MAAM,GAAG78B,KAAK,CAAC8pB,IAAI,CAACoS,iBAAiB,CAAC;YAC5C,IAAMY,YAAY,GAAGD,MAAM,CAACA,MAAM,CAAC/4B,MAAM,GAAG,CAAC,CAAC;YAE9C,IAAIg5B,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACxB,OAAOb,iBAAiB,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC;;YAG3C,IAAA,IAAA7S,EAAA,GAAA,GAAA8S,OAAA,GAA0BF,MAAM,EAAA5S,EAAA,GAAA8S,OAAA,CAAAj5B,MAAA,EAAAmmB,EAAA,GAAE;gBAA7B,IAAA+S,UAAA,GAAAD,OAAA,CAAA9S,EAAA,CAAA,EAAOlf,EAAE,GAAAiyB,UAAA,CAAA,EAAA,EAAEC,KAAK,GAAAD,UAAA,CAAA,EAAA;gBACnB,IAAIC,KAAK,EAAE;oBACThB,iBAAiB,CAAClxB,EAAE,CAAC;oBACrB;;;SAGL,EACD;YACEmyB,SAAS,EAAE;gBAAC,CAAC;gBAAE,CAAC;aAAA;SACjB,CACF;QACDlf,OAAO,IAAA,OAAA,KAAA,IAAPA,OAAO,CAAE+L,gBAAgB,CAACjqB,WAAW,CAACD,UAAU,CAACsF,QAAQ,CAAC,CAAC,CAACwD,OAAO,CAAC,SAAAw0B,EAAE;YACpEf,QAAQ,CAACgB,OAAO,CAACD,EAAE,CAAC;SACrB,CAAC;KACH,EAAE;QAAC9kB,OAAO;QAAE4jB,iBAAiB;KAAC,CAAC;AAClC;SCxCgBoB,yBAAyBA;IACvC,IAAMhlB,OAAO,GAAGY,UAAU,EAAE;IAC5B,IAAMd,aAAa,GAAGU,gBAAgB,EAAE;IAExC,OAAO,SAASykB,sBAAsBA,CAACn4B,QAAgB;;QACrD,IAAI,CAACkT,OAAO,CAAC5J,OAAO,EAAE;YACpB;;QAEF,IAAMotB,SAAS,GAAA,CAAA0B,gBAAA,GAAGllB,OAAO,CAAC5J,OAAO,KAAA,OAAA,KAAA,IAAf8uB,gBAAA,CAAiBnS,aAAa,CAAA,kBAC/BjmB,QAAQ,GAAA,KAAI,CACT;QAEpB,IAAI,CAAC02B,SAAS,EAAE;YACd;;QAGF,IAAMhQ,SAAS,GAAGgQ,SAAS,CAAChQ,SAAS,IAAI,CAAC;QAE1CtS,QAAQ,CAACpB,aAAa,CAAC1J,OAAO,EAAEod,SAAS,CAAC;KAC3C;AACH;SCzBgB2R,yBAAyBA;IACvC,IAAMC,oBAAoB,GAAG9tB,qBAAqB,EAAE;IAEpD,IAAI,CAAC8tB,oBAAoB,EAAE;QACzB,OAAO,KAAK;;IAGd,OAAOA,oBAAoB,CAAC35B,MAAM,KAAK,CAAC;AAC1C;;SCegB45B,cAAcA,CAAAj2B,IAAA;;QAC5Bk2B,gBAAgB,GAAAl2B,IAAA,CAAhBk2B,gBAAgB,EAChBx4B,QAAQ,GAAAsC,IAAA,CAARtC,QAAQ,EACRy4B,eAAe,GAAAn2B,IAAA,CAAfm2B,eAAe,EACf/I,cAAc,GAAAptB,IAAA,CAAdotB,cAAc,EACd3F,OAAO,GAAAznB,IAAA,CAAPynB,OAAO;IAEP,WACE/tB,sNAAAA,EAAC2uB,MAAM,EAAA;QACLyD,QAAQ,EAAEqK,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;QAClCl6B,SAAS,sJAAEkiB,KAAAA,AAAE,EACXC,QAAM,CAACgY,MAAM,EACbr8B,uBAAuB,CAACC,WAAW,EAAA,aACxB0D,QAAQ,EAAA,CAAAghB,GAAA,GAAA,CAAA,GAAAA,GAAA,CAEhBtmB,UAAU,CAACq6B,MAAM,CAAA,GAAGyD,gBAAgB,EAAAxX,GAAA,EAExC;QACD+I,OAAO,EAAEA,OAAO;sBACJzpB,8BAA8B,CAACovB,cAAc,CAAC;yBAC3C8I,gBAAgB;QAC/BG,IAAI,EAAC,KAAK;yBACI;MACd;AAEN;AAEA,IAAMC,mBAAmB,GAAG;IAC1Br8B,mBAAmB,EAAE;CACtB;AACD,IAAMs8B,aAAa,GAAG;IACpBt8B,mBAAmB,EAAE;CACtB;AAED,IAAMu8B,oBAAoB,GAAG;IAC3B,0BAA0B,EAAE;QAC1BJ,MAAM,EAAE;YACN,QAAQ,EAAEE,mBAAmB;YAC7B,cAAc,EAAEA;;;CAGrB;AAED,IAAMlY,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,WAAA,GAAAC,QAAA,CAAA;IAC9B48B,MAAM,EAAE;QACN,GAAG,EAAE,aAAa;QAClBn9B,OAAO,EAAE,cAAc;QACvBoB,UAAU,EAAE,0BAA0B;QACtC2kB,QAAQ,EAAE,UAAU;QACpB1kB,MAAM,EAAE,4CAA4C;QACpDC,KAAK,EAAE,4CAA4C;QACnD6xB,cAAc,EAAE,uDAAuD;QACvEzD,OAAO,EAAE,MAAM;QACfqH,kBAAkB,EAAE,KAAK;QACzB/D,eAAe,EAAA,SAASwK,aAAa,GAAA,GAAG;QACxC,eAAe,EAAE;YACfjN,OAAO,EAAE,EAAE;YACXxK,QAAQ,EAAE,UAAU;YACpBhN,GAAG,EAAE,MAAM;YACXiO,IAAI,EAAE,MAAM;YACZwJ,KAAK,EAAE,MAAM;YACb/H,MAAM,EAAE,MAAM;YACd+G,MAAM,EAAE,iDAAiD;YACzDrJ,YAAY,EAAE;SACf;QACD,qBAAqB,EAAE;YACrBsX,mBAAmB,EACjB;SACH;QACD,kBAAkB,EAAE;YAClBA,mBAAmB,EACjB;SACH;QACD,sBAAsB,EAAE;YACtBA,mBAAmB,EACjB;SACH;QACD,0BAA0B,EAAE;YAC1BA,mBAAmB,EACjB;SACH;QACD,iBAAiB,EAAE;YACjBA,mBAAmB,EACjB;SACH;QACD,sBAAsB,EAAE;YACtBA,mBAAmB,EACjB;SACH;QACD,mBAAmB,EAAE;YACnBA,mBAAmB,EACjB;SACH;QACD,0BAA0B,EAAE;YAC1BA,mBAAmB,EAAE;SACtB;QACD,mBAAmB,EAAE;YACnBA,mBAAmB,EACjB;SACH;QACD,yBAAyB,EAAE;YACzBA,mBAAmB,EACjB;;;AAEL,GAAA,WAAA,GACEl8B,QAAQ,CAAC,QAAQ,EAAE+7B,aAAa,CAAC,EAAA;IACpC,iBAAiB,EAAA,WAAA,GAAA/8B,QAAA,CAAA,CAAA,GACZg9B,oBAAoB,CACxB;IACD,iBAAiB,EAAA,WAAA,GAAAh9B,QAAA,CAAA,CAAA,GACZg9B,oBAAoB;AACxB,EACF,CAAC;SCzHcG,kBAAkBA;IAChC,IAAA1rB,SAAA,6MAA4CC,WAAAA,AAAQ,EAAgB,IAAI,CAAC,EAAlE0rB,cAAc,GAAA3rB,SAAA,CAAA,EAAA,EAAEupB,iBAAiB,GAAAvpB,SAAA,CAAA,EAAA;IACxC,IAAM4qB,sBAAsB,GAAGD,yBAAyB,EAAE;IAC1DrB,gCAAgC,CAACC,iBAAiB,CAAC;IACnD,IAAMpY,YAAY,GAAG3M,eAAe,EAAE;IAEtC,IAAMonB,gBAAgB,GAAG7uB,mBAAmB,EAAE;IAC9C,IAAM+I,qBAAqB,GAAGa,wBAAwB,EAAE;IACxD,IAAMklB,kBAAkB,GAAGf,yBAAyB,EAAE;IAEtD,iNACEr8B,gBAAAA,EAAAA,OAAAA;QACEuC,SAAS,EAAEkiB,yJAAAA,AAAE,EAACC,QAAM,CAAC2Y,GAAG,CAAC;QACzBV,IAAI,EAAC,SAAS;sBACH,qBAAqB;QAChC/yB,EAAE,EAAC,qBAAqB;QACxBub,GAAG,EAAE9N;OAEJ8lB,gBAAgB,CAACl+B,GAAG,CAAC,SAAAy0B,cAAc;QAClC,IAAM1vB,QAAQ,GAAGK,0BAA0B,CAACqvB,cAAc,CAAC;QAC3D,IAAM8I,gBAAgB,GAAGx4B,QAAQ,KAAKk5B,cAAc;QAEpD,IAAI3P,gBAAgB,CAACmG,cAAc,CAAC,IAAI0J,kBAAkB,EAAE;YAC1D,OAAO,IAAI;;QAGb,IAAMX,eAAe,GAAG,CAAC/Z,YAAY,IAAI,CAAC8Z,gBAAgB;QAE1D,iNACEx8B,gBAAAA,EAACu8B,cAAc,EAAA;YACbx7B,GAAG,EAAEiD,QAAQ;YACbA,QAAQ,EAAEA,QAAQ;YAClBw4B,gBAAgB,EAAEA,gBAAgB;YAClCC,eAAe,EAAEA,eAAe;YAChC/I,cAAc,EAAEA,cAAc;YAC9B3F,OAAO,EAAE,SAAAA;gBACP+M,iBAAiB,CAAC92B,QAAQ,CAAC;gBAC3Bm4B,sBAAsB,CAACn4B,QAAQ,CAAC;;UAElC;KAEL,CAAC,CACE;AAEV;AAEA,IAAM0gB,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAC;IAC/Bw9B,GAAG,EAAE;QACH,GAAG,EAAE,kBAAkB;QACvB99B,OAAO,EAAE,MAAM;QACfgmB,aAAa,EAAE,KAAK;QACpBoK,cAAc,EAAE,cAAc;QAC9ByB,OAAO,EAAE;KACV;IACD,oBAAoB,EAAE;QACpBiM,GAAG,EAAE;YACH79B,OAAO,EAAE,KAAK;YACdsvB,MAAM,EAAE,SAAS;YACjBrvB,aAAa,EAAE;;KAElB;IACD,8CAA8C,EAAE;QAC9C49B,GAAG,EAAE;YACH79B,OAAO,EAAE,KAAK;YACdsvB,MAAM,EAAE,SAAS;YACjBrvB,aAAa,EAAE;;;CAGpB,CAAC;;SCvEc69B,cAAcA;IAC5B,IAAM1f,WAAW,GAAGJ,cAAc,EAAE;IAEpC,iNACExd,gBAAAA,EAAC2uB,MAAM,EAAA;QACLpsB,SAAS,MAAEkiB,qJAAAA,AAAE,EACXC,QAAM,CAAC6Y,cAAc,EACrBl9B,uBAAuB,CAACI,mBAAmB,CAC5C;QACDstB,OAAO,EAAEnQ,WAAW;sBACT,OAAO;QAClBuU,KAAK,EAAC;iNAENnyB,gBAAAA,EAAAA,OAAAA;QAAKuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAAC8Y,eAAe;MAAK,CACvC;AAEb;AAEA,IAAMC,SAAS,GAAG;IAChB,QAAQ,EAAE;QACR,yBAAyB,EAAE;YACzBl9B,mBAAmB,EAAE;;;CAG1B;AAED,IAAMmkB,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,WAAA,GAAAC,QAAA,CAAA;IAC9By9B,cAAc,EAAE;QACd,GAAG,EAAE,sBAAsB;QAC3BjY,QAAQ,EAAE,UAAU;QACpByK,KAAK,EAAE,qCAAqC;QAC5CnvB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbtB,OAAO,EAAE,MAAM;QACfmwB,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBrX,GAAG,EAAE,KAAK;QACV4X,SAAS,EAAE,kBAAkB;QAC7BkB,OAAO,EAAE,GAAG;QACZ1L,YAAY,EAAE,KAAK;QACnB,QAAQ,EAAE;YACRsJ,UAAU,EAAE;SACb;QACD,QAAQ,EAAE;YACRA,UAAU,EAAE;;KAEf;IACDwO,eAAe,EAAE;QACf,GAAG,EAAE,sBAAsB;QAC3B5X,eAAe,EAAE,aAAa;QAC9B6M,gBAAgB,EAAE,WAAW;QAC7BC,cAAc,EAAE,MAAM;QACtB9xB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACb0xB,eAAe,EAAA,SAASmL,QAAQ,GAAA,GAAG;QACnC,QAAQ,EAAE;YACRn9B,mBAAmB,EAAE;SACtB;QACD,QAAQ,EAAE;YACRA,mBAAmB,EAAE;;;AAExB,GAAA,WAAA,GACEO,QAAQ,CAAC,iBAAiB,EAAE;IAC7BP,mBAAmB,EAAE;CACtB,CAAC,EAAA,WAAA,GACCO,QAAQ,CAAC,gBAAgB,EAAE28B,SAAS,CAAC,CACzC,CAAC;AC1EF,IAAME,KAAK,GAAA,WAAA,GAAMh/B,WAAW,CAACD,UAAU,CAAC2mB,WAAW,CAAC,GAAA,MAAA,WAAA,GAAI1mB,WAAW,CACjED,UAAU,CAACi2B,SAAS,CACnB;AAEH,IAAMiJ,YAAY,GAAA,WAAA,GAAG;IAAC,QAAQ;IAAEj/B,WAAW,CAACD,UAAU,CAACsI,KAAK,CAAC;CAAC,CAAC7H,IAAI,CAAC,EAAE,CAAC;AACvE,IAAM0+B,QAAQ,GAAA,WAAA,GAAGl/B,WAAW,CAACD,UAAU,CAACsF,QAAQ,CAAC;AAEjD,SAAgB85B,SAASA,CAAAx3B,IAAA;QAAGtF,KAAK,GAAAsF,IAAA,CAALtF,KAAK;IAC/B,IAAI,CAACA,KAAK,EAAE;QACV,OAAO,IAAI;;IAGb,IAAM+8B,CAAC,GAAGC,QAAQ,CAACh9B,KAAK,CAAC;IAEzB,iNACEhB,gBAAAA,EAAAA,SAAAA,MAAAA,WACE29B,KAAK,GAAA,MAAIC,YAAY,GAAA,8CAKrBD,KAAK,GAAA,MAAII,CAAC,GAAA,4CAIVJ,KAAK,GAAA,MAAIE,QAAQ,GAAA,eAAaE,CAAC,GAAA,uCAGlC,CAAS;AAEZ;AAEA,SAASC,QAAQA,CAACh9B,KAAa;IAC7B,OAAO;QACL48B,YAAY;QACZ,oBAAoB;QACpB5f,uBAAuB,CAAChd,KAAK,CAAC;QAC9B,IAAI;KACL,CAAC7B,IAAI,CAAC,EAAE,CAAC;AACZ;;SCrCgB8+B,SAASA;IACvB,iNAAOj+B,gBAAAA,EAAAA,OAAAA;QAAKuC,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACwZ,SAAS;MAAK;AACjD;AAEA,IAAMxZ,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,WAAA,GAAAC,QAAA,CAAA;IAC9Bo+B,SAAS,EAAE;QACT,GAAG,EAAE,gBAAgB;QACrBpO,OAAO,EAAE,EAAE;QACXxK,QAAQ,EAAE,UAAU;QACpBhN,GAAG,EAAE,KAAK;QACViO,IAAI,EAAE,qCAAqC;QAC3C2J,SAAS,EAAE,kBAAkB;QAC7BrvB,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACd6xB,gBAAgB,EAAE,WAAW;QAC7B6D,kBAAkB,EAAE,KAAK;QACzB5D,cAAc,EAAE,MAAM;QACtBH,eAAe,EAAA,SAAS4L,YAAY,GAAA;;AACrC,GAAA,WAAA,GACEr9B,QAAQ,CAAC,WAAW,EAAE;IACvBP,mBAAmB,EAAE;CACtB,CAAC,CACH,CAAC;SCNc69B,eAAeA;IAC7B,IAAM37B,cAAc,GAAG2N,uBAAuB,EAAE;IAEhD,IAAMoR,kBAAkB,GAAGlB,qBAAqB,EAAE;IAElD,IAAI7d,cAAc,EAAE;QAClB,OAAO,IAAI;;IAGb,iNACEzC,gBAAAA,EAACk4B,IAAI,EAAA;QAAC31B,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAAC2Z,OAAO;OAChCr+B,0NAAAA,EAACs+B,MAAM,EAAA,KAAG,EAET9c,kBAAkB,6MAAGxhB,gBAAAA,EAACk5B,cAAc,EAAA,KAAG,GAAG,IAAI,CAC1C;AAEX;AAEA,SAAgBoF,MAAMA;IACpB,IAAA/sB,SAAA,6MAAsBC,WAAAA,AAAQ,EAAC,CAAC,CAAC,EAA1B+sB,GAAG,GAAAhtB,SAAA,CAAA,EAAA,EAAEitB,MAAM,GAAAjtB,SAAA,CAAA,EAAA;IAClB,IAAMyK,mBAAmB,GAAGN,sBAAsB,EAAE;IACpD,IAAMvE,cAAc,GAAGa,iBAAiB,EAAE;IAC1C,IAAMymB,WAAW,GAAGjxB,0BAA0B,EAAE;IAChD,IAAMkxB,SAAS,GAAGtwB,wBAAwB,EAAE;IAC5C,IAAAuwB,UAAA,GAAsD1gB,SAAS,EAAE,EAAzDE,mBAAmB,GAAAwgB,UAAA,CAAnBxgB,mBAAmB,EAAExK,UAAU,GAAAgrB,UAAA,CAAVhrB,UAAU,EAAE0K,SAAQ,GAAAsgB,UAAA,CAARtgB,QAAQ;IAEjD,IAAMugB,KAAK,GAAGznB,cAAc,IAAA,OAAA,KAAA,IAAdA,cAAc,CAAE7J,OAAO;IACrC,IAAMtM,KAAK,GAAG49B,KAAK,IAAA,OAAA,KAAA,IAALA,KAAK,CAAE59B,KAAK;IAE1B,QACEhB,yNAAAA,EAACy4B,QAAQ,EAAA;QAACl2B,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACma,eAAe;WAC5C7+B,sNAAAA,EAAC89B,SAAS,EAAA;QAAC98B,KAAK,EAAEA;MAAS,4MAC3BhB,gBAAAA,EAAAA,SAAAA;;QAEE0+B,SAAS,EAAEA,SAAS;sBACR,6BAA6B;QACzC5a,OAAO,EAAE9H,mBAAmB;QAC5BzZ,SAAS,sJAAEkiB,KAAAA,AAAE,EAACC,QAAM,CAACoa,MAAM,CAAC;QAC5BjQ,IAAI,EAAC,MAAM;yBACG,eAAe;QAC7B4P,WAAW,EAAEA,WAAW;QACxBpgB,QAAQ,EAAE,SAAAA,SAAA4C,KAAK;YACbud,MAAM,CAACD,GAAG,GAAG,CAAC,CAAC;YACf13B,UAAU,CAAC;;gBACTwX,SAAQ,CAAA,CAAA0gB,mBAAA,GAAC9d,KAAK,IAAA,OAAA,KAAA,IAAA,CAAA+d,aAAA,GAAL/d,KAAK,CAAEpJ,MAAM,KAAA,OAAA,KAAA,IAAbmnB,aAAA,CAAeh+B,KAAK,KAAA,OAAA+9B,mBAAA,GAAI/9B,KAAK,CAAC;aACxC,CAAC;SACH;QACDmkB,GAAG,EAAEhO;MACL,EACDxD,UAAU,IACT3T,yNAAAA,EAAAA,OAAAA;QACE28B,IAAI,EAAC,QAAQ;QACbp6B,SAAS,sJAAEkiB,KAAAA,AAAE,EAAC,2BAA2B,EAAEC,QAAM,CAACua,cAAc,CAAC;qBACvD,QAAQ;QAClBr1B,EAAE,EAAC,eAAe;uBACN;OAEXuU,mBAAmB,CAChB,GACJ,IAAI,GACRne,yNAAAA,EAACi+B,SAAS,EAAA,KAAG,4MACbj+B,gBAAAA,EAACs9B,cAAc,EAAA,KAAG,CACT;AAEf;AAEA,IAAM5Y,QAAM,GAAA,WAAA,GAAGtlB,UAAU,CAACS,MAAM,CAAA,WAAA,GAAAC,QAAA,CAAA;IAC9Bu+B,OAAO,EAAE;QACPjN,OAAO,EAAE,2BAA2B;QACpChB,MAAM,EAAE;KACT;IACDyO,eAAe,EAAE;QACf,GAAG,EAAE,sBAAsB;QAC3BlI,IAAI,EAAE,GAAG;QACTp3B,OAAO,EAAE,OAAO;QAChB2xB,QAAQ,EAAE;KACX;IACD+N,cAAc,EAAE;QACdC,IAAI,EAAE,eAAe;QACrBC,QAAQ,EAAE,YAAY;QACtBv+B,MAAM,EAAE,KAAK;QACbjB,QAAQ,EAAE,QAAQ;QAClB2lB,QAAQ,EAAE,UAAU;QACpB8Z,UAAU,EAAE,QAAQ;QACpBv+B,KAAK,EAAE;KACR;IACDi+B,MAAM,EAAE;QACN7P,OAAO,EAAE,MAAM;QACftuB,UAAU,EAAE,sBAAsB;QAClC2xB,KAAK,EAAE,oCAAoC;QAC3C5M,YAAY,EAAE,uCAAuC;QACrD0L,OAAO,EAAE,iCAAiC;QAC1CxwB,MAAM,EAAE,gCAAgC;QACxCglB,eAAe,EAAE,kCAAkC;QACnDmJ,MAAM,EAAE,4CAA4C;QACpDluB,KAAK,EAAE,MAAM;QACb,QAAQ,EAAE;YACR+kB,eAAe,EAAE,yCAAyC;YAC1DmJ,MAAM,EAAE;SACT;QACD,eAAe,EAAE;YACfuD,KAAK,EAAE;;KAEV;IAEDiL,cAAc,EAAE;QACd,GAAG,EAAE,sBAAsB;QAC3BjY,QAAQ,EAAE,UAAU;QACpByK,KAAK,EAAE,qCAAqC;QAC5CnvB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbtB,OAAO,EAAE,MAAM;QACfmwB,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBrX,GAAG,EAAE,KAAK;QACV4X,SAAS,EAAE,kBAAkB;QAC7BkB,OAAO,EAAE,GAAG;QACZ1L,YAAY,EAAE,KAAK;QACnB,QAAQ,EAAE;YACRsJ,UAAU,EAAE;SACb;QACD,QAAQ,EAAE;YACRA,UAAU,EAAE;;KAEf;IACDwO,eAAe,EAAE;QACf,GAAG,EAAE,sBAAsB;QAC3B5X,eAAe,EAAE,aAAa;QAC9B6M,gBAAgB,EAAE,WAAW;QAC7BC,cAAc,EAAE,MAAM;QACtB9xB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACb0xB,eAAe,EAAA,SAASmL,QAAQ,GAAA,GAAG;QACnC,QAAQ,EAAE;YACRn9B,mBAAmB,EAAE;SACtB;QACD,QAAQ,EAAE;YACRA,mBAAmB,EAAE;;;AAExB,GAAA,WAAA,GACEO,QAAQ,CAAC,iBAAiB,EAAE;IAC7BP,mBAAmB,EAAE;CACtB,CAAC,EAAA,WAAA,GACCO,QAAQ,CAAC,gBAAgB,EAAE;IAC5B,gCAAgC,EAAE;QAChCP,mBAAmB,EAAE;;CAExB,CAAC,CACH,CAAC;SClKc8+B,MAAMA;IACpB,iNACEr/B,gBAAAA,EAACy4B,QAAQ,EAAA;QACPl2B,SAAS,MAAEkiB,qJAAAA,AAAE,EAAC,YAAY,EAAEpkB,uBAAuB,CAACK,iBAAiB;iNAErEV,gBAAAA,EAACo+B,eAAe,EAAA,KAAG,2MACnBp+B,iBAAAA,EAACi9B,kBAAkB,EAAA,KAAG,CACb;AAEf;ACFA,SAASqC,WAAWA,CAAC1Q,KAAkB;IACrC,iNACE5uB,gBAAAA,EAAC+W,yBAAyB,EAAA,MACxB/W,0NAAAA,EAACD,cAAc,EAAA,KAAG,4MAClBC,gBAAAA,EAAC+L,oBAAoB,EAAA,OAAA,MAAA,CAAA,CAAA,GAAK6iB,KAAK,GAC7B5uB,0NAAAA,EAACu/B,cAAc,EAAA,KAAG,CACG,CACG;AAEhC;AAEA,SAASA,cAAcA;IACrB,IAAAnb,qBAAA,GAA+BxP,qBAAqB,EAAE,EAA/C/S,oBAAoB,GAAAuiB,qBAAA,CAAA,EAAA;IAC3B,IAAM1Y,oBAAoB,GAAGoC,uBAAuB,EAAE;IAEtD,IAAAxB,eAAA,6MAAkCtM,WAAAA,AAAc,EAAC,CAAC6B,oBAAoB,CAAC,EAAhE29B,SAAS,GAAAlzB,eAAA,CAAA,EAAA,EAAEmzB,YAAY,GAAAnzB,eAAA,CAAA,EAAA;IAC9B,IAAMsV,MAAM,GAAGlT,aAAa,EAAE;8MAE9B1O,YAAAA,AAAe,EAAC;QACd,IAAI6B,oBAAoB,IAAI,CAAC6J,oBAAoB,EAAE;YACjD;;QAGF,IAAI,CAAC8zB,SAAS,EAAE;YACdC,YAAY,CAAC,IAAI,CAAC;;KAErB,EAAE;QAACD,SAAS;QAAE9zB,oBAAoB;QAAE7J,oBAAoB;KAAC,CAAC;IAE3D,IAAI,CAAC+f,MAAM,EAAE;QACX,OAAO,IAAI;;IAGb,iNACE5hB,gBAAAA,EAACikB,UAAU,EAAA,UACTjkB,sNAAAA,EAAC2yB,SAAS,EAAA,KAAG,4MACb3yB,gBAAAA,EAAC0/B,qBAAqB,EAAA;QAACF,SAAS,EAAEA;MAAa,CACpC;AAEjB;AAEA,SAASE,qBAAqBA,CAAAp5B,IAAA;QAAGk5B,SAAS,GAAAl5B,IAAA,CAATk5B,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;QACd,OAAO,IAAI;;IAGb,OACEx/B,0NAAAA,EAAAA,qMAAAA,CAAAA,WAAAA,EAAAA,gNACEA,gBAAAA,EAACq/B,MAAM,EAAA,KAAG,4MACVr/B,gBAAAA,EAACw2B,IAAI,EAAA,KAAG,EACRx2B,0NAAAA,EAAC+5B,OAAO,EAAA,KAAG,CACV;AAEP;AAEA,sCAAA;AACA,IAAA,mBAAA,WAAA,6MAAe/5B,OAAAA,AAAU,EAACs/B,WAAW,EAAEn+B,aAAa,CAAC;ACvEtB,IAEVw+B,aAAc,GAAA,WAAA,GAAA,SAAAC,gBAAA;IAAAC,cAAA,CAAAF,aAAA,EAAAC,gBAAA;IAIjC,SAAAD,cAAY/Q,KAAoC;;QAC9CkR,KAAA,GAAAF,gBAAA,CAAAG,IAAA,CAAA,IAAA,EAAMnR,KAAK,CAAC,IAAA,IAAA;QACZkR,KAAA,CAAKruB,KAAK,GAAG;YAAEuuB,QAAQ,EAAE;SAAO;QAAC,OAAAF,KAAA;;IAClCH,aAAA,CAEMM,wBAAwB,GAA/B,SAAAA;QACE,OAAO;YAAED,QAAQ,EAAE;SAAM;KAC1B;IAAA,IAAAE,MAAA,GAAAP,aAAA,CAAAQ,SAAA;IAAAD,MAAA,CAEDE,iBAAiB,GAAjB,SAAAA,kBAAkBC,KAAY,EAAEC,SAAc;;QAE5CC,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,EAAEC,SAAS,CAAC;KACxE;IAAAJ,MAAA,CAEDM,MAAM,GAAN,SAAAA;QACE,IAAI,IAAI,CAAC/uB,KAAK,CAACuuB,QAAQ,EAAE;YACvB,OAAO,IAAI;;QAGb,OAAO,IAAI,CAACpR,KAAK,CAAC5iB,QAAQ;KAC3B;IAAA,OAAA2zB,aAAA;AAAA,wMAxBwC3/B,YAG1C;SCEeygC,aAAaA,CAAAn6B,IAAA;QAC3B4B,OAAO,GAAA5B,IAAA,CAAP4B,OAAO,EAAAw4B,SAAA,GAAAp6B,IAAA,CACPqrB,IAAI,EAAJA,IAAI,GAAA+O,SAAA,KAAA,KAAA,IAAG,EAAE,GAAAA,SAAA,EAAAC,eAAA,GAAAr6B,IAAA,CACTnE,UAAU,EAAVA,UAAU,GAAAw+B,eAAA,KAAA,KAAA,IAAG79B,UAAU,CAAC4C,KAAK,GAAAi7B,eAAA,EAAA/P,aAAA,GAAAtqB,IAAA,CAC7BuqB,QAAQ,EAARA,QAAQ,GAAAD,aAAA,KAAA,KAAA,IAAG,KAAK,GAAAA,aAAA,EAChBxlB,WAAW,GAAA9E,IAAA,CAAX8E,WAAW,EACXmY,QAAQ,GAAAjd,IAAA,CAARid,QAAQ;IASR,IAAI,CAACrb,OAAO,IAAI,CAACqb,QAAQ,IAAI,CAACnY,WAAW,EAAE;QACzC,OAAO,IAAI;;IAGb,iNACEpL,gBAAAA,EAAC0xB,aAAa,EAAA;QACZxpB,OAAO,EAAEA,OAAO;QAChBypB,IAAI,EAAEA,IAAI;QACVxvB,UAAU,EAAEA,UAAU;QACtB0uB,QAAQ,EAAEA,QAAQ;QAClBzlB,WAAW,EAAEmY,QAAQ,GAAG;YAAA,OAAMA,QAAQ;YAAGnY;MACzC;AAEN;SCXwBk0B,aAAWA,CAAC1Q,KAAkB;IACpD,IAAM7hB,gBAAgB,GAAGD,sBAAsB,CAAC;QAC9CG,YAAY,EAAE2hB,KAAK,CAAC3hB,YAAY;QAChCE,eAAe,EAAEyhB,KAAK,CAACzhB,eAAe;QACtCC,gBAAgB,EAAEwhB,KAAK,CAACxhB,gBAAAA;KACzB,CAAC;IAEF,iNACEpN,gBAAAA,EAAC2/B,aAAa,EAAA,gNACZ3/B,gBAAAA,EAACyM,oBAAoB,CAACJ,QAAQ,EAAA;QAACrL,KAAK,EAAE+L;iNACpC/M,gBAAAA,EAAC4gC,gBAAgB,EAAA,OAAA,MAAA,CAAA,CAAA,GAAKhS,KAAK,EAAI,CACD,CAClB;AAEpB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "debugId": null}}]}