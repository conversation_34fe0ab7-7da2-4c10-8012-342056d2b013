(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7839],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(95155);s(12115);var a=s(6874),i=s.n(a),n=s(66766),l=s(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(n.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:l.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:l.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:l.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:l.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:l.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:l.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:l.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(n.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},54165:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>h,Es:()=>u,L3:()=>x,c7:()=>f,lG:()=>l,rr:()=>m,zM:()=>o});var r=s(95155);s(12115);var a=s(4033),i=s(54416),n=s(59434);function l(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,r.jsx)(a.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...s}=e;return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function h(e){let{className:t,children:s,...l}=e;return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...l,children:[s,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s})}function u(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s})}function x(e){let{className:t,...s}=e;return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...s})}function m(e){let{className:t,...s}=e;return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...s})}},56277:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(95155),a=s(7583),i=s(70347),n=s(54165),l=s(21751),o=s(60723),c=s(35695),d=s(12115);let h=()=>{let e=(0,c.useRouter)(),t=(0,c.useSearchParams)(),s=t.get("token"),a=t.get("userType")||"teacher",[i,h]=(0,d.useState)("loading"),[f,u]=(0,d.useState)("");return console.log("Token that is",s),console.log("User type:",a),(0,d.useEffect)(()=>{(async()=>{if(!s){h("error"),u("No verification token");return}try{let t;t="student"===a?await (0,o.Xc)(s):await (0,l.A$)(s),console.log("Verification response:",t),h("success"),u(t.message||"Email verified successfully!"),setTimeout(()=>{e.push("/")},2e3)}catch(e){console.error("Verification error:",e),h("error"),u("Failed to verify email. The token is invalid or expired.")}})()},[s,a,e]),(0,r.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,r.jsxs)(n.lG,{children:["loading"===i&&(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Verifying your email..."}),"success"===i&&(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["Your Email is",(0,r.jsx)("span",{className:"text-orange-500 italic",children:" Verified"}),(0,r.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:f})]}),"error"===i&&(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["Verification",(0,r.jsx)("span",{className:"text-red-500 italic",children:" Failed"}),(0,r.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:f})]})]})})},f=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.default,{}),(0,r.jsx)(d.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading verification..."}),children:(0,r.jsx)(h,{})}),(0,r.jsx)(a.default,{})]})},63657:(e,t,s)=>{Promise.resolve().then(s.bind(s,56277))},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var r=s(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(a),n=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}function o(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?o(Object(s),!0).forEach(function(t){var r,a,i;r=e,a=t,i=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in r?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>r.createElement(h,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>r.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function h(e){var t=t=>{var s,{attr:a,size:i,title:o}=e,d=function(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)s=i[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,n),h=i||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:h,width:h,xmlns:"http://www.w3.org/2000/svg"}),o&&r.createElement("title",null,o),e.children)};return void 0!==i?r.createElement(i.Consumer,null,e=>t(e)):t(a)}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,347,8441,1684,7358],()=>t(63657)),_N_E=e.O()}]);