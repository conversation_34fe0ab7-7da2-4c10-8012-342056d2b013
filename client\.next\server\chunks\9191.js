"use strict";exports.id=9191,exports.ids=[9191],exports.modules={99191:(e,t,r)=>{r.d(t,{i3:()=>eO,UC:()=>eP,ZL:()=>eR,Kq:()=>eC,bL:()=>eT,l9:()=>eN});var n,o=r(43210),i=r.t(o,2);function a(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return o.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}(...e),e)}var u=r(60687);function c(e,t=[]){let r=[],n=()=>{let t=r.map(e=>o.createContext(e));return function(r){let n=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let i=o.createContext(n),a=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,s=r?.[e]?.[a]||i,c=o.useMemo(()=>l,Object.values(l));return(0,u.jsx)(s.Provider,{value:c,children:n})};return l.displayName=t+"Provider",[l,function(r,l){let s=l?.[e]?.[a]||i,u=o.useContext(s);if(u)return u;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}var d=r(51215),f=r(11329),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,f.TL)(`Primitive.${t}`),n=o.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function m(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var v="dismissableLayer.update",h=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:c,onInteractOutside:d,onDismiss:f,...y}=e,w=o.useContext(h),[b,E]=o.useState(null),C=b?.ownerDocument??globalThis?.document,[,T]=o.useState({}),N=s(t,e=>E(e)),R=Array.from(w.layers),[P]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),O=R.indexOf(P),L=b?R.indexOf(b):-1,j=w.layersWithOutsidePointerEventsDisabled.size>0,S=L>=O,A=function(e,t=globalThis?.document){let r=m(e),n=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=n,t.addEventListener("click",i.current,{once:!0})):n()}else t.removeEventListener("click",i.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));!S||r||(l?.(e),d?.(e),e.defaultPrevented||f?.())},C),D=function(e,t=globalThis?.document){let r=m(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(c?.(e),d?.(e),e.defaultPrevented||f?.())},C);return function(e,t=globalThis?.document){let r=m(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{L===w.layers.size-1&&(i?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},C),o.useEffect(()=>{if(b)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(b)),w.layers.add(b),g(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[b,C,r,w]),o.useEffect(()=>()=>{b&&(w.layers.delete(b),w.layersWithOutsidePointerEventsDisabled.delete(b),g())},[b,w]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,u.jsx)(p.div,{...y,ref:N,style:{pointerEvents:j?S?"auto":"none":void 0,...e.style},onFocusCapture:a(e.onFocusCapture,D.onFocusCapture),onBlurCapture:a(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:a(e.onPointerDownCapture,A.onPointerDownCapture)})});function g(){let e=new CustomEvent(v);document.dispatchEvent(e)}function x(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&o.addEventListener(e,t,{once:!0}),n)o&&d.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(h),n=o.useRef(null),i=s(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var w=globalThis?.document?o.useLayoutEffect:()=>{},b=i[" useId ".trim().toString()]||(()=>void 0),E=0,C=r(4503),T=r(25605),N=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,u.jsx)(p.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,u.jsx)("polygon",{points:"0,0 30,0 15,10"})})});N.displayName="Arrow";var R="Popper",[P,O]=c(R),[L,j]=P(R),S=e=>{let{__scopePopper:t,children:r}=e,[n,i]=o.useState(null);return(0,u.jsx)(L,{scope:t,anchor:n,onAnchorChange:i,children:r})};S.displayName=R;var A="PopperAnchor",D=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...i}=e,a=j(A,r),l=o.useRef(null),c=s(t,l);return o.useEffect(()=>{a.onAnchorChange(n?.current||l.current)}),n?null:(0,u.jsx)(p.div,{...i,ref:c})});D.displayName=A;var k="PopperContent",[M,$]=P(k),I=o.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:i=0,align:a="center",alignOffset:l=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:v=0,sticky:h="partial",hideWhenDetached:y=!1,updatePositionStrategy:g="optimized",onPlaced:x,...b}=e,E=j(k,r),[N,R]=o.useState(null),P=s(t,e=>R(e)),[O,L]=o.useState(null),S=function(e){let[t,r]=o.useState(void 0);return w(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(O),A=S?.width??0,D=S?.height??0,$="number"==typeof v?v:{top:0,right:0,bottom:0,left:0,...v},I=Array.isArray(f)?f:[f],_=I.length>0,W={padding:$,boundary:I.filter(B),altBoundary:_},{refs:U,floatingStyles:H,placement:Y,isPositioned:X,middlewareData:V}=(0,C.we)({strategy:"fixed",placement:n+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(0,T.ll)(...e,{animationFrame:"always"===g}),elements:{reference:E.anchor},middleware:[(0,C.cY)({mainAxis:i+D,alignmentAxis:l}),d&&(0,C.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?(0,C.ER)():void 0,...W}),d&&(0,C.UU)({...W}),(0,C.Ej)({...W,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),O&&(0,C.UE)({element:O,padding:c}),z({arrowWidth:A,arrowHeight:D}),y&&(0,C.jD)({strategy:"referenceHidden",...W})]}),[q,K]=F(Y),Z=m(x);w(()=>{X&&Z?.()},[X,Z]);let G=V.arrow?.x,J=V.arrow?.y,Q=V.arrow?.centerOffset!==0,[ee,et]=o.useState();return w(()=>{N&&et(window.getComputedStyle(N).zIndex)},[N]),(0,u.jsx)("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:X?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,u.jsx)(M,{scope:r,placedSide:q,onArrowChange:L,arrowX:G,arrowY:J,shouldHideArrow:Q,children:(0,u.jsx)(p.div,{"data-side":q,"data-align":K,...b,ref:P,style:{...b.style,animation:X?void 0:"none"}})})})});I.displayName=k;var _="PopperArrow",W={top:"bottom",right:"left",bottom:"top",left:"right"},U=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=$(_,r),i=W[o.placedSide];return(0,u.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,u.jsx)(N,{...n,ref:t,style:{...n.style,display:"block"}})})});function B(e){return null!==e}U.displayName=_;var z=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[s,u]=F(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===s?(p=i?c:`${d}px`,m=`${-l}px`):"top"===s?(p=i?c:`${d}px`,m=`${n.floating.height+l}px`):"right"===s?(p=`${-l}px`,m=i?c:`${f}px`):"left"===s&&(p=`${n.floating.width+l}px`,m=i?c:`${f}px`),{data:{x:p,y:m}}}});function F(e){let[t,r="center"]=e.split("-");return[t,r]}var H=o.forwardRef((e,t)=>{let{container:r,...n}=e,[i,a]=o.useState(!1);w(()=>a(!0),[]);let l=r||i&&globalThis?.document?.body;return l?d.createPortal((0,u.jsx)(p.div,{...n,ref:t}),l):null});H.displayName="Portal";var Y=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,i]=o.useState(),a=o.useRef(null),l=o.useRef(e),s=o.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>r[e][t]??e,t));return o.useEffect(()=>{let e=X(a.current);s.current="mounted"===u?e:"none"},[u]),w(()=>{let t=a.current,r=l.current;if(r!==e){let n=s.current,o=X(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),w(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let o=X(a.current).includes(r.animationName);if(r.target===n&&o&&(c("ANIMATION_END"),!l.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},o=e=>{e.target===n&&(s.current=X(a.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof r?r({present:n.isPresent}):o.Children.only(r),a=s(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||n.isPresent?o.cloneElement(i,{ref:a}):null};function X(e){return e?.animationName||"none"}Y.displayName="Presence";var V=i[" useInsertionEffect ".trim().toString()]||w,q=(Symbol("RADIX:SYNC_STATE"),Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"})),K=o.forwardRef((e,t)=>(0,u.jsx)(p.span,{...e,ref:t,style:{...q,...e.style}}));K.displayName="VisuallyHidden";var[Z,G]=c("Tooltip",[O]),J=O(),Q="TooltipProvider",ee="tooltip.open",[et,er]=Z(Q),en=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:n=300,disableHoverableContent:i=!1,children:a}=e,l=o.useRef(!0),s=o.useRef(!1),c=o.useRef(0);return o.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,u.jsx)(et,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:o.useCallback(()=>{window.clearTimeout(c.current),l.current=!1},[]),onClose:o.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.current=!0,n)},[n]),isPointerInTransitRef:s,onPointerInTransitChange:o.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:a})};en.displayName=Q;var eo="Tooltip",[ei,ea]=Z(eo),el=e=>{let{__scopeTooltip:t,children:r,open:n,defaultOpen:i,onOpenChange:a,disableHoverableContent:l,delayDuration:s}=e,c=er(eo,e.__scopeTooltip),d=J(t),[f,p]=o.useState(null),m=function(e){let[t,r]=o.useState(b());return w(()=>{r(e=>e??String(E++))},[void 0]),e||(t?`radix-${t}`:"")}(),v=o.useRef(0),h=l??c.disableHoverableContent,y=s??c.delayDuration,g=o.useRef(!1),[x,C]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,a,l]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),a=o.useRef(t);return V(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==r&&(a.current?.(r),i.current=r)},[r,i]),[r,n,a]}({defaultProp:t,onChange:r}),s=void 0!==e,u=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[u,o.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else a(t)},[s,e,a,l])]}({prop:n,defaultProp:i??!1,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(ee))):c.onClose(),a?.(e)},caller:eo}),T=o.useMemo(()=>x?g.current?"delayed-open":"instant-open":"closed",[x]),N=o.useCallback(()=>{window.clearTimeout(v.current),v.current=0,g.current=!1,C(!0)},[C]),R=o.useCallback(()=>{window.clearTimeout(v.current),v.current=0,C(!1)},[C]),P=o.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{g.current=!0,C(!0),v.current=0},y)},[y,C]);return o.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,u.jsx)(S,{...d,children:(0,u.jsx)(ei,{scope:t,contentId:m,open:x,stateAttribute:T,trigger:f,onTriggerChange:p,onTriggerEnter:o.useCallback(()=>{c.isOpenDelayedRef.current?P():N()},[c.isOpenDelayedRef,P,N]),onTriggerLeave:o.useCallback(()=>{h?R():(window.clearTimeout(v.current),v.current=0)},[R,h]),onOpen:N,onClose:R,disableHoverableContent:h,children:r})})};el.displayName=eo;var es="TooltipTrigger",eu=o.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,i=ea(es,r),l=er(es,r),c=J(r),d=s(t,o.useRef(null),i.onTriggerChange),f=o.useRef(!1),m=o.useRef(!1),v=o.useCallback(()=>f.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,u.jsx)(D,{asChild:!0,...c,children:(0,u.jsx)(p.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...n,ref:d,onPointerMove:a(e.onPointerMove,e=>{"touch"===e.pointerType||m.current||l.isPointerInTransitRef.current||(i.onTriggerEnter(),m.current=!0)}),onPointerLeave:a(e.onPointerLeave,()=>{i.onTriggerLeave(),m.current=!1}),onPointerDown:a(e.onPointerDown,()=>{i.open&&i.onClose(),f.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:a(e.onFocus,()=>{f.current||i.onOpen()}),onBlur:a(e.onBlur,i.onClose),onClick:a(e.onClick,i.onClose)})})});eu.displayName=es;var ec="TooltipPortal",[ed,ef]=Z(ec,{forceMount:void 0}),ep=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=ea(ec,t);return(0,u.jsx)(ed,{scope:t,forceMount:r,children:(0,u.jsx)(Y,{present:r||i.open,children:(0,u.jsx)(H,{asChild:!0,container:o,children:n})})})};ep.displayName=ec;var em="TooltipContent",ev=o.forwardRef((e,t)=>{let r=ef(em,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,a=ea(em,e.__scopeTooltip);return(0,u.jsx)(Y,{present:n||a.open,children:a.disableHoverableContent?(0,u.jsx)(ew,{side:o,...i,ref:t}):(0,u.jsx)(eh,{side:o,...i,ref:t})})}),eh=o.forwardRef((e,t)=>{let r=ea(em,e.__scopeTooltip),n=er(em,e.__scopeTooltip),i=o.useRef(null),a=s(t,i),[l,c]=o.useState(null),{trigger:d,onClose:f}=r,p=i.current,{onPointerInTransitChange:m}=n,v=o.useCallback(()=>{c(null),m(!1)},[m]),h=o.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),m(!0)},[m]);return o.useEffect(()=>()=>v(),[v]),o.useEffect(()=>{if(d&&p){let e=e=>h(e,p),t=e=>h(e,d);return d.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[d,p,h,v]),o.useEffect(()=>{if(l){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=d?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],s=a.x,u=a.y,c=l.x,d=l.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,l);n?v():o&&(v(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[d,p,l,f,v]),(0,u.jsx)(ew,{...e,ref:a})}),[ey,eg]=Z(eo,{isInside:!1}),ex=(0,f.Dc)("TooltipContent"),ew=o.forwardRef((e,t)=>{let{__scopeTooltip:r,children:n,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:l,...s}=e,c=ea(em,r),d=J(r),{onClose:f}=c;return o.useEffect(()=>(document.addEventListener(ee,f),()=>document.removeEventListener(ee,f)),[f]),o.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,f]),(0,u.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,u.jsxs)(I,{"data-state":c.stateAttribute,...d,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,u.jsx)(ex,{children:n}),(0,u.jsx)(ey,{scope:r,isInside:!0,children:(0,u.jsx)(K,{id:c.contentId,role:"tooltip",children:i||n})})]})})});ev.displayName=em;var eb="TooltipArrow",eE=o.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=J(r);return eg(eb,r).isInside?null:(0,u.jsx)(U,{...o,...n,ref:t})});eE.displayName=eb;var eC=en,eT=el,eN=eu,eR=ep,eP=ev,eO=eE}};