<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentSiblingsDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'sibling_name' => 'required|string|max:200',
            "sibling_date_of_birth"    => "required|string|max:200",
            "studying_std"    => "required|string|max:200",
            "school_name"    => "required|string|max:200",
        ];
    }
}
