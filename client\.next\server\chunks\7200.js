exports.id=7200,exports.ids=[7200],exports.modules={3792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(37413),o=r(50417);function i({children:e}){return(0,n.jsx)(o<PERSON>,{children:(0,n.jsx)(o<PERSON>,{children:(0,n.jsx)("div",{className:"flex flex-1 flex-col",children:(0,n.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-2",children:(0,n.jsx)("div",{className:"flex flex-col",children:e})})})})})}},35950:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>a});var n=r(60687);r(43210);var o=r(42123),i=r(4780);function a({className:e,orientation:t="horizontal",decorative:r=!0,...a}){return(0,n.jsx)(o.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...a})}},50417:(e,t,r)=>{"use strict";r.d(t,{SidebarInset:()=>o,SidebarProvider:()=>i});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","Sidebar"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarContent"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarFooter"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarGroup"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarGroupAction"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarGroupContent"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarGroupLabel"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarHeader"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarInput");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarInset");(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenu"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuAction"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuBadge"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuButton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuItem"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuSub"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubButton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubItem");let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarRail"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarSeparator"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","SidebarTrigger"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx","useSidebar")},55492:(e,t,r)=>{"use strict";r.d(t,{SidebarInset:()=>c,SidebarProvider:()=>p});var n=r(60687),o=r(43210),i=r(24224),a=r(82120),s=r(4780);r(29523),r(89667),r(35950);r(85726);var d=r(76242);let l=o.createContext(null);function p({defaultOpen:e=!0,open:t,onOpenChange:r,className:i,style:p,children:c,...u}){let b=(0,a.a)(),[f,m]=o.useState(!1),[h,v]=o.useState(e),S=t??h,C=o.useCallback(e=>{let t="function"==typeof e?e(S):e;r?r(t):v(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[r,S]),x=o.useCallback(()=>b?m(e=>!e):C(e=>!e),[b,C,m]);o.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),x())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[x]);let g=S?"expanded":"collapsed",w=o.useMemo(()=>({state:g,open:S,setOpen:C,isMobile:b,openMobile:f,setOpenMobile:m,toggleSidebar:x}),[g,S,C,b,f,m,x]);return(0,n.jsx)(l.Provider,{value:w,children:(0,n.jsx)(d.Bc,{delayDuration:0,children:(0,n.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...p},className:(0,s.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",i),...u,children:c})})})}function c({className:e,...t}){return(0,n.jsx)("main",{"data-slot":"sidebar-inset",className:(0,s.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}(0,i.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}})},76242:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>a,ZI:()=>l,k$:()=>d,m_:()=>s});var n=r(60687);r(43210);var o=r(99191),i=r(4780);function a({delayDuration:e=0,...t}){return(0,n.jsx)(o.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function s({...e}){return(0,n.jsx)(a,{children:(0,n.jsx)(o.bL,{"data-slot":"tooltip",...e})})}function d({...e}){return(0,n.jsx)(o.l9,{"data-slot":"tooltip-trigger",...e})}function l({className:e,sideOffset:t=0,children:r,...a}){return(0,n.jsx)(o.ZL,{children:(0,n.jsxs)(o.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...a,children:[r,(0,n.jsx)(o.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},82120:(e,t,r)=>{"use strict";r.d(t,{a:()=>o});var n=r(43210);function o(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(60687),o=r(4780);function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"skeleton",className:(0,o.cn)("bg-accent animate-pulse rounded-md",e),...t})}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687);r(43210);var o=r(4780);function i({className:e,type:t,...r}){return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91141:(e,t,r)=>{Promise.resolve().then(r.bind(r,55492))},97221:(e,t,r)=>{Promise.resolve().then(r.bind(r,50417))}};