exports.id=2800,exports.ids=[2800],exports.modules={4038:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,47429,23)),Promise.resolve().then(s.bind(s,48482)),Promise.resolve().then(s.bind(s,95631)),Promise.resolve().then(s.bind(s,99420))},4780:(e,t,s)=>{"use strict";s.d(t,{MB:()=>n,ZO:()=>i,cn:()=>l,xh:()=>o});var a=s(49384),r=s(82348);function l(...e){return(0,r.QP)((0,a.$)(e))}let i=()=>localStorage.getItem("studentToken"),n=()=>{localStorage.removeItem("studentToken")},o=()=>!!i()},9236:(e,t,s)=>{"use strict";s.d(t,{HK:()=>r,Ou:()=>h,Pz:()=>l,a1:()=>u,au:()=>m,fm:()=>n,jc:()=>i,kI:()=>c,pO:()=>o,sl:()=>d});var a=s(28527);let r=async(e=1,t=10)=>(await a.S.get(`/notifications/classes?page=${e}&limit=${t}`)).data.data,l=async()=>(await a.S.get("/notifications/classes/count")).data.data.count,i=async e=>(await a.S.post(`/notifications/classes/mark-read/${e}`)).data,n=async()=>(await a.S.post("/notifications/classes/mark-all-read")).data,o=async()=>(await a.S.delete("/notifications/classes/delete-all")).data,c=async(e=1,t=10)=>(await a.S.get(`/notifications/students?page=${e}&limit=${t}`)).data.data,d=async()=>(await a.S.get("/notifications/students/count")).data.data.count,h=async e=>(await a.S.post(`/notifications/students/mark-read/${e}`)).data,u=async()=>(await a.S.post("/notifications/students/mark-all-read")).data,m=async()=>(await a.S.delete("/notifications/students/delete-all")).data},15347:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},17741:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app-components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx","default")},20672:(e,t,s)=>{"use strict";s.d(t,{V:()=>r});var a=s(28527);let r=(0,s(9317).zD)("class/fetchClassDetails",async(e,{rejectWithValue:t})=>{try{return(await a.S.get(`/classes/details/${e}`)).data}catch(e){return t(e.response?.data||"Fetch failed")}})},27808:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>h});var a=s(60687),r=s(54864),l=s(9317),i=s(30072),n=s(50346),o=s(36097),c=s(45201);let d=(0,l.U1)({reducer:{user:i.Ay,formProgress:n.Ay,class:o.A,studentProfile:c.Ay}});function h({children:e}){return(0,a.jsx)(r.Kq,{store:d,children:e})}},28281:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,46303)),Promise.resolve().then(s.bind(s,90269))},28527:(e,t,s)=>{"use strict";s.d(t,{S:()=>i});var a=s(51060),r=s(52581);let l=process.env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",l);let i=a.A.create({baseURL:l,headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":l;let s=localStorage.getItem("studentToken");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(r.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data")),Promise.reject(e)))},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>n});var a=s(60687);s(43210);var r=s(11329),l=s(24224),i=s(4780);let n=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:s,asChild:l=!1,...o}){let c=l?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(n({variant:t,size:s,className:e})),...o})}},30072:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>i,gV:()=>r,lM:()=>l});let a=(0,s(9317).Z0)({name:"user",initialState:{user:null,isAuthenticated:!1},reducers:{setUser:(e,t)=>{e.user=t.payload.user,e.isAuthenticated=!0,localStorage.setItem("user",JSON.stringify(t.payload.user))},clearUser:e=>{e.user=null,e.isAuthenticated=!1,localStorage.removeItem("user")}}}),{setUser:r,clearUser:l}=a.actions,i=a.reducer},32584:(e,t,s)=>{"use strict";s.d(t,{eu:()=>i,q5:()=>n});var a=s(60687);s(43210);var r=s(17403),l=s(4780);function i({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function n({className:e,...t}){return(0,a.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},36097:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,B:()=>i});var a=s(9317),r=s(20672);let l=(0,a.Z0)({name:"class",initialState:{classData:null,loading:!1,error:null},reducers:{setClassData(e,t){e.classData=t.payload}},extraReducers:e=>{e.addCase(r.V.pending,e=>{e.loading=!0,e.error=null}).addCase(r.V.fulfilled,(e,t)=>{e.loading=!1,e.classData=t.payload}).addCase(r.V.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setClassData:i}=l.actions,n=l.reducer},37075:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app-components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx","default")},38542:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,79167,23)),Promise.resolve().then(s.bind(s,64616)),Promise.resolve().then(s.bind(s,49360)),Promise.resolve().then(s.bind(s,27808))},39777:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>u});var a=s(37413);s(61135);var r=s(99420),l=s(48482),i=s(73125),n=s.n(i),o=s(36162),c=s(95631),d=s(61120);let h="G-N06ZRQXN1Y",u={title:"Uest - Your Gateway to Educational Excellence",description:"Your Gateway to Educational Excellence"};function m({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsx)("head",{children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.default,{src:`https://www.googletagmanager.com/gtag/js?id=${h}`,strategy:"afterInteractive"}),(0,a.jsx)(o.default,{id:"gtag-init",strategy:"afterInteractive",children:`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${h}', {
                page_path: window.location.pathname,
              });
            `})]})}),(0,a.jsx)("body",{className:`antialiased ${n().className}`,children:(0,a.jsxs)(r.ReduxProvider,{children:[(0,a.jsx)(d.Suspense,{fallback:null,children:(0,a.jsx)(c.default,{})}),e,(0,a.jsx)(l.Toaster,{})]})})]})}},40988:(e,t,s)=>{"use strict";s.d(t,{AM:()=>i,Wv:()=>n,hl:()=>o});var a=s(60687);s(43210);var r=s(36141),l=s(4780);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"popover",...e})}function n({...e}){return(0,a.jsx)(r.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:t="center",sideOffset:s=4,...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:s,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...i})})}},41831:(e,t,s)=>{"use strict";s.d(t,{RY:()=>o,Ty:()=>c,Xc:()=>d,af:()=>n,bZ:()=>i,iM:()=>r,zy:()=>l});var a=s(28527);let r=async e=>(await a.S.post("/student/continue-with-email",e)).data,l=async e=>(await a.S.post("/student/register",e)).data,i=async e=>(await a.S.post("/student/login",e)).data,n=async()=>(localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),{success:!0,message:"Logged out successfully"});async function o(e){return(await a.S.post("/student/verify-otp",e)).data}async function c(e){return(await a.S.post("/student/resend-otp",e)).data}let d=async e=>(await a.S.post("/student/verify-email",{token:e})).data},45201:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,Ig:()=>o,XY:()=>n});var a=s(9317),r=s(74004);let l=(0,a.Z0)({name:"studentProfile",initialState:{profileData:null,loading:!1,error:null},reducers:{setStudentProfileData(e,t){e.profileData=t.payload},updateProfilePhoto(e,t){e.profileData?.profile&&(e.profileData.profile.photo=t.payload)},clearStudentProfileData(e){e.profileData=null,e.loading=!1,e.error=null}},extraReducers:e=>{e.addCase(r.N.pending,e=>{e.loading=!0,e.error=null}).addCase(r.N.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(r.N.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}).addCase(r.A.pending,e=>{e.loading=!0,e.error=null}).addCase(r.A.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(r.A.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setStudentProfileData:i,updateProfilePhoto:n,clearStudentProfileData:o}=l.actions,c=l.reducer},46303:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(60687);s(43210);var r=s(85814),l=s.n(r),i=s(30474),n=s(69587);let o=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(({href:e,icon:t,label:s})=>(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(l(),{href:e,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:s,children:(0,a.jsx)(t,{className:"text-xl text-white hover:text-gray-400 transition"})})},s))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},48482:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx","Toaster")},49360:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var a=s(16189);function r(){return(0,a.usePathname)(),(0,a.useSearchParams)(),null}s(43210)},50346:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,_3:()=>r,ac:()=>i});var a=s(9317),r=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let l=(0,a.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let s=t.payload;e.completedForms[s]||(e.completedForms[s]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:n}=l.actions,o=l.reducer},52299:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},54413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(37413),r=s(4536),l=s.n(r),i=s(37075),n=s(17741);function o(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white overflow-hidden",children:(0,a.jsxs)("div",{className:"text-center px-4",children:[(0,a.jsx)("h1",{className:"text-9xl font-extrabold text-[#FD904B] tracking-widest mb-5",children:"404"}),(0,a.jsx)("h2",{className:"mt-4 text-3xl font-semibold text-gray-800",children:"Page Not Found"}),(0,a.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"Oops! The page you're looking for doesn't exist."}),(0,a.jsx)(l(),{href:"/",children:(0,a.jsx)("button",{className:"mt-8 px-6 py-3 bg-[#FD904B] text-white font-medium rounded-lg hover:bg-[#974813] transition-colors duration-300",children:"Back to Home"})})]})}),(0,a.jsx)(n.default,{})]})}},58759:(e,t,s)=>{"use strict";s.d(t,{A$:()=>d,DY:()=>l,Lx:()=>i,RY:()=>n,Ty:()=>o,bi:()=>c,iM:()=>r});var a=s(28527);async function r(e){return(await a.S.post("/auth-client/continue-with-email",e)).data}async function l(e){return(await a.S.post("/auth-client/register",e)).data}async function i(e){return(await a.S.post("/auth-client/login",e)).data}async function n(e){return(await a.S.post("/auth-client/verify-otp",e)).data}async function o(e){return(await a.S.post("/auth-client/resend-otp",e)).data}let c=async(e,t)=>(await a.S.post("/auth-client/generate-jwt",{contact:e,password:t})).data,d=async e=>(await a.S.get("/auth-client/verify-email",{params:{token:e}})).data},61135:()=>{},64616:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>i});var a=s(60687),r=s(10218),l=s(52581);let i=({...e})=>{let{theme:t="system"}=(0,r.D)();return(0,a.jsx)(l.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74004:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,N:()=>l});var a=s(28527),r=s(9317);let l=(0,r.zD)("studentProfile/fetchStudentProfile",async(e,{rejectWithValue:t})=>{try{let e=localStorage.getItem("studentToken");if(!e)return t("No authentication token found");let s=await a.S.get("/student-profile/all-data",{headers:{Authorization:`Bearer ${e}`}});if(s.data&&"object"==typeof s.data){if(void 0!==s.data.success&&void 0!==s.data.data)return s.data.data;return s.data}return null}catch(e){if(e.response?.status===404)return null;return t(e.response?.data?.message||"Failed to fetch student data")}}),i=(0,r.zD)("studentProfile/updateStudentProfile",async(e,{rejectWithValue:t})=>{try{let s=localStorage.getItem("studentToken");if(!s)return t("No authentication token found");let r=await (0,a.S)({method:"put",url:"/student-profile/combined",data:e,headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`}});if(r.data&&"object"==typeof r.data){if(void 0!==r.data.success&&void 0!==r.data.data)return r.data.data;return r.data}return null}catch(e){return t(e.response?.data?.message||"Failed to update student profile")}})},90269:(e,t,s)=>{"use strict";s.d(t,{default:()=>W});var a=s(60687),r=s(85814),l=s.n(r),i=s(30474),n=s(27351),o=s(41923),c=s(49158),d=s(57800),h=s(14952),u=s(11860),m=s(12941),x=s(58887),f=s(58869),p=s(81620),g=s(86356),v=s(71057),b=s(49625),j=s(29523),N=s(54864);let w=()=>(0,N.wA)();var y=s(43210),k=s(4780),S=s(16189);s(70334);let C=()=>{(0,S.useRouter)();let{profileData:e}=(0,N.d4)(e=>e.studentProfile);return e?.profile?.id,null};var A=s(97051),$=s(40988),P=s(93500),z=s(9236),R=s(52581),T=s(50765);function F({userType:e}){let[t,s]=(0,y.useState)([]),[r,l]=(0,y.useState)(0),[i,n]=(0,y.useState)(!1),[o,c]=(0,y.useState)(!1),[d,h]=(0,y.useState)(!1),u=(0,S.useRouter)(),m=Array.isArray(t)?t:[];(0,y.useCallback)(async()=>{try{let t,a;c(!0),"class"===e?(t=await (0,z.HK)(1,20),a=await (0,z.Pz)()):(t=await (0,z.kI)(1,20),a=await (0,z.sl)());let r=t?.notifications||t||[];s(Array.isArray(r)?r:[]),l(a)}catch(e){console.error("Error fetching notifications:",e),s([]),l(0)}finally{c(!1)}},[e]);let x=async t=>{try{"class"===e?await (0,z.jc)(t.id):await (0,z.Ou)(t.id),s(e=>e.map(e=>e.id===t.id?{...e,isRead:!0}:e)),l(e=>Math.max(0,e-1)),n(!1),t.data?.actionType==="OPEN_CHAT"&&t.data?.redirectUrl&&u.push(t.data.redirectUrl)}catch(e){console.error("Error handling notification click:",e),R.oR.error("Failed to process notification")}},f=async()=>{try{"class"===e?await (0,z.fm)():await (0,z.a1)(),s(e=>e.map(e=>({...e,isRead:!0}))),l(0),R.oR.success("All notifications marked as read")}catch(e){console.error("Error marking all notifications as read:",e),R.oR.error("Failed to mark all notifications as read")}},p=async()=>{h(!1);try{"class"===e?await (0,z.pO)():await (0,z.au)(),s([]),l(0),R.oR.success("All notifications removed successfully")}catch(e){console.error("Error removing all notifications:",e),R.oR.error("Failed to remove all notifications")}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)($.AM,{open:i,onOpenChange:n,children:[(0,a.jsx)($.Wv,{asChild:!0,children:(0,a.jsxs)(j.$,{variant:"outline",size:"icon",className:"relative group rounded-full border-2 border-orange-500 hover:border-orange-400 bg-black hover:bg-gray-900 transition-all duration-200 h-8 w-8 md:h-10 md:w-10",children:[(0,a.jsx)("div",{className:"absolute rounded-full inset-0 bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center justify-center",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 md:h-5 md:w-5 text-orange-500 group-hover:text-orange-400 transition-colors duration-200"}),r>0&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white",children:(0,a.jsx)("span",{className:"text-white text-[10px] md:text-xs font-bold leading-none",children:r>99?"99+":r})})]})]})}),(0,a.jsxs)($.hl,{className:"w-80 p-0",align:"end",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Notifications"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[r>0&&(0,a.jsx)(j.$,{variant:"ghost",size:"sm",onClick:f,className:"text-xs",children:"Mark all read"}),t.length>0&&0===r&&(0,a.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>{h(!0)},className:"text-xs text-red-600 hover:text-red-700 hover:bg-red-50",children:"Remove all"})]})]})}),(0,a.jsx)("div",{className:"h-80 overflow-y-auto",children:o?(0,a.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"Loading notifications..."}):0===t.length?(0,a.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"No notifications yet"}):(0,a.jsx)("div",{className:"divide-y",children:Array.isArray(t)&&t.map(e=>(0,a.jsx)("div",{className:`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${e.isRead?"":"bg-blue-50/50"}`,onClick:()=>x(e),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full mt-2 ${e.isRead?"bg-gray-300":"bg-blue-500"}`}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:(0,T.m)(new Date(e.createdAt),{addSuffix:!0})})]})]})},e.id))})}),m.length>0&&(0,a.jsx)("div",{className:"p-3 border-t bg-muted/30",children:(0,a.jsx)(j.$,{variant:"ghost",size:"sm",className:"w-full text-xs",onClick:()=>{n(!1),u.push("/notifications")},children:"View All Notifications"})})]})]}),(0,a.jsx)(P.Lt,{open:d,onOpenChange:h,children:(0,a.jsxs)(P.EO,{children:[(0,a.jsxs)(P.wd,{children:[(0,a.jsx)(P.r7,{children:"Remove All Notifications"}),(0,a.jsx)(P.$v,{children:"Are you sure you want to remove all notifications? This action cannot be undone."})]}),(0,a.jsxs)(P.ck,{children:[(0,a.jsx)(P.Zr,{children:"Cancel"}),(0,a.jsx)(P.Rx,{onClick:p,className:"bg-red-600 hover:bg-red-700",children:"Remove All"})]})]})})]})}var I=s(32584),E=s(41831),D=s(30072),L=s(45201),_=s(74004),U=s(28527),M=s(17135),O=s(84517),H=s(92449),B=s(58759),G=s(90971);let Z=({studentId:e})=>{let[t,s]=(0,y.useState)(0);return(0,y.useEffect)(()=>{(async()=>{if(!e){s(0);return}let t=await (0,G.G)(e);t.success&&t.data?s(t.data.streak||0):s(0)})()},[e]),(0,a.jsxs)("span",{className:"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1",children:["\uD83D\uDD25 ",t]})},W=()=>{let{isAuthenticated:e,user:t}=(0,N.d4)(e=>e.user),[s,r]=(0,y.useState)(!1),[A,P]=(0,y.useState)(!1),[z,T]=(0,y.useState)(null),G=w(),W=(0,S.useRouter)(),Y=(0,y.useRef)(null),[q,V]=(0,y.useState)(0),[X,J]=(0,y.useState)(!1),Q=(0,M.d)(0),K=q/20;(0,y.useEffect)(()=>{let e=(0,k.xh)();if(P(e),e){let e=localStorage.getItem("student_data");e&&T(JSON.parse(e)),G((0,_.N)())}let t=()=>{let e=(0,k.xh)();if(P(e),e){let e=localStorage.getItem("student_data");e&&T(JSON.parse(e)),G((0,_.N)())}else T(null)};return window.addEventListener("storage",t),Y.current&&V(Y.current.getBoundingClientRect().width),()=>{window.removeEventListener("storage",t)}},[G]),(0,O.N)((e,t)=>{if(X||0===q)return;let s=Q.get()-K*t/1e3;s<=-q&&(s=0),Q.set(s)});let ee=()=>r(!s),et=async()=>{try{let e=await (0,E.af)();!1!==e.success?((0,k.MB)(),P(!1),T(null),localStorage.removeItem("student_data"),G((0,L.Ig)()),R.oR.success("Logged out successfully"),window.dispatchEvent(new Event("storage"))):(R.oR.error(e.message||"Failed to logout"),(0,k.MB)(),P(!1),T(null),localStorage.removeItem("student_data"),G((0,L.Ig)()))}catch(e){console.log("Failed to logout",e),R.oR.error("Failed to logout"),localStorage.removeItem("student_data"),(0,k.MB)(),P(!1),T(null),G((0,L.Ig)())}},es=async()=>{try{let e=await (0,B.bi)(t?.contactNo,t?.password);if(e.success){let{token:s}=e.data,a=`http://127.0.0.1:8000/login-class-link?uid=${t?.id}&token=${s}`;window.location.href=a}else R.oR.error(e.message||"Failed to generate token")}catch(e){console.error("Failed to generate token",e),R.oR.error("Failed to generate token")}},ea=[{href:"/verified-classes",label:"Find Tutor",icon:(0,a.jsx)(n.A,{className:"w-4 h-4"})},{href:"/uwhiz",label:"U - Whiz",icon:(0,a.jsx)(o.A,{className:"w-4 h-4"}),isNew:!0},{href:"/mock-exam-card",label:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:"Daily Quiz"}),A&&(0,a.jsx)(Z,{studentId:z?.id})]}),icon:(0,a.jsx)(c.A,{className:"w-4 h-4"})},{href:"/careers",label:"Career",icon:(0,a.jsx)(d.A,{className:"w-4 h-4"})}],er=(0,a.jsxs)("div",{className:"inline-flex items-center space-x-4 whitespace-nowrap",children:[(0,a.jsx)("span",{className:"text-sm md:text-xl font-semibold text-black",children:"U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion"}),(0,a.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition",style:{border:"2px solid black"},onClick:()=>W.push("/uwhiz-info/1"),children:["Apply Now ",(0,a.jsx)(h.A,{className:"ml-2 h-4 w-4"})]})]});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:"sticky top-0 z-50 w-full bg-black overflow-x-hidden",children:[(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex h-20 items-center justify-between",children:[(0,a.jsx)(l(),{href:"/",className:"flex items-center space-x-2 transition-transform hover:scale-105",children:(0,a.jsx)(i.default,{src:"/logo_black.png",alt:"Preply Logo",width:150,height:50,className:"rounded-sm"})}),(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-4",children:ea.map(e=>(0,a.jsxs)(l(),{href:e.href,className:"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400",children:[e.icon,e.label,"Find School"===e.label&&(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black",children:"Coming Soon"}),e.isNew&&(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse",children:"Trending"})]},e.href))}),(0,a.jsxs)("div",{className:"flex md:hidden items-center space-x-2",children:[e&&(0,a.jsx)(F,{userType:"class"}),A&&(0,a.jsx)(F,{userType:"student"}),(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"text-orange-400 hover:bg-orange-500/10",onClick:ee,children:s?(0,a.jsx)(u.A,{className:"h-6 w-6"}):(0,a.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F,{userType:"class"}),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(l(),{href:"/coins",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",size:"icon",className:"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500",children:[(0,a.jsx)("div",{className:"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,a.jsx)("div",{className:"relative z-10 ",children:(0,a.jsx)(i.default,{src:"/uest_coin.png",alt:"Coin Icon",width:32,height:32,className:"object-contain"})})]})})}),(0,a.jsx)(l(),{href:"/classes/chat",passHref:!0,children:(0,a.jsx)(j.$,{variant:"outline",size:"icon",className:"relative rounded-full border-2 border-orange-500 group bg-black text-white hover:text-orange-400 h-10 w-10",children:(0,a.jsx)(x.A,{className:"h-5 w-5"})})})]}),(0,a.jsx)("div",{className:"h-8 border-l border-orange-500/20"}),e&&(0,a.jsxs)($.AM,{children:[(0,a.jsx)($.Wv,{asChild:!0,children:(0,a.jsx)(I.eu,{className:"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10",children:(0,a.jsx)(I.q5,{className:"bg-white text-black flex items-center justify-center text-sm font-semibold",children:t?.firstName&&t?.lastName?`${t.firstName[0]}${t.lastName[0]}`.toUpperCase():"CT"})})}),(0,a.jsxs)($.hl,{className:"w-64 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3 pb-2 border-b",children:[(0,a.jsx)(I.eu,{className:"h-12 w-12 border-2 border-[#ff914d]",children:(0,a.jsx)(I.q5,{className:"bg-white text-black",children:t?.firstName&&t?.lastName?`${t.firstName[0]}${t.lastName[0]}`.toUpperCase():"CT"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-black",children:t?.firstName&&t?.lastName?`${t.firstName} ${t.lastName}`:t?.className||"Class Account"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:t?.contactNo||"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,a.jsxs)(l(),{href:"/classes/profile",className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Profile"})]})}),(0,a.jsxs)(j.$,{onClick:()=>es(),className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"My Dashboard"})]}),(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,a.jsxs)(l(),{href:"/classes/referral-dashboard",className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Referral Dashboard"})]})}),(0,a.jsx)(j.$,{variant:"outline",className:"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10",onClick:async()=>{try{(await U.S.post("/auth-client/logout",{})).data.success&&(W.push("/"),G((0,D.lM)()),localStorage.removeItem("token"),R.oR.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),R.oR.error("Failed to logout")}},children:"Logout"})]})]})]}),!e&&!A&&(0,a.jsx)(j.$,{className:"bg-customOrange hover:bg-[#E88143] text-white mr-4",asChild:!0,children:(0,a.jsx)(l(),{href:"/class/login",children:"Join as a Tutor/Class"})}),!e&&!A&&(0,a.jsx)(j.$,{variant:"outline",className:"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white",asChild:!0,children:(0,a.jsx)(l(),{href:"/student/login",children:"Student Login"})}),A&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F,{userType:"student"}),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(l(),{href:"/coins",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",size:"icon",className:"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500",children:[(0,a.jsx)("div",{className:"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,a.jsx)("div",{className:"relative z-10 ",children:(0,a.jsx)(i.default,{src:"/uest_coin.png",alt:"Coin Icon",width:32,height:32,className:"object-contain"})})]})})}),(0,a.jsx)(l(),{href:"/student/chat",passHref:!0,children:(0,a.jsx)(j.$,{variant:"outline",size:"icon",className:"relative rounded-full group border-2 border-orange-500 bg-black text-white hover:text-orange-400 h-10 w-10",children:(0,a.jsx)(x.A,{className:"h-5 w-5"})})})]}),A&&(0,a.jsxs)($.AM,{children:[(0,a.jsx)($.Wv,{asChild:!0,children:(0,a.jsx)(I.eu,{className:"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10",children:(0,a.jsx)(I.q5,{className:"bg-white text-black flex items-center justify-center text-sm font-semibold",children:z?.firstName&&z?.lastName?`${z.firstName[0]}${z.lastName[0]}`.toUpperCase():"ST"})})}),(0,a.jsxs)($.hl,{className:"w-64 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3 pb-2 border-b",children:[(0,a.jsx)(I.eu,{className:"h-12 w-12 border-2 border-[#ff914d]",children:(0,a.jsx)(I.q5,{className:"bg-white text-black",children:z?.firstName&&z?.lastName?`${z.firstName[0]}${z.lastName[0]}`.toUpperCase():"ST"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-black",children:z?.firstName&&z?.lastName?`${z.firstName} ${z.lastName}`:"Student Account"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:z?.contactNo||"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,a.jsxs)(l(),{href:"/student/profile",className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Profile"})]})}),(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,a.jsxs)(l(),{href:"/student/wishlist",className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"My Wishlist"})]})}),(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",children:(0,a.jsxs)(l(),{href:"/student/referral-dashboard",className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Referral Dashboard"})]})}),(0,a.jsx)(j.$,{variant:"outline",className:"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10",onClick:et,children:"Logout"})]})]})]})]})]})}),(0,a.jsxs)("div",{className:"w-screen bg-[#FD904B] border-y border-black relative mt-1",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0"}),(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden",children:(0,a.jsxs)(H.P.div,{className:"inline-flex py-2 px-4",style:{x:Q},onMouseEnter:()=>J(!0),onMouseLeave:()=>J(!1),children:[(0,a.jsx)("div",{ref:Y,className:"inline-flex items-center space-x-4 whitespace-nowrap pr-8",children:er}),(0,a.jsx)("div",{className:"inline-flex items-center space-x-4 whitespace-nowrap pr-8",children:er})]})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${s?"translate-x-0":"translate-x-full"}`,children:(0,a.jsxs)("div",{className:"flex flex-col h-full p-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"text-orange-400 hover:bg-orange-500/10 rounded-full",onClick:ee,children:(0,a.jsx)(u.A,{className:"h-6 w-6"})})}),(0,a.jsx)("nav",{className:"flex flex-col space-y-2 mt-8",children:ea.map(e=>(0,a.jsxs)(l(),{href:e.href,className:"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center",onClick:ee,children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon,"string"==typeof e.label?(0,a.jsx)("span",{children:e.label}):e.label]}),"Find School"===e.label&&(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse",children:"Coming Soon"}),e.isNew&&(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white",children:"New"})]},e.href))}),(0,a.jsxs)("div",{className:"mt-auto space-y-4",children:[e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l(),{href:"/classes/profile",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full",children:(0,a.jsx)(f.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"Profile"})]})]})}),(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:()=>es(),children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full",children:(0,a.jsx)(b.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"My Dashboard"})]})]}),(0,a.jsx)(l(),{href:"/classes/referral-dashboard",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full",children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"Referral Dashboard"})]})]})}),(0,a.jsx)(l(),{href:"/coins",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full",children:(0,a.jsx)(i.default,{src:"/uest_coin.png",alt:"Coin Icon",width:20,height:20,className:"object-contain"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"My Coins"})]})]})}),(0,a.jsx)(l(),{href:"/classes/chat",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-white bg-black hover:bg-[#ff914d]/90",onClick:ee,children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-3 py-2",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"Chat"})]})]})}),(0,a.jsx)(j.$,{variant:"outline",className:"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:text-white mt-3",onClick:async()=>{try{(await U.S.post("/auth-client/logout",{})).data.success&&(W.push("/"),G((0,D.lM)()),localStorage.removeItem("token"),R.oR.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),R.oR.error("Failed to logout")}ee()},children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Logout"})]})})]}),A&&(0,a.jsxs)(a.Fragment,{children:[z?.firstName&&z?.lastName&&(0,a.jsx)("div",{className:"p-3 border border-[#ff914d]/20 rounded-lg bg-white",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(I.eu,{className:"h-12 w-12 border-2 border-[#ff914d]",children:(0,a.jsx)(I.q5,{className:"bg-white text-black",children:`${z.firstName[0]}${z.lastName[0]}`.toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-black",children:`${z.firstName} ${z.lastName}`}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:z.email})]})]})}),(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",onClick:ee,children:(0,a.jsxs)(l(),{href:"/student/profile",className:"flex items-center justify-center gap-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Profile"})]})}),(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",onClick:ee,children:(0,a.jsxs)(l(),{href:"/student/wishlist",className:"flex items-center justify-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"My Wishlist"})]})}),(0,a.jsx)(j.$,{asChild:!0,className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white",onClick:ee,children:(0,a.jsxs)(l(),{href:"/student/referral-dashboard",className:"flex items-center justify-center gap-3",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Referral Dashboard"})]})}),(0,a.jsx)(l(),{href:"/coins",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3",onClick:ee,children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center justify-center gap-3",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500",children:(0,a.jsx)(i.default,{src:"/uest_coin.png",alt:"Coin Icon",width:20,height:20,className:"object-contain"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"My Coins"})]})]})}),(0,a.jsx)(l(),{href:"/student/chat",passHref:!0,children:(0,a.jsxs)(j.$,{variant:"outline",className:"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3",onClick:ee,children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center justify-center gap-3",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"font-medium text-gray-300",children:"Chat"})]})]})}),(0,a.jsx)(j.$,{variant:"outline",className:"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10",onClick:()=>{et(),ee()},children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Logout"})]})})]}),!e&&!A&&(0,a.jsxs)("div",{className:"space-y-3 pt-3",children:[(0,a.jsx)(j.$,{variant:"default",className:"w-full bg-orange-500 hover:bg-orange-600",asChild:!0,children:(0,a.jsx)(l(),{href:"/class/login",onClick:ee,children:"Tutor/Classes Login"})}),(0,a.jsx)(j.$,{variant:"outline",className:"w-full border-customOrange text-orange-500 hover:bg-orange",asChild:!0,children:(0,a.jsx)(l(),{href:"/student/login",onClick:ee,children:"Student Login"})})]})]})]})}),A&&(0,a.jsx)(C,{})]})]})}},90971:(e,t,s)=>{"use strict";s.d(t,{$:()=>r,G:()=>l});var a=s(28527);let r=async e=>{try{let t=await a.S.put(`/mock-exam-streak/${e}`,{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:`Failed to save mock exam streak: ${e.response?.data?.error||e.message}`}}},l=async e=>{try{let t=await a.S.get(`/mock-exam-streak/${e}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:`Failed to get mock exam streak: ${e.response?.data?.error||e.message}`}}}},91833:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,17741)),Promise.resolve().then(s.bind(s,37075))},93500:(e,t,s)=>{"use strict";s.d(t,{$v:()=>f,EO:()=>h,Lt:()=>n,Rx:()=>p,Zr:()=>g,ck:()=>m,r7:()=>x,tv:()=>o,wd:()=>u});var a=s(60687);s(43210);var r=s(32771),l=s(4780),i=s(29523);function n({...e}){return(0,a.jsx)(r.bL,{"data-slot":"alert-dialog",...e})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"alert-dialog-trigger",...e})}function c({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...e})}function d({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function h({className:e,...t}){return(0,a.jsxs)(c,{children:[(0,a.jsx)(d,{}),(0,a.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",e),...t})}function f({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}function p({className:e,...t}){return(0,a.jsx)(r.rc,{className:(0,l.cn)((0,i.r)(),e),...t})}function g({className:e,...t}){return(0,a.jsx)(r.ZD,{className:(0,l.cn)((0,i.r)({variant:"outline"}),e),...t})}},95631:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\hooks\\\\AnalyticsProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx","default")},99420:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx","ReduxProvider")}};