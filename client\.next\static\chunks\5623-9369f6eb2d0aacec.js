"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5623],{6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return m}});let n=i(88229),r=i(95155),s=n._(i(12115)),o=i(82757),a=i(95227),l=i(69818),u=i(6654),h=i(69991),d=i(85929);i(43230);let c=i(24930);function p(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}let m=s.default.forwardRef(function(t,e){let i,n;let{href:o,as:m,children:f,prefetch:y=null,passHref:g,replace:v,shallow:x,scroll:w,onClick:b,onMouseEnter:T,onTouchStart:P,legacyBehavior:A=!1,...S}=t;i=f,A&&("string"==typeof i||"number"==typeof i)&&(i=(0,r.jsx)("a",{children:i}));let M=s.default.useContext(a.AppRouterContext),E=!1!==y,k=null===y?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:D,as:V}=s.default.useMemo(()=>{let t=p(o);return{href:t,as:m?p(m):t}},[o,m]);A&&(n=s.default.Children.only(i));let C=A?n&&"object"==typeof n&&n.ref:e,R=s.default.useCallback(t=>(E&&null!==M&&(0,c.mountLinkInstance)(t,D,M,k),()=>{(0,c.unmountLinkInstance)(t)}),[E,D,M,k]),j={ref:(0,u.useMergedRef)(R,C),onClick(t){A||"function"!=typeof b||b(t),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(t),M&&!t.defaultPrevented&&!function(t,e,i,n,r,o,a){let{nodeName:l}=t.currentTarget;!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t))&&(t.preventDefault(),s.default.startTransition(()=>{let t=null==a||a;"beforePopState"in e?e[r?"replace":"push"](i,n,{shallow:o,scroll:t}):e[r?"replace":"push"](n||i,{scroll:t})}))}(t,M,D,V,v,x,w)},onMouseEnter(t){A||"function"!=typeof T||T(t),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(t),M&&E&&(0,c.onNavigationIntent)(t.currentTarget)},onTouchStart:function(t){A||"function"!=typeof P||P(t),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(t),M&&E&&(0,c.onNavigationIntent)(t.currentTarget)}};return(0,h.isAbsoluteUrl)(V)?j.href=V:A&&!g&&("a"!==n.type||"href"in n.props)||(j.href=(0,d.addBasePath)(V)),A?s.default.cloneElement(n,j):(0,r.jsx)("a",{...S,...j,children:i})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},8619:(t,e,i)=>{i.d(e,{d:()=>a});var n=i(60098),r=i(12115),s=i(51508),o=i(82885);function a(t){let e=(0,o.M)(()=>(0,n.OQ)(t)),{isStatic:i}=(0,r.useContext)(s.Q);if(i){let[,i]=(0,r.useState)(t);(0,r.useEffect)(()=>e.on("change",i),[])}return e}},14087:(t,e,i)=>{i.d(e,{N:()=>o});var n=i(69515),r=i(12115),s=i(51508);function o(t){let e=(0,r.useRef)(0),{isStatic:i}=(0,r.useContext)(s.Q);(0,r.useEffect)(()=>{if(i)return;let r=({timestamp:i,delta:n})=>{e.current||(e.current=i),t(i-e.current,n)};return n.Gt.update(r,!0),()=>(0,n.WG)(r)},[t])}},17576:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},19320:(t,e,i)=>{function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function s(t,e,i,n){if("function"==typeof e){let[s,o]=r(n);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,o]=r(n);e=e(void 0!==i?i:t.custom,s,o)}return e}function o(t,e,i){let n=t.getProps();return s(n,e,void 0!==i?i:n.custom,t)}function a(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sf});var l,u,h=i(69515);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],c=new Set(d),p=new Set(["width","height","top","left","right","bottom",...d]);var m=i(60098);let f=t=>Array.isArray(t);var y=i(23387);let g=t=>!!(t&&t.getVelocity);function v(t,e){let i=t.getValue("willChange");if(g(i)&&i.add)return i.add(e);if(!i&&y.W.WillChange){let i=new y.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let x=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),w="data-"+x("framerAppearId"),b=(t,e)=>i=>e(t(i)),T=(...t)=>t.reduce(b),P=(t,e,i)=>i>e?e:i<t?t:i,A=t=>1e3*t,S=t=>t/1e3;var M=i(74261);let E={layout:0,mainThread:0,waapi:0},k=()=>{},D=()=>{},V=t=>e=>"string"==typeof e&&e.startsWith(t),C=V("--"),R=V("var(--"),j=t=>!!R(t)&&L.test(t.split("/*")[0].trim()),L=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,F={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},O={...F,transform:t=>P(0,1,t)},B={...F,default:1},I=t=>Math.round(1e5*t)/1e5,N=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,U=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,W=(t,e)=>i=>!!("string"==typeof i&&U.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),$=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(N);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},G=t=>P(0,255,t),_={...F,transform:t=>Math.round(G(t))},z={test:W("rgb","red"),parse:$("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+_.transform(t)+", "+_.transform(e)+", "+_.transform(i)+", "+I(O.transform(n))+")"},X={test:W("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:z.transform},Y=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),H=Y("deg"),q=Y("%"),K=Y("px"),Q=Y("vh"),Z=Y("vw"),J={...q,parse:t=>q.parse(t)/100,transform:t=>q.transform(100*t)},tt={test:W("hsl","hue"),parse:$("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+q.transform(I(e))+", "+q.transform(I(i))+", "+I(O.transform(n))+")"},te={test:t=>z.test(t)||X.test(t)||tt.test(t),parse:t=>z.test(t)?z.parse(t):tt.test(t)?tt.parse(t):X.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?z.transform(t):tt.transform(t),getAnimatableNone:t=>{let e=te.parse(t);return e.alpha=0,te.transform(e)}},ti=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tn="number",tr="color",ts=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function to(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(ts,t=>(te.test(t)?(n.color.push(s),r.push(tr),i.push(te.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tn),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function ta(t){return to(t).values}function tl(t){let{split:e,types:i}=to(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tn?r+=I(t[s]):e===tr?r+=te.transform(t[s]):r+=t[s]}return r}}let tu=t=>"number"==typeof t?0:te.test(t)?te.getAnimatableNone(t):t,th={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(N)?.length||0)+(t.match(ti)?.length||0)>0},parse:ta,createTransformer:tl,getAnimatableNone:function(t){let e=ta(t);return tl(t)(e.map(tu))}};function td(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tc(t,e){return i=>i>0?e:t}let tp=(t,e,i)=>t+(e-t)*i,tm=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tf=[X,z,tt],ty=t=>tf.find(e=>e.test(t));function tg(t){let e=ty(t);if(k(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tt&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=td(a,n,t+1/3),s=td(a,n,t),o=td(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tv=(t,e)=>{let i=tg(t),n=tg(e);if(!i||!n)return tc(t,e);let r={...i};return t=>(r.red=tm(i.red,n.red,t),r.green=tm(i.green,n.green,t),r.blue=tm(i.blue,n.blue,t),r.alpha=tp(i.alpha,n.alpha,t),z.transform(r))},tx=new Set(["none","hidden"]);function tw(t,e){return i=>tp(t,e,i)}function tb(t){return"number"==typeof t?tw:"string"==typeof t?j(t)?tc:te.test(t)?tv:tA:Array.isArray(t)?tT:"object"==typeof t?te.test(t)?tv:tP:tc}function tT(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tb(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tP(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tb(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tA=(t,e)=>{let i=th.createTransformer(e),n=to(t),r=to(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tx.has(t)&&!r.values.length||tx.has(e)&&!n.values.length?function(t,e){return tx.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):T(tT(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(k(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),tc(t,e))};function tS(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tp(t,e,i):tb(t)(t,e)}let tM=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>h.Gt.update(e,t),stop:()=>(0,h.WG)(e),now:()=>h.uv.isProcessing?h.uv.timestamp:M.k.now()}},tE=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tk(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var tD=i(62923);function tV(t,e,i){let n=Math.max(e-5,0);return(0,tD.f)(i-t(n),e-n)}let tC={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tR(t,e){return t*Math.sqrt(1-e*e)}let tj=["duration","bounce"],tL=["stiffness","damping","mass"];function tF(t,e){return e.some(e=>void 0!==t[e])}function tO(t=tC.visualDuration,e=tC.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tC.velocity,stiffness:tC.stiffness,damping:tC.damping,mass:tC.mass,isResolvedFromDuration:!1,...t};if(!tF(t,tL)&&tF(t,tj)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*P(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tC.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tC.duration,bounce:e=tC.bounce,velocity:i=tC.velocity,mass:n=tC.mass}){let r,s;k(t<=A(tC.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=P(tC.minDamping,tC.maxDamping,o),t=P(tC.minDuration,tC.maxDuration,S(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tR(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tR(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=A(t),isNaN(a))return{stiffness:tC.stiffness,damping:tC.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tC.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-S(n.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(u*d)),g=a-o,v=S(Math.sqrt(u/d)),x=5>Math.abs(g);if(r||(r=x?tC.restSpeed.granular:tC.restSpeed.default),s||(s=x?tC.restDelta.granular:tC.restDelta.default),y<1){let t=tR(v,y);i=e=>a-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),n=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;y<1&&(n=0===t?A(f):tV(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tk(w),2e4),e=tE(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tB({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,v=p+g,x=void 0===o?v:o(v);x!==v&&(g=x-p);let w=t=>-g*Math.exp(-t/n),b=t=>x+w(t),T=t=>{let e=w(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=tO({keyframes:[m.value,y(m.value)],velocity:tV(b,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}tO.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tk(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:S(r)}}(t,100,tO);return t.ease=e.ease,t.duration=A(e.duration),t.type="keyframes",t};var tI=i(19827);let tN=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tU(t,e,i,n){if(t===e&&i===n)return tI.l;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=tN(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tN(r(t),e,n)}let tW=tU(.42,0,1,1),t$=tU(0,0,.58,1),tG=tU(.42,0,.58,1),t_=t=>Array.isArray(t)&&"number"!=typeof t[0],tz=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tX=t=>e=>1-t(1-e),tY=tU(.33,1.53,.69,.99),tH=tX(tY),tq=tz(tH),tK=t=>(t*=2)<1?.5*tH(t):.5*(2-Math.pow(2,-10*(t-1))),tQ=t=>1-Math.sin(Math.acos(t)),tZ=tX(tQ),tJ=tz(tQ),t0=t=>Array.isArray(t)&&"number"==typeof t[0],t1={linear:tI.l,easeIn:tW,easeInOut:tG,easeOut:t$,circIn:tQ,circInOut:tJ,circOut:tZ,backIn:tH,backInOut:tq,backOut:tY,anticipate:tK},t2=t=>"string"==typeof t,t5=t=>{if(t0(t)){D(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,r]=t;return tU(e,i,n,r)}return t2(t)?(D(void 0!==t1[t],`Invalid easing type '${t}'`,"invalid-easing-type"),t1[t]):t},t4=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function t9({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=t_(n)?n.map(t5):t5(n),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(D(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||y.W.mix||tS,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=T(Array.isArray(e)?e[i]||tI.l:e,s)),n.push(s)}return n}(e,n,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=t4(t[n],t[n+1],i);return a[n](r)};return i?e=>u(P(t[0],t[s-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=t4(0,e,n);t.push(tp(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||tG).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let t6=t=>null!==t;function t3(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(t6),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let t8={decay:tB,inertia:tB,tween:t9,keyframes:t9,spring:tO};function t7(t){"string"==typeof t.type&&(t.type=t8[t.type])}class et{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ee=t=>t/100;class ei extends et{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==M.k.now()&&this.tick(M.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},E.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;t7(t);let{type:e=t9,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||t9;a!==t9&&"number"!=typeof o[0]&&(this.mixKeyframes=T(ee,tS(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tk(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=s)),v=P(0,1,i)*o}let w=g?{done:!1,value:u[0]}:x.next(v);r&&(w.value=r(w.value));let{done:b}=w;g||null===a||(b=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return T&&p!==tB&&(w.value=t3(u,this.options,f,this.speed)),m&&m(w.value),T&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return S(this.calculatedDuration)}get time(){return S(this.currentTime)}set time(t){t=A(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(M.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=S(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tM,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(M.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,E.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let en=t=>180*t/Math.PI,er=t=>eo(en(Math.atan2(t[1],t[0]))),es={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:er,rotateZ:er,skewX:t=>en(Math.atan(t[1])),skewY:t=>en(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eo=t=>((t%=360)<0&&(t+=360),t),ea=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),el=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eu={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ea,scaleY:el,scale:t=>(ea(t)+el(t))/2,rotateX:t=>eo(en(Math.atan2(t[6],t[5]))),rotateY:t=>eo(en(Math.atan2(-t[2],t[0]))),rotateZ:er,rotate:er,skewX:t=>en(Math.atan(t[4])),skewY:t=>en(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eh(t){return+!!t.includes("scale")}function ed(t,e){let i,n;if(!t||"none"===t)return eh(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=eu,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=es,n=e}if(!n)return eh(e);let s=i[e],o=n[1].split(",").map(ep);return"function"==typeof s?s(o):o[s]}let ec=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ed(i,e)};function ep(t){return parseFloat(t.trim())}let em=t=>t===F||t===K,ef=new Set(["x","y","z"]),ey=d.filter(t=>!ef.has(t)),eg={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ed(e,"x"),y:(t,{transform:e})=>ed(e,"y")};eg.translateX=eg.x,eg.translateY=eg.y;let ev=new Set,ex=!1,ew=!1,eb=!1;function eT(){if(ew){let t=Array.from(ev).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ey.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ew=!1,ex=!1,ev.forEach(t=>t.complete(eb)),ev.clear()}function eP(){ev.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ew=!0)})}class eA{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(ev.add(this),ex||(ex=!0,h.Gt.read(eP),h.Gt.resolveKeyframes(eT))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),ev.delete(this)}cancel(){"scheduled"===this.state&&(ev.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eS=t=>t.startsWith("--");function eM(t){let e;return()=>(void 0===e&&(e=t()),e)}let eE=eM(()=>void 0!==window.ScrollTimeline);var ek=i(24744);let eD={},eV=function(t,e){let i=eM(t);return()=>eD[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eC=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eR={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eC([0,.65,.55,1]),circOut:eC([.55,0,1,.45]),backIn:eC([.31,.01,.66,-.59]),backOut:eC([.33,1.53,.69,.99])};function ej(t){return"function"==typeof t&&"applyToOptions"in t}class eL extends et{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,D("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return ej(t)&&eV()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eV()?tE(e,i):"ease-out":t0(e)?eC(e):Array.isArray(e)?e.map(e=>t(e,i)||eR.easeOut):eR[e]}(a,r);Array.isArray(d)&&(h.easing=d),ek.Q.value&&E.waapi++;let c={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return ek.Q.value&&p.finished.finally(()=>{E.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=t3(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eS(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return S(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return S(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=A(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eE())?(this.animation.timeline=t,tI.l):e(this)}}let eF={anticipate:tK,backInOut:tq,circInOut:tJ};class eO extends eL{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eF&&(t.ease=eF[t.ease])}(t),t7(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ei({...s,autoplay:!1}),a=A(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eB=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(th.test(t)||"0"===t)&&!t.startsWith("url("));var eI=i(27351);let eN=new Set(["opacity","clipPath","filter","transform"]),eU=eM(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eW extends et{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=M.k.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eA;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=M.k.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eB(r,e),a=eB(s,e);return k(o===a,`You are trying to animate ${e} from "${r}" to "${s}". "${o?s:r}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ej(i))&&n)}(t,r,s,o)&&((y.W.instantAnimations||!a)&&u?.(t3(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,eI.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eU()&&i&&eN.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(h)?new eO({...h,element:h.motionValue.owner.current}):new ei(h);d.finished.then(()=>this.notifyFinished()).catch(tI.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eb=!0,eP(),eT(),eb=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e$=t=>null!==t,eG={type:"spring",stiffness:500,damping:25,restSpeed:10},e_=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ez={type:"keyframes",duration:.8},eX={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eY=(t,{keyframes:e})=>e.length>2?ez:c.has(t)?t.startsWith("scale")?e_(e[1]):eG:eX,eH=(t,e,i,n={},r,s)=>o=>{let l=a(n,t)||{},u=l.delay||n.delay||0,{elapsed:d=0}=n;d-=A(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-d,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(c,eY(t,c)),c.duration&&(c.duration=A(c.duration)),c.repeatDelay&&(c.repeatDelay=A(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(p=!0)),(y.W.instantAnimations||y.W.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!l.type&&!l.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e$),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(c.keyframes,l);if(void 0!==t){h.Gt.update(()=>{c.onUpdate(t),c.onComplete()});return}}return l.isSync?new ei(c):new eW(c)};function eq(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:l,...u}=e;n&&(s=n);let d=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...a(s||{},e)},l=n.get();if(void 0!==l&&!n.isAnimating&&!Array.isArray(r)&&r===l&&!o.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let i=t.props[w];if(i){let t=window.MotionHandoffAnimation(i,e,h.Gt);null!==t&&(o.startTime=t,m=!0)}}v(t,e),n.start(eH(e,n,r,t.shouldReduceMotion&&p.has(e)?{type:!1}:o,t,m));let f=n.animation;f&&d.push(f)}return l&&Promise.all(d).then(()=>{h.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=o(t,e)||{};for(let e in r={...r,...i}){var s;let i=f(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,m.OQ)(i))}}(t,l)})}),d}function eK(t,e,i={}){let n=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(eq(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=0,s=1,o){let a=[],l=t.variantChildren.size,u=(l-1)*r,h="function"==typeof n,d=h?t=>n(t,l):1===s?(t=0)=>t*r:(t=0)=>u-t*r;return Array.from(t.variantChildren).sort(eQ).forEach((t,r)=>{t.notify("AnimationStart",e),a.push(eK(t,e,{...o,delay:i+(h?0:n)+d(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,n,s,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function eQ(t,e){return t.sortNodePosition(e)}function eZ(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function eJ(t){return"string"==typeof t||Array.isArray(t)}let e0=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],e1=["initial",...e0],e2=e1.length,e5=[...e0].reverse(),e4=e0.length;function e9(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function e6(){return{animate:e9(!0),whileInView:e9(),whileHover:e9(),whileTap:e9(),whileDrag:e9(),whileFocus:e9(),exit:e9()}}class e3{constructor(t){this.isMounted=!1,this.node=t}update(){}}class e8 extends e3{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>eK(t,e,i)));else if("string"==typeof e)n=eK(t,e,i);else{let r="function"==typeof e?o(t,e,i.custom):e;n=Promise.all(eq(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=e6(),r=!0,s=e=>(i,n)=>{let r=o(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<e2;t++){let n=e1[t],r=e.props[n];(eJ(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<e4;e++){var m,y;let o=e5[e],g=i[o],v=void 0!==l[o]?l[o]:u[o],x=eJ(v),w=o===a?g.isActive:null;!1===w&&(p=e);let b=v===u[o]&&v!==l[o]&&x;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...c},!g.isActive&&null===w||!v&&!g.prevProp||n(v)||"boolean"==typeof v)continue;let T=(m=g.prevProp,"string"==typeof(y=v)?y!==m:!!Array.isArray(y)&&!eZ(y,m)),P=T||o===a&&g.isActive&&!b&&x||e>p&&x,A=!1,S=Array.isArray(v)?v:[v],M=S.reduce(s(o),{});!1===w&&(M={});let{prevResolvedValues:E={}}=g,k={...E,...M},D=e=>{P=!0,d.has(e)&&(A=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in k){let e=M[t],i=E[t];if(c.hasOwnProperty(t))continue;let n=!1;(f(e)&&f(i)?eZ(e,i):e===i)?void 0!==e&&d.has(t)?D(t):g.protectedKeys[t]=!0:null!=e?D(t):d.add(t)}g.prevProp=v,g.prevResolvedValues=M,g.isActive&&(c={...c,...M}),r&&t.blockInitialAnimation&&(P=!1);let V=!(b&&T)||A;P&&V&&h.push(...S.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let g=!!h.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),r=!1,g?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=a(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=e6(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let e7=0;class it extends e3{constructor(){super(...arguments),this.id=e7++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ie={x:!1,y:!1};function ii(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let ir=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function is(t){return{point:{x:t.pageX,y:t.pageY}}}let io=t=>e=>ir(e)&&t(e,is(e));function ia(t,e,i,n){return ii(t,e,io(i),n)}function il({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iu(t){return t.max-t.min}function ih(t,e,i,n=.5){t.origin=n,t.originPoint=tp(e.min,e.max,t.origin),t.scale=iu(i)/iu(e),t.translate=tp(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function id(t,e,i,n){ih(t.x,e.x,i.x,n?n.originX:void 0),ih(t.y,e.y,i.y,n?n.originY:void 0)}function ic(t,e,i){t.min=i.min+e.min,t.max=t.min+iu(e)}function ip(t,e,i){t.min=e.min-i.min,t.max=t.min+iu(e)}function im(t,e,i){ip(t.x,e.x,i.x),ip(t.y,e.y,i.y)}let iy=()=>({translate:0,scale:1,origin:0,originPoint:0}),ig=()=>({x:iy(),y:iy()}),iv=()=>({min:0,max:0}),ix=()=>({x:iv(),y:iv()});function iw(t){return[t("x"),t("y")]}function ib(t){return void 0===t||1===t}function iT({scale:t,scaleX:e,scaleY:i}){return!ib(t)||!ib(e)||!ib(i)}function iP(t){return iT(t)||iA(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iA(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iS(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iM(t,e=0,i=1,n,r){t.min=iS(t.min,e,i,n,r),t.max=iS(t.max,e,i,n,r)}function iE(t,{x:e,y:i}){iM(t.x,e.translate,e.scale,e.originPoint),iM(t.y,i.translate,i.scale,i.originPoint)}function ik(t,e){t.min=t.min+e,t.max=t.max+e}function iD(t,e,i,n,r=.5){let s=tp(t.min,t.max,r);iM(t,e,i,s,n)}function iV(t,e){iD(t.x,e.x,e.scaleX,e.scale,e.originX),iD(t.y,e.y,e.scaleY,e.scale,e.originY)}function iC(t,e){return il(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iR=({current:t})=>t?t.ownerDocument.defaultView:null;function ij(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iL=(t,e)=>Math.abs(t-e);class iF{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iI(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iL(t.x,e.x)**2+iL(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=h.uv;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iO(e,this.transformPagePoint),h.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iI("pointercancel"===t.type?this.lastMoveEventInfo:iO(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!ir(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=iO(is(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=h.uv;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,iI(o,this.history)),this.removeListeners=T(ia(this.contextWindow,"pointermove",this.handlePointerMove),ia(this.contextWindow,"pointerup",this.handlePointerUp),ia(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,h.WG)(this.updatePoint)}}function iO(t,e){return e?{point:e(t.point)}:t}function iB(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iI({point:t},e){return{point:t,delta:iB(t,iN(e)),offset:iB(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iN(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>A(.1)));)i--;if(!n)return{x:0,y:0};let s=S(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iN(t){return t[t.length-1]}function iU(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iW(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i$(t,e,i){return{min:iG(t,e),max:iG(t,i)}}function iG(t,e){return"number"==typeof t?t:t[e]||0}let i_=new WeakMap;class iz{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ix(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iF(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(is(t).point)},onStart:(t,e)=>{var i;let{drag:n,dragPropagation:r,onDragStart:s}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=n)||"y"===i?ie[i]?null:(ie[i]=!0,()=>{ie[i]=!1}):ie.x||ie.y?null:(ie.x=ie.y=!0,()=>{ie.x=ie.y=!1}),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iw(t=>{let e=this.getAxisMotionValue(t).get()||0;if(q.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iu(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&h.Gt.postRender(()=>s(t,e)),v(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>iw(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:iR(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&h.Gt.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!iX(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tp(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tp(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&ij(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iU(t.x,i,r),y:iU(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i$(t,"left","right"),y:i$(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iw(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ij(e))return!1;let n=e.current;D(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=iC(t,i),{scroll:r}=e;return r&&(ik(n.x,r.offset.x),ik(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:iW((t=r.layout.layoutBox).x,s.x),y:iW(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=il(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iw(o=>{if(!iX(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return v(this.visualElement,t),i.start(eH(t,i,0,e,this.visualElement,!1))}stopAnimation(){iw(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iw(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iw(e=>{let{drag:i}=this.getProps();if(!iX(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tp(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ij(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iw(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iu(t),r=iu(e);return r>n?i=t4(e.min,e.max-n,t.min):n>r&&(i=t4(t.min,t.max-r,e.min)),P(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iw(e=>{if(!iX(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tp(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i_.set(this.visualElement,this);let t=ia(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ij(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),h.Gt.read(e);let r=ii(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iw(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function iX(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class iY extends e3{constructor(t){super(t),this.removeGroupControls=tI.l,this.removeListeners=tI.l,this.controls=new iz(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tI.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let iH=t=>(e,i)=>{t&&h.Gt.postRender(()=>t(e,i))};class iq extends e3{constructor(){super(...arguments),this.removePointerDownListener=tI.l}onPointerDown(t){this.session=new iF(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iR(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:iH(t),onStart:iH(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&h.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ia(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var iK=i(95155);let{schedule:iQ}=(0,i(58437).I)(queueMicrotask,!1);var iZ=i(12115),iJ=i(32082),i0=i(90869);let i1=(0,iZ.createContext)({}),i2={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function i5(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let i4={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!K.test(t))return t;t=parseFloat(t)}let i=i5(t,e.target.x),n=i5(t,e.target.y);return`${i}% ${n}%`}},i9={},i6=!1;class i3 extends iZ.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;!function(t){for(let e in t)i9[e]=t[e],C(e)&&(i9[e].isCSSVariable=!0)}(i7),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),i6&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),i2.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,i6=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||h.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),iQ.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function i8(t){let[e,i]=(0,iJ.xQ)(),n=(0,iZ.useContext)(i0.L);return(0,iK.jsx)(i3,{...t,layoutGroup:n,switchLayoutGroup:(0,iZ.useContext)(i1),isPresent:e,safeToRemove:i})}let i7={borderRadius:{...i4,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:i4,borderTopRightRadius:i4,borderBottomLeftRadius:i4,borderBottomRightRadius:i4,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=th.parse(t);if(n.length>5)return t;let r=th.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tp(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nt=i(6983);function ne(t){return(0,nt.G)(t)&&"ownerSVGElement"in t}var ni=i(75626),nn=i(56668);let nr=(t,e)=>t.depth-e.depth;class ns{constructor(){this.children=[],this.isDirty=!1}add(t){(0,nn.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,nn.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nr),this.isDirty=!1,this.children.forEach(t)}}function no(t){return g(t)?t.get():t}let na=["TopLeft","TopRight","BottomLeft","BottomRight"],nl=na.length,nu=t=>"string"==typeof t?parseFloat(t):t,nh=t=>"number"==typeof t||K.test(t);function nd(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nc=nm(0,.5,tZ),np=nm(.5,.95,tI.l);function nm(t,e,i){return n=>n<t?0:n>e?1:i(t4(t,e,n))}function nf(t,e){t.min=e.min,t.max=e.max}function ny(t,e){nf(t.x,e.x),nf(t.y,e.y)}function ng(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nv(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nx(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(q.test(e)&&(e=parseFloat(e),e=tp(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tp(s.min,s.max,n);t===s&&(a-=e),t.min=nv(t.min,e,i,a,r),t.max=nv(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nw=["x","scaleX","originX"],nb=["y","scaleY","originY"];function nT(t,e,i,n){nx(t.x,e,nw,i?i.x:void 0,n?n.x:void 0),nx(t.y,e,nb,i?i.y:void 0,n?n.y:void 0)}function nP(t){return 0===t.translate&&1===t.scale}function nA(t){return nP(t.x)&&nP(t.y)}function nS(t,e){return t.min===e.min&&t.max===e.max}function nM(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nE(t,e){return nM(t.x,e.x)&&nM(t.y,e.y)}function nk(t){return iu(t.x)/iu(t.y)}function nD(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nV{constructor(){this.members=[]}add(t){(0,nn.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,nn.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nC={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nR=["","X","Y","Z"],nj=0;function nL(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nF({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=nj++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ek.Q.value&&(nC.nodes=nC.calculatedTargetDeltas=nC.calculatedProjections=0),this.nodes.forEach(nI),this.nodes.forEach(nz),this.nodes.forEach(nX),this.nodes.forEach(nN),ek.Q.addProjectionMetrics&&ek.Q.addProjectionMetrics(nC)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ns)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ni.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ne(e)&&!(ne(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i;let n=0,r=()=>this.root.updateBlockedByResize=!1;h.Gt.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=M.k.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&((0,h.WG)(n),t(s-e))};return h.Gt.setup(n,!0),()=>(0,h.WG)(n)}(r,250),i2.hasAnimatedSinceResize&&(i2.hasAnimatedSinceResize=!1,this.nodes.forEach(n_)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||nZ,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=r.getProps(),u=!this.targetLayout||!nE(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...a(s,"layout"),onPlay:o,onComplete:l};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||n_(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,h.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nY),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[w];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",h.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nW);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(n$);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nG),this.nodes.forEach(nO),this.nodes.forEach(nB)):this.nodes.forEach(n$),this.clearAllSnapshots();let t=M.k.now();h.uv.delta=P(0,1e3/60,t-h.uv.timestamp),h.uv.timestamp=t,h.uv.isProcessing=!0,h.PP.update.process(h.uv),h.PP.preRender.process(h.uv),h.PP.render.process(h.uv),h.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,iQ.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nU),this.sharedNodes.forEach(nH)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,h.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){h.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||iu(this.snapshot.measuredBox.x)||iu(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ix(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nA(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iP(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),n1((e=n).x),n1(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ix();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(n5))){let{scroll:t}=this.root;t&&(ik(e.x,t.offset.x),ik(e.y,t.offset.y))}return e}removeElementScroll(t){let e=ix();if(ny(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&ny(e,t),ik(e.x,r.offset.x),ik(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=ix();ny(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iV(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iP(n.latestValues)&&iV(i,n.latestValues)}return iP(this.latestValues)&&iV(i,this.latestValues),i}removeTransform(t){let e=ix();ny(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iP(i.latestValues))continue;iT(i.latestValues)&&i.updateSnapshot();let n=ix();ny(n,i.measurePageBox()),nT(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iP(this.latestValues)&&nT(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==h.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=h.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ix(),this.relativeTargetOrigin=ix(),im(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),ny(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ix(),this.targetWithTransforms=ix()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,ic(s.x,o.x,a.x),ic(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ny(this.target,this.layout.layoutBox),iE(this.target,this.targetDelta)):ny(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ix(),this.relativeTargetOrigin=ix(),im(this.relativeTargetOrigin,this.target,t.target),ny(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ek.Q.value&&nC.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iT(this.parent.latestValues)||iA(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===h.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;ny(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iV(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iE(t,s)),n&&iP(r.latestValues)&&iV(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=ix());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ng(this.prevProjectionDelta.x,this.projectionDelta.x),ng(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),id(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nD(this.projectionDelta.x,this.prevProjectionDelta.x)&&nD(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ek.Q.value&&nC.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ig(),this.projectionDelta=ig(),this.projectionDeltaWithTransform=ig()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=ig();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ix(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nQ));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(nq(o.x,t.x,n),nq(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,y;if(im(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=n,nK(p.x,m.x,f.x,y),nK(p.y,m.y,f.y,y),i&&(u=this.relativeTarget,c=i,nS(u.x,c.x)&&nS(u.y,c.y)))this.isProjectionDirty=!1;i||(i=ix()),ny(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tp(0,i.opacity??1,nc(n)),t.opacityExit=tp(e.opacity??1,0,np(n))):s&&(t.opacity=tp(e.opacity??1,i.opacity??1,n));for(let r=0;r<nl;r++){let s=`border${na[r]}Radius`,o=nd(e,s),a=nd(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nh(o)===nh(a)?(t[s]=Math.max(tp(nu(o),nu(a),n),0),(q.test(a)||q.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tp(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,h.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=h.Gt.update(()=>{i2.hasAnimatedSinceResize=!0,E.layout++,this.motionValue||(this.motionValue=(0,m.OQ)(0)),this.currentAnimation=function(t,e,i){let n=g(t)?t:(0,m.OQ)(t);return n.start(eH("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{E.layout--},onComplete:()=>{E.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&n2(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ix();let e=iu(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iu(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}ny(e,i),iV(e,r),id(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nV),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nL("z",t,n,this.animationValues);for(let e=0;e<nR.length;e++)nL(`rotate${nR[e]}`,t,n,this.animationValues),nL(`skew${nR[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=no(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=no(e?.pointerEvents)||""),this.hasProjected&&!iP(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,i9){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=i9[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?no(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nW),this.root.sharedNodes.clear()}}}function nO(t){t.updateLayout()}function nB(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iw(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=iu(n);n.min=i[t].min,n.max=n.min+r}):n2(r,e.layoutBox,i)&&iw(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=iu(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=ig();id(o,i,e.layoutBox);let a=ig();s?id(a,t.applyTransform(n,!0),e.measuredBox):id(a,i,e.layoutBox);let l=!nA(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=ix();im(o,e.layoutBox,r.layoutBox);let a=ix();im(a,i,s.layoutBox),nE(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nI(t){ek.Q.value&&nC.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nN(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nU(t){t.clearSnapshot()}function nW(t){t.clearMeasurements()}function n$(t){t.isLayoutDirty=!1}function nG(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n_(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nz(t){t.resolveTargetDelta()}function nX(t){t.calcProjection()}function nY(t){t.resetSkewAndRotation()}function nH(t){t.removeLeadSnapshot()}function nq(t,e,i){t.translate=tp(e.translate,0,i),t.scale=tp(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nK(t,e,i,n){t.min=tp(e.min,i.min,n),t.max=tp(e.max,i.max,n)}function nQ(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nZ={duration:.45,ease:[.4,0,.1,1]},nJ=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),n0=nJ("applewebkit/")&&!nJ("chrome/")?Math.round:tI.l;function n1(t){t.min=n0(t.min),t.max=n0(t.max)}function n2(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nk(e)-nk(i)))}function n5(t){return t!==t.root&&t.scroll?.wasRoot}let n4=nF({attachResizeListener:(t,e)=>ii(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),n9={current:void 0},n6=nF({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!n9.current){let t=new n4({});t.mount(window),t.setOptions({layoutScroll:!0}),n9.current=t}return n9.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function n3(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function n8(t){return!("touch"===t.pointerType||ie.x||ie.y)}function n7(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&h.Gt.postRender(()=>r(e,is(e)))}class rt extends e3{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=n3(t,i),o=t=>{if(!n8(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{n8(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(n7(this.node,e,"Start"),t=>n7(this.node,t,"End"))))}unmount(){}}class re extends e3{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=T(ii(this.node.current,"focus",()=>this.onFocus()),ii(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ri=(t,e)=>!!e&&(t===e||ri(t,e.parentElement)),rn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rr=new WeakSet;function rs(t){return e=>{"Enter"===e.key&&t(e)}}function ro(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ra=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rs(()=>{if(rr.has(i))return;ro(i,"down");let t=rs(()=>{ro(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ro(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rl(t){return ir(t)&&!(ie.x||ie.y)}function ru(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&h.Gt.postRender(()=>r(e,is(e)))}class rh extends e3{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=n3(t,i),o=t=>{let n=t.currentTarget;if(!rl(t))return;rr.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rr.has(n)&&rr.delete(n),rl(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||ri(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{if((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eI.s)(t))t.addEventListener("focus",t=>ra(t,r)),!rn.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),(t,{success:e})=>ru(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rd=new WeakMap,rc=new WeakMap,rp=t=>{let e=rd.get(t.target);e&&e(t)},rm=t=>{t.forEach(rp)},rf={some:0,all:1};class ry extends e3{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rf[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rc.has(i)||rc.set(i,{});let n=rc.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rm,{root:t,...e})),n[r]}(e);return rd.set(t,i),n.observe(t),()=>{rd.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rg=(0,iZ.createContext)({strict:!1});var rv=i(51508);let rx=(0,iZ.createContext)({});function rw(t){return n(t.animate)||e1.some(e=>eJ(t[e]))}function rb(t){return!!(rw(t)||t.variants)}function rT(t){return Array.isArray(t)?t.join(" "):t}var rP=i(68972);let rA={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rS={};for(let t in rA)rS[t]={isEnabled:e=>rA[t].some(t=>!!e[t])};let rM=Symbol.for("motionComponentSymbol");var rE=i(80845),rk=i(97494);function rD(t,{layout:e,layoutId:i}){return c.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!i9[t]||"opacity"===t)}let rV=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rC={...F,transform:Math.round},rR={borderWidth:K,borderTopWidth:K,borderRightWidth:K,borderBottomWidth:K,borderLeftWidth:K,borderRadius:K,radius:K,borderTopLeftRadius:K,borderTopRightRadius:K,borderBottomRightRadius:K,borderBottomLeftRadius:K,width:K,maxWidth:K,height:K,maxHeight:K,top:K,right:K,bottom:K,left:K,padding:K,paddingTop:K,paddingRight:K,paddingBottom:K,paddingLeft:K,margin:K,marginTop:K,marginRight:K,marginBottom:K,marginLeft:K,backgroundPositionX:K,backgroundPositionY:K,rotate:H,rotateX:H,rotateY:H,rotateZ:H,scale:B,scaleX:B,scaleY:B,scaleZ:B,skew:H,skewX:H,skewY:H,distance:K,translateX:K,translateY:K,translateZ:K,x:K,y:K,z:K,perspective:K,transformPerspective:K,opacity:O,originX:J,originY:J,originZ:K,zIndex:rC,fillOpacity:O,strokeOpacity:O,numOctaves:rC},rj={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rL=d.length;function rF(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(c.has(t)){o=!0;continue}if(C(t)){r[t]=i;continue}{let e=rV(i,rR[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rL;s++){let o=d[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rV(a,rR[o]);if(!l){r=!1;let e=rj[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rO=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rB(t,e,i){for(let n in e)g(e[n])||rD(n,i)||(t[n]=e[n])}let rI={offset:"stroke-dashoffset",array:"stroke-dasharray"},rN={offset:"strokeDashoffset",array:"strokeDasharray"};function rU(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rF(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rI:rN;t[s.offset]=K.transform(-n);let o=K.transform(e),a=K.transform(i);t[s.array]=`${o} ${a}`}(d,r,s,o,!1)}let rW=()=>({...rO(),attrs:{}}),r$=t=>"string"==typeof t&&"svg"===t.toLowerCase();var rG=i(95500);let r_=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rz(t){if("string"!=typeof t||t.includes("-"));else if(r_.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var rX=i(82885);let rY=t=>(e,i)=>{let r=(0,iZ.useContext)(rx),o=(0,iZ.useContext)(rE.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,o){return{latestValues:function(t,e,i,r){let o={},a=r(t,{});for(let t in a)o[t]=no(a[t]);let{initial:l,animate:u}=t,h=rw(t),d=rb(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=s(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(i,r,o,t),renderState:e()}})(t,e,r,o);return i?a():(0,rX.M)(a)};function rH(t,e,i){let{style:n}=t,r={};for(let s in n)(g(n[s])||e.style&&g(e.style[s])||rD(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let rq={useVisualState:rY({scrapeMotionValuesFromProps:rH,createRenderState:rO})};function rK(t,e,i){let n=rH(t,e,i);for(let i in t)(g(t[i])||g(e[i]))&&(n[-1!==d.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let rQ={useVisualState:rY({scrapeMotionValuesFromProps:rK,createRenderState:rW})},rZ=t=>e=>e.test(t),rJ=[F,K,q,H,Z,Q,{test:t=>"auto"===t,parse:t=>t}],r0=t=>rJ.find(rZ(t)),r1=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),r2=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,r5=t=>/^0[^.\s]+$/u.test(t),r4=new Set(["brightness","contrast","saturate","opacity"]);function r9(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(N)||[];if(!n)return t;let r=i.replace(n,""),s=+!!r4.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let r6=/\b([a-z-]*)\(.*?\)/gu,r3={...th,getAnimatableNone:t=>{let e=t.match(r6);return e?e.map(r9).join(" "):t}},r8={...rR,color:te,backgroundColor:te,outlineColor:te,fill:te,stroke:te,borderColor:te,borderTopColor:te,borderRightColor:te,borderBottomColor:te,borderLeftColor:te,filter:r3,WebkitFilter:r3},r7=t=>r8[t];function st(t,e){let i=r7(t);return i!==r3&&(i=th),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let se=new Set(["auto","none","0"]);class si extends eA{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&j(n=n.trim())){let r=function t(e,i,n=1){D(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[r,s]=function(t){let e=r2.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return r1(t)?parseFloat(t):t}return j(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!p.has(i)||2!==t.length)return;let[n,r]=t,s=r0(n),o=r0(r);if(s!==o){if(em(s)&&em(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eg[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||r5(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!se.has(e)&&to(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=st(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eg[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eg[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sn=[...rJ,te,th],sr=t=>sn.find(rZ(t)),ss={current:null},so={current:!1},sa=new WeakMap,sl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class su{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eA,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=M.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,h.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rw(e),this.isVariantNode=rb(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&g(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sa.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),so.current||function(){if(so.current=!0,rP.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ss.current=t.matches;t.addEventListener("change",e),e()}else ss.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ss.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,h.WG)(this.notifyUpdate),(0,h.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=c.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&h.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rS){let e=rS[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ix()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sl.length;e++){let i=sl[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(g(r))t.addValue(n,r);else if(g(s))t.addValue(n,(0,m.OQ)(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,(0,m.OQ)(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,m.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(r1(i)||r5(i))?i=parseFloat(i):!sr(i)&&th.test(e)&&(i=st(t,e)),this.setBaseTarget(t,g(i)?i.get():i)),g(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=s(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||g(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new ni.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sh extends su{constructor(){super(...arguments),this.KeyframeResolver=si}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;g(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sd(t,{style:e,vars:i},n,r){let s;let o=t.style;for(s in e)o[s]=e[s];for(s in r?.applyProjectionStyles(o,n),i)o.setProperty(s,i[s])}class sc extends sh{constructor(){super(...arguments),this.type="html",this.renderInstance=sd}readValueFromInstance(t,e){if(c.has(e))return this.projection?.isProjecting?eh(e):ec(t,e);{let i=window.getComputedStyle(t),n=(C(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iC(t,e)}build(t,e,i){rF(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return rH(t,e,i)}}let sp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sm extends sh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ix}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(c.has(e)){let t=r7(e);return t&&t.default||0}return e=sp.has(e)?e:x(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rK(t,e,i)}build(t,e,i){rU(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){for(let i in sd(t,e,void 0,n),e.attrs)t.setAttribute(sp.has(i)?i:x(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=r$(t.tagName),super.mount(t)}}let sf=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((l={animation:{Feature:e8},exit:{Feature:it},inView:{Feature:ry},tap:{Feature:rh},focus:{Feature:re},hover:{Feature:rt},pan:{Feature:iq},drag:{Feature:iY,ProjectionNode:n6,MeasureLayout:i8},layout:{ProjectionNode:n6,MeasureLayout:i8}},u=(t,e)=>rz(t)?new sm(e):new sc(e,{allowProjection:t!==iZ.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let u;let h={...(0,iZ.useContext)(rv.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,iZ.useContext)(i0.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(rw(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eJ(e)?e:void 0,animate:eJ(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,iZ.useContext)(rx));return(0,iZ.useMemo)(()=>({initial:e,animate:i}),[rT(e),rT(i)])}(t),p=o(t,d);if(!d&&rP.B){n=0,l=0,(0,iZ.useContext)(rg).strict;let t=function(t){let{drag:e,layout:i}=rS;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,iZ.useContext)(rx),o=(0,iZ.useContext)(rg),a=(0,iZ.useContext)(rE.t),l=(0,iZ.useContext)(rv.Q).reducedMotion,u=(0,iZ.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,iZ.useContext)(i1);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&ij(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,d);let c=(0,iZ.useRef)(!1);(0,iZ.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[w],m=(0,iZ.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rk.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),iQ.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,iZ.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(a,p,h,r,t.ProjectionNode)}return(0,iK.jsxs)(rx.Provider,{value:c,children:[u&&c.visualElement?(0,iK.jsx)(u,{visualElement:c.visualElement,...h}):null,s(a,t,(i=c.visualElement,(0,iZ.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):ij(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}n&&function(t){for(let e in t)rS[e]={...rS[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!==(i=null!==(e=a.displayName)&&void 0!==e?e:a.name)&&void 0!==i?i:"",")"));let u=(0,iZ.forwardRef)(l);return u[rM]=a,u}({...rz(t)?rQ:rq,preloadedFeatures:l,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(rz(e)?function(t,e,i,n){let r=(0,iZ.useMemo)(()=>{let i=rW();return rU(i,e,r$(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rB(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rB(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,iZ.useMemo)(()=>{let i=rO();return rF(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=(0,rG.J)(i,"string"==typeof e,t),l=e!==iZ.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,iZ.useMemo)(()=>g(u)?u.get():u,[u]);return(0,iZ.createElement)(e,{...l,children:h})}}(e),createVisualElement:u,Component:t})}))},19827:(t,e,i)=>{i.d(e,{l:()=>n});let n=t=>t},23387:(t,e,i)=>{i.d(e,{W:()=>n});let n={}},23861:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},24744:(t,e,i)=>{i.d(e,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},27351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},32082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(12115),r=i(80845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},35563:(t,e,i)=>{i.d(e,{rc:()=>F,ZD:()=>O,UC:()=>L,VY:()=>I,hJ:()=>j,ZL:()=>R,bL:()=>V,hE:()=>B,l9:()=>C});var n=i(12115),r=i(95155);function s(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function o(...t){return n.useCallback(function(...t){return e=>{let i=!1,n=t.map(t=>{let n=s(t,e);return i||"function"!=typeof n||(i=!0),n});if(i)return()=>{for(let e=0;e<n.length;e++){let i=n[e];"function"==typeof i?i():s(t[e],null)}}}}(...t),t)}var a=i(4033),l=i(66634),u="AlertDialog",[h,d]=function(t,e=[]){let i=[],s=()=>{let e=i.map(t=>n.createContext(t));return function(i){let r=i?.[t]||e;return n.useMemo(()=>({[`__scope${t}`]:{...i,[t]:r}}),[i,r])}};return s.scopeName=t,[function(e,s){let o=n.createContext(s),a=i.length;i=[...i,s];let l=e=>{let{scope:i,children:s,...l}=e,u=i?.[t]?.[a]||o,h=n.useMemo(()=>l,Object.values(l));return(0,r.jsx)(u.Provider,{value:h,children:s})};return l.displayName=e+"Provider",[l,function(i,r){let l=r?.[t]?.[a]||o,u=n.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${i}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let i=()=>{let i=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let r=i.reduce((e,{useScope:i,scopeName:n})=>{let r=i(t)[`__scope${n}`];return{...e,...r}},{});return n.useMemo(()=>({[`__scope${e.scopeName}`]:r}),[r])}};return i.scopeName=e.scopeName,i}(s,...e)]}(u,[a.Hs]),c=(0,a.Hs)(),p=t=>{let{__scopeAlertDialog:e,...i}=t,n=c(e);return(0,r.jsx)(a.bL,{...n,...i,modal:!0})};p.displayName=u;var m=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=c(i);return(0,r.jsx)(a.l9,{...s,...n,ref:e})});m.displayName="AlertDialogTrigger";var f=t=>{let{__scopeAlertDialog:e,...i}=t,n=c(e);return(0,r.jsx)(a.ZL,{...n,...i})};f.displayName="AlertDialogPortal";var y=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=c(i);return(0,r.jsx)(a.hJ,{...s,...n,ref:e})});y.displayName="AlertDialogOverlay";var g="AlertDialogContent",[v,x]=h(g),w=(0,l.Dc)("AlertDialogContent"),b=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,children:s,...l}=t,u=c(i),h=n.useRef(null),d=o(e,h),p=n.useRef(null);return(0,r.jsx)(a.G$,{contentName:g,titleName:T,docsSlug:"alert-dialog",children:(0,r.jsx)(v,{scope:i,cancelRef:p,children:(0,r.jsxs)(a.UC,{role:"alertdialog",...u,...l,ref:d,onOpenAutoFocus:function(t,e,{checkForDefaultPrevented:i=!0}={}){return function(n){if(t?.(n),!1===i||!n.defaultPrevented)return e?.(n)}}(l.onOpenAutoFocus,t=>{var e;t.preventDefault(),null===(e=p.current)||void 0===e||e.focus({preventScroll:!0})}),onPointerDownOutside:t=>t.preventDefault(),onInteractOutside:t=>t.preventDefault(),children:[(0,r.jsx)(w,{children:s}),(0,r.jsx)(D,{contentRef:h})]})})})});b.displayName=g;var T="AlertDialogTitle",P=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=c(i);return(0,r.jsx)(a.hE,{...s,...n,ref:e})});P.displayName=T;var A="AlertDialogDescription",S=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=c(i);return(0,r.jsx)(a.VY,{...s,...n,ref:e})});S.displayName=A;var M=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=c(i);return(0,r.jsx)(a.bm,{...s,...n,ref:e})});M.displayName="AlertDialogAction";var E="AlertDialogCancel",k=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,{cancelRef:s}=x(E,i),l=c(i),u=o(e,s);return(0,r.jsx)(a.bm,{...l,...n,ref:u})});k.displayName=E;var D=t=>{let{contentRef:e}=t,i="`".concat(g,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(g,"` by passing a `").concat(A,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(g,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var t;document.getElementById(null===(t=e.current)||void 0===t?void 0:t.getAttribute("aria-describedby"))||console.warn(i)},[i,e]),null},V=p,C=m,R=f,j=y,L=b,F=M,O=k,B=P,I=S},50958:(t,e,i)=>{i.d(e,{O:()=>r});var n=i(89447);function r(t,e,i){var r;let s=(+(0,n.a)(t)-+(0,n.a)(e))/1e3;return(r=null==i?void 0:i.roundingMethod,t=>{let e=(r?Math[r]:Math.trunc)(t);return 0===e?0:e})(s)}},51508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},55607:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},56668:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function r(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>r,Kq:()=>n})},58437:(t,e,i)=>{i.d(e,{I:()=>o});var n=i(23387);let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(24744);function o(t,e){let i=!1,o=!0,a={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=r.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,o=!1,a=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){a.has(e)&&(d.schedule(e),t()),u++,e(l)}let d={schedule:(t,e=!1,s=!1)=>{let o=s&&r?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(l=t,r){o=!0;return}r=!0,[i,n]=[n,i],i.forEach(h),e&&s.Q.value&&s.Q.value.frameloop[e].push(u),u=0,i.clear(),r=!1,o&&(o=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:h,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:y,postRender:g}=u,v=()=>{let r=n.W.useManualTiming?a.timestamp:performance.now();i=!1,n.W.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(r-a.timestamp,40),1)),a.timestamp=r,a.isProcessing=!0,h.process(a),d.process(a),c.process(a),p.process(a),m.process(a),f.process(a),y.process(a),g.process(a),a.isProcessing=!1,i&&e&&(o=!1,t(v))},x=()=>{i=!0,o=!0,a.isProcessing||t(v)};return{schedule:r.reduce((t,e)=>{let n=u[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<r.length;e++)u[r[e]].cancel(t)},state:a,steps:u}}},60098:(t,e,i)=>{i.d(e,{OQ:()=>h});var n=i(75626),r=i(62923),s=i(74261),o=i(69515);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,r.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},62923:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}},65932:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("badge-cent",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"M12 7v10",key:"jspqdw"}],["path",{d:"M15.4 10a4 4 0 1 0 0 4",key:"2eqtx8"}]])},66516:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},68972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},69515:(t,e,i)=>{i.d(e,{Gt:()=>r,PP:()=>a,WG:()=>s,uv:()=>o});var n=i(19827);let{schedule:r,cancel:s,state:o,steps:a}=(0,i(58437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},69991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return y},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class y extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class g extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}},71007:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},73783:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},74261:(t,e,i)=>{let n;i.d(e,{k:()=>a});var r=i(23387),s=i(69515);function o(){n=void 0}let a={now:()=>(void 0===n&&a.set(s.uv.isProcessing||r.W.useManualTiming?s.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(o)}}},74783:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},75350:(t,e,i)=>{i.d(e,{m:()=>p});var n=i(64261),r=i(8093),s=i(95490),o=i(97444),a=i(61183),l=i(89447);function u(t,e){let i=+(0,l.a)(t)-+(0,l.a)(e);return i<0?-1:i>0?1:i}var h=i(25703),d=i(32944),c=i(50958);function p(t,e){return function(t,e,i){var n,p;let m;let f=(0,s.q)(),y=null!==(p=null!==(n=null==i?void 0:i.locale)&&void 0!==n?n:f.locale)&&void 0!==p?p:r.c,g=u(t,e);if(isNaN(g))throw RangeError("Invalid time value");let v=Object.assign({},i,{addSuffix:null==i?void 0:i.addSuffix,comparison:g}),[x,w]=(0,a.x)(null==i?void 0:i.in,...g>0?[e,t]:[t,e]),b=(0,c.O)(w,x),T=Math.round((b-((0,o.G)(w)-(0,o.G)(x))/1e3)/60);if(T<2){if(null==i?void 0:i.includeSeconds){if(b<5)return y.formatDistance("lessThanXSeconds",5,v);if(b<10)return y.formatDistance("lessThanXSeconds",10,v);if(b<20)return y.formatDistance("lessThanXSeconds",20,v);else if(b<40)return y.formatDistance("halfAMinute",0,v);else if(b<60)return y.formatDistance("lessThanXMinutes",1,v);else return y.formatDistance("xMinutes",1,v)}return 0===T?y.formatDistance("lessThanXMinutes",1,v):y.formatDistance("xMinutes",T,v)}if(T<45)return y.formatDistance("xMinutes",T,v);if(T<90)return y.formatDistance("aboutXHours",1,v);if(T<h.F6){let t=Math.round(T/60);return y.formatDistance("aboutXHours",t,v)}if(T<2520)return y.formatDistance("xDays",1,v);else if(T<h.Nw){let t=Math.round(T/h.F6);return y.formatDistance("xDays",t,v)}else if(T<2*h.Nw)return m=Math.round(T/h.Nw),y.formatDistance("aboutXMonths",m,v);if((m=function(t,e,i){let[n,r,s]=(0,a.x)(void 0,t,t,e),o=u(r,s),h=Math.abs(function(t,e,i){let[n,r]=(0,a.x)(void 0,t,e);return 12*(n.getFullYear()-r.getFullYear())+(n.getMonth()-r.getMonth())}(r,s));if(h<1)return 0;1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-o*h);let c=u(r,s)===-o;(function(t,e){let i=(0,l.a)(t,void 0);return+function(t,e){let i=(0,l.a)(t,null==e?void 0:e.in);return i.setHours(23,59,59,999),i}(i,void 0)==+(0,d.p)(i,e)})(n)&&1===h&&1===u(n,s)&&(c=!1);let p=o*(h-+c);return 0===p?0:p}(w,x))<12){let t=Math.round(T/h.Nw);return y.formatDistance("xMonths",t,v)}{let t=m%12,e=Math.trunc(m/12);return t<3?y.formatDistance("aboutXYears",e,v):t<9?y.formatDistance("overXYears",e,v):y.formatDistance("almostXYears",e+1,v)}}(t,(0,n.A)(t),e)}},75626:(t,e,i)=>{i.d(e,{v:()=>r});var n=i(56668);class r{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},78859:(t,e)=>{function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},79772:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("circle-user",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]])},80845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(12115).createContext)(null)},81497:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},82757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=i(6966)._(i(78859)),r=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||r.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},82885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(12115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},86151:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},87083:(t,e,i)=>{i.d(e,{H4:()=>T,bL:()=>b});var n=i(12115),r=i(95155),s=globalThis?.document?n.useLayoutEffect:()=>{};function o(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}i(47650);var a=Symbol("radix.slottable");function l(t){return n.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===a}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((t,e)=>{let i=function(t){let e=function(t){let e=n.forwardRef((t,e)=>{let{children:i,...r}=t;if(n.isValidElement(i)){var s;let t,a;let l=(s=i,(a=(t=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?s.ref:(a=(t=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(t,e){let i={...e};for(let n in e){let r=t[n],s=e[n];/^on[A-Z]/.test(n)?r&&s?i[n]=(...t)=>{s(...t),r(...t)}:r&&(i[n]=r):"style"===n?i[n]={...r,...s}:"className"===n&&(i[n]=[r,s].filter(Boolean).join(" "))}return{...t,...i}}(r,i.props);return i.type!==n.Fragment&&(u.ref=e?function(...t){return e=>{let i=!1,n=t.map(t=>{let n=o(t,e);return i||"function"!=typeof n||(i=!0),n});if(i)return()=>{for(let e=0;e<n.length;e++){let i=n[e];"function"==typeof i?i():o(t[e],null)}}}}(e,l):l),n.cloneElement(i,u)}return n.Children.count(i)>1?n.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=n.forwardRef((t,i)=>{let{children:s,...o}=t,a=n.Children.toArray(s),u=a.find(l);if(u){let t=u.props.children,s=a.map(e=>e!==u?e:n.Children.count(t)>1?n.Children.only(null):n.isValidElement(t)?t.props.children:null);return(0,r.jsx)(e,{...o,ref:i,children:n.isValidElement(t)?n.cloneElement(t,void 0,s):null})}return(0,r.jsx)(e,{...o,ref:i,children:s})});return i.displayName=`${t}.Slot`,i}(`Primitive.${e}`),s=n.forwardRef((t,n)=>{let{asChild:s,...o}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(s?i:e,{...o,ref:n})});return s.displayName=`Primitive.${e}`,{...t,[e]:s}},{}),h="Avatar",[d,c]=function(t,e=[]){let i=[],s=()=>{let e=i.map(t=>n.createContext(t));return function(i){let r=i?.[t]||e;return n.useMemo(()=>({[`__scope${t}`]:{...i,[t]:r}}),[i,r])}};return s.scopeName=t,[function(e,s){let o=n.createContext(s),a=i.length;i=[...i,s];let l=e=>{let{scope:i,children:s,...l}=e,u=i?.[t]?.[a]||o,h=n.useMemo(()=>l,Object.values(l));return(0,r.jsx)(u.Provider,{value:h,children:s})};return l.displayName=e+"Provider",[l,function(i,r){let l=r?.[t]?.[a]||o,u=n.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${i}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let i=()=>{let i=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let r=i.reduce((e,{useScope:i,scopeName:n})=>{let r=i(t)[`__scope${n}`];return{...e,...r}},{});return n.useMemo(()=>({[`__scope${e.scopeName}`]:r}),[r])}};return i.scopeName=e.scopeName,i}(s,...e)]}(h),[p,m]=d(h),f=n.forwardRef((t,e)=>{let{__scopeAvatar:i,...s}=t,[o,a]=n.useState("idle");return(0,r.jsx)(p,{scope:i,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:(0,r.jsx)(u.span,{...s,ref:e})})});f.displayName=h;var y="AvatarImage";n.forwardRef((t,e)=>{let{__scopeAvatar:i,src:o,onLoadingStatusChange:a=()=>{},...l}=t,h=m(y,i),d=function(t,e){let{referrerPolicy:i,crossOrigin:r}=e,o=n.useSyncExternalStore(w,()=>!0,()=>!1),a=n.useRef(null),l=o?(a.current||(a.current=new window.Image),a.current):null,[u,h]=n.useState(()=>x(l,t));return s(()=>{h(x(l,t))},[l,t]),s(()=>{let t=t=>()=>{h(t)};if(!l)return;let e=t("loaded"),n=t("error");return l.addEventListener("load",e),l.addEventListener("error",n),i&&(l.referrerPolicy=i),"string"==typeof r&&(l.crossOrigin=r),()=>{l.removeEventListener("load",e),l.removeEventListener("error",n)}},[l,r,i]),u}(o,l),c=function(t){let e=n.useRef(t);return n.useEffect(()=>{e.current=t}),n.useMemo(()=>(...t)=>e.current?.(...t),[])}(t=>{a(t),h.onImageLoadingStatusChange(t)});return s(()=>{"idle"!==d&&c(d)},[d,c]),"loaded"===d?(0,r.jsx)(u.img,{...l,ref:e,src:o}):null}).displayName=y;var g="AvatarFallback",v=n.forwardRef((t,e)=>{let{__scopeAvatar:i,delayMs:s,...o}=t,a=m(g,i),[l,h]=n.useState(void 0===s);return n.useEffect(()=>{if(void 0!==s){let t=window.setTimeout(()=>h(!0),s);return()=>window.clearTimeout(t)}},[s]),l&&"loaded"!==a.imageLoadingStatus?(0,r.jsx)(u.span,{...o,ref:e}):null});function x(t,e){return t?e?(t.src!==e&&(t.src=e),t.complete&&t.naturalWidth>0?"loaded":"loading"):"error":"idle"}function w(){return()=>{}}v.displayName=g;var b=f,T=v},87949:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},90869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(12115).createContext)({})},92138:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},95500:(t,e,i)=>{i.d(e,{J:()=>a,D:()=>o});let n=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||n.has(t)}let s=t=>!r(t);function o(t){"function"==typeof t&&(s=e=>e.startsWith("on")?!r(e):t(e))}try{o(require("@emotion/is-prop-valid").default)}catch{}function a(t,e,i){let n={};for(let o in t)("values"!==o||"object"!=typeof t.values)&&(s(o)||!0===i&&r(o)||!e&&!r(o)||t.draggable&&o.startsWith("onDrag"))&&(n[o]=t[o]);return n}},97494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(12115);let r=i(68972).B?n.useLayoutEffect:n.useEffect}}]);