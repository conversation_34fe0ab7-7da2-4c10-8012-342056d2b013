"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4211],{5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>o});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},46081:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(12115),l=r(95155);function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return i.scopeName=e,[function(t,o){let i=n.createContext(o),a=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[a]||i,d=n.useMemo(()=>s,Object.values(s));return(0,l.jsx)(u.Provider,{value:d,children:o})};return s.displayName=t+"Provider",[s,function(r,l){let s=l?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(i,...t)]}},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},63540:(e,t,r)=>{r.d(t,{sG:()=>c,hO:()=>f});var n=r(12115),l=r(47650),o=r(6101),i=r(95155),a=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(d);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...l,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,o.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...l}=e,o=n?a:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function f(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79899:(e,t,r)=>{r.d(t,{UC:()=>tu,In:()=>ta,q7:()=>tc,VF:()=>tp,p4:()=>tf,ZL:()=>ts,bL:()=>tl,wn:()=>tm,PP:()=>tv,l9:()=>to,WT:()=>ti,LM:()=>td});var n,l=r(12115),o=r.t(l,2),i=r(47650);function a(e,[t,r]){return Math.min(r,Math.max(t,e))}function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var u=r(46081),d=r(6101),c=r(95155),f=l.forwardRef((e,t)=>{let{children:r,...n}=e,o=l.Children.toArray(r),i=o.find(m);if(i){let e=i.props.children,r=o.map(t=>t!==i?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,c.jsx)(p,{...n,ref:t,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,c.jsx)(p,{...n,ref:t,children:r})});f.displayName="Slot";var p=l.forwardRef((e,t)=>{let{children:r,...n}=e;if(l.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==l.Fragment&&(o.ref=t?(0,d.t)(t,e):e),l.cloneElement(r,o)}return l.Children.count(r)>1?l.Children.only(null):null});p.displayName="SlotClone";var v=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function m(e){return l.isValidElement(e)&&e.type===v}var h=l.createContext(void 0),y=r(63540);function g(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var w="dismissableLayer.update",x=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=l.forwardRef((e,t)=>{var r,o;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,h=l.useContext(x),[b,S]=l.useState(null),R=null!==(o=null==b?void 0:b.ownerDocument)&&void 0!==o?o:null===(r=globalThis)||void 0===r?void 0:r.document,[,j]=l.useState({}),P=(0,d.s)(t,e=>S(e)),N=Array.from(h.layers),[A]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),T=N.indexOf(A),k=b?N.indexOf(b):-1,L=h.layersWithOutsidePointerEventsDisabled.size>0,D=k>=T,I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=g(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){C("dismissableLayer.pointerDownOutside",n,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...h.branches].some(e=>e.contains(t));!D||r||(null==u||u(e),null==p||p(e),e.defaultPrevented||null==v||v())},R),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=g(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&C("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...h.branches].some(e=>e.contains(t))||(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},R);return!function(e,t=globalThis?.document){let r=g(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{k===h.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},R),l.useEffect(()=>{if(b)return i&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(n=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(b)),h.layers.add(b),E(),()=>{i&&1===h.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=n)}},[b,R,i,h]),l.useEffect(()=>()=>{b&&(h.layers.delete(b),h.layersWithOutsidePointerEventsDisabled.delete(b),E())},[b,h]),l.useEffect(()=>{let e=()=>j({});return document.addEventListener(w,e),()=>document.removeEventListener(w,e)},[]),(0,c.jsx)(y.sG.div,{...m,ref:P,style:{pointerEvents:L?D?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,O.onFocusCapture),onBlurCapture:s(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,I.onPointerDownCapture)})});function E(){let e=new CustomEvent(w);document.dispatchEvent(e)}function C(e,t,r,n){let{discrete:l}=n,o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),l?(0,y.hO)(o,i):o.dispatchEvent(i)}b.displayName="DismissableLayer",l.forwardRef((e,t)=>{let r=l.useContext(x),n=l.useRef(null),o=(0,d.s)(t,n);return l.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(y.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var S=0;function R(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var j="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0},A=l.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[s,u]=l.useState(null),f=g(o),p=g(i),v=l.useRef(null),m=(0,d.s)(t,e=>u(e)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let e=function(e){if(h.paused||!s)return;let t=e.target;s.contains(t)?v.current=t:L(v.current,{select:!0})},t=function(e){if(h.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||L(v.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&L(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,h.paused]),l.useEffect(()=>{if(s){D.add(h);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(j,N);s.addEventListener(j,f),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(L(n,{select:t}),document.activeElement!==r)return}(T(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&L(s))}return()=>{s.removeEventListener(j,f),setTimeout(()=>{let t=new CustomEvent(P,N);s.addEventListener(P,p),s.dispatchEvent(t),t.defaultPrevented||L(null!=e?e:document.body,{select:!0}),s.removeEventListener(P,p),D.remove(h)},0)}}},[s,f,p,h]);let w=l.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,l=document.activeElement;if(t&&l){let t=e.currentTarget,[n,o]=function(e){let t=T(e);return[k(t,e),k(t.reverse(),e)]}(t);n&&o?e.shiftKey||l!==o?e.shiftKey&&l===n&&(e.preventDefault(),r&&L(o,{select:!0})):(e.preventDefault(),r&&L(n,{select:!0})):l===t&&e.preventDefault()}},[r,n,h.paused]);return(0,c.jsx)(y.sG.div,{tabIndex:-1,...a,ref:m,onKeyDown:w})});function T(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function k(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function L(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}A.displayName="FocusScope";var D=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=I(e,t)).unshift(t)},remove(t){var r;null===(r=(e=I(e,t))[0])||void 0===r||r.resume()}}}();function I(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var O=globalThis?.document?l.useLayoutEffect:()=>{},M=o["useId".toString()]||(()=>void 0),F=0;function W(e){let[t,r]=l.useState(M());return O(()=>{e||r(e=>e??String(F++))},[e]),e||(t?`radix-${t}`:"")}var _=r(84945),H=r(22475),B=l.forwardRef((e,t)=>{let{children:r,width:n=10,height:l=5,...o}=e;return(0,c.jsx)(y.sG.svg,{...o,ref:t,width:n,height:l,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});B.displayName="Arrow";var V="Popper",[G,K]=(0,u.A)(V),[z,U]=G(V),q=e=>{let{__scopePopper:t,children:r}=e,[n,o]=l.useState(null);return(0,c.jsx)(z,{scope:t,anchor:n,onAnchorChange:o,children:r})};q.displayName=V;var Y="PopperAnchor",$=l.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=U(Y,r),a=l.useRef(null),s=(0,d.s)(t,a);return l.useEffect(()=>{i.onAnchorChange((null==n?void 0:n.current)||a.current)}),n?null:(0,c.jsx)(y.sG.div,{...o,ref:s})});$.displayName=Y;var X="PopperContent",[Z,J]=G(X),Q=l.forwardRef((e,t)=>{var r,n,o,i,a,s,u,f;let{__scopePopper:p,side:v="bottom",sideOffset:m=0,align:h="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:b=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:S="partial",hideWhenDetached:R=!1,updatePositionStrategy:j="optimized",onPlaced:P,...N}=e,A=U(X,p),[T,k]=l.useState(null),L=(0,d.s)(t,e=>k(e)),[D,I]=l.useState(null),M=function(e){let[t,r]=l.useState(void 0);return O(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,l;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,l=t.blockSize}else n=e.offsetWidth,l=e.offsetHeight;r({width:n,height:l})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(D),F=null!==(u=null==M?void 0:M.width)&&void 0!==u?u:0,W=null!==(f=null==M?void 0:M.height)&&void 0!==f?f:0,B="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},V=Array.isArray(E)?E:[E],G=V.length>0,K={padding:B,boundary:V.filter(en),altBoundary:G},{refs:z,floatingStyles:q,placement:Y,isPositioned:$,middlewareData:J}=(0,_.we)({strategy:"fixed",placement:v+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,H.ll)(...t,{animationFrame:"always"===j})},elements:{reference:A.anchor},middleware:[(0,_.cY)({mainAxis:m+W,alignmentAxis:w}),b&&(0,_.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?(0,_.ER)():void 0,...K}),b&&(0,_.UU)({...K}),(0,_.Ej)({...K,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:l}=e,{width:o,height:i}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(l,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),D&&(0,_.UE)({element:D,padding:x}),el({arrowWidth:F,arrowHeight:W}),R&&(0,_.jD)({strategy:"referenceHidden",...K})]}),[Q,ee]=eo(Y),et=g(P);O(()=>{$&&(null==et||et())},[$,et]);let er=null===(r=J.arrow)||void 0===r?void 0:r.x,ei=null===(n=J.arrow)||void 0===n?void 0:n.y,ea=(null===(o=J.arrow)||void 0===o?void 0:o.centerOffset)!==0,[es,eu]=l.useState();return O(()=>{T&&eu(window.getComputedStyle(T).zIndex)},[T]),(0,c.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:$?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:es,"--radix-popper-transform-origin":[null===(i=J.transformOrigin)||void 0===i?void 0:i.x,null===(a=J.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(s=J.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(Z,{scope:p,placedSide:Q,onArrowChange:I,arrowX:er,arrowY:ei,shouldHideArrow:ea,children:(0,c.jsx)(y.sG.div,{"data-side":Q,"data-align":ee,...N,ref:L,style:{...N.style,animation:$?void 0:"none"}})})})});Q.displayName=X;var ee="PopperArrow",et={top:"bottom",right:"left",bottom:"top",left:"right"},er=l.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,l=J(ee,r),o=et[l.placedSide];return(0,c.jsx)("span",{ref:l.onArrowChange,style:{position:"absolute",left:l.arrowX,top:l.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[l.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[l.placedSide],visibility:l.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(B,{...n,ref:t,style:{...n.style,display:"block"}})})});function en(e){return null!==e}er.displayName=ee;var el=e=>({name:"transformOrigin",options:e,fn(t){var r,n,l,o,i;let{placement:a,rects:s,middlewareData:u}=t,d=(null===(r=u.arrow)||void 0===r?void 0:r.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=eo(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(o=null===(n=u.arrow)||void 0===n?void 0:n.x)&&void 0!==o?o:0)+c/2,y=(null!==(i=null===(l=u.arrow)||void 0===l?void 0:l.y)&&void 0!==i?i:0)+f/2,g="",w="";return"bottom"===p?(g=d?m:"".concat(h,"px"),w="".concat(-f,"px")):"top"===p?(g=d?m:"".concat(h,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=d?m:"".concat(y,"px")):"left"===p&&(g="".concat(s.floating.width+f,"px"),w=d?m:"".concat(y,"px")),{data:{x:g,y:w}}}});function eo(e){let[t,r="center"]=e.split("-");return[t,r]}var ei=l.forwardRef((e,t)=>{var r,n;let{container:o,...a}=e,[s,u]=l.useState(!1);O(()=>u(!0),[]);let d=o||s&&(null===(n=globalThis)||void 0===n?void 0:null===(r=n.document)||void 0===r?void 0:r.body);return d?i.createPortal((0,c.jsx)(y.sG.div,{...a,ref:t}),d):null});ei.displayName="Portal";var ea=l.forwardRef((e,t)=>{let{children:r,...n}=e,o=l.Children.toArray(r),i=o.find(ed);if(i){let e=i.props.children,r=o.map(t=>t!==i?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,c.jsx)(es,{...n,ref:t,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,c.jsx)(es,{...n,ref:t,children:r})});ea.displayName="Slot";var es=l.forwardRef((e,t)=>{let{children:r,...n}=e;if(l.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==l.Fragment&&(o.ref=t?(0,d.t)(t,e):e),l.cloneElement(r,o)}return l.Children.count(r)>1?l.Children.only(null):null});es.displayName="SlotClone";var eu=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function ed(e){return l.isValidElement(e)&&e.type===eu}function ec({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,o]=function({defaultProp:e,onChange:t}){let r=l.useState(e),[n]=r,o=l.useRef(n),i=g(t);return l.useEffect(()=>{o.current!==n&&(i(n),o.current=n)},[n,o,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,a=i?e:n,s=g(r);return[a,l.useCallback(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&s(r)}else o(t)},[i,e,o,s])]}var ef=l.forwardRef((e,t)=>(0,c.jsx)(y.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ef.displayName="VisuallyHidden";var ep=r(38168),ev=r(31114),em=[" ","Enter","ArrowUp","ArrowDown"],eh=[" ","Enter"],ey="Select",[eg,ew,ex]=function(e){let t=e+"CollectionProvider",[r,n]=(0,u.A)(t),[o,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=l.useRef(null),i=l.useRef(new Map).current;return(0,c.jsx)(o,{scope:t,itemMap:i,collectionRef:n,children:r})};a.displayName=t;let s=e+"CollectionSlot",p=l.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=i(s,r),o=(0,d.s)(t,l.collectionRef);return(0,c.jsx)(f,{ref:o,children:n})});p.displayName=s;let v=e+"CollectionItemSlot",m="data-radix-collection-item",h=l.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,a=l.useRef(null),s=(0,d.s)(t,a),u=i(v,r);return l.useEffect(()=>(u.itemMap.set(a,{ref:a,...o}),()=>void u.itemMap.delete(a))),(0,c.jsx)(f,{[m]:"",ref:s,children:n})});return h.displayName=v,[{Provider:a,Slot:p,ItemSlot:h},function(t){let r=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(ey),[eb,eE]=(0,u.A)(ey,[ex,K]),eC=K(),[eS,eR]=eb(ey),[ej,eP]=eb(ey),eN=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:m,form:y}=e,g=eC(t),[w,x]=l.useState(null),[b,E]=l.useState(null),[C,S]=l.useState(!1),R=function(e){let t=l.useContext(h);return e||t||"ltr"}(d),[j=!1,P]=ec({prop:n,defaultProp:o,onChange:i}),[N,A]=ec({prop:a,defaultProp:s,onChange:u}),T=l.useRef(null),k=!w||y||!!w.closest("form"),[L,D]=l.useState(new Set),I=Array.from(L).map(e=>e.props.value).join(";");return(0,c.jsx)(q,{...g,children:(0,c.jsxs)(eS,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:W(),value:N,onValueChange:A,open:j,onOpenChange:P,dir:R,triggerPointerDownPosRef:T,disabled:v,children:[(0,c.jsx)(eg.Provider,{scope:t,children:(0,c.jsx)(ej,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{D(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),k?(0,c.jsxs)(tt,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>A(e.target.value),disabled:v,form:y,children:[void 0===N?(0,c.jsx)("option",{value:""}):null,Array.from(L)]},I):null]})})};eN.displayName=ey;var eA="SelectTrigger",eT=l.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,i=eC(r),a=eR(eA,r),u=a.disabled||n,f=(0,d.s)(t,a.onTriggerChange),p=ew(r),v=l.useRef("touch"),[m,h,g]=tr(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===a.value),n=tn(t,e,r);void 0!==n&&a.onValueChange(n.value)}),w=e=>{u||(a.onOpenChange(!0),g()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)($,{asChild:!0,...i,children:(0,c.jsx)(y.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":te(a.value)?"":void 0,...o,ref:f,onClick:s(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&w(e)}),onPointerDown:s(o.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:s(o.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&em.includes(e.key)&&(w(),e.preventDefault())})})})});eT.displayName=eA;var ek="SelectValue",eL=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:i="",...a}=e,s=eR(ek,r),{onValueNodeHasChildrenChange:u}=s,f=void 0!==o,p=(0,d.s)(t,s.onValueNodeChange);return O(()=>{u(f)},[u,f]),(0,c.jsx)(y.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:te(s.value)?(0,c.jsx)(c.Fragment,{children:i}):o})});eL.displayName=ek;var eD=l.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,c.jsx)(y.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});eD.displayName="SelectIcon";var eI=e=>(0,c.jsx)(ei,{asChild:!0,...e});eI.displayName="SelectPortal";var eO="SelectContent",eM=l.forwardRef((e,t)=>{let r=eR(eO,e.__scopeSelect),[n,o]=l.useState();return(O(()=>{o(new DocumentFragment)},[]),r.open)?(0,c.jsx)(e_,{...e,ref:t}):n?i.createPortal((0,c.jsx)(eF,{scope:e.__scopeSelect,children:(0,c.jsx)(eg.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),n):null});eM.displayName=eO;var[eF,eW]=eb(eO),e_=l.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:f,align:p,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:x,...E}=e,C=eR(eO,r),[j,P]=l.useState(null),[N,T]=l.useState(null),k=(0,d.s)(t,e=>P(e)),[L,D]=l.useState(null),[I,O]=l.useState(null),M=ew(r),[F,W]=l.useState(!1),_=l.useRef(!1);l.useEffect(()=>{if(j)return(0,ep.Eq)(j)},[j]),l.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:R()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:R()),S++,()=>{1===S&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),S--}},[]);let H=l.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&N&&(N.scrollTop=0),r===n&&N&&(N.scrollTop=N.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[M,N]),B=l.useCallback(()=>H([L,j]),[H,L,j]);l.useEffect(()=>{F&&B()},[F,B]);let{onOpenChange:V,triggerPointerDownPosRef:G}=C;l.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=G.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=G.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||V(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,V,G]),l.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[K,z]=tr(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=tn(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),U=l.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==C.value&&C.value===t||n)&&(D(e),n&&(_.current=!0))},[C.value]),q=l.useCallback(()=>null==j?void 0:j.focus(),[j]),Y=l.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==C.value&&C.value===t||n)&&O(e)},[C.value]),$="popper"===n?eB:eH,X=$===eB?{side:u,sideOffset:f,align:p,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:x}:{};return(0,c.jsx)(eF,{scope:r,content:j,viewport:N,onViewportChange:T,itemRefCallback:U,selectedItem:L,onItemLeave:q,itemTextRefCallback:Y,focusSelectedItem:B,selectedItemText:I,position:n,isPositioned:F,searchRef:K,children:(0,c.jsx)(ev.A,{as:ea,allowPinchZoom:!0,children:(0,c.jsx)(A,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:s(o,e=>{var t;null===(t=C.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(b,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,c.jsx)($,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...X,onPlaced:()=>W(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:s(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});e_.displayName="SelectContentImpl";var eH=l.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...o}=e,i=eR(eO,r),s=eW(eO,r),[u,f]=l.useState(null),[p,v]=l.useState(null),m=(0,d.s)(t,e=>v(e)),h=ew(r),g=l.useRef(!1),w=l.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:C}=s,S=l.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&x&&b&&E){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),l=E.getBoundingClientRect();if("rtl"!==i.dir){let n=l.left-t.left,o=r.left-n,i=e.left-o,s=e.width+i,d=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let n=t.right-l.right,o=window.innerWidth-r.right-n,i=window.innerWidth-e.right-o,s=e.width+i,d=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let o=h(),s=window.innerHeight-20,d=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),y=f+v+d+parseInt(c.paddingBottom,10)+m,w=Math.min(5*b.offsetHeight,y),C=window.getComputedStyle(x),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),j=e.top+e.height/2-10,P=b.offsetHeight/2,N=f+v+(b.offsetTop+P);if(N<=j){let e=o.length>0&&b===o[o.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-j,P+(e?R:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+m);u.style.height=N+t+"px"}else{let e=o.length>0&&b===o[0].ref.current;u.style.top="0px";let t=Math.max(j,f+x.offsetTop+(e?S:0)+P);u.style.height=t+(y-N)+"px",x.scrollTop=N-j+x.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=w+"px",u.style.maxHeight=s+"px",null==n||n(),requestAnimationFrame(()=>g.current=!0)}},[h,i.trigger,i.valueNode,u,p,x,b,E,i.dir,n]);O(()=>S(),[S]);let[R,j]=l.useState();O(()=>{p&&j(window.getComputedStyle(p).zIndex)},[p]);let P=l.useCallback(e=>{e&&!0===w.current&&(S(),null==C||C(),w.current=!1)},[S,C]);return(0,c.jsx)(eV,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:P,children:(0,c.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,c.jsx)(y.sG.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});eH.displayName="SelectItemAlignedPosition";var eB=l.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,i=eC(r);return(0,c.jsx)(Q,{...i,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName="SelectPopperPosition";var[eV,eG]=eb(eO,{}),eK="SelectViewport",ez=l.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,i=eW(eK,r),a=eG(eK,r),u=(0,d.s)(t,i.onViewportChange),f=l.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,c.jsx)(eg.Slot,{scope:r,children:(0,c.jsx)(y.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:s(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=a;if((null==n?void 0:n.current)&&r){let e=Math.abs(f.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,i=Math.min(n,o),a=o-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});ez.displayName=eK;var eU="SelectGroup",[eq,eY]=eb(eU);l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=W();return(0,c.jsx)(eq,{scope:r,id:l,children:(0,c.jsx)(y.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=eU;var e$="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=eY(e$,r);return(0,c.jsx)(y.sG.div,{id:l.id,...n,ref:t})}).displayName=e$;var eX="SelectItem",[eZ,eJ]=eb(eX),eQ=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:i,...a}=e,u=eR(eX,r),f=eW(eX,r),p=u.value===n,[v,m]=l.useState(null!=i?i:""),[h,g]=l.useState(!1),w=(0,d.s)(t,e=>{var t;return null===(t=f.itemRefCallback)||void 0===t?void 0:t.call(f,e,n,o)}),x=W(),b=l.useRef("touch"),E=()=>{o||(u.onValueChange(n),u.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(eZ,{scope:r,value:n,disabled:o,textId:x,isSelected:p,onItemTextChange:l.useCallback(e=>{m(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,c.jsx)(eg.ItemSlot,{scope:r,value:n,disabled:o,textValue:v,children:(0,c.jsx)(y.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":h?"":void 0,"aria-selected":p&&h,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:w,onFocus:s(a.onFocus,()=>g(!0)),onBlur:s(a.onBlur,()=>g(!1)),onClick:s(a.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:s(a.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:s(a.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:s(a.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=f.onItemLeave)||void 0===t||t.call(f)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:s(a.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=f.onItemLeave)||void 0===t||t.call(f)}}),onKeyDown:s(a.onKeyDown,e=>{var t;((null===(t=f.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(eh.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eQ.displayName=eX;var e0="SelectItemText",e1=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,...a}=e,s=eR(e0,r),u=eW(e0,r),f=eJ(e0,r),p=eP(e0,r),[v,m]=l.useState(null),h=(0,d.s)(t,e=>m(e),f.onItemTextChange,e=>{var t;return null===(t=u.itemTextRefCallback)||void 0===t?void 0:t.call(u,e,f.value,f.disabled)}),g=null==v?void 0:v.textContent,w=l.useMemo(()=>(0,c.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return O(()=>(x(w),()=>b(w)),[x,b,w]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(y.sG.span,{id:f.textId,...a,ref:h}),f.isSelected&&s.valueNode&&!s.valueNodeHasChildren?i.createPortal(a.children,s.valueNode):null]})});e1.displayName=e0;var e5="SelectItemIndicator",e6=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eJ(e5,r).isSelected?(0,c.jsx)(y.sG.span,{"aria-hidden":!0,...n,ref:t}):null});e6.displayName=e5;var e9="SelectScrollUpButton",e2=l.forwardRef((e,t)=>{let r=eW(e9,e.__scopeSelect),n=eG(e9,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,d.s)(t,n.onScrollButtonChange);return O(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,c.jsx)(e8,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});e2.displayName=e9;var e4="SelectScrollDownButton",e3=l.forwardRef((e,t)=>{let r=eW(e4,e.__scopeSelect),n=eG(e4,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,d.s)(t,n.onScrollButtonChange);return O(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,c.jsx)(e8,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});e3.displayName=e4;var e8=l.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,i=eW("SelectScrollButton",r),a=l.useRef(null),u=ew(r),d=l.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),O(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,c.jsx)(y.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:s(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(n,50))}),onPointerMove:s(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===a.current&&(a.current=window.setInterval(n,50))}),onPointerLeave:s(o.onPointerLeave,()=>{d()})})});l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,c.jsx)(y.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var e7="SelectArrow";function te(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=eC(r),o=eR(e7,r),i=eW(e7,r);return o.open&&"popper"===i.position?(0,c.jsx)(er,{...l,...n,ref:t}):null}).displayName=e7;var tt=l.forwardRef((e,t)=>{let{value:r,...n}=e,o=l.useRef(null),i=(0,d.s)(t,o),a=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return l.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,c.jsx)(ef,{asChild:!0,children:(0,c.jsx)("select",{...n,ref:i,defaultValue:r})})});function tr(e){let t=g(e),r=l.useRef(""),n=l.useRef(0),o=l.useCallback(e=>{let l=r.current+e;t(l),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),i=l.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,i]}function tn(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,l=Math.max(i,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}tt.displayName="BubbleSelect";var tl=eN,to=eT,ti=eL,ta=eD,ts=eI,tu=eM,td=ez,tc=eQ,tf=e1,tp=e6,tv=e2,tm=e3}}]);