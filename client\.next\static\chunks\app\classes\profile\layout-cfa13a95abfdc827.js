(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1948],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(95155);s(12115);var a=s(6874),l=s.n(a),i=s(66766),o=s(29911);let n=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:o.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:o.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:o.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:o.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:o.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:o.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:o.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},14050:(e,t,s)=>{"use strict";s.d(t,{b:()=>d});var r=s(12115);s(47650);var a=s(66634),l=s(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,a.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?s:t,{...i,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),o="horizontal",n=["horizontal","vertical"],c=r.forwardRef((e,t)=>{var s;let{decorative:r,orientation:a=o,...c}=e,d=(s=a,n.includes(s))?a:o;return(0,l.jsx)(i.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var d=c},20185:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>i,RO:()=>c,Wz:()=>l,sA:()=>o});var r=s(94314),a=s(18159);let l=(e,t)=>{var s,a,l,i,o,n,c,d;e.contactNo&&t((0,r.ac)(r._3.PROFILE)),(null===(a=e.ClassAbout)||void 0===a?void 0:null===(s=a.tutorBio)||void 0===s?void 0:s.length)>50&&t((0,r.ac)(r._3.DESCRIPTION)),(null===(l=e.ClassAbout)||void 0===l?void 0:l.profilePhoto)&&(null===(i=e.ClassAbout)||void 0===i?void 0:i.classesLogo)&&t((0,r.ac)(r._3.PHOTO_LOGO)),(null===(o=e.education)||void 0===o?void 0:o.length)>0&&t((0,r.ac)(r._3.EDUCATION)),(null===(n=e.certificates)||void 0===n?void 0:n.length)>0&&t((0,r.ac)(r._3.CERTIFICATES)),(null===(c=e.experience)||void 0===c?void 0:c.length)>0&&t((0,r.ac)(r._3.EXPERIENCE)),(null===(d=e.tuitionClasses)||void 0===d?void 0:d.length)>0&&t((0,r.ac)(r._3.TUTIONCLASS)),e.address&&t((0,r.ac)(r._3.ADDRESS))},i=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch(t){return[e]}},o=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch(t){return e||"N/A"}},n=new TextEncoder().encode("secret123");async function c(e){try{let{payload:t}=await (0,a.V)(e,n);return t}catch(e){return console.error("Invalid token:",e),null}}},22346:(e,t,s)=>{"use strict";s.d(t,{Separator:()=>i});var r=s(95155);s(12115);var a=s(14050),l=s(59434);function i(e){let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(a.b,{"data-slot":"separator-root",decorative:i,orientation:s,className:(0,l.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},24944:(e,t,s)=>{"use strict";s.d(t,{k:()=>i});var r=s(95155);s(12115);var a=s(55863),l=s(59434);function i(e){let{className:t,value:s,...i}=e;return(0,r.jsx)(a.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...i,children:(0,r.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},35919:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,B:()=>i});var r=s(51990),a=s(45436);let l=(0,r.Z0)({name:"class",initialState:{classData:null,loading:!1,error:null},reducers:{setClassData(e,t){e.classData=t.payload}},extraReducers:e=>{e.addCase(a.V.pending,e=>{e.loading=!0,e.error=null}).addCase(a.V.fulfilled,(e,t)=>{e.loading=!1,e.classData=t.payload}).addCase(a.V.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setClassData:i}=l.actions,o=l.reducer},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},45436:(e,t,s)=>{"use strict";s.d(t,{V:()=>a});var r=s(55077);let a=(0,s(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:s}=t;try{return(await r.S.get("/classes/details/".concat(e))).data}catch(e){var a;return s((null===(a=e.response)||void 0===a?void 0:a.data)||"Fetch failed")}})},76607:(e,t,s)=>{Promise.resolve().then(s.bind(s,86053))},86053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(95155),a=s(22346),l=s(6874),i=s.n(l),o=s(35695),n=s(34540),c=s(40646);function d(e){let{items:t}=e,s=(0,o.usePathname)(),{completedForms:a}=(0,n.d4)(e=>e.formProgress),l=e=>e.toLowerCase().replace(/ & /g,"_").replace(/\s+/g,"_");return(0,r.jsx)("nav",{className:"space-y-1",children:t.map((e,o)=>{let n=l(e.title),d=s===e.href,h=o>0&&!a[l(t[o-1].title)];return(0,r.jsxs)(i(),{href:h?"#":e.href,className:"flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ".concat(d?"bg-muted text-primary":h?"text-gray-400 cursor-not-allowed":"text-muted-foreground hover:text-primary"),onClick:e=>{h&&e.preventDefault()},children:[(0,r.jsx)("span",{children:e.title}),a[n]&&(0,r.jsx)(c.A,{size:16,className:"text-green-500"})]},e.href)})})}var h=s(24944),u=s(12115),f=s(55077),x=s(35919),m=s(20185),p=s(30285),v=s(70347),g=s(7583);let j=[{title:"About",href:"/classes/profile"},{title:"Description",href:"/classes/profile/description"},{title:"Address",href:"/classes/profile/address"},{title:"Photo & Logo",href:"/classes/profile/photo-and-logo"},{title:"Education",href:"/classes/profile/education"},{title:"Experience",href:"/classes/profile/experience"},{title:"Certificates",href:"/classes/profile/certificates"},{title:"Tution Class",href:"/classes/profile/tution-class"}];function w(e){let{children:t}=e,{completedSteps:s,totalSteps:l}=(0,n.d4)(e=>e.formProgress),{user:i}=function(){let e=(0,n.d4)(e=>e.user.isAuthenticated),t=(0,o.useRouter)();return console.log(e),(0,u.useEffect)(()=>{e||t.replace("/?authError=1")},[e,t]),{user:e}}(),{user:c}=(0,n.d4)(e=>e.user),w=(0,n.wA)(),[y,N]=(0,u.useState)(!1),[b,S]=(0,u.useState)(!1),[E,A]=(0,u.useState)("");if((0,u.useEffect)(()=>{let e=async()=>{try{var e,t,s,r,a,l;let i=await f.S.get("/classes/details/".concat(c.id));w((0,x.B)(i.data)),(0,m.Wz)(i.data,w),((null===(t=i.data)||void 0===t?void 0:null===(e=t.status)||void 0===e?void 0:e.status)==="PENDING"||(null===(r=i.data)||void 0===r?void 0:null===(s=r.status)||void 0===s?void 0:s.status)==="APPROVED")&&(S(!0),A(null===(l=i.data)||void 0===l?void 0:null===(a=l.status)||void 0===a?void 0:a.status))}catch(e){console.error("Failed to fetch class:",e)}};(null==c?void 0:c.id)&&e()},[c,w]),!i)return null;let C=s/l*100,P=100===Math.round(C),_=async()=>{try{N(!0),await f.S.post("/classes-profile/send-for-review/".concat(c.id)),S(!0)}catch(e){console.error("Error sending for review:",e)}finally{N(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.default,{}),(0,r.jsxs)("div",{className:"space-y-6 p-10 pb-4 md:block",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Edit Profile"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Start creating your public profile. Your progress will be automatically saved as you complete each section. You can return at any time to finish your registration."})]}),(0,r.jsx)(h.k,{value:C,className:"h-2"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[Math.round(C),"% complete"]}),P&&(0,r.jsx)("div",{className:"mt-4",children:b?(0,r.jsx)(p.$,{className:"bg-gray-400 text-white cursor-not-allowed",disabled:!0,children:"APPROVED"===E?"Profile Approved ✅":"Profile Sent for Review"}):(0,r.jsx)(p.$,{className:"bg-green-600 hover:bg-green-700 text-white",disabled:y,onClick:_,children:"Send for Review"})}),(0,r.jsx)(a.Separator,{className:"my-6"}),(0,r.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[(0,r.jsx)("aside",{className:"-mx-4 lg:w-1/6 pb-12",children:(0,r.jsx)(d,{items:j})}),(0,r.jsx)("div",{className:"flex justify-center w-full",children:(0,r.jsx)("div",{className:"flex-1 lg:max-w-2xl pb-12",children:t})})]})]}),(0,r.jsx)(g.default,{})]})}},94314:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,_3:()=>a,ac:()=>i});var r=s(51990),a=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let l=(0,r.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let s=t.payload;e.completedForms[s]||(e.completedForms[s]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:o}=l.actions,n=l.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,8159,1682,347,8441,1684,7358],()=>t(76607)),_N_E=e.O()}]);