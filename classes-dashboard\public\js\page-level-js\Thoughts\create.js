/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Thoughts/resources/views/js/create.js ***!
  \*******************************************************/
$("#createthoughts_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createthoughtsRoute.store, 'post', '#createthoughts_form', '#savethoughts', '#newThoughtsEntry', '#thoughts_table');
    return false;
  }
});
/******/ })()
;