{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/chatService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const fetchingprivateMessages = async (userId1: string, userId2: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`chat/messages/private?userId1=${userId1}&userId2=${userId2}`, {\r\n      withCredentials: true\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching private messages:', error);\r\n  }\r\n}\r\n\r\nexport const fetchingMessageUsers = async (userId: string, userType: 'student' | 'class') => {\r\n  try {\r\n    const response = await axiosInstance.get(`chat/messages/users?userId=${userId}&userType=${userType}`, {\r\n      withCredentials: true\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching message users:', error);\r\n  }\r\n}\r\n\r\nexport const fetchingUnreadMessageUsers = async (userId: string, userType: 'student' | 'class') => {\r\n  try {\r\n    const response = await axiosInstance.get(`chat/messages/unread-users?userId=${userId}&userType=${userType}`, {\r\n      withCredentials: true\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching unread message users:', error);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,MAAM,0BAA0B,OAAO,SAAiB;IAC7D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ,SAAS,EAAE,SAAS,EAAE;YACtG,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;AACF;AAEO,MAAM,uBAAuB,OAAO,QAAgB;IACzD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,OAAO,UAAU,EAAE,UAAU,EAAE;YACpG,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAEO,MAAM,6BAA6B,OAAO,QAAgB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,UAAU,EAAE,UAAU,EAAE;YAC3G,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;IACxD;AACF", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/SharedChat.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, useRef, FormEvent, useMemo } from 'react';\r\nimport {\r\n    Avatar,\r\n    AvatarFallback,\r\n} from '@/components/ui/avatar';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n    ArrowLeft,\r\n    Search,\r\n    Send,\r\n    X,\r\n    MessageSquare,\r\n    Users,\r\n    Check,\r\n    CheckCheck,\r\n    RefreshCw\r\n} from 'lucide-react';\r\nimport { io, Socket } from 'socket.io-client';\r\nimport { format } from 'date-fns';\r\nimport { toast } from 'sonner';\r\nimport { ChatMessage, OnlineUser, SharedChatProps } from '@/lib/types';\r\nimport Image from 'next/image';\r\nimport { useIsMobile } from '@/hooks/use-mobile';\r\nimport { fetchingMessageUsers, fetchingprivateMessages } from '@/services/chatService';\r\nimport { useRouter } from 'next/navigation';\r\nimport EmojiPicker, { EmojiStyle } from 'emoji-picker-react';\r\n\r\nexport default function SharedChat({ userType, isAuthenticated, username, userId, initialSelectedUser, initialSelectedUserId, initialSelectedUserName }: SharedChatProps) {\r\n    const [privateMessages, setPrivateMessages] = useState<ChatMessage[]>([]);\r\n    const [messageInput, setMessageInput] = useState('');\r\n    const [isUsernameSet, setIsUsernameSet] = useState(!!username);\r\n    const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);\r\n    const [offlineMessageUsers, setOfflineMessageUsers] = useState<Array<{ username: string; userId: string }>>([]);\r\n    const [searchQuery, setSearchQuery] = useState('');\r\n    const [selectedUser, setSelectedUser] = useState<string | null>(initialSelectedUser || null);\r\n    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);\r\n    const [currentRoomId, setCurrentRoomId] = useState<string | null>(null);\r\n    const [sidebarOpen, setSidebarOpen] = useState(true);\r\n    const [showEmojiPicker, setShowEmojiPicker] = useState(false);\r\n    const [seenMessages, setSeenMessages] = useState<Set<string>>(new Set());\r\n    const [recipientIsViewing, setRecipientIsViewing] = useState<boolean>(false);\r\n    const [unreadMessageCounts, setUnreadMessageCounts] = useState<Map<string, number>>(new Map());\r\n    const [userFilter, setUserFilter] = useState<'all' | 'unread'>('all');\r\n    const router = useRouter();\r\n\r\n    const messagesEndRef = useRef<HTMLDivElement>(null);\r\n    const socketRef = useRef<Socket | null>(null);\r\n    const emojiPickerRef = useRef<HTMLDivElement>(null);\r\n    const isMobile = useIsMobile();\r\n\r\n    const isProduction = process.env.NEXT_PUBLIC_NODE_ENV === \"production\";\r\n\r\n    const socketUrl = isProduction\r\n        ? process.env.NEXT_PUBLIC_SOCKET_URL_PROD || \"https://www.uest.in\"\r\n        : process.env.NEXT_PUBLIC_SOCKET_URL_LOCAL || \"https://localhost:4005\";\r\n\r\n    const socketPath = process.env.NEXT_PUBLIC_SOCKET_PATH || \"/uapi/socket.io\";\r\n\r\n    // Debug logs\r\n    console.log(\"Environment:\", process.env.NEXT_PUBLIC_NODE_ENV);\r\n    console.log(\"Is Production:\", isProduction);\r\n    console.log(\"Socket URL:\", socketUrl);\r\n    console.log(\"Socket Path:\", socketPath);\r\n\r\n\r\n    const handleEmojiClick = (emojiData: any) => {\r\n        setMessageInput((prev) => prev + emojiData.emoji);\r\n    };\r\n\r\n    const toggleEmojiPicker = (e: React.MouseEvent) => {\r\n        e.preventDefault();\r\n        setShowEmojiPicker((prev) => !prev);\r\n    };\r\n\r\n    useEffect(() => {\r\n        setIsUsernameSet(!!username);\r\n    }, [username]);\r\n\r\n    useEffect(() => {\r\n        const handleClickOutside = (event: MouseEvent) => {\r\n            if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {\r\n                setShowEmojiPicker(false);\r\n            }\r\n        };\r\n\r\n        if (showEmojiPicker) {\r\n            document.addEventListener('mousedown', handleClickOutside);\r\n        }\r\n\r\n        return () => {\r\n            document.removeEventListener('mousedown', handleClickOutside);\r\n        };\r\n    }, [showEmojiPicker]);\r\n\r\n    useEffect(() => {\r\n        if (isAuthenticated && isUsernameSet && username) {\r\n            if (socketRef.current) {\r\n                socketRef.current.disconnect();\r\n            }\r\n\r\n            socketRef.current = io(socketUrl, {\r\n                withCredentials: true,\r\n                path: socketPath,\r\n            });\r\n\r\n            socketRef.current.on('connect', () => {\r\n                socketRef.current?.emit('join', { username, userType, userId });\r\n                socketRef.current?.emit('getOnlineUsers');\r\n                socketRef.current?.emit('getUnreadCounts', { userId, userType });\r\n            });\r\n\r\n            socketRef.current.on('connect_error', (error) => {\r\n                toast.error(`Connection error: ${error.message}`);\r\n            });\r\n\r\n            socketRef.current.on('roomJoined', (data: { roomId: string }) => {\r\n                setCurrentRoomId(data.roomId);\r\n            });\r\n\r\n            socketRef.current.on('roomLeft', () => {\r\n                setCurrentRoomId(null);\r\n            });\r\n\r\n            socketRef.current.on('messagesMarkedAsSeen', (data: { byUserId: string, messageIds: string[] }) => {\r\n                if (data.byUserId === selectedUserId) {\r\n                    setSeenMessages(prev => {\r\n                        const newSet = new Set(prev);\r\n                        data.messageIds.forEach(messageId => {\r\n                            newSet.add(messageId);\r\n                        });\r\n                        return newSet;\r\n                    });\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('privateMessage', (message: ChatMessage) => {\r\n                if (selectedUser &&\r\n                    ((message.sender === username && message.recipient === selectedUser) ||\r\n                        (message.sender === selectedUser && message.recipient === username))) {\r\n                    setPrivateMessages(prev => {\r\n                        const messageExists = prev.some(msg => msg.id === message.id);\r\n                        if (messageExists) {\r\n                            return prev;\r\n                        }\r\n                        return [...prev, message];\r\n                    });\r\n                }\r\n\r\n                if (message.sender !== username && !offlineMessageUsers.some(user => user.userId === message.senderId)) {\r\n                    setOfflineMessageUsers(prev => [\r\n                        ...prev,\r\n                        { username: message.sender, userId: message.senderId }\r\n                    ]);\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('onlineUsers', (users: OnlineUser[]) => {\r\n                const uniqueUsers = Array.from(new Map(users.map(user => [user.userId, user])).values());\r\n                setOnlineUsers(uniqueUsers);\r\n            });\r\n\r\n            socketRef.current.on('userStartedViewing', (data: { viewerId: string }) => {\r\n                if (data.viewerId === selectedUserId) {\r\n                    setRecipientIsViewing(true);\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('userStoppedViewing', (data: { viewerId: string }) => {\r\n                if (data.viewerId === selectedUserId) {\r\n                    setRecipientIsViewing(false);\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('unreadCountUpdate', (data: { senderId: string, senderName: string, unreadCount: number }) => {\r\n                setUnreadMessageCounts(prev => {\r\n                    const newMap = new Map(prev);\r\n                    if (data.unreadCount === 0) {\r\n                        newMap.delete(data.senderId);\r\n                    } else {\r\n                        newMap.set(data.senderId, data.unreadCount);\r\n                    }\r\n                    return newMap;\r\n                });\r\n            });\r\n\r\n            socketRef.current.on('unreadCountsData', (data: Array<{ userId: string, unreadCount: number }>) => {\r\n                const unreadCountsMap = new Map<string, number>();\r\n                data.forEach((user: any) => {\r\n                    unreadCountsMap.set(user.userId, user.unreadCount);\r\n                });\r\n                setUnreadMessageCounts(unreadCountsMap);\r\n            });\r\n\r\n            socketRef.current.on('updateMessageUsers', (data: { username: string, userId: string }) => {\r\n                setOfflineMessageUsers(prev => {\r\n                    if (!prev.some(user => user.userId === data.userId)) {\r\n                        return [...prev, { username: data.username, userId: data.userId }];\r\n                    }\r\n                    return prev;\r\n                });\r\n            });\r\n\r\n            socketRef.current.on('error', (error: { message: string }) => {\r\n                toast.error(error.message);\r\n            });\r\n\r\n            return () => {\r\n                if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n                    socketRef.current.emit('leaveChatRoom', {\r\n                        userId: userId,\r\n                        recipientId: selectedUserId\r\n                    });\r\n                }\r\n\r\n                socketRef.current?.off('connect');\r\n                socketRef.current?.off('connect_error');\r\n                socketRef.current?.off('privateMessage');\r\n                socketRef.current?.off('onlineUsers');\r\n                socketRef.current?.off('error');\r\n                socketRef.current?.off('roomJoined');\r\n                socketRef.current?.off('roomLeft');\r\n                socketRef.current?.off('messagesMarkedAsSeen');\r\n                socketRef.current?.off('userStartedViewing');\r\n                socketRef.current?.off('userStoppedViewing');\r\n                socketRef.current?.off('unreadCountUpdate');\r\n                socketRef.current?.off('unreadCountsData');\r\n                socketRef.current?.off('updateMessageUsers');\r\n                socketRef.current?.disconnect();\r\n            };\r\n        }\r\n    }, [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser]);\r\n\r\n    useEffect(() => {\r\n        const fetchPrivateMessages = async () => {\r\n            if (selectedUserId && userId && isUsernameSet) {\r\n                try {\r\n                    const data = await fetchingprivateMessages(userId, selectedUserId);\r\n                    const uniqueMessages = (data || []).filter((message: any, index: number, self: any[]) =>\r\n                        index === self.findIndex((m: any) => m.id === message.id)\r\n                    );\r\n                    setPrivateMessages(uniqueMessages);\r\n                } catch {\r\n                    toast.error('Failed to load messages. Please try again.');\r\n                    setPrivateMessages([]);\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchPrivateMessages();\r\n\r\n        const refreshInterval = setInterval(() => {\r\n            if (selectedUserId && userId && isUsernameSet) {\r\n                fetchPrivateMessages();\r\n            }\r\n        }, 60000);\r\n\r\n        return () => clearInterval(refreshInterval);\r\n    }, [selectedUserId, userId, isUsernameSet]);\r\n\r\n    useEffect(() => {\r\n        const fetchMessageUsers = async () => {\r\n            if (isUsernameSet && userId) {\r\n                try {\r\n                    const data = await fetchingMessageUsers(userId, userType);\r\n                    const uniqueUsers = Array.from(new Map(data.map((user: any) => [user.userId, user])).values());\r\n                    setOfflineMessageUsers(uniqueUsers as Array<{ username: string; userId: string }>);\r\n                } catch {\r\n                    // Silently handle error\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchMessageUsers();\r\n\r\n        const refreshInterval = setInterval(fetchMessageUsers, 30000);\r\n\r\n        return () => clearInterval(refreshInterval);\r\n    }, [isUsernameSet, userId, userType]);\r\n\r\n    useEffect(() => {\r\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n    }, [privateMessages]);\r\n\r\n    useEffect(() => {\r\n        if ((initialSelectedUser || initialSelectedUserId) && isMobile) {\r\n            setSidebarOpen(false);\r\n        } else {\r\n            setSidebarOpen(!isMobile);\r\n        }\r\n    }, [initialSelectedUser, initialSelectedUserId, isMobile]);\r\n\r\n    useEffect(() => {\r\n        const handleBeforeUnload = () => {\r\n            if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n                socketRef.current.emit('leaveChatRoom', {\r\n                    userId: userId,\r\n                    recipientId: selectedUserId\r\n                });\r\n            }\r\n        };\r\n\r\n        window.addEventListener('beforeunload', handleBeforeUnload);\r\n\r\n        return () => {\r\n            window.removeEventListener('beforeunload', handleBeforeUnload);\r\n        };\r\n    }, [currentRoomId, userId, selectedUserId]);\r\n\r\n    useEffect(() => {\r\n        if (selectedUserId && userId && privateMessages.length > 0) {\r\n            const unseenMessages = privateMessages.filter(msg =>\r\n                msg.sender === selectedUser &&\r\n                msg.recipient === username &&\r\n                !seenMessages.has(msg.id)\r\n            );\r\n\r\n            if (unseenMessages.length > 0) {\r\n                const unseenMessageIds = unseenMessages.map(msg => msg.id);\r\n\r\n                socketRef.current?.emit('markMessagesAsSeen', {\r\n                    senderId: selectedUserId,\r\n                    recipientId: userId,\r\n                    messageIds: unseenMessageIds\r\n                });\r\n            }\r\n        }\r\n    }, [selectedUserId, userId, privateMessages, selectedUser, username, seenMessages]);\r\n\r\n    useEffect(() => {\r\n        if (selectedUserId && userId && socketRef.current) {\r\n            socketRef.current.emit('joinChatRoom', {\r\n                userId: userId,\r\n                recipientId: selectedUserId\r\n            });\r\n        }\r\n\r\n        return () => {\r\n            if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n                socketRef.current.emit('leaveChatRoom', {\r\n                    userId: userId,\r\n                    recipientId: selectedUserId\r\n                });\r\n            }\r\n        };\r\n    }, [selectedUserId, userId, currentRoomId]);\r\n\r\n    const handleSendMessage = async (e: FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        if (!messageInput.trim() || !selectedUser || !userId) {\r\n            return;\r\n        }\r\n\r\n        const messageText = messageInput.trim();\r\n        setMessageInput('');\r\n\r\n        try {\r\n\r\n            if (!selectedUserId) {\r\n                toast.error('No recipient selected. Please select a user first.');\r\n                setMessageInput(messageText);\r\n                return;\r\n            }\r\n\r\n            const recipientType = userType === 'student' ? 'class' : 'student';\r\n\r\n            const messageData = {\r\n                text: messageText,\r\n                senderId: userId,\r\n                recipientId: selectedUserId,\r\n                senderType: userType,\r\n                recipientType: recipientType,\r\n                recipientUsername: selectedUser\r\n            };\r\n\r\n            if(userType == 'student'){\r\n                router.replace('/student/chat');\r\n            }\r\n\r\n            socketRef.current?.emit('sendPrivateMessage', messageData);\r\n\r\n            if (!isUserOnline(selectedUser)) {\r\n                toast.info(`${selectedUser} is offline. Your message will be delivered when they come online.`);\r\n            }\r\n        } catch {\r\n            toast.error('Failed to send message. Please try again.');\r\n            setMessageInput(messageText);\r\n        }\r\n    };\r\n\r\n    const handleUserSelect = async (user: any) => {\r\n        leaveCurrentRoom();\r\n\r\n        setPrivateMessages([]);\r\n        setSelectedUser(user.username);\r\n        setSeenMessages(new Set());\r\n        setRecipientIsViewing(false);\r\n\r\n        const targetUserId = user.userId || user.username;\r\n        setSelectedUserId(targetUserId);\r\n\r\n        setUnreadMessageCounts(prev => {\r\n            const newMap = new Map(prev);\r\n            newMap.delete(targetUserId);\r\n            return newMap;\r\n        });\r\n\r\n        if (isMobile) {\r\n            setSidebarOpen(false);\r\n        }\r\n\r\n        if (targetUserId && userId) {\r\n            try {\r\n                const data = await fetchingprivateMessages(userId, targetUserId);\r\n                setPrivateMessages(data || []);\r\n            } catch {\r\n                toast.error('Failed to load conversation history.');\r\n            }\r\n        }\r\n    };\r\n\r\n    const leaveCurrentRoom = () => {\r\n        if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n            socketRef.current.emit('leaveChatRoom', {\r\n                userId: userId,\r\n                recipientId: selectedUserId\r\n            });\r\n        }\r\n    };\r\n\r\n    const handleBackToSidebar = () => {\r\n        leaveCurrentRoom();\r\n\r\n        setSidebarOpen(true);\r\n        if (isMobile) {\r\n            setSelectedUser(null);\r\n            setSelectedUserId(null);\r\n        }\r\n    };\r\n\r\n    const formatTime = useMemo(() => {\r\n        return (timestamp: Date) => format(new Date(timestamp), 'h:mm a');\r\n    }, []);\r\n\r\n    const allAvailableUsers = useMemo(() => {\r\n        const userMap = new Map<string, { username: string; userType: string; userId: string }>();\r\n\r\n        offlineMessageUsers.forEach(messageUser => {\r\n            userMap.set(messageUser.userId, {\r\n                username: messageUser.username,\r\n                userType: userType === 'student' ? 'class' : 'student',\r\n                userId: messageUser.userId\r\n            });\r\n        });\r\n\r\n        onlineUsers.forEach(onlineUser => {\r\n            if (onlineUser.username !== username && onlineUser.userType !== userType) {\r\n                userMap.set(onlineUser.userId || onlineUser.username, {\r\n                    username: onlineUser.username,\r\n                    userType: onlineUser.userType,\r\n                    userId: onlineUser.userId || onlineUser.username\r\n                });\r\n            }\r\n        });\r\n\r\n        if (initialSelectedUserId && initialSelectedUserName) {\r\n            userMap.set(initialSelectedUserId, {\r\n                username: initialSelectedUserName,\r\n                userType: userType === 'student' ? 'class' : 'student',\r\n                userId: initialSelectedUserId\r\n            });\r\n        }\r\n\r\n        const filteredUsers = Array.from(userMap.values()).filter(user => {\r\n            const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase());\r\n            const isNotCurrentUser = user.username !== username;\r\n            const hasDifferentUserType = user.userType !== userType;\r\n\r\n            return matchesSearch && isNotCurrentUser && hasDifferentUserType;\r\n        });\r\n\r\n        return filteredUsers;\r\n    }, [offlineMessageUsers, onlineUsers, searchQuery, username, userType, initialSelectedUserId, initialSelectedUserName]);\r\n\r\n    const isUserOnline = useMemo(() => {\r\n        const onlineUserIds = new Set(onlineUsers.map(user => user.userId));\r\n        return (username: string) => {\r\n            const user = allAvailableUsers.find(u => u.username === username);\r\n            if (user) {\r\n                return onlineUserIds.has(user.userId);\r\n            }\r\n\r\n            if (username === initialSelectedUserName && initialSelectedUserId) {\r\n                return onlineUserIds.has(initialSelectedUserId);\r\n            }\r\n\r\n            const onlineUser = onlineUsers.find(u => u.username === username);\r\n            return !!onlineUser;\r\n        };\r\n    }, [onlineUsers, allAvailableUsers, initialSelectedUserName, initialSelectedUserId]);\r\n\r\n    const unreadUsersCount = useMemo(() => {\r\n        return allAvailableUsers.filter(user => {\r\n            const userIdToCheck = user.userId;\r\n            return unreadMessageCounts.has(userIdToCheck);\r\n        }).length;\r\n    }, [allAvailableUsers, unreadMessageCounts]);\r\n\r\n    const filteredUsers = useMemo(() => {\r\n        if (userFilter === 'unread') {\r\n            return allAvailableUsers.filter(user => {\r\n                const userIdToCheck = user.userId;\r\n                return unreadMessageCounts.has(userIdToCheck);\r\n            });\r\n        }\r\n        return allAvailableUsers;\r\n    }, [allAvailableUsers, userFilter, unreadMessageCounts]);\r\n\r\n    useEffect(() => {\r\n        if (initialSelectedUserId && initialSelectedUserName && !selectedUserId) {\r\n            setSelectedUser(initialSelectedUserName);\r\n            setSelectedUserId(initialSelectedUserId);\r\n\r\n            if (userId && isAuthenticated) {\r\n                fetchingprivateMessages(userId, initialSelectedUserId)\r\n                    .then(data => setPrivateMessages(data || []))\r\n                    .catch(() => toast.error('Failed to load conversation history.'));\r\n            }\r\n        }\r\n    }, [initialSelectedUserId, initialSelectedUserName, selectedUserId, userId, isAuthenticated]);\r\n\r\n    useEffect(() => {\r\n        if (initialSelectedUser && !selectedUserId && allAvailableUsers.length > 0 && !initialSelectedUserId) {\r\n            const foundUser = allAvailableUsers.find(user => user.username === initialSelectedUser);\r\n            if (foundUser) {\r\n                setSelectedUser(foundUser.username);\r\n                setSelectedUserId(foundUser.userId);\r\n\r\n                if (foundUser.userId && userId) {\r\n                    fetchingprivateMessages(userId, foundUser.userId)\r\n                        .then(data => setPrivateMessages(data || []))\r\n                        .catch(() => toast.error('Failed to load conversation history.'));\r\n                }\r\n            }\r\n        }\r\n    }, [initialSelectedUser, allAvailableUsers, selectedUserId, userId, initialSelectedUserId]);\r\n\r\n    if (!isAuthenticated) {\r\n        return (\r\n            <div className=\"flex items-center justify-center min-h-[calc(100vh-64px)] bg-background p-4\">\r\n                <div className=\"w-full max-w-md p-4 sm:p-6 bg-card rounded-lg shadow-lg\">\r\n                    <h2 className=\"text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center\">Login Required</h2>\r\n                    <div className=\"flex flex-col items-center justify-center text-center\">\r\n                        <p className=\"text-center text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base\">\r\n                            Please login as a student to access the chat feature.\r\n                        </p>\r\n                        <Button\r\n                            onClick={() => router.push('/')}\r\n                            className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                        >\r\n                            Go to Login\r\n                        </Button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex h-[calc(100vh-64px)] bg-background text-foreground relative overflow-hidden\">\r\n            {sidebarOpen && (\r\n                <aside className={`border-r border-gray-200 flex flex-col bg-gradient-to-b from-white to-gray-50 shadow-lg ${isMobile\r\n                    ? 'absolute inset-0 z-50 w-full'\r\n                    : 'relative w-80 min-w-80 lg:w-96 lg:min-w-96'\r\n                    }`}>\r\n                    <div className=\"p-4 flex items-center justify-between border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <div className=\"relative\">\r\n                                <Image\r\n                                    src=\"/logo.png\"\r\n                                    alt=\"Uest Logo\"\r\n                                    width={isMobile ? 100 : 140}\r\n                                    height={isMobile ? 25 : 35}\r\n                                    className=\"object-contain cursor-pointer hover:opacity-80 transition-all duration-300 hover:scale-105\"\r\n                                    onClick={() => router.push('/')}\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <Button\r\n                                variant=\"outline\"\r\n                                size={isMobile ? \"sm\" : \"default\"}\r\n                                onClick={async () => {\r\n                                    socketRef.current?.emit('getOnlineUsers');\r\n                                    socketRef.current?.emit('getUnreadCounts', { userId, userType });\r\n\r\n                                    if (isUsernameSet && userId) {\r\n                                        try {\r\n                                            const data = await fetchingMessageUsers(userId, userType);\r\n                                            const uniqueUsers = Array.from(new Map(data.map((user: any) => [user.userId, user])).values());\r\n                                            setOfflineMessageUsers(uniqueUsers as Array<{ username: string; userId: string }>);\r\n                                        } catch {\r\n                                            // Silently handle refresh error\r\n                                        }\r\n                                    }\r\n                                }}\r\n                                className={`bg-white/90 border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md ${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'\r\n                                    }`}\r\n                                title=\"Refresh chat list\"\r\n                            >\r\n                                <RefreshCw className={`${isMobile ? 'h-4 w-4' : 'h-4 w-4'}`} />\r\n                                {!isMobile && <span className=\"ml-2\">Refresh</span>}\r\n                            </Button>\r\n                            {isMobile && (\r\n                                <Button\r\n                                    variant=\"ghost\"\r\n                                    size=\"icon\"\r\n                                    className=\"rounded-xl h-10 w-10 hover:bg-gray-100 transition-all duration-300\"\r\n                                    onClick={() => setSidebarOpen(false)}\r\n                                >\r\n                                    <X className=\"h-5 w-5\" />\r\n                                </Button>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className={`${isMobile ? 'p-3' : 'p-4'} bg-white/50`}>\r\n                        <div className=\"relative group\">\r\n                            <Search className={`absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 group-focus-within:text-gray-600 transition-colors duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />\r\n                            <Input\r\n                                placeholder=\"Search conversations...\"\r\n                                className={`pl-12 pr-4 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-2 focus:ring-gray-100 transition-all duration-300 shadow-sm hover:shadow-md ${isMobile ? 'py-2.5 text-sm' : 'py-3 text-base'\r\n                                    }`}\r\n                                value={searchQuery}\r\n                                onChange={(e) => setSearchQuery(e.target.value)}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className={`${isMobile ? 'px-3 pb-3' : 'px-4 pb-4'}`}>\r\n                        <div className=\"bg-gray-100/80 rounded-2xl p-1.5 shadow-inner\">\r\n                            <div className=\"flex gap-1\">\r\n                                <button\r\n                                    onClick={() => setUserFilter('all')}\r\n                                    className={`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${\r\n                                        userFilter === 'all'\r\n                                            ? 'bg-white text-gray-900 shadow-lg border border-gray-200'\r\n                                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'\r\n                                    } ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`}\r\n                                >\r\n                                    <div className={`flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`}>\r\n                                        <Users className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />\r\n                                        <span className={isMobile ? 'text-[10px] leading-tight' : ''}>\r\n                                            {isMobile ? 'All' : 'All Users'}\r\n                                        </span>\r\n                                        <span className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${\r\n                                            userFilter === 'all'\r\n                                                ? 'bg-black text-white'\r\n                                                : 'bg-gray-200 text-gray-600'\r\n                                        }`}>\r\n                                            {allAvailableUsers.length}\r\n                                        </span>\r\n                                    </div>\r\n                                </button>\r\n\r\n                                <button\r\n                                    onClick={() => setUserFilter('unread')}\r\n                                    className={`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${\r\n                                        userFilter === 'unread'\r\n                                            ? 'bg-white text-gray-900 shadow-lg border border-gray-200'\r\n                                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'\r\n                                    } ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`}\r\n                                >\r\n                                    <div className={`flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`}>\r\n                                        <div className=\"relative\">\r\n                                            <MessageSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />\r\n                                            {unreadUsersCount > 0 && (\r\n                                                <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-[8px] rounded-full h-2 w-2 animate-pulse\"></div>\r\n                                            )}\r\n                                        </div>\r\n                                        <span className={isMobile ? 'text-[10px] leading-tight' : ''}>\r\n                                            {isMobile ? 'Unread' : 'Unread Only'}\r\n                                        </span>\r\n                                        <span className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${\r\n                                            userFilter === 'unread'\r\n                                                ? unreadUsersCount > 0\r\n                                                    ? 'bg-red-100 text-red-800'\r\n                                                    : 'bg-gray-100 text-gray-600'\r\n                                                : unreadUsersCount > 0\r\n                                                    ? 'bg-red-500 text-white animate-pulse'\r\n                                                    : 'bg-gray-200 text-gray-600'\r\n                                        }`}>\r\n                                            {unreadUsersCount}\r\n                                        </span>\r\n                                    </div>\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex-1 overflow-y-auto overscroll-contain\">\r\n                        <div className={`space-y-2 ${isMobile ? 'px-2 pb-2' : 'px-3 pb-3'}`}>\r\n                            {filteredUsers.length > 0 || (selectedUser && initialSelectedUserId) ? (\r\n                                <>\r\n                                {selectedUser && initialSelectedUserId && initialSelectedUserName &&\r\n                                 !filteredUsers.find(u => u.userId === initialSelectedUserId) && (\r\n                                    <div\r\n                                        key={initialSelectedUserId}\r\n                                        className={`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'\r\n                                            } bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700`}\r\n                                        onClick={() => handleUserSelect({\r\n                                            username: initialSelectedUserName,\r\n                                            userType: userType === 'student' ? 'class' : 'student',\r\n                                            userId: initialSelectedUserId\r\n                                        })}\r\n                                    >\r\n                                        <div className=\"flex gap-3 items-center\">\r\n                                            <div className=\"relative\">\r\n                                                <Avatar className={`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} border-white/50`}>\r\n                                                    <AvatarFallback className={`font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} bg-white text-black`}>\r\n                                                        {initialSelectedUserName.substring(0, 2).toUpperCase()}\r\n                                                    </AvatarFallback>\r\n                                                </Avatar>\r\n                                                <div className={`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'\r\n                                                    } border-white ${isUserOnline(initialSelectedUserName) ? 'bg-green-500 shadow-lg' : 'bg-gray-400'\r\n                                                    }`}>\r\n                                                    <div className={`rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'\r\n                                                        } ${isUserOnline(initialSelectedUserName) ? 'bg-green-300 animate-pulse' : 'bg-gray-300'\r\n                                                        }`}></div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex-1 min-w-0\">\r\n                                                <div className=\"flex justify-between items-center\">\r\n                                                    <h3 className={`font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} text-white`}>\r\n                                                        {initialSelectedUserName}\r\n                                                    </h3>\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2 mt-1\">\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`}>\r\n                                                        Tutor\r\n                                                    </div>\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`}>\r\n                                                        <div className={`w-2 h-2 rounded-full ${isUserOnline(initialSelectedUserName) ? 'bg-green-500' : 'bg-gray-400'}`}></div>\r\n                                                        {isUserOnline(initialSelectedUserName) ? 'Online' : 'Offline'}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                )}\r\n                                {filteredUsers.map((user) => (\r\n                                    <div\r\n                                        key={user.userId} \r\n                                        className={`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'\r\n                                            } ${selectedUser === user.username\r\n                                                ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700'\r\n                                                : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300'\r\n                                            }`}\r\n                                        onClick={() => handleUserSelect(user)}\r\n                                    >\r\n                                        <div className=\"flex gap-3 items-center\">\r\n                                            <div className=\"relative\">\r\n                                                <Avatar className={`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} ${\r\n                                                    selectedUser === user.username\r\n                                                        ? 'border-white/50'\r\n                                                        : 'border-gray-300 group-hover:border-gray-400'\r\n                                                }`}>\r\n                                                    <AvatarFallback className={`font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} ${selectedUser === user.username\r\n                                                        ? 'bg-white text-black'\r\n                                                        : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 group-hover:from-gray-200 group-hover:to-gray-300'\r\n                                                        }`}>\r\n                                                        {user.username.substring(0, 2).toUpperCase()}\r\n                                                    </AvatarFallback>\r\n                                                </Avatar>\r\n                                                <div className={`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'\r\n                                                    } ${selectedUser === user.username ? 'border-white' : 'border-white'\r\n                                                    } ${isUserOnline(user.username) ? 'bg-green-500 shadow-lg' : 'bg-gray-400'\r\n                                                    }`}>\r\n                                                    <div className={`rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'\r\n                                                        } ${isUserOnline(user.username) ? 'bg-green-300 animate-pulse' : 'bg-gray-300'\r\n                                                        }`}></div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex-1 min-w-0\">\r\n                                                <div className=\"flex justify-between items-center\">\r\n                                                    <h3 className={`font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} ${selectedUser === user.username\r\n                                                        ? 'text-white'\r\n                                                        : 'text-gray-900 group-hover:text-black'\r\n                                                        }`}>\r\n                                                        {user.username}\r\n                                                    </h3>\r\n                                                    {unreadMessageCounts.has(user.userId) && (\r\n                                                        <div className=\"bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse\">\r\n                                                            {unreadMessageCounts.get(user.userId)}\r\n                                                        </div>\r\n                                                    )}\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2 mt-1\">\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${\r\n                                                        selectedUser === user.username\r\n                                                            ? 'bg-white/20 text-white'\r\n                                                            : user.userType === 'student'\r\n                                                                ? 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'\r\n                                                                : 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'\r\n                                                    }`}>\r\n                                                        {user.userType === 'student' ? 'Student' : 'Tutor'}\r\n                                                    </div>\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${\r\n                                                        selectedUser === user.username\r\n                                                            ? 'bg-white/20 text-white'\r\n                                                            : isUserOnline(user.username)\r\n                                                                ? 'bg-green-100 text-green-700 group-hover:bg-green-200'\r\n                                                                : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'\r\n                                                    }`}>\r\n                                                        <div className={`w-2 h-2 rounded-full ${isUserOnline(user.username) ? 'bg-green-500' : 'bg-gray-400'}`}></div>\r\n                                                        {isUserOnline(user.username) ? 'Online' : 'Offline'}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                                </>\r\n                            ) : (\r\n                                <div className=\"p-6 text-center\">\r\n                                    <div className=\"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm\">\r\n                                        <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                                            {userFilter === 'unread' ? (\r\n                                                <MessageSquare className=\"h-8 w-8 text-gray-400\" />\r\n                                            ) : (\r\n                                                <Users className=\"h-8 w-8 text-gray-400\" />\r\n                                            )}\r\n                                        </div>\r\n                                        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-sm' : 'text-base'}`}>\r\n                                            {userFilter === 'unread' ? 'No unread messages' : 'No users found'}\r\n                                        </h3>\r\n                                        <p className=\"text-xs text-gray-600 mb-3\">\r\n                                            {userFilter === 'unread'\r\n                                                ? 'All messages have been read or no conversations yet'\r\n                                                : `You can only chat with ${userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you`\r\n                                            }\r\n                                        </p>\r\n                                        <p className=\"text-xs text-gray-500\">\r\n                                            {userFilter === 'unread'\r\n                                                ? 'Switch to \"All Users\" to see all conversations'\r\n                                                : 'Users will appear here when you exchange messages with them'\r\n                                            }\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </aside>\r\n            )}\r\n\r\n            {isMobile && sidebarOpen && (\r\n                <div\r\n                    className=\"absolute inset-0 bg-black/20 z-40\"\r\n                    onClick={() => setSidebarOpen(false)}\r\n                />\r\n            )}\r\n\r\n            <main className=\"flex-1 flex flex-col min-w-0 bg-white\">\r\n                <div className={`border-b-2 border-gray-200 flex items-center gap-3 bg-white ${isMobile ? 'p-3' : 'p-4'}`}>\r\n                    {isMobile && !sidebarOpen && (\r\n                        <Button variant=\"ghost\" size=\"icon\" className={`flex-shrink-0 rounded-xl hover:bg-gray-100 ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`} onClick={handleBackToSidebar}>\r\n                            <ArrowLeft className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />\r\n                        </Button>\r\n                    )}\r\n                    <div className=\"flex gap-3 items-center min-w-0 flex-1\">\r\n                        <div className=\"relative\">\r\n                            <Avatar className={`border-2 border-gray-300 flex-shrink-0 shadow-md ${isMobile ? 'h-9 w-9' : 'h-12 w-12'}`}>\r\n                                {selectedUser ? (\r\n                                    <AvatarFallback className={`font-semibold bg-gray-100 text-black ${isMobile ? 'text-xs' : 'text-sm'}`}>\r\n                                        {selectedUser.substring(0, 2).toUpperCase()}\r\n                                    </AvatarFallback>\r\n                                ) : (\r\n                                    <AvatarFallback className=\"bg-gray-100\">\r\n                                        <Users className={`text-gray-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />\r\n                                    </AvatarFallback>\r\n                                )}\r\n                            </Avatar>\r\n                            {selectedUser && (\r\n                                <div className={`absolute -bottom-1 -right-1 rounded-full border-2 border-white flex items-center justify-center ${isMobile ? 'h-3 w-3' : 'h-4 w-4'\r\n                                    } ${isUserOnline(selectedUser) ? 'bg-green-500' : 'bg-gray-400'\r\n                                    }`}>\r\n                                    <div className={`rounded-full ${isMobile ? 'h-1.5 w-1.5' : 'h-2 w-2'\r\n                                        } ${isUserOnline(selectedUser) ? 'bg-green-400 animate-pulse' : 'bg-gray-300'\r\n                                        }`}></div>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                        <div className=\"min-w-0 flex-1\">\r\n                            <h1 className={`font-semibold flex items-center gap-2 truncate text-black ${isMobile ? 'text-base' : 'text-lg'}`}>\r\n                                {selectedUser ? (\r\n                                    <span className=\"truncate\">{selectedUser}</span>\r\n                                ) : 'Select a user'}\r\n                            </h1>\r\n                            <p className={`text-gray-600 truncate ${isMobile ? 'text-xs' : 'text-sm'}`}>\r\n                                {selectedUser ? (\r\n                                    isUserOnline(selectedUser) ? 'Online' : 'Offline (messages will be delivered when online)'\r\n                                ) : 'Choose someone to chat with'}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className={`flex-1 overflow-y-auto bg-gray-50 ${isMobile ? 'p-3' : 'p-4'}`}>\r\n                    <div className={`mx-auto ${isMobile ? 'space-y-3 max-w-full' : 'space-y-4 max-w-4xl'}`}>\r\n                        {selectedUser ? (\r\n                            privateMessages.length > 0 ? (\r\n                                privateMessages.map((message) => {\r\n                                    const isCurrentUser = message.sender === username;\r\n                                    const senderName = message.sender || 'Unknown';\r\n\r\n                                    return (\r\n                                        <div\r\n                                            key={message.id}\r\n                                            className={`flex items-end ${isCurrentUser ? 'justify-end' : ''} ${isMobile ? 'gap-2' : 'gap-3'}`}\r\n                                        >\r\n                                            {!isCurrentUser && (\r\n                                                <Avatar className={`border-2 border-gray-300 shadow-sm ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`}>\r\n                                                    <AvatarFallback className={`bg-gray-200 text-black font-semibold ${isMobile ? 'text-xs' : 'text-xs'}`}>\r\n                                                        {senderName.substring(0, 2).toUpperCase()}\r\n                                                    </AvatarFallback>\r\n                                                </Avatar>\r\n                                            )}\r\n                                            <div className={`${isCurrentUser ? 'text-right' : ''} ${isMobile ? 'max-w-[80%]' : 'max-w-[70%]'}`}>\r\n                                                <div\r\n                                                    className={`${isCurrentUser\r\n                                                        ? 'bg-black text-white'\r\n                                                        : 'bg-white text-black border-2 border-gray-200'\r\n                                                        } rounded-2xl shadow-lg break-words ${isMobile ? 'p-3' : 'p-4'}`}\r\n                                                >\r\n                                                    <div className={`leading-relaxed ${isMobile ? 'text-sm' : 'text-base'}`}>\r\n                                                        {message.text}\r\n                                                    </div>\r\n                                                    <div className={`text-xs mt-2 flex items-end justify-end gap-1 ${isCurrentUser\r\n                                                        ? 'text-gray-300'\r\n                                                        : 'text-gray-500'\r\n                                                        }`}>\r\n                                                        {formatTime(message.timestamp)}\r\n                                                        {isCurrentUser && (\r\n                                                            <span>\r\n                                                                {seenMessages.has(message.id) ? (\r\n                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />\r\n                                                                ) : recipientIsViewing ? (\r\n                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />\r\n                                                                ) : isUserOnline(selectedUser) ? (\r\n                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`} />\r\n                                                                ) : (\r\n                                                                    <Check className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`} />\r\n                                                                )}\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })\r\n                            ) : (\r\n                                <div className=\"flex flex-col items-center justify-center h-full py-12 text-center\">\r\n                                    <MessageSquare className=\"text-gray-400 mb-4 h-16 w-16\" />\r\n                                    <p className=\"text-gray-600 text-lg font-medium\">No messages yet</p>\r\n                                    <p className=\"text-gray-500 text-sm mt-2\">Send a message to start the conversation</p>\r\n                                </div>\r\n                            )\r\n                        ) : (\r\n                            <div className=\"flex flex-col items-center justify-center h-full py-12 text-center\">\r\n                                <MessageSquare className=\"text-gray-400 mb-4 h-16 w-16\" />\r\n                                <p className=\"text-gray-600 text-lg font-medium\">Select a user to start chatting</p>\r\n                                <p className=\"text-gray-500 text-sm mt-2\">Choose a user from the sidebar to start a private conversation</p>\r\n                                <p className=\"text-gray-500 text-sm mt-4 max-w-md\">\r\n                                    Note: You can only chat with {userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you.\r\n                                    {filteredUsers.length === 0 && (\r\n                                        <span className=\"block mt-2\">There are currently no users to chat with. When you exchange messages with someone, they will appear in the sidebar.</span>\r\n                                    )}\r\n                                </p>\r\n                            </div>\r\n                        )}\r\n                        <div ref={messagesEndRef} />\r\n                    </div>\r\n                </div>\r\n\r\n                <form onSubmit={handleSendMessage} className={`border-t-2 border-gray-200 bg-white ${isMobile ? 'p-3' : 'p-4'}`}>\r\n                    <div className={`flex items-center mx-auto ${isMobile ? 'gap-2 max-w-full' : 'gap-3 max-w-4xl'}`}>\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={toggleEmojiPicker}\r\n                            className={`bg-white border-2 border-gray-200 text-black hover:bg-gray-100 rounded-xl font-medium transition-all duration-300 ${isMobile ? 'text-lg px-2 py-1' : 'text-2xl px-3 py-1'\r\n                                }`}\r\n                        >\r\n                            😊\r\n                        </button>\r\n\r\n                        {showEmojiPicker && (\r\n                            <div\r\n                                ref={emojiPickerRef}\r\n                                className={`absolute z-10 ${isMobile ? 'bottom-12 left-4 right-4' : 'bottom-12 left-96'\r\n                                    }`}\r\n                            >\r\n                                <EmojiPicker\r\n                                    onEmojiClick={handleEmojiClick}\r\n                                    emojiStyle={EmojiStyle.APPLE}\r\n                                    searchDisabled={true}\r\n                                    width={isMobile ? '100%' : undefined}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                        <Input\r\n                            placeholder={selectedUser ? \"Type your message...\" : \"Select a user to start chatting\"}\r\n                            className={`flex-1 bg-gray-50 border-2 border-gray-200 text-black placeholder:text-gray-500 focus:border-black rounded-xl transition-all duration-300 ${isMobile ? 'px-3 py-2 text-sm' : 'px-4 py-3 text-base'\r\n                                }`}\r\n                            value={messageInput}\r\n                            onChange={(e) => setMessageInput(e.target.value)}\r\n                            disabled={!selectedUser}\r\n                            maxLength={250}\r\n                        />\r\n                        <Button\r\n                            type=\"submit\"\r\n                            size={isMobile ? \"default\" : \"lg\"}\r\n                            disabled={!messageInput.trim() || !selectedUser}\r\n                            className={`bg-black text-white hover:bg-gray-800 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 ${isMobile ? 'px-4 py-2' : 'px-6 py-3'\r\n                                }`}\r\n                        >\r\n                            <Send className={`${isMobile ? 'h-4 w-4 mr-1' : 'h-5 w-5 mr-2'}`} />\r\n                            {isMobile ? 'Send' : 'Send'}\r\n                        </Button>\r\n                    </div>\r\n                </form>\r\n            </main>\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AA5BA;;;;;;;;;;;;;;;AA8Be,SAAS,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,uBAAuB,EAAmB;IACpK,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C,EAAE;IAC9G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,uBAAuB;IACvF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,IAAI;IACxF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC/D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,QAAQ,GAAG,CAAC,oBAAoB,KAAK;IAE1D,MAAM,YAAY,eACZ,2DAA2C,wBAC3C,+DAA4C;IAElD,MAAM,aAAa,uDAAuC;IAE1D,aAAa;IACb,QAAQ,GAAG,CAAC,gBAAgB,QAAQ,GAAG,CAAC,oBAAoB;IAC5D,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,eAAe;IAC3B,QAAQ,GAAG,CAAC,gBAAgB;IAG5B,MAAM,mBAAmB,CAAC;QACtB,gBAAgB,CAAC,OAAS,OAAO,UAAU,KAAK;IACpD;IAEA,MAAM,oBAAoB,CAAC;QACvB,EAAE,cAAc;QAChB,mBAAmB,CAAC,OAAS,CAAC;IAClC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,iBAAiB,CAAC,CAAC;IACvB,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,qBAAqB,CAAC;YACxB,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAClF,mBAAmB;YACvB;QACJ;QAEA,IAAI,iBAAiB;YACjB,SAAS,gBAAgB,CAAC,aAAa;QAC3C;QAEA,OAAO;YACH,SAAS,mBAAmB,CAAC,aAAa;QAC9C;IACJ,GAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,mBAAmB,iBAAiB,UAAU;YAC9C,IAAI,UAAU,OAAO,EAAE;gBACnB,UAAU,OAAO,CAAC,UAAU;YAChC;YAEA,UAAU,OAAO,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,WAAW;gBAC9B,iBAAiB;gBACjB,MAAM;YACV;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,WAAW;gBAC5B,UAAU,OAAO,EAAE,KAAK,QAAQ;oBAAE;oBAAU;oBAAU;gBAAO;gBAC7D,UAAU,OAAO,EAAE,KAAK;gBACxB,UAAU,OAAO,EAAE,KAAK,mBAAmB;oBAAE;oBAAQ;gBAAS;YAClE;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,iBAAiB,CAAC;gBACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE;YACpD;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC;gBAChC,iBAAiB,KAAK,MAAM;YAChC;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,YAAY;gBAC7B,iBAAiB;YACrB;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,wBAAwB,CAAC;gBAC1C,IAAI,KAAK,QAAQ,KAAK,gBAAgB;oBAClC,gBAAgB,CAAA;wBACZ,MAAM,SAAS,IAAI,IAAI;wBACvB,KAAK,UAAU,CAAC,OAAO,CAAC,CAAA;4BACpB,OAAO,GAAG,CAAC;wBACf;wBACA,OAAO;oBACX;gBACJ;YACJ;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACpC,IAAI,gBACA,CAAC,AAAC,QAAQ,MAAM,KAAK,YAAY,QAAQ,SAAS,KAAK,gBAClD,QAAQ,MAAM,KAAK,gBAAgB,QAAQ,SAAS,KAAK,QAAS,GAAG;oBAC1E,mBAAmB,CAAA;wBACf,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,EAAE;wBAC5D,IAAI,eAAe;4BACf,OAAO;wBACX;wBACA,OAAO;+BAAI;4BAAM;yBAAQ;oBAC7B;gBACJ;gBAEA,IAAI,QAAQ,MAAM,KAAK,YAAY,CAAC,oBAAoB,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,QAAQ,GAAG;oBACpG,uBAAuB,CAAA,OAAQ;+BACxB;4BACH;gCAAE,UAAU,QAAQ,MAAM;gCAAE,QAAQ,QAAQ,QAAQ;4BAAC;yBACxD;gBACL;YACJ;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC;gBACjC,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ;wBAAC,KAAK,MAAM;wBAAE;qBAAK,GAAG,MAAM;gBACrF,eAAe;YACnB;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,sBAAsB,CAAC;gBACxC,IAAI,KAAK,QAAQ,KAAK,gBAAgB;oBAClC,sBAAsB;gBAC1B;YACJ;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,sBAAsB,CAAC;gBACxC,IAAI,KAAK,QAAQ,KAAK,gBAAgB;oBAClC,sBAAsB;gBAC1B;YACJ;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,qBAAqB,CAAC;gBACvC,uBAAuB,CAAA;oBACnB,MAAM,SAAS,IAAI,IAAI;oBACvB,IAAI,KAAK,WAAW,KAAK,GAAG;wBACxB,OAAO,MAAM,CAAC,KAAK,QAAQ;oBAC/B,OAAO;wBACH,OAAO,GAAG,CAAC,KAAK,QAAQ,EAAE,KAAK,WAAW;oBAC9C;oBACA,OAAO;gBACX;YACJ;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,oBAAoB,CAAC;gBACtC,MAAM,kBAAkB,IAAI;gBAC5B,KAAK,OAAO,CAAC,CAAC;oBACV,gBAAgB,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,WAAW;gBACrD;gBACA,uBAAuB;YAC3B;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,sBAAsB,CAAC;gBACxC,uBAAuB,CAAA;oBACnB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,KAAK,MAAM,GAAG;wBACjD,OAAO;+BAAI;4BAAM;gCAAE,UAAU,KAAK,QAAQ;gCAAE,QAAQ,KAAK,MAAM;4BAAC;yBAAE;oBACtE;oBACA,OAAO;gBACX;YACJ;YAEA,UAAU,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC;gBAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;YAC7B;YAEA,OAAO;gBACH,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;oBAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;wBACpC,QAAQ;wBACR,aAAa;oBACjB;gBACJ;gBAEA,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE,IAAI;gBACvB,UAAU,OAAO,EAAE;YACvB;QACJ;IACJ,GAAG;QAAC;QAAU;QAAe;QAAiB;QAAU;QAAQ;KAAa;IAE7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,uBAAuB;YACzB,IAAI,kBAAkB,UAAU,eAAe;gBAC3C,IAAI;oBACA,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ;oBACnD,MAAM,iBAAiB,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC,SAAc,OAAe,OACrE,UAAU,KAAK,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,QAAQ,EAAE;oBAE5D,mBAAmB;gBACvB,EAAE,OAAM;oBACJ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,mBAAmB,EAAE;gBACzB;YACJ;QACJ;QAEA;QAEA,MAAM,kBAAkB,YAAY;YAChC,IAAI,kBAAkB,UAAU,eAAe;gBAC3C;YACJ;QACJ,GAAG;QAEH,OAAO,IAAM,cAAc;IAC/B,GAAG;QAAC;QAAgB;QAAQ;KAAc;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,oBAAoB;YACtB,IAAI,iBAAiB,QAAQ;gBACzB,IAAI;oBACA,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;oBAChD,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,OAAc;4BAAC,KAAK,MAAM;4BAAE;yBAAK,GAAG,MAAM;oBAC3F,uBAAuB;gBAC3B,EAAE,OAAM;gBACJ,wBAAwB;gBAC5B;YACJ;QACJ;QAEA;QAEA,MAAM,kBAAkB,YAAY,mBAAmB;QAEvD,OAAO,IAAM,cAAc;IAC/B,GAAG;QAAC;QAAe;QAAQ;KAAS;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAChE,GAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,uBAAuB,qBAAqB,KAAK,UAAU;YAC5D,eAAe;QACnB,OAAO;YACH,eAAe,CAAC;QACpB;IACJ,GAAG;QAAC;QAAqB;QAAuB;KAAS;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,qBAAqB;YACvB,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;gBAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;oBACpC,QAAQ;oBACR,aAAa;gBACjB;YACJ;QACJ;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACH,OAAO,mBAAmB,CAAC,gBAAgB;QAC/C;IACJ,GAAG;QAAC;QAAe;QAAQ;KAAe;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,kBAAkB,UAAU,gBAAgB,MAAM,GAAG,GAAG;YACxD,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAA,MAC1C,IAAI,MAAM,KAAK,gBACf,IAAI,SAAS,KAAK,YAClB,CAAC,aAAa,GAAG,CAAC,IAAI,EAAE;YAG5B,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC3B,MAAM,mBAAmB,eAAe,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;gBAEzD,UAAU,OAAO,EAAE,KAAK,sBAAsB;oBAC1C,UAAU;oBACV,aAAa;oBACb,YAAY;gBAChB;YACJ;QACJ;IACJ,GAAG;QAAC;QAAgB;QAAQ;QAAiB;QAAc;QAAU;KAAa;IAElF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,kBAAkB,UAAU,UAAU,OAAO,EAAE;YAC/C,UAAU,OAAO,CAAC,IAAI,CAAC,gBAAgB;gBACnC,QAAQ;gBACR,aAAa;YACjB;QACJ;QAEA,OAAO;YACH,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;gBAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;oBACpC,QAAQ;oBACR,aAAa;gBACjB;YACJ;QACJ;IACJ,GAAG;QAAC;QAAgB;QAAQ;KAAc;IAE1C,MAAM,oBAAoB,OAAO;QAC7B,EAAE,cAAc;QAEhB,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ;YAClD;QACJ;QAEA,MAAM,cAAc,aAAa,IAAI;QACrC,gBAAgB;QAEhB,IAAI;YAEA,IAAI,CAAC,gBAAgB;gBACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACJ;YAEA,MAAM,gBAAgB,aAAa,YAAY,UAAU;YAEzD,MAAM,cAAc;gBAChB,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,YAAY;gBACZ,eAAe;gBACf,mBAAmB;YACvB;YAEA,IAAG,YAAY,WAAU;gBACrB,OAAO,OAAO,CAAC;YACnB;YAEA,UAAU,OAAO,EAAE,KAAK,sBAAsB;YAE9C,IAAI,CAAC,aAAa,eAAe;gBAC7B,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,aAAa,kEAAkE,CAAC;YAClG;QACJ,EAAE,OAAM;YACJ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;QACpB;IACJ;IAEA,MAAM,mBAAmB,OAAO;QAC5B;QAEA,mBAAmB,EAAE;QACrB,gBAAgB,KAAK,QAAQ;QAC7B,gBAAgB,IAAI;QACpB,sBAAsB;QAEtB,MAAM,eAAe,KAAK,MAAM,IAAI,KAAK,QAAQ;QACjD,kBAAkB;QAElB,uBAAuB,CAAA;YACnB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACX;QAEA,IAAI,UAAU;YACV,eAAe;QACnB;QAEA,IAAI,gBAAgB,QAAQ;YACxB,IAAI;gBACA,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ;gBACnD,mBAAmB,QAAQ,EAAE;YACjC,EAAE,OAAM;gBACJ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAChB;QACJ;IACJ;IAEA,MAAM,mBAAmB;QACrB,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;YAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;gBACpC,QAAQ;gBACR,aAAa;YACjB;QACJ;IACJ;IAEA,MAAM,sBAAsB;QACxB;QAEA,eAAe;QACf,IAAI,UAAU;YACV,gBAAgB;YAChB,kBAAkB;QACtB;IACJ;IAEA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,CAAC,YAAoB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY;IAC5D,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,UAAU,IAAI;QAEpB,oBAAoB,OAAO,CAAC,CAAA;YACxB,QAAQ,GAAG,CAAC,YAAY,MAAM,EAAE;gBAC5B,UAAU,YAAY,QAAQ;gBAC9B,UAAU,aAAa,YAAY,UAAU;gBAC7C,QAAQ,YAAY,MAAM;YAC9B;QACJ;QAEA,YAAY,OAAO,CAAC,CAAA;YAChB,IAAI,WAAW,QAAQ,KAAK,YAAY,WAAW,QAAQ,KAAK,UAAU;gBACtE,QAAQ,GAAG,CAAC,WAAW,MAAM,IAAI,WAAW,QAAQ,EAAE;oBAClD,UAAU,WAAW,QAAQ;oBAC7B,UAAU,WAAW,QAAQ;oBAC7B,QAAQ,WAAW,MAAM,IAAI,WAAW,QAAQ;gBACpD;YACJ;QACJ;QAEA,IAAI,yBAAyB,yBAAyB;YAClD,QAAQ,GAAG,CAAC,uBAAuB;gBAC/B,UAAU;gBACV,UAAU,aAAa,YAAY,UAAU;gBAC7C,QAAQ;YACZ;QACJ;QAEA,MAAM,gBAAgB,MAAM,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,CAAC,CAAA;YACtD,MAAM,gBAAgB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAClF,MAAM,mBAAmB,KAAK,QAAQ,KAAK;YAC3C,MAAM,uBAAuB,KAAK,QAAQ,KAAK;YAE/C,OAAO,iBAAiB,oBAAoB;QAChD;QAEA,OAAO;IACX,GAAG;QAAC;QAAqB;QAAa;QAAa;QAAU;QAAU;QAAuB;KAAwB;IAEtH,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,gBAAgB,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;QACjE,OAAO,CAAC;YACJ,MAAM,OAAO,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YACxD,IAAI,MAAM;gBACN,OAAO,cAAc,GAAG,CAAC,KAAK,MAAM;YACxC;YAEA,IAAI,aAAa,2BAA2B,uBAAuB;gBAC/D,OAAO,cAAc,GAAG,CAAC;YAC7B;YAEA,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YACxD,OAAO,CAAC,CAAC;QACb;IACJ,GAAG;QAAC;QAAa;QAAmB;QAAyB;KAAsB;IAEnF,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,kBAAkB,MAAM,CAAC,CAAA;YAC5B,MAAM,gBAAgB,KAAK,MAAM;YACjC,OAAO,oBAAoB,GAAG,CAAC;QACnC,GAAG,MAAM;IACb,GAAG;QAAC;QAAmB;KAAoB;IAE3C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,eAAe,UAAU;YACzB,OAAO,kBAAkB,MAAM,CAAC,CAAA;gBAC5B,MAAM,gBAAgB,KAAK,MAAM;gBACjC,OAAO,oBAAoB,GAAG,CAAC;YACnC;QACJ;QACA,OAAO;IACX,GAAG;QAAC;QAAmB;QAAY;KAAoB;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,yBAAyB,2BAA2B,CAAC,gBAAgB;YACrE,gBAAgB;YAChB,kBAAkB;YAElB,IAAI,UAAU,iBAAiB;gBAC3B,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,uBAC3B,IAAI,CAAC,CAAA,OAAQ,mBAAmB,QAAQ,EAAE,GAC1C,KAAK,CAAC,IAAM,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACjC;QACJ;IACJ,GAAG;QAAC;QAAuB;QAAyB;QAAgB;QAAQ;KAAgB;IAE5F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,uBAAuB,CAAC,kBAAkB,kBAAkB,MAAM,GAAG,KAAK,CAAC,uBAAuB;YAClG,MAAM,YAAY,kBAAkB,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YACnE,IAAI,WAAW;gBACX,gBAAgB,UAAU,QAAQ;gBAClC,kBAAkB,UAAU,MAAM;gBAElC,IAAI,UAAU,MAAM,IAAI,QAAQ;oBAC5B,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,UAAU,MAAM,EAC3C,IAAI,CAAC,CAAA,OAAQ,mBAAmB,QAAQ,EAAE,GAC1C,KAAK,CAAC,IAAM,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACjC;YACJ;QACJ;IACJ,GAAG;QAAC;QAAqB;QAAmB;QAAgB;QAAQ;KAAsB;IAE1F,IAAI,CAAC,iBAAiB;QAClB,qBACI,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAE,WAAU;0CAAsE;;;;;;0CAGnF,8OAAC,kIAAA,CAAA,SAAM;gCACH,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACb;;;;;;;;;;;;;;;;;;;;;;;IAOrB;IAEA,qBACI,8OAAC;QAAI,WAAU;;YACV,6BACG,8OAAC;gBAAM,WAAW,CAAC,wFAAwF,EAAE,WACvG,iCACA,8CACA;;kCACF,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACF,KAAI;wCACJ,KAAI;wCACJ,OAAO,WAAW,MAAM;wCACxB,QAAQ,WAAW,KAAK;wCACxB,WAAU;wCACV,SAAS,IAAM,OAAO,IAAI,CAAC;;;;;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAM,WAAW,OAAO;wCACxB,SAAS;4CACL,UAAU,OAAO,EAAE,KAAK;4CACxB,UAAU,OAAO,EAAE,KAAK,mBAAmB;gDAAE;gDAAQ;4CAAS;4CAE9D,IAAI,iBAAiB,QAAQ;gDACzB,IAAI;oDACA,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;oDAChD,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,OAAc;4DAAC,KAAK,MAAM;4DAAE;yDAAK,GAAG,MAAM;oDAC3F,uBAAuB;gDAC3B,EAAE,OAAM;gDACJ,gCAAgC;gDACpC;4CACJ;wCACJ;wCACA,WAAW,CAAC,qKAAqK,EAAE,WAAW,sBAAsB,qBAC9M;wCACN,OAAM;;0DAEN,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;4CAC1D,CAAC,0BAAY,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;oCAExC,0BACG,8OAAC,kIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM7B,8OAAC;wBAAI,WAAW,GAAG,WAAW,QAAQ,MAAM,YAAY,CAAC;kCACrD,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAW,CAAC,uHAAuH,EAAE,WAAW,YAAY,WAAW;;;;;;8CAC/K,8OAAC,iIAAA,CAAA,QAAK;oCACF,aAAY;oCACZ,WAAW,CAAC,wMAAwM,EAAE,WAAW,mBAAmB,kBAC9O;oCACN,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;kCAK1D,8OAAC;wBAAI,WAAW,GAAG,WAAW,cAAc,aAAa;kCACrD,cAAA,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCACG,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yIAAyI,EACjJ,eAAe,QACT,4DACA,sDACT,CAAC,EAAE,WAAW,wBAAwB,qBAAqB;kDAE5D,cAAA,8OAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW,mBAAmB,IAAI;;8DACzE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;8DACvD,8OAAC;oDAAK,WAAW,WAAW,8BAA8B;8DACrD,WAAW,QAAQ;;;;;;8DAExB,8OAAC;oDAAK,WAAW,CAAC,iHAAiH,EAC/H,eAAe,QACT,wBACA,6BACR;8DACG,kBAAkB,MAAM;;;;;;;;;;;;;;;;;kDAKrC,8OAAC;wCACG,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yIAAyI,EACjJ,eAAe,WACT,4DACA,sDACT,CAAC,EAAE,WAAW,wBAAwB,qBAAqB;kDAE5D,cAAA,8OAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW,mBAAmB,IAAI;;8DACzE,8OAAC;oDAAI,WAAU;;sEACX,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;wDAC9D,mBAAmB,mBAChB,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGvB,8OAAC;oDAAK,WAAW,WAAW,8BAA8B;8DACrD,WAAW,WAAW;;;;;;8DAE3B,8OAAC;oDAAK,WAAW,CAAC,iHAAiH,EAC/H,eAAe,WACT,mBAAmB,IACf,4BACA,8BACJ,mBAAmB,IACf,wCACA,6BACZ;8DACG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzB,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAW,CAAC,UAAU,EAAE,WAAW,cAAc,aAAa;sCAC9D,cAAc,MAAM,GAAG,KAAM,gBAAgB,sCAC1C;;oCACC,gBAAgB,yBAAyB,2BACzC,CAAC,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,wCACnC,8OAAC;wCAEG,WAAW,CAAC,gGAAgG,EAAE,WAAW,QAAQ,MAC5H,oFAAoF,CAAC;wCAC1F,SAAS,IAAM,iBAAiB;gDAC5B,UAAU;gDACV,UAAU,aAAa,YAAY,UAAU;gDAC7C,QAAQ;4CACZ;kDAEA,cAAA,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAW,CAAC,qEAAqE,EAAE,WAAW,cAAc,YAAY,gBAAgB,CAAC;sEAC7I,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAW,CAAC,6CAA6C,EAAE,WAAW,YAAY,UAAU,oBAAoB,CAAC;0EAC5H,wBAAwB,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;sEAG5D,8OAAC;4DAAI,WAAW,CAAC,mHAAmH,EAAE,WAAW,YAAY,UACxJ,cAAc,EAAE,aAAa,2BAA2B,2BAA2B,eAClF;sEACF,cAAA,8OAAC;gEAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW,YAAY,cAC9E,CAAC,EAAE,aAAa,2BAA2B,+BAA+B,eACzE;;;;;;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;sEACX,cAAA,8OAAC;gEAAG,WAAW,CAAC,sDAAsD,EAAE,WAAW,YAAY,YAAY,WAAW,CAAC;0EAClH;;;;;;;;;;;sEAGT,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAW,CAAC,iIAAiI,CAAC;8EAAE;;;;;;8EAGrJ,8OAAC;oEAAI,WAAW,CAAC,iIAAiI,CAAC;;sFAC/I,8OAAC;4EAAI,WAAW,CAAC,qBAAqB,EAAE,aAAa,2BAA2B,iBAAiB,eAAe;;;;;;wEAC/G,aAAa,2BAA2B,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;uCApC/D;;;;;oCA2CZ,cAAc,GAAG,CAAC,CAAC,qBAChB,8OAAC;4CAEG,WAAW,CAAC,gGAAgG,EAAE,WAAW,QAAQ,MAC5H,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GAC5B,wFACA,0EACJ;4CACN,SAAS,IAAM,iBAAiB;sDAEhC,cAAA,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAW,CAAC,qEAAqE,EAAE,WAAW,cAAc,YAAY,CAAC,EAC7H,iBAAiB,KAAK,QAAQ,GACxB,oBACA,+CACR;0EACE,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAW,CAAC,6CAA6C,EAAE,WAAW,YAAY,UAAU,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GACvI,wBACA,+GACA;8EACD,KAAK,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;0EAGlD,8OAAC;gEAAI,WAAW,CAAC,mHAAmH,EAAE,WAAW,YAAY,UACxJ,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GAAG,iBAAiB,eACrD,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,2BAA2B,eAC3D;0EACF,cAAA,8OAAC;oEAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW,YAAY,cAC9E,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,+BAA+B,eAC/D;;;;;;;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;wEAAG,WAAW,CAAC,sDAAsD,EAAE,WAAW,YAAY,YAAY,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GACtI,eACA,wCACA;kFACD,KAAK,QAAQ;;;;;;oEAEjB,oBAAoB,GAAG,CAAC,KAAK,MAAM,mBAChC,8OAAC;wEAAI,WAAU;kFACV,oBAAoB,GAAG,CAAC,KAAK,MAAM;;;;;;;;;;;;0EAIhD,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;wEAAI,WAAW,CAAC,2GAA2G,EACxH,iBAAiB,KAAK,QAAQ,GACxB,2BACA,KAAK,QAAQ,KAAK,YACd,sDACA,qDACZ;kFACG,KAAK,QAAQ,KAAK,YAAY,YAAY;;;;;;kFAE/C,8OAAC;wEAAI,WAAW,CAAC,2GAA2G,EACxH,iBAAiB,KAAK,QAAQ,GACxB,2BACA,aAAa,KAAK,QAAQ,IACtB,yDACA,qDACZ;;0FACE,8OAAC;gFAAI,WAAW,CAAC,qBAAqB,EAAE,aAAa,KAAK,QAAQ,IAAI,iBAAiB,eAAe;;;;;;4EACrG,aAAa,KAAK,QAAQ,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;2CA/DrD,KAAK,MAAM;;;;;;6DAwExB,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;sDACV,eAAe,yBACZ,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;qEAEzB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAGzB,8OAAC;4CAAG,WAAW,CAAC,iCAAiC,EAAE,WAAW,YAAY,aAAa;sDAClF,eAAe,WAAW,uBAAuB;;;;;;sDAEtD,8OAAC;4CAAE,WAAU;sDACR,eAAe,WACV,wDACA,CAAC,uBAAuB,EAAE,aAAa,YAAY,WAAW,WAAW,qCAAqC,CAAC;;;;;;sDAGzH,8OAAC;4CAAE,WAAU;sDACR,eAAe,WACV,mDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWrC,YAAY,6BACT,8OAAC;gBACG,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAItC,8OAAC;gBAAK,WAAU;;kCACZ,8OAAC;wBAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW,QAAQ,OAAO;;4BACpG,YAAY,CAAC,6BACV,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAW,CAAC,2CAA2C,EAAE,WAAW,YAAY,aAAa;gCAAE,SAAS;0CACxI,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,iDAAiD,EAAE,WAAW,YAAY,aAAa;0DACtG,6BACG,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAW,CAAC,qCAAqC,EAAE,WAAW,YAAY,WAAW;8DAChG,aAAa,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;yEAG7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACtB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAW,CAAC,cAAc,EAAE,WAAW,YAAY,WAAW;;;;;;;;;;;;;;;;4CAIhF,8BACG,8OAAC;gDAAI,WAAW,CAAC,gGAAgG,EAAE,WAAW,YAAY,UACrI,CAAC,EAAE,aAAa,gBAAgB,iBAAiB,eAChD;0DACF,cAAA,8OAAC;oDAAI,WAAW,CAAC,aAAa,EAAE,WAAW,gBAAgB,UACtD,CAAC,EAAE,aAAa,gBAAgB,+BAA+B,eAC9D;;;;;;;;;;;;;;;;;kDAIlB,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAW,CAAC,0DAA0D,EAAE,WAAW,cAAc,WAAW;0DAC3G,6BACG,8OAAC;oDAAK,WAAU;8DAAY;;;;;2DAC5B;;;;;;0DAER,8OAAC;gDAAE,WAAW,CAAC,uBAAuB,EAAE,WAAW,YAAY,WAAW;0DACrE,eACG,aAAa,gBAAgB,WAAW,qDACxC;;;;;;;;;;;;;;;;;;;;;;;;kCAMpB,8OAAC;wBAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW,QAAQ,OAAO;kCAC3E,cAAA,8OAAC;4BAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,yBAAyB,uBAAuB;;gCACjF,eACG,gBAAgB,MAAM,GAAG,IACrB,gBAAgB,GAAG,CAAC,CAAC;oCACjB,MAAM,gBAAgB,QAAQ,MAAM,KAAK;oCACzC,MAAM,aAAa,QAAQ,MAAM,IAAI;oCAErC,qBACI,8OAAC;wCAEG,WAAW,CAAC,eAAe,EAAE,gBAAgB,gBAAgB,GAAG,CAAC,EAAE,WAAW,UAAU,SAAS;;4CAEhG,CAAC,+BACE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,mCAAmC,EAAE,WAAW,YAAY,WAAW;0DACvF,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAW,CAAC,qCAAqC,EAAE,WAAW,YAAY,WAAW;8DAChG,WAAW,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;0DAInD,8OAAC;gDAAI,WAAW,GAAG,gBAAgB,eAAe,GAAG,CAAC,EAAE,WAAW,gBAAgB,eAAe;0DAC9F,cAAA,8OAAC;oDACG,WAAW,GAAG,gBACR,wBACA,+CACD,mCAAmC,EAAE,WAAW,QAAQ,OAAO;;sEAEpE,8OAAC;4DAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,YAAY,aAAa;sEAClE,QAAQ,IAAI;;;;;;sEAEjB,8OAAC;4DAAI,WAAW,CAAC,8CAA8C,EAAE,gBAC3D,kBACA,iBACA;;gEACD,WAAW,QAAQ,SAAS;gEAC5B,+BACG,8OAAC;8EACI,aAAa,GAAG,CAAC,QAAQ,EAAE,kBACxB,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;+EAC1E,mCACA,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;+EAC1E,aAAa,8BACb,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;6FAE1E,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAlCxF,QAAQ,EAAE;;;;;gCA2C3B,mBAEA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;yDAIlD,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;;gDAAsC;gDACjB,aAAa,YAAY,WAAW;gDAAW;gDAC5E,cAAc,MAAM,KAAK,mBACtB,8OAAC;oDAAK,WAAU;8DAAa;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,KAAK;;;;;;;;;;;;;;;;;kCAIlB,8OAAC;wBAAK,UAAU;wBAAmB,WAAW,CAAC,oCAAoC,EAAE,WAAW,QAAQ,OAAO;kCAC3G,cAAA,8OAAC;4BAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW,qBAAqB,mBAAmB;;8CAC5F,8OAAC;oCACG,MAAK;oCACL,SAAS;oCACT,WAAW,CAAC,kHAAkH,EAAE,WAAW,sBAAsB,sBAC3J;8CACT;;;;;;gCAIA,iCACG,8OAAC;oCACG,KAAK;oCACL,WAAW,CAAC,cAAc,EAAE,WAAW,6BAA6B,qBAC9D;8CAEN,cAAA,8OAAC,mLAAA,CAAA,UAAW;wCACR,cAAc;wCACd,YAAY,mLAAA,CAAA,aAAU,CAAC,KAAK;wCAC5B,gBAAgB;wCAChB,OAAO,WAAW,SAAS;;;;;;;;;;;8CAIvC,8OAAC,iIAAA,CAAA,QAAK;oCACF,aAAa,eAAe,yBAAyB;oCACrD,WAAW,CAAC,0IAA0I,EAAE,WAAW,sBAAsB,uBACnL;oCACN,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,UAAU,CAAC;oCACX,WAAW;;;;;;8CAEf,8OAAC,kIAAA,CAAA,SAAM;oCACH,MAAK;oCACL,MAAM,WAAW,YAAY;oCAC7B,UAAU,CAAC,aAAa,IAAI,MAAM,CAAC;oCACnC,WAAW,CAAC,6GAA6G,EAAE,WAAW,cAAc,aAC9I;;sDAEN,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAW,GAAG,WAAW,iBAAiB,gBAAgB;;;;;;wCAC/D,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1904, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/chat/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Suspense, useEffect, useState } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport SharedChat from '@/app-components/SharedChat';\r\nimport { isStudentAuthenticated } from '@/lib/utils';\r\n\r\nfunction ChatWithParams() {\r\n    const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n    const [username, setUsername] = useState('');\r\n    const [userId, setUserId] = useState('');\r\n    const searchParams = useSearchParams();\r\n    const selectedUserId = searchParams.get('userId');\r\n    const selectedUserName = searchParams.get('userName');\r\n\r\n    useEffect(() => {\r\n        const studentIsAuthenticated = isStudentAuthenticated();\r\n        setIsAuthenticated(studentIsAuthenticated);\r\n\r\n        if (studentIsAuthenticated) {\r\n            const studentData = localStorage.getItem('student_data');\r\n            if (studentData) {\r\n                try {\r\n                    const parsedData = JSON.parse(studentData);\r\n                    const studentName = `${parsedData.firstName} ${parsedData.lastName}` || parsedData.email.split('@')[0];\r\n                    setUsername(studentName);\r\n                    setUserId(parsedData.id);\r\n                } catch (error) {\r\n                    console.error('Error parsing student data:', error);\r\n                }\r\n            }\r\n        } else {\r\n            const userData = localStorage.getItem('user');\r\n            if (userData) {\r\n                try {\r\n                    const parsedData = JSON.parse(userData);\r\n                    const userName = `${parsedData.firstName} ${parsedData.lastName}` || parsedData.email.split('@')[0];\r\n                    setUsername(userName);\r\n                    setUserId(parsedData.id);\r\n                    setIsAuthenticated(true);\r\n                } catch (error) {\r\n                    console.error('Error parsing user data:', error);\r\n                }\r\n            }\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <SharedChat\r\n            userType=\"student\"\r\n            isAuthenticated={isAuthenticated}\r\n            username={username}\r\n            userId={userId}\r\n            loginPath=\"/\"\r\n            initialSelectedUserId={selectedUserId || undefined}\r\n            initialSelectedUserName={selectedUserName || undefined}\r\n        />\r\n    );\r\n}\r\n\r\nexport default function ChatUI() {\r\n    return (\r\n        <Suspense>\r\n            <ChatWithParams />\r\n        </Suspense>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,SAAS;IACL,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,iBAAiB,aAAa,GAAG,CAAC;IACxC,MAAM,mBAAmB,aAAa,GAAG,CAAC;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,yBAAyB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACpD,mBAAmB;QAEnB,IAAI,wBAAwB;YACxB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACb,IAAI;oBACA,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,MAAM,cAAc,GAAG,WAAW,SAAS,CAAC,CAAC,EAAE,WAAW,QAAQ,EAAE,IAAI,WAAW,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACtG,YAAY;oBACZ,UAAU,WAAW,EAAE;gBAC3B,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,+BAA+B;gBACjD;YACJ;QACJ,OAAO;YACH,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACV,IAAI;oBACA,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,MAAM,WAAW,GAAG,WAAW,SAAS,CAAC,CAAC,EAAE,WAAW,QAAQ,EAAE,IAAI,WAAW,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACnG,YAAY;oBACZ,UAAU,WAAW,EAAE;oBACvB,mBAAmB;gBACvB,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,4BAA4B;gBAC9C;YACJ;QACJ;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC,uIAAA,CAAA,UAAU;QACP,UAAS;QACT,iBAAiB;QACjB,UAAU;QACV,QAAQ;QACR,WAAU;QACV,uBAAuB,kBAAkB;QACzC,yBAAyB,oBAAoB;;;;;;AAGzD;AAEe,SAAS;IACpB,qBACI,8OAAC,qMAAA,CAAA,WAAQ;kBACL,cAAA,8OAAC;;;;;;;;;;AAGb", "debugId": null}}]}