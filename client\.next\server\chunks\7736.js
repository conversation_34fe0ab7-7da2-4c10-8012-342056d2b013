"use strict";exports.id=7736,exports.ids=[7736],exports.modules={6211:(e,t,a)=>{a.d(t,{A0:()=>d,BF:()=>i,Hj:()=>o,XI:()=>s,nA:()=>c,nd:()=>l});var r=a(60687);a(43210);var n=a(4780);function s({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function d({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function l({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},12837:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("indian-rupee",[["path",{d:"M6 3h12",key:"ggurg9"}],["path",{d:"M6 8h12",key:"6g4wlu"}],["path",{d:"m6 13 8.5 8",key:"u1kupk"}],["path",{d:"M6 13h3",key:"wdp6ag"}],["path",{d:"M9 13c6.667 0 6.667-10 0-10",key:"1nkvk2"}]])},16145:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},25334:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34179:(e,t,a)=>{a.d(t,{E:()=>g});var r=a(60687),n=a(43210),s=a(6211),d=a(29523),i=a(41862),o=a(63826),l=a(47033),c=a(14952),u=a(16145),x=a(56090),h=a(93772);function g({columns:e,data:t,fetchData:a,totalItems:g,pageSize:m=10,isLoading:p=!1,onEdit:v,onDelete:f,deletingId:b,hidePagination:y=!1}){let[k,j]=(0,n.useState)(0),w=(0,x.N4)({data:t,columns:e,getCoreRowModel:(0,h.HT)(),getPaginationRowModel:(0,h.kW)(),manualPagination:!0,pageCount:y?1:Math.ceil(g/m),state:{pagination:{pageIndex:y?0:k,pageSize:m}},onPaginationChange:e=>{if(y)return;let t="function"==typeof e?e(w.getState().pagination):e;j(t.pageIndex),w.setState(e=>({...e,pagination:t}))},meta:{onEdit:v,onDelete:f,deletingId:b}});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-siderbar rounded-lg shadow-sm border overflow-hidden",children:p?(0,r.jsx)("div",{className:"flex justify-center items-center p-8",children:(0,r.jsx)(i.A,{className:"h-8 w-8 animate-spin text-primary"})}):0===t.length?(0,r.jsx)("div",{className:"text-center p-8 text-muted-foreground",children:(0,r.jsx)("p",{children:"No data found."})}):(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsxs)(s.XI,{children:[(0,r.jsx)(s.A0,{children:w.getHeaderGroups().map(e=>(0,r.jsx)(s.Hj,{className:"bg-muted/50",children:e.headers.map(e=>(0,r.jsx)(s.nd,{className:"font-semibold",children:e.isPlaceholder?null:(0,x.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)(s.BF,{className:"dark:text-white",children:w.getRowModel().rows.map(e=>(0,r.jsx)(s.Hj,{className:"hover:bg-muted/50 transition-colors",children:e.getVisibleCells().map(e=>(0,r.jsx)(s.nA,{children:(0,x.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]})})}),!y&&!p&&t.length>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between gap-2 mt-4",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",t.length," of ",g," items"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>w.setPageIndex(0),disabled:!w.getCanPreviousPage()||p,className:"border rounded-md p-2",children:(0,r.jsx)(o.A,{className:`h-10 w-10 ${(w.getCanPreviousPage(),"text-slate-800")}`})}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>w.previousPage(),disabled:!w.getCanPreviousPage()||p,className:"border rounded-md p-2",children:(0,r.jsx)(l.A,{className:`h-10 w-10 ${(w.getCanPreviousPage(),"text-slate-800")}`})}),(0,r.jsxs)("span",{className:"text-sm px-2",children:["Page ",w.getState().pagination.pageIndex+1," of"," ",w.getPageCount()]}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>w.nextPage(),disabled:!w.getCanNextPage()||p,className:"border rounded-md p-2",children:(0,r.jsx)(c.A,{className:`h-10 w-10  ${(w.getCanNextPage(),"text-slate-800")}`})}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>w.setPageIndex(w.getPageCount()-1),disabled:!w.getCanNextPage()||p,className:"border rounded-md p-2",children:(0,r.jsx)(u.A,{className:`h-10 w-10 ${(w.getCanNextPage(),"text-slate-800")}`})})]})]})]})}},40228:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41862:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>i,Zp:()=>s,aR:()=>d,wL:()=>c});var r=a(60687);a(43210);var n=a(4780);function s({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},63826:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},70615:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},93508:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("user-check",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},96834:(e,t,a)=>{a.d(t,{E:()=>o});var r=a(60687);a(43210);var n=a(11329),s=a(24224),d=a(4780);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:a=!1,...s}){let o=a?n.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,d.cn)(i({variant:t}),e),...s})}}};