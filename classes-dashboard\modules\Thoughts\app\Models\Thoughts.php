<?php

namespace Thoughts\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Thoughts extends Model
{
    use HasFactory;

    protected $connection = 'main';
    protected $table = 'ClassesThought';
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'classId',
        'thoughts',
        'status',
        'updatedAt',
        'createdAt',
    ];
    public $timestamps = false;
}
