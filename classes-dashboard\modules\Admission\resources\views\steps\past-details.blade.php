<div id="past-details" class="content" role="tabpanel" aria-labelledby="past-details-trigger">
    <form id="past-details-forms">
        @csrf
        <input type="hidden" name="student_id" />
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    {!! Form::label('prev_standard', 'Prev Standard *',['class' => 'form-label']) !!}
                    {!! Form::text('prev_standard', null, ['id' => 'prev_standard','placeholder'=>'Enter Prev Standard','class' => 'form-control']) !!}
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    {!! Form::label('prev_school', 'Prev School *',['class' => 'form-label']) !!}
                    {!! Form::text('prev_school', null, ['id' => 'prev_school','placeholder'=>'Enter Prev School','class' => 'form-control']) !!}
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    {!! Form::label('prev_passing_year', 'Prev Passing Year *',['class' => 'form-label']) !!}
                    {!! Form::text('prev_passing_year', null, ['id' => 'prev_passing_year','placeholder'=>'Enter Prev Passing Year','class' => 'form-control']) !!}
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    {!! Form::label('prev_school_left_date', 'Prev School Left Date *',['class' => 'form-label']) !!}
                    {!! Form::text('prev_school_left_date', null, ['readonly','id' => 'prev_school_left_date','placeholder'=>'Enter Prev School Left Date','class' => 'form-control datepicker']) !!}
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    {!! Form::label('left_reason', 'Left Reason *',['class' => 'form-label']) !!}
                    {!! Form::text('left_reason', null, ['id' => 'left_reason','placeholder'=>'Enter Left Reason','class' => 'form-control']) !!}
                </div>
            </div>
        </div>
    </form>
    <button class="btn btn-secondary" onclick="stepper.previous()">Previous</button>
    <button class="btn btn-primary" id="submit-past-details">Save</button>
    <a href="{{ route('student.show', ':id') }}" class="btn btn-secondary gotourl" onclick="stepper.next()">Go To Details</a>
</div>