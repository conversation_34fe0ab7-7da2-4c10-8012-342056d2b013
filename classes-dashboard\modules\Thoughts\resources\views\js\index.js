var columns = [
    {
        data: "action",
        name: "action",
        orderable: false,
    },
    {
        data: "thoughts",
        name: "thoughts",
    },
    {
        data: "createdAt",
        name: "createdAt",
    },
    {
        data: "status",
        name: "status",
    },
];

var table = commonDatatable(
    "#thoughts_table",
    thoughtsRoute.index,
    columns
);

$(document).on("click", "#addThoughtsEntry", function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = thoughtsRoute.create;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Add New Thoughts");
        $("#createContent").html(result);
    };
    commonAjax(params);
});

$(document).on("click", ".deleteThoughtsEntry", function () {
    var did = $(this).attr("data-deletethoughtsid");
    var url = thoughtsRoute.delete;
    url = url.replace(":did", did);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `DELETE`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        toastr.success(result.success);
        table.draw();
    };
    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).on("click", ".editThoughtsEntry", function () {
    var editid = $(this).attr("data-editthoughtsid");
    var url = thoughtsRoute.edit;
    url = url.replace(":editid", editid);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Edit Event");
        $("#createContent").html(result);
    };
    commonAjax(params);
});
