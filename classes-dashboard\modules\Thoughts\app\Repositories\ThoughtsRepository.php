<?php

namespace Thoughts\Repositories;

use Thoughts\Models\Thoughts;
use Thoughts\Interfaces\ThoughtsInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ThoughtsRepository implements ThoughtsInterface
{
    protected $thoughts;
    function __construct(Thoughts $thoughts)
    {
        $this->thoughts = $thoughts;
    }

    public function getAll($request)
    {
        $thoughts = $this->thoughts::where('classId', Auth::id());
    
        searchColumn($request->input('columns'), $thoughts);
        orderColumn($request, $thoughts, 'ClassesThought.id');

        return $thoughts;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
        ->addColumn('thoughts', function ($data) {
            return $data->thoughts;
        })
        ->addColumn('createdAt', function ($data) {
            return $data->createdAt;
        })
        ->addColumn('status', function ($data) {
            return status_color($data->status);
        })
        ->addColumn('action', function ($data) {
            $button = "";
            if ($data->status != "APPROVED") {
                $button .= '<button data-toggle="modal" data-target="#newThoughtsEntry" type="button" class="editThoughtsEntry btn" title="Edit" data-editthoughtsid="' . $data->id . '"><i class="fa fa-edit"></i></button>';
                $button .= '<button type="button" class="deleteThoughtsEntry btn" title="Delete" data-deletethoughtsid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
            }
            return $button;
        })->rawColumns(['action', 'status'])
        ->make(true);
    }

    public function createThoughts($data)
    {
        $this->thoughts::create([
            'id' => generateUUID(),
            'thoughts' => $data->thoughts,
            'classId' => Auth::id(),
            'createdAt' => now(),
            'updatedAt' => now(),
        ]);
    }

    public function getThoughtsById($id)
    {
        return $this->thoughts::Find($id);
    }

    public function updateThoughts($data, $id)
    {
        $event = $this->getThoughtsById($id);
        $event->thoughts = $data->thoughts;
        $event->save();
    }
}
