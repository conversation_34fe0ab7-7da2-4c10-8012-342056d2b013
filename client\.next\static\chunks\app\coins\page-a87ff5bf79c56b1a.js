(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[523],{7583:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(95155);r(12115);var a=r(6874),n=r.n(a),i=r(66766),l=r(29911);let o=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(n(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:l.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:l.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:l.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:l.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:l.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:l.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:l.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:r,label:a}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(n(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,s.jsx)(r,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(n(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(n(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(95155);r(12115);var a=r(66634),n=r(74466),i=r(59434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:n=!1,...o}=e,c=n?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...o})}},47863:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},53904:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(95155);r(12115);var a=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>d});var s=r(95155);r(12115);var a=r(59434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},66932:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68856:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(95155),a=r(59434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",t),...r})}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(a),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var s,a,n;s=e,a=t,n=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:n,title:o}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,i),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>t(e)):t(a)}},76625:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(95155),a=r(12115),n=r(26126),i=r(30285),l=r(66695),o=r(7632),c=r(55077),d=r(34540),u=r(70347),m=r(7583),h=r(66766),x=r(68856),f=r(47863),p=r(66474),g=r(53904),v=r(66932),b=r(56671),j=r(62523),y=r(59434),w=r(55594),N=r(62177),k=r(90221);let O=w.z.object({amount:w.z.string().min(1,"Amount is required").refine(e=>{let t=parseInt(e,10);return!isNaN(t)&&t>=1&&t<=1e4},{message:"Amount must be between ₹1 and ₹10,000"})}),C=()=>{let[e,t]=(0,a.useState)(0),[r,w]=(0,a.useState)([]),[C,S]=(0,a.useState)(!0),[A,E]=(0,a.useState)("all"),[P,_]=(0,a.useState)("desc"),{user:z}=(0,d.d4)(e=>e.user),[T,D]=(0,a.useState)(!1),{register:F,handleSubmit:R,formState:{errors:I,isSubmitting:L},reset:M}=(0,N.mN)({resolver:(0,k.u)(O)}),U=async e=>{await $(parseInt(e.amount))},$=async e=>{let t=null!==localStorage.getItem("studentToken"),r=(null==z?void 0:z.role)==="STUDENT"||t;D(!0);try{let{order:t}=(await c.S.post(r?"/coins/create-order":"/coins/create-order/class",{amount:100*e})).data,s={key:"rzp_test_Opr6M8CKpK12pF",amount:t.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:t.id,prefill:{name:(null==z?void 0:z.firstName)+" "+(null==z?void 0:z.lastName),email:null==z?void 0:z.email},handler:async function(t){try{await c.S.post(r?"/coins/verify":"/coins/verify/class",{razorpay_order_id:t.razorpay_order_id,razorpay_payment_id:t.razorpay_payment_id,razorpay_signature:t.razorpay_signature,amount:100*e}),b.oR.success("Coins added successfully!"),Z(),M()}catch(e){b.oR.error("Payment verification failed")}},theme:{color:"#f97316"}};new window.Razorpay(s).open()}catch(e){b.oR.error("Payment initialization failed")}finally{D(!1)}};(0,a.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]);let Z=(0,a.useCallback)(async()=>{S(!0);try{let e=null!==localStorage.getItem("studentToken"),r=(null==z?void 0:z.role)==="STUDENT"||e,s=await c.S.get(r?"/coins/get-total-coins/student":"/coins/get-total-coins"),a=await c.S.get(r?"/coins/transaction-history/student":"/coins/transaction-history");t(s.data.coins),w(a.data.transactions)}catch(e){b.oR.error("Failed to load coin data. Please try again."),console.error("Error fetching data",e)}finally{S(!1)}},[null==z?void 0:z.role]);(0,a.useEffect)(()=>{Z()},[null==z?void 0:z.id,Z]),(0,a.useEffect)(()=>{let e=()=>{Z()};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[Z]);let W=r.filter(e=>"all"===A||e.type.toLowerCase()===A).sort((e,t)=>{let r=new Date(e.createdAt).getTime(),s=new Date(t.createdAt).getTime();return"desc"===P?s-r:r-s}),q=e=>{let{txn:t}=e,[r,n]=(0,a.useState)(!1);return(0,s.jsxs)(l.Zp,{className:"p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200",onClick:()=>n(!r),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h3",{className:"text-base font-semibold text-foreground",children:"CREDIT"===t.type?(0,s.jsx)("span",{className:"text-green-500",children:"Credit"}):(0,s.jsx)("span",{className:"text-red-500",children:"Debit"})}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[t.amount," Coins • ",(0,o.GP)(new Date(t.createdAt),"MMM dd, yyyy")]})]}),r?(0,s.jsx)(f.A,{className:"h-5 w-5 text-muted-foreground"}):(0,s.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"})]}),r&&(0,s.jsxs)("div",{className:"mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Reason:"})," ",t.reason]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Time:"})," ",(0,o.GP)(new Date(t.createdAt),"p")]})]})]})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.default,{}),(0,s.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8 py-12 max-w-7xl mx-auto space-y-6",children:[(0,s.jsx)("div",{className:"sticky top-16 z-10 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60 py-4",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 rounded-full bg-orange-100 p-2",children:(0,s.jsx)(h.default,{src:"/uest_coin.png",alt:"Coin Icon",fill:!0,className:"object-contain"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:[localStorage.getItem("studentToken")?"Student":"Class"," Uest Coin Balance"]}),(0,s.jsxs)(n.E,{variant:"outline",className:"text-customOrange text-xl font-semibold border-customOrange mt-1",children:[e," Coins"]})]})]}),(0,s.jsxs)(i.$,{variant:"outline",onClick:Z,disabled:C,className:"flex gap-2 w-full sm:w-auto",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 ".concat(C?"animate-spin":"")}),"Refresh"]})]})}),(0,s.jsx)("form",{onSubmit:R(U),children:(0,s.jsxs)(l.Zp,{className:"p-6 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-foreground mb-4",children:"Add Uest Coins"}),(0,s.jsx)("div",{className:"grid gap-4 sm:grid-cols-2",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.p,{type:"number",placeholder:"Enter amount (₹1 - ₹10,000)",className:"border-customOrange/30 focus:border-customOrange pr-10",disabled:T||L,...F("amount")}),(0,s.jsx)("span",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm",children:"₹"}),I.amount&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I.amount.message})]}),(0,s.jsxs)(i.$,{type:"submit",disabled:T||L,className:(0,y.cn)("w-full bg-customOrange hover:bg-orange-600 text-white",(T||L)&&"opacity-75 cursor-not-allowed"),children:[(T||L)&&(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin mr-2"}),T||L?"Processing...":"Add Coins"]})]})})]})}),C?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(x.E,{className:"h-20 w-full rounded-lg"}),[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(x.E,{className:"h-16 w-full rounded-lg"},t))]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-center",children:[(0,s.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,s.jsx)(i.$,{variant:"all"===A?"default":"outline",size:"sm",onClick:()=>E("all"),className:"all"===A?"bg-customOrange hover:bg-orange-600":"",children:"All"}),(0,s.jsx)(i.$,{variant:"credit"===A?"default":"outline",size:"sm",onClick:()=>E("credit"),className:"credit"===A?"bg-customOrange hover:bg-orange-600":"",children:"Credit"}),(0,s.jsx)(i.$,{variant:"debit"===A?"default":"outline",size:"sm",onClick:()=>E("debit"),className:"debit"===A?"bg-customOrange hover:bg-orange-600":"",children:"Debit"})]}),(0,s.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>_("desc"===P?"asc":"desc"),className:"flex gap-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"Sort by Date ","desc"===P?"↓":"↑"]})]}),W.length>0?(0,s.jsx)("div",{className:"grid gap-4",children:W.map(e=>(0,s.jsx)(q,{txn:e},e.id))}):(0,s.jsxs)(l.Zp,{className:"p-8 bg-white rounded-lg shadow-sm border text-center",children:[(0,s.jsx)("div",{className:"relative w-24 h-24 mx-auto mb-4 opacity-50",children:(0,s.jsx)(h.default,{src:"/uest_coin.png",alt:"No Transactions",fill:!0,className:"object-contain"})}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"No transactions found. Start earning or spending coins!"}),(0,s.jsx)(i.$,{variant:"outline",className:"bg-orange-50 text-customOrange hover:bg-orange-100",onClick:Z,children:"Refresh Data"})]})]})]}),(0,s.jsx)(m.default,{})]})}},92659:(e,t,r)=>{Promise.resolve().then(r.bind(r,76625))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,818,5623,1342,7632,347,8441,1684,7358],()=>t(92659)),_N_E=e.O()}]);