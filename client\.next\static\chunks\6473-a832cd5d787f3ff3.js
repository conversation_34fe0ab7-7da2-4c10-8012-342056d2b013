"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6473],{26473:(e,t,r)=>{r.d(t,{A:()=>C});var s=r(95155),a=r(12115),n=r(91394),o=r(30285),l=r(62523),i=r(53904),c=r(54416),d=r(47924),u=r(17580),m=r(81497),h=r(35169),x=r(90437),g=r(5196),f=r(12486),p=r(14298),b=r(7632),v=r(56671),w=r(66766),y=r(40416),j=r(55077);let N=async(e,t)=>{try{return(await j.S.get("chat/messages/private?userId1=".concat(e,"&userId2=").concat(t),{withCredentials:!0})).data}catch(e){console.error("Error fetching private messages:",e)}},I=async(e,t)=>{try{return(await j.S.get("chat/messages/users?userId=".concat(e,"&userType=").concat(t),{withCredentials:!0})).data}catch(e){console.error("Error fetching message users:",e)}};var k=r(35695),S=r(44996);function C(e){let{userType:t,isAuthenticated:r,username:j,userId:C,initialSelectedUser:A,initialSelectedUserId:E,initialSelectedUserName:U}=e,[R,M]=(0,a.useState)([]),[T,L]=(0,a.useState)(""),[z,P]=(0,a.useState)(!!j),[_,D]=(0,a.useState)([]),[O,q]=(0,a.useState)([]),[F,$]=(0,a.useState)(""),[V,B]=(0,a.useState)(A||null),[W,Y]=(0,a.useState)(null),[G,J]=(0,a.useState)(null),[X,H]=(0,a.useState)(!0),[Q,Z]=(0,a.useState)(!1),[K,ee]=(0,a.useState)(new Set),[et,er]=(0,a.useState)(!1),[es,ea]=(0,a.useState)(new Map),[en,eo]=(0,a.useState)("all"),el=(0,k.useRouter)(),ei=(0,a.useRef)(null),ec=(0,a.useRef)(null),ed=(0,a.useRef)(null),eu=(0,y.a)();(0,a.useEffect)(()=>{P(!!j)},[j]),(0,a.useEffect)(()=>{let e=e=>{ed.current&&!ed.current.contains(e.target)&&Z(!1)};return Q&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[Q]),(0,a.useEffect)(()=>{if(r&&z&&j)return ec.current&&ec.current.disconnect(),ec.current=(0,p.io)("https://www.uest.in",{withCredentials:!0,path:"/uapi/socket.io"}),ec.current.on("connect",()=>{var e,r,s;null===(e=ec.current)||void 0===e||e.emit("join",{username:j,userType:t,userId:C}),null===(r=ec.current)||void 0===r||r.emit("getOnlineUsers"),null===(s=ec.current)||void 0===s||s.emit("getUnreadCounts",{userId:C,userType:t})}),ec.current.on("connect_error",e=>{v.oR.error("Connection error: ".concat(e.message))}),ec.current.on("roomJoined",e=>{J(e.roomId)}),ec.current.on("roomLeft",()=>{J(null)}),ec.current.on("messagesMarkedAsSeen",e=>{e.byUserId===W&&ee(t=>{let r=new Set(t);return e.messageIds.forEach(e=>{r.add(e)}),r})}),ec.current.on("privateMessage",e=>{V&&(e.sender===j&&e.recipient===V||e.sender===V&&e.recipient===j)&&M(t=>t.some(t=>t.id===e.id)?t:[...t,e]),e.sender===j||O.some(t=>t.userId===e.senderId)||q(t=>[...t,{username:e.sender,userId:e.senderId}])}),ec.current.on("onlineUsers",e=>{D(Array.from(new Map(e.map(e=>[e.userId,e])).values()))}),ec.current.on("userStartedViewing",e=>{e.viewerId===W&&er(!0)}),ec.current.on("userStoppedViewing",e=>{e.viewerId===W&&er(!1)}),ec.current.on("unreadCountUpdate",e=>{ea(t=>{let r=new Map(t);return 0===e.unreadCount?r.delete(e.senderId):r.set(e.senderId,e.unreadCount),r})}),ec.current.on("unreadCountsData",e=>{let t=new Map;e.forEach(e=>{t.set(e.userId,e.unreadCount)}),ea(t)}),ec.current.on("updateMessageUsers",e=>{q(t=>t.some(t=>t.userId===e.userId)?t:[...t,{username:e.username,userId:e.userId}])}),ec.current.on("error",e=>{v.oR.error(e.message)}),()=>{var e,t,r,s,a,n,o,l,i,c,d,u,m,h;G&&C&&W&&ec.current&&ec.current.emit("leaveChatRoom",{userId:C,recipientId:W}),null===(e=ec.current)||void 0===e||e.off("connect"),null===(t=ec.current)||void 0===t||t.off("connect_error"),null===(r=ec.current)||void 0===r||r.off("privateMessage"),null===(s=ec.current)||void 0===s||s.off("onlineUsers"),null===(a=ec.current)||void 0===a||a.off("error"),null===(n=ec.current)||void 0===n||n.off("roomJoined"),null===(o=ec.current)||void 0===o||o.off("roomLeft"),null===(l=ec.current)||void 0===l||l.off("messagesMarkedAsSeen"),null===(i=ec.current)||void 0===i||i.off("userStartedViewing"),null===(c=ec.current)||void 0===c||c.off("userStoppedViewing"),null===(d=ec.current)||void 0===d||d.off("unreadCountUpdate"),null===(u=ec.current)||void 0===u||u.off("unreadCountsData"),null===(m=ec.current)||void 0===m||m.off("updateMessageUsers"),null===(h=ec.current)||void 0===h||h.disconnect()}},[j,z,r,t,C,V]),(0,a.useEffect)(()=>{let e=async()=>{if(W&&C&&z)try{let e=(await N(C,W)||[]).filter((e,t,r)=>t===r.findIndex(t=>t.id===e.id));M(e)}catch(e){v.oR.error("Failed to load messages. Please try again."),M([])}};e();let t=setInterval(()=>{W&&C&&z&&e()},6e4);return()=>clearInterval(t)},[W,C,z]),(0,a.useEffect)(()=>{let e=async()=>{if(z&&C)try{let e=await I(C,t),r=Array.from(new Map(e.map(e=>[e.userId,e])).values());q(r)}catch(e){}};e();let r=setInterval(e,3e4);return()=>clearInterval(r)},[z,C,t]),(0,a.useEffect)(()=>{var e;null===(e=ei.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[R]),(0,a.useEffect)(()=>{(A||E)&&eu?H(!1):H(!eu)},[A,E,eu]),(0,a.useEffect)(()=>{let e=()=>{G&&C&&W&&ec.current&&ec.current.emit("leaveChatRoom",{userId:C,recipientId:W})};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[G,C,W]),(0,a.useEffect)(()=>{if(W&&C&&R.length>0){let t=R.filter(e=>e.sender===V&&e.recipient===j&&!K.has(e.id));if(t.length>0){var e;let r=t.map(e=>e.id);null===(e=ec.current)||void 0===e||e.emit("markMessagesAsSeen",{senderId:W,recipientId:C,messageIds:r})}}},[W,C,R,V,j,K]),(0,a.useEffect)(()=>(W&&C&&ec.current&&ec.current.emit("joinChatRoom",{userId:C,recipientId:W}),()=>{G&&C&&W&&ec.current&&ec.current.emit("leaveChatRoom",{userId:C,recipientId:W})}),[W,C,G]);let em=async e=>{if(e.preventDefault(),!T.trim()||!V||!C)return;let r=T.trim();L("");try{var s;if(!W){v.oR.error("No recipient selected. Please select a user first."),L(r);return}let e="student"===t?"class":"student";"student"==t&&el.replace("/student/chat"),null===(s=ec.current)||void 0===s||s.emit("sendPrivateMessage",{text:r,senderId:C,recipientId:W,senderType:t,recipientType:e,recipientUsername:V}),ep(V)||v.oR.info("".concat(V," is offline. Your message will be delivered when they come online."))}catch(e){v.oR.error("Failed to send message. Please try again."),L(r)}},eh=async e=>{ex(),M([]),B(e.username),ee(new Set),er(!1);let t=e.userId||e.username;if(Y(t),ea(e=>{let r=new Map(e);return r.delete(t),r}),eu&&H(!1),t&&C)try{let e=await N(C,t);M(e||[])}catch(e){v.oR.error("Failed to load conversation history.")}},ex=()=>{G&&C&&W&&ec.current&&ec.current.emit("leaveChatRoom",{userId:C,recipientId:W})},eg=(0,a.useMemo)(()=>e=>(0,b.GP)(new Date(e),"h:mm a"),[]),ef=(0,a.useMemo)(()=>{let e=new Map;return O.forEach(r=>{e.set(r.userId,{username:r.username,userType:"student"===t?"class":"student",userId:r.userId})}),_.forEach(r=>{r.username!==j&&r.userType!==t&&e.set(r.userId||r.username,{username:r.username,userType:r.userType,userId:r.userId||r.username})}),E&&U&&e.set(E,{username:U,userType:"student"===t?"class":"student",userId:E}),Array.from(e.values()).filter(e=>{let r=e.username.toLowerCase().includes(F.toLowerCase()),s=e.username!==j,a=e.userType!==t;return r&&s&&a})},[O,_,F,j,t,E,U]),ep=(0,a.useMemo)(()=>{let e=new Set(_.map(e=>e.userId));return t=>{let r=ef.find(e=>e.username===t);return r?e.has(r.userId):t===U&&E?e.has(E):!!_.find(e=>e.username===t)}},[_,ef,U,E]),eb=(0,a.useMemo)(()=>ef.filter(e=>{let t=e.userId;return es.has(t)}).length,[ef,es]),ev=(0,a.useMemo)(()=>"unread"===en?ef.filter(e=>{let t=e.userId;return es.has(t)}):ef,[ef,en,es]);return((0,a.useEffect)(()=>{E&&U&&!W&&(B(U),Y(E),C&&r&&N(C,E).then(e=>M(e||[])).catch(()=>v.oR.error("Failed to load conversation history.")))},[E,U,W,C,r]),(0,a.useEffect)(()=>{if(A&&!W&&ef.length>0&&!E){let e=ef.find(e=>e.username===A);e&&(B(e.username),Y(e.userId),e.userId&&C&&N(C,e.userId).then(e=>M(e||[])).catch(()=>v.oR.error("Failed to load conversation history.")))}},[A,ef,W,C,E]),r)?(0,s.jsxs)("div",{className:"flex h-[calc(100vh-64px)] bg-background text-foreground relative overflow-hidden",children:[X&&(0,s.jsxs)("aside",{className:"border-r border-gray-200 flex flex-col bg-gradient-to-b from-white to-gray-50 shadow-lg ".concat(eu?"absolute inset-0 z-50 w-full":"relative w-80 min-w-80 lg:w-96 lg:min-w-96"),children:[(0,s.jsxs)("div",{className:"p-4 flex items-center justify-between border-b border-gray-200 bg-white/80 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(w.default,{src:"/logo.png",alt:"Uest Logo",width:eu?100:140,height:eu?25:35,className:"object-contain cursor-pointer hover:opacity-80 transition-all duration-300 hover:scale-105",onClick:()=>el.push("/")})})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(o.$,{variant:"outline",size:eu?"sm":"default",onClick:async()=>{var e,r;if(null===(e=ec.current)||void 0===e||e.emit("getOnlineUsers"),null===(r=ec.current)||void 0===r||r.emit("getUnreadCounts",{userId:C,userType:t}),z&&C)try{let e=await I(C,t),r=Array.from(new Map(e.map(e=>[e.userId,e])).values());q(r)}catch(e){}},className:"bg-white/90 border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md ".concat(eu?"px-3 py-2 text-xs":"px-4 py-2 text-sm"),title:"Refresh chat list",children:[(0,s.jsx)(i.A,{className:"".concat("h-4 w-4")}),!eu&&(0,s.jsx)("span",{className:"ml-2",children:"Refresh"})]}),eu&&(0,s.jsx)(o.$,{variant:"ghost",size:"icon",className:"rounded-xl h-10 w-10 hover:bg-gray-100 transition-all duration-300",onClick:()=>H(!1),children:(0,s.jsx)(c.A,{className:"h-5 w-5"})})]})]}),(0,s.jsx)("div",{className:"".concat(eu?"p-3":"p-4"," bg-white/50"),children:(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(d.A,{className:"absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 group-focus-within:text-gray-600 transition-colors duration-300 ".concat(eu?"h-4 w-4":"h-5 w-5")}),(0,s.jsx)(l.p,{placeholder:"Search conversations...",className:"pl-12 pr-4 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-2 focus:ring-gray-100 transition-all duration-300 shadow-sm hover:shadow-md ".concat(eu?"py-2.5 text-sm":"py-3 text-base"),value:F,onChange:e=>$(e.target.value)})]})}),(0,s.jsx)("div",{className:"".concat(eu?"px-3 pb-3":"px-4 pb-4"),children:(0,s.jsx)("div",{className:"bg-gray-100/80 rounded-2xl p-1.5 shadow-inner",children:(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)("button",{onClick:()=>eo("all"),className:"relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ".concat("all"===en?"bg-white text-gray-900 shadow-lg border border-gray-200":"text-gray-600 hover:text-gray-900 hover:bg-white/50"," ").concat(eu?"px-3 py-2.5 text-xs":"px-4 py-3 text-sm"),children:(0,s.jsxs)("div",{className:"flex items-center gap-2 ".concat(eu?"flex-col gap-1":""),children:[(0,s.jsx)(u.A,{className:"".concat(eu?"h-3 w-3":"h-4 w-4")}),(0,s.jsx)("span",{className:eu?"text-[10px] leading-tight":"",children:eu?"All":"All Users"}),(0,s.jsx)("span",{className:"inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ".concat("all"===en?"bg-black text-white":"bg-gray-200 text-gray-600"),children:ef.length})]})}),(0,s.jsx)("button",{onClick:()=>eo("unread"),className:"relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ".concat("unread"===en?"bg-white text-gray-900 shadow-lg border border-gray-200":"text-gray-600 hover:text-gray-900 hover:bg-white/50"," ").concat(eu?"px-3 py-2.5 text-xs":"px-4 py-3 text-sm"),children:(0,s.jsxs)("div",{className:"flex items-center gap-2 ".concat(eu?"flex-col gap-1":""),children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.A,{className:"".concat(eu?"h-3 w-3":"h-4 w-4")}),eb>0&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-[8px] rounded-full h-2 w-2 animate-pulse"})]}),(0,s.jsx)("span",{className:eu?"text-[10px] leading-tight":"",children:eu?"Unread":"Unread Only"}),(0,s.jsx)("span",{className:"inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ".concat("unread"===en?eb>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-600":eb>0?"bg-red-500 text-white animate-pulse":"bg-gray-200 text-gray-600"),children:eb})]})})]})})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto overscroll-contain",children:(0,s.jsx)("div",{className:"space-y-2 ".concat(eu?"px-2 pb-2":"px-3 pb-3"),children:ev.length>0||V&&E?(0,s.jsxs)(s.Fragment,{children:[V&&E&&U&&!ev.find(e=>e.userId===E)&&(0,s.jsx)("div",{className:"group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ".concat(eu?"p-3":"p-4"," bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700"),onClick:()=>eh({username:U,userType:"student"===t?"class":"student",userId:E}),children:(0,s.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.eu,{className:"border-2 shadow-md transition-all duration-300 group-hover:scale-110 ".concat(eu?"h-10 w-10":"h-12 w-12"," border-white/50"),children:(0,s.jsx)(n.q5,{className:"font-semibold transition-colors duration-300 ".concat(eu?"text-xs":"text-sm"," bg-white text-black"),children:U.substring(0,2).toUpperCase()})}),(0,s.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ".concat(eu?"h-4 w-4":"h-5 w-5"," border-white ").concat(ep(U)?"bg-green-500 shadow-lg":"bg-gray-400"),children:(0,s.jsx)("div",{className:"rounded-full transition-all duration-300 ".concat(eu?"h-2 w-2":"h-2.5 w-2.5"," ").concat(ep(U)?"bg-green-300 animate-pulse":"bg-gray-300")})})]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsx)("h3",{className:"font-semibold truncate transition-colors duration-300 ".concat(eu?"text-sm":"text-base"," text-white"),children:U})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)("div",{className:"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white",children:"Tutor"}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(ep(U)?"bg-green-500":"bg-gray-400")}),ep(U)?"Online":"Offline"]})]})]})]})},E),ev.map(e=>(0,s.jsx)("div",{className:"group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ".concat(eu?"p-3":"p-4"," ").concat(V===e.username?"bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700":"bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300"),onClick:()=>eh(e),children:(0,s.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.eu,{className:"border-2 shadow-md transition-all duration-300 group-hover:scale-110 ".concat(eu?"h-10 w-10":"h-12 w-12"," ").concat(V===e.username?"border-white/50":"border-gray-300 group-hover:border-gray-400"),children:(0,s.jsx)(n.q5,{className:"font-semibold transition-colors duration-300 ".concat(eu?"text-xs":"text-sm"," ").concat(V===e.username?"bg-white text-black":"bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 group-hover:from-gray-200 group-hover:to-gray-300"),children:e.username.substring(0,2).toUpperCase()})}),(0,s.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ".concat(eu?"h-4 w-4":"h-5 w-5"," ").concat((e.username,"border-white")," ").concat(ep(e.username)?"bg-green-500 shadow-lg":"bg-gray-400"),children:(0,s.jsx)("div",{className:"rounded-full transition-all duration-300 ".concat(eu?"h-2 w-2":"h-2.5 w-2.5"," ").concat(ep(e.username)?"bg-green-300 animate-pulse":"bg-gray-300")})})]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"font-semibold truncate transition-colors duration-300 ".concat(eu?"text-sm":"text-base"," ").concat(V===e.username?"text-white":"text-gray-900 group-hover:text-black"),children:e.username}),es.has(e.userId)&&(0,s.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse",children:es.get(e.userId)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)("div",{className:"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ".concat(V===e.username?"bg-white/20 text-white":(e.userType,"bg-gray-100 text-gray-700 group-hover:bg-gray-200")),children:"student"===e.userType?"Student":"Tutor"}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ".concat(V===e.username?"bg-white/20 text-white":ep(e.username)?"bg-green-100 text-green-700 group-hover:bg-green-200":"bg-gray-100 text-gray-600 group-hover:bg-gray-200"),children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(ep(e.username)?"bg-green-500":"bg-gray-400")}),ep(e.username)?"Online":"Offline"]})]})]})]})},e.userId))]}):(0,s.jsx)("div",{className:"p-6 text-center",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:"unread"===en?(0,s.jsx)(m.A,{className:"h-8 w-8 text-gray-400"}):(0,s.jsx)(u.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 ".concat(eu?"text-sm":"text-base"),children:"unread"===en?"No unread messages":"No users found"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"unread"===en?"All messages have been read or no conversations yet":"You can only chat with ".concat("student"===t?"tutors":"students"," who have exchanged messages with you")}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"unread"===en?'Switch to "All Users" to see all conversations':"Users will appear here when you exchange messages with them"})]})})})})]}),eu&&X&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20 z-40",onClick:()=>H(!1)}),(0,s.jsxs)("main",{className:"flex-1 flex flex-col min-w-0 bg-white",children:[(0,s.jsxs)("div",{className:"border-b-2 border-gray-200 flex items-center gap-3 bg-white ".concat(eu?"p-3":"p-4"),children:[eu&&!X&&(0,s.jsx)(o.$,{variant:"ghost",size:"icon",className:"flex-shrink-0 rounded-xl hover:bg-gray-100 ".concat(eu?"h-8 w-8":"h-10 w-10"),onClick:()=>{ex(),H(!0),eu&&(B(null),Y(null))},children:(0,s.jsx)(h.A,{className:"".concat(eu?"h-4 w-4":"h-5 w-5")})}),(0,s.jsxs)("div",{className:"flex gap-3 items-center min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.eu,{className:"border-2 border-gray-300 flex-shrink-0 shadow-md ".concat(eu?"h-9 w-9":"h-12 w-12"),children:V?(0,s.jsx)(n.q5,{className:"font-semibold bg-gray-100 text-black ".concat(eu?"text-xs":"text-sm"),children:V.substring(0,2).toUpperCase()}):(0,s.jsx)(n.q5,{className:"bg-gray-100",children:(0,s.jsx)(u.A,{className:"text-gray-500 ".concat(eu?"h-4 w-4":"h-6 w-6")})})}),V&&(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 rounded-full border-2 border-white flex items-center justify-center ".concat(eu?"h-3 w-3":"h-4 w-4"," ").concat(ep(V)?"bg-green-500":"bg-gray-400"),children:(0,s.jsx)("div",{className:"rounded-full ".concat(eu?"h-1.5 w-1.5":"h-2 w-2"," ").concat(ep(V)?"bg-green-400 animate-pulse":"bg-gray-300")})})]}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"font-semibold flex items-center gap-2 truncate text-black ".concat(eu?"text-base":"text-lg"),children:V?(0,s.jsx)("span",{className:"truncate",children:V}):"Select a user"}),(0,s.jsx)("p",{className:"text-gray-600 truncate ".concat(eu?"text-xs":"text-sm"),children:V?ep(V)?"Online":"Offline (messages will be delivered when online)":"Choose someone to chat with"})]})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 ".concat(eu?"p-3":"p-4"),children:(0,s.jsxs)("div",{className:"mx-auto ".concat(eu?"space-y-3 max-w-full":"space-y-4 max-w-4xl"),children:[V?R.length>0?R.map(e=>{let t=e.sender===j,r=e.sender||"Unknown";return(0,s.jsxs)("div",{className:"flex items-end ".concat(t?"justify-end":""," ").concat(eu?"gap-2":"gap-3"),children:[!t&&(0,s.jsx)(n.eu,{className:"border-2 border-gray-300 shadow-sm ".concat(eu?"h-6 w-6":"h-8 w-8"),children:(0,s.jsx)(n.q5,{className:"bg-gray-200 text-black font-semibold ".concat("text-xs"),children:r.substring(0,2).toUpperCase()})}),(0,s.jsx)("div",{className:"".concat(t?"text-right":""," ").concat(eu?"max-w-[80%]":"max-w-[70%]"),children:(0,s.jsxs)("div",{className:"".concat(t?"bg-black text-white":"bg-white text-black border-2 border-gray-200"," rounded-2xl shadow-lg break-words ").concat(eu?"p-3":"p-4"),children:[(0,s.jsx)("div",{className:"leading-relaxed ".concat(eu?"text-sm":"text-base"),children:e.text}),(0,s.jsxs)("div",{className:"text-xs mt-2 flex items-end justify-end gap-1 ".concat(t?"text-gray-300":"text-gray-500"),children:[eg(e.timestamp),t&&(0,s.jsx)("span",{children:K.has(e.id)?(0,s.jsx)(x.A,{className:"".concat(eu?"h-3 w-3":"h-4 w-4"," text-blue-500")}):et?(0,s.jsx)(x.A,{className:"".concat(eu?"h-3 w-3":"h-4 w-4"," text-blue-500")}):ep(V)?(0,s.jsx)(x.A,{className:"".concat(eu?"h-3 w-3":"h-4 w-4"," text-gray-400")}):(0,s.jsx)(g.A,{className:"".concat(eu?"h-3 w-3":"h-4 w-4"," text-gray-400")})})]})]})})]},e.id)}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 text-center",children:[(0,s.jsx)(m.A,{className:"text-gray-400 mb-4 h-16 w-16"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg font-medium",children:"No messages yet"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Send a message to start the conversation"})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 text-center",children:[(0,s.jsx)(m.A,{className:"text-gray-400 mb-4 h-16 w-16"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg font-medium",children:"Select a user to start chatting"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Choose a user from the sidebar to start a private conversation"}),(0,s.jsxs)("p",{className:"text-gray-500 text-sm mt-4 max-w-md",children:["Note: You can only chat with ","student"===t?"tutors":"students"," who have exchanged messages with you.",0===ev.length&&(0,s.jsx)("span",{className:"block mt-2",children:"There are currently no users to chat with. When you exchange messages with someone, they will appear in the sidebar."})]})]}),(0,s.jsx)("div",{ref:ei})]})}),(0,s.jsx)("form",{onSubmit:em,className:"border-t-2 border-gray-200 bg-white ".concat(eu?"p-3":"p-4"),children:(0,s.jsxs)("div",{className:"flex items-center mx-auto ".concat(eu?"gap-2 max-w-full":"gap-3 max-w-4xl"),children:[(0,s.jsx)("button",{type:"button",onClick:e=>{e.preventDefault(),Z(e=>!e)},className:"bg-white border-2 border-gray-200 text-black hover:bg-gray-100 rounded-xl font-medium transition-all duration-300 ".concat(eu?"text-lg px-2 py-1":"text-2xl px-3 py-1"),children:"\uD83D\uDE0A"}),Q&&(0,s.jsx)("div",{ref:ed,className:"absolute z-10 ".concat(eu?"bottom-12 left-4 right-4":"bottom-12 left-96"),children:(0,s.jsx)(S.Ay,{onEmojiClick:e=>{L(t=>t+e.emoji)},emojiStyle:S.Ai.APPLE,searchDisabled:!0,width:eu?"100%":void 0})}),(0,s.jsx)(l.p,{placeholder:V?"Type your message...":"Select a user to start chatting",className:"flex-1 bg-gray-50 border-2 border-gray-200 text-black placeholder:text-gray-500 focus:border-black rounded-xl transition-all duration-300 ".concat(eu?"px-3 py-2 text-sm":"px-4 py-3 text-base"),value:T,onChange:e=>L(e.target.value),disabled:!V,maxLength:250}),(0,s.jsxs)(o.$,{type:"submit",size:eu?"default":"lg",disabled:!T.trim()||!V,className:"bg-black text-white hover:bg-gray-800 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 ".concat(eu?"px-4 py-2":"px-6 py-3"),children:[(0,s.jsx)(f.A,{className:"".concat(eu?"h-4 w-4 mr-1":"h-5 w-5 mr-2")}),"Send"]})]})})]})]}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[calc(100vh-64px)] bg-background p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-4 sm:p-6 bg-card rounded-lg shadow-lg",children:[(0,s.jsx)("h2",{className:"text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center",children:"Login Required"}),(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center text-center",children:[(0,s.jsx)("p",{className:"text-center text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"Please login as a student to access the chat feature."}),(0,s.jsx)(o.$,{onClick:()=>el.push("/"),className:"w-full bg-orange-500 hover:bg-orange-600",children:"Go to Login"})]})]})})}},30285:(e,t,r)=>{r.d(t,{$:()=>i,r:()=>l});var s=r(95155);r(12115);var a=r(66634),n=r(74466),o=r(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:r,size:n,asChild:i=!1,...c}=e,d=i?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:r,size:n,className:t})),...c})}},40416:(e,t,r)=>{r.d(t,{a:()=>a});var s=r(12115);function a(){let[e,t]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},55077:(e,t,r)=>{r.d(t,{S:()=>o});var s=r(23464),a=r(56671);let n=r(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let o=s.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});o.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":n;let r=localStorage.getItem("studentToken");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(a.oR.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,r)=>{r.d(t,{MB:()=>l,ZO:()=>o,cn:()=>n,xh:()=>i});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}let o=()=>localStorage.getItem("studentToken"),l=()=>{localStorage.removeItem("studentToken")},i=()=>!!o()},62523:(e,t,r)=>{r.d(t,{p:()=>n});var s=r(95155);r(12115);var a=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},91394:(e,t,r)=>{r.d(t,{eu:()=>o,q5:()=>l});var s=r(95155);r(12115);var a=r(87083),n=r(59434);function o(e){let{className:t,...r}=e;return(0,s.jsx)(a.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r})}}}]);